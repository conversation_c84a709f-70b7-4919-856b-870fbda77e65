<template>
    <Card title="施工事件分布统计" card-type="card-long-2">
        <template #content>
            <div class="card-content">
                <SingleDataBar
                    v-if="show"
                    y-name="数量"
                    :need-legend="true"
                    :bar-width="20"
                    :split-number="10"
                    :legend-data="legendData"
                    :data="cardData"
                />
            </div>
        </template>
        <template #titleContent>
            <DatePicker
                v-model="dateTime"
                style="height: fit-content;"
                size="mini"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
            />
        </template>
    </Card>
</template>

<script setup>
import {Card, SingleDataBar} from '@/components/Common';
import {ref, watch} from 'vue';
import {DatePicker} from 'element-ui';
import dayjs from 'dayjs';
import {getRoadEventNum} from '@/api/construction';

// 时间筛选
const dateTime = ref(dayjs().format('YYYY-MM-DD'));

const legendData = [
    {
        name: '施工事件分布数量',
        color: 'rgba(87, 255, 213, .6)',
    },
];

const show = ref(true);
const cardData = ref([]);

async function fetchData() {
    show.value = false;
    const {data} = await getRoadEventNum({
        day: dateTime.value,
    });
    cardData.value = data?.roadEventList?.map(item => ({
        name: item.roadName,
        value: item.eventNum,
        color: 'rgba(87, 255, 213, .6)',
    }));
    show.value = true;
}

watch(
    () => dateTime.value,
    () => {
        fetchData();
    },
    {
        immediate: true,
    }
);

</script>

<style lang="less" scoped>
.card-content {
    height: 315px;
}
</style>