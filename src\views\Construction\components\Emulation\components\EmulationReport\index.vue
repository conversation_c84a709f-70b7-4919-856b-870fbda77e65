<template>
    <modal-card
        :visible="visible"
        :width="1150"
        :show-foot="false"
        @close="$emit('update:visible', false)"
    >
        <template #title>
            <div class="report-title">
                <span>{{ reportTips }}</span>
            </div>
        </template>
        <template
            v-if="reportData?.avgSpeedInfos?.length > 1"
            #head-right
        >
            <div class="btn-strategy">
                <el-checkbox
                    v-model="isHasPolicy"
                >
                    策略下发对比
                </el-checkbox>
            </div>
        </template>
        <div class="report-content show-scroll">
            <draggable
                v-model="chartList"
                class="echarts-list"
                animation="200"
                group="module"
            >
                <div
                    v-for="item in chartList"
                    :key="item.name"
                    class="echarts-item"
                >
                    <div class="search">
                        <span>{{ item.name }}</span>
                        <div class="form-warp">
                            <search-form
                                :emulation-time="emulationTime"
                                :type="item.code"
                                @search="handleSearchData"
                            />
                        </div>
                    </div>
                    <div class="echarts-box">
                        <echarts-items
                            :key="item.key"
                            :data-props="item.data"
                            :type="item.code"
                            :name="item.key"
                            :color="item.color"
                            :echarts-data="{
                                data: reportData[item.key],
                                flag: isHasPolicy,
                            }"
                        />
                    </div>
                </div>
            </draggable>
        </div>
    </modal-card>
</template>

<script>
import {ModalCard} from '@/components/Common';
import {ref, watch} from 'vue';
import draggable from 'vuedraggable';
import SearchForm from './SearchForm.vue';
import EchartsItems from './Charts/EchartsItem.vue';
import {getEvaluateReport} from '@/api/emulation';
import {Checkbox} from 'element-ui';

export default {
    components: {
        ModalCard,
        draggable,
        SearchForm,
        EchartsItems,
        [Checkbox.name]: Checkbox,
    },
    props: {
        visible: Boolean,
        schemeId: Number,
        emulationTime: Object,
    },
    setup(props) {

        const chartList = ref([
            {
                code: 1,
                name: '排队长度',
                key: 'queueInfos',
                data: 'queueList',
                color: '#01FFE5',
            },
            {
                code: 2,
                name: '拥堵里程',
                key: 'congestLengthInfos',
                data: 'lengthList',
                color: '#0077FF',
            },
            {
                code: 3,
                name: '拥堵指数',
                key: 'congestIndexInfos',
                data: 'indexList',
                color: '#CFCC7F',
            },
            {
                code: 4,
                name: '平均速度',
                key: 'avgSpeedInfos',
                data: 'avgSpeedList',
                color: '#842FFF',
            },
            {
                code: 5,
                name: '流量趋势',
                key: 'flowTrendInfos',
                data: 'flowList',
                color: '#01FFE5',
            },
            {
                code: 6,
                name: '服务水平',
                key: 'serveLevelInfos',
                data: 'serveList',
                color: '#0077FF',
            },
            {
                code: 7,
                name: '路段饱和度',
                key: 'saturationInfos',
                data: 'saturationList',
                color: '#CFCC7F',
            },
            {
                code: 8,
                name: '路段拥堵排名',
                key: 'congestRankings',
                data: 'congestRankingDetailList',
                color: '#842FFF',
            },
            {
                code: 9,
                name: '平均排队长度',
                key: 'queueRankings',
                data: 'queueRankingDetailList',
                color: '#842FFF',
            },
        ]);
        const reportTips = ref('');
        const reportData = ref([]);
        const isHasPolicy = ref(false);

        async function fetchReportData() {
            const {data} = await getEvaluateReport({
                schemeId: props.schemeId,
            });
            reportTips.value = data.evaluateTips;
            reportData.value = data;
        }

        async function handleSearchData(params) {
            const paramsData = {
                schemeId: props.schemeId,
                evaluateType: params.evaluateType,
                startTime: params.startTime || undefined,
                endTime: params.endTime || undefined,
            };
            const {data} = await getEvaluateReport(paramsData);
            const key = chartList.value?.find(item => item.code === params.evaluateType)?.key;
            reportData.value[key] = data[key];
        }

        watch(
            () => props.schemeId,
            val => {
                if (val) {
                    reportTips.value = '';
                    reportData.value = [];
                    fetchReportData();
                }
            },
            {
                immediate: true,
            }
        );

        return {
            chartList,
            reportTips,
            reportData,
            isHasPolicy,
            handleSearchData,
        };
    },
};
</script>

<style lang="less" scoped>

.report {
    &-content {
        padding: 20px;
        height: 640px;
        overflow-y: auto;
    }

    &-title {
        font-size: 16px;

        span {
            font-size: 14px;
        }
    }
}

.echarts {
    &-list {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
    }

    &-item {
        width: calc((100% - 90px) / 2);
        margin-bottom: 50px;
    }

    &-box {
        width: 100%;
        height: 228px;
        // background: rgba(0,10,40,0.3);
        // border: 1px solid rgba(45, 83, 151, .24);
        margin-top: 16px;
        background-image: url('/src/assets/images/grid-bg.png');
        background-size: 100% 100%;
    }
}

.search {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    >span {
        margin-right: 8px;
        width: 160px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        position: relative;
        padding-left: 12px;

        &::before {
            content: '';
            width: 4px;
            height: 12px;
            background-color: #28c282;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
        }
    }

    .search-form {
        width: 100%;
        display: flex;
        justify-content: space-between;
    }
}

.btn-strategy {
    margin-right: 4px;
    transform: scale(.8);
}

</style>