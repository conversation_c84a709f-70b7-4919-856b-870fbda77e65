<template>
    <div class="info-card" :style="{'--w': widthPx, '--h': heightPx}">
        <div class="card-content">
            <div class="info-card__left">
                <div v-if="needIcon" class="info-card__left__icon">
                    <icon :name="info.icon || 'light'"/>
                </div>
                <div class="info-card__left__info">
                    <span class="cnTitle" :style="{fontSize: styleObj.cnTitleFontSize * ratio + 'px'}">
                        {{ info.cnTitle }}
                    </span>
                    <span class="enTitle" :style="{fontSize: styleObj.enTitleFontSize * ratio + 'px'}">
                        {{ info.enTitle }}
                    </span>
                </div>
            </div>
            <div class="info-card__right" :style="{fontSize: styleObj.unitFontSize * ratio + 'px'}">
                <span class="value" :style="{fontSize: styleObj.numFontSize * ratio + 'px'}">{{ info.value }}</span>
                <span
                    v-if="info.unit" class="unit"
                    :style="{fontSize: styleObj.unitFontSize * ratio + 'px'}"
                >{{ info.unit }}</span>
            </div>
        </div>

        <div
            v-if="needProgress" class="proge"
            :style="{'--high': info.high, '--middle': info.middle, '--low': info.low, '--middleWord': info.middleWord}"
        >
            <div class="progress-wrapper">
                <div class="progress-red progress"></div>
                <div class="progress-blue progress"></div>
                <div class="progress-yellow progress"></div>
            </div>
            <div class="progress-separator">
                <div class="separator separator-high"></div>
                <div class="separator separator-middle"></div>
                <div class="separator separator-low"></div>
            </div>
            <div class="word-separator">
                <div class="word word-high">
                    <span class="word-title">高</span>
                    <span class="word-number">{{ info.highCount }}</span>
                </div>
                <div class="word word-middle">
                    <span class="word-title">中</span>
                    <span class="word-number">{{ info.centerCount }}</span>
                </div>
                <div class="word word-low">
                    <span class="word-title">低</span>
                    <span class="word-number">{{ info.lowCount }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {Icon} from '@/components/Common';
import {useUnit} from '@/utils/hooks/useUnit';
import {computed} from 'vue';

export default {
    name: 'info-card',
    props: {
        info: {
            type: Object,
            default: () => ({}),
        },
        width: {
            type: [String, Number],
            default: '274',
        },
        height: {
            type: [String, Number],
            default: '72',
        },
        needIcon: {
            type: Boolean,
            default: true,
        },
        needProgress: {
            type: Boolean,
            default: true,
        },
        styleObj: {
            type: Object,
            default: () => ({
                cnTitleFontSize: 18,
                enTitleFontSize: 14,
                unitFontSize: 14,
                numFontSize: 32,
            }),
        },
    },
    components: {
        Icon,
    },
    setup(props) {
        const {ratio} = useUnit();
        const widthPx = computed(() => ratio.value * props.width + 'px');
        const heightPx = computed(() => ratio.value * props.height + 'px');

        return {
            ratio,
            widthPx,
            heightPx,
        };
    },
};
</script>

<style lang="less" scoped>
.info-card {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: var(--w);
    height: var(--h);
    padding: 0 0 0 24px;
    background-color: rgba(18, 74, 166, 0.24);
    border-top: 1px solid;
    border-image: linear-gradient(to right, rgb(36, 104, 242), rgba(1, 255, 229, .5)) 1;
    backdrop-filter: blur(10px);
    position: relative;

    .proge {
        width: 100%;
        padding: 0 24px 0 40px;
        margin-top: 22px;
        .progress-wrapper {
            width: 100%;
            display: flex;
            height: 8px;
            overflow: hidden;

            .progress {
                height: 100%;
                background-size: 12px 100%;
                transform: skew(-45deg);
            }

            .progress-red {
                width: var(--high);
                background-image: linear-gradient(to right, rgb(255, 105, 105) 8px, transparent 4px);
            }

            .progress-blue {
                width: var(--middle);
                background-image: linear-gradient(to right,  rgb(255, 249, 147) 8px, transparent 4px);
                margin: 0 4px;
            }

            .progress-yellow {
                width: var(--low);
                background-image: linear-gradient(to right, rgb(0, 255, 114) 8px, transparent 4px);
            }
        }

        .progress-separator {
            position: relative;
            width: 100%;
            .separator {
                position: absolute;
                bottom: -9px;
                width: 4px;
                height: 6px;
                background-color: #fff;
                clip-path: polygon(50% 0, 100% 100%, 0 100%);

                &-high {
                    left: 0;
                }

                &-middle {
                    left: var(--high);
                }

                &-low {
                    right: 0;
                }
            }
        }

        .word-separator {
            position: relative;
            width: 100%;
            .word {
                position: absolute;
                bottom: -28px;

                span {
                    &.word-title {
                        font-size: 14px;
                        font-family: 'PingFang';
                        font-weight: 500;
                        color: rgba(#fff, .5);
                    }
                    &.word-number {
                        font-size: 16px;
                        font-family: 'RoboData';
                        font-weight: 400;
                        color: rgba(#fff, .9);
                        margin-left: 2px;
                    }
                }

                &-high {
                    left: 0;
                }

                &-middle {
                    left: var(--middleWord);
                }

                &-low {
                    right: 0;
                }
            }
        }
    }

    .card-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    &.active {
        background-image: linear-gradient(to right, rgba(1, 255, 229, .3), rgba(1, 255, 229, 0));
    }

    &__left {
        display: flex;
        align-items: flex-start;

        &__icon {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 31px;
            height: 24px;
            margin-right: 9px;
            background: url('@/assets/images/base/iconBg.png') no-repeat center center / 100% 100%;
        }

        &__info {
            display: flex;
            flex-direction: column;
            width: 164px;

            .cnTitle {
                font-family: 'PingFang';
                font-weight: 500;
                color: #fff;
                margin-bottom: 6px;
            }

            .enTitle {
                display: inline-block;
                white-space: nowrap;
                overflow: hidden;
                text-overflow:ellipsis;
                width: 100%;
                font-family: 'RoboData';
                font-weight: 400;
                color: rgba(#fff, .3);
                overflow: hidden;
            }
        }
    }

    &__right {
        position: absolute;
        right: 21px;
        font-weight: 400;
        font-family: 'RoboData';
        color: rgba(#fff, .5);

        .value {
            font-family: 'RoboData';
            color: rgba(#fff, .9);
            margin-right: 4px;
        }
    }
}
</style>