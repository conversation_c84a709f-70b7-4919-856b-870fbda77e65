<template>
    <div class="breakdown">
        <div class="breakdown-list">
            <!-- <div class="breakdown-list-title">故障处理清单列表</div> -->
            <div class="breakdown-list-table">
                <div class="list-table-search">
                    <div class="list-table-search-left">
                        <div class="search-input">
                            <span class="search-title">故障时间：</span>
                            <el-date-picker
                                v-model="times"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                @change="timeChangeFn"
                            />
                        </div>
                        <div class="search-input">
                            <maintain-select
                                v-model="params.systemId" title="系统选择："
                                :options="systemDict"
                            />
                        </div>
                        <div class="search-input">
                            <span>设备查询：</span>
                            <el-input
                                v-model="params.deviceCondition" size="small"
                                placeholder="请输入设备名称/设备IP/设备桩号"
                            />
                        </div>
                        <div class="search-input">
                            <maintain-select
                                v-model="params.source" title="故障来源："
                                :options="sourceDict"
                            />
                        </div>
                    </div>
                    <div class="list-table-search-right">
                        <search-buttons
                            @search="searchFn"
                            @reset="resetFn"
                        />
                    </div>
                </div>
                <my-table
                    :data="data" :column="column"
                    :loading="loading"
                    :page-size.sync="params.pageSize"
                    :current-page.sync="params.pageNumber"
                    :total="total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    @export-excel="exportExcel"
                />
            </div>
        </div>

        <!-- <derive-card v-model="visible"/> -->
    </div>
</template>

<script>
import {ref} from 'vue';
import {Input, DatePicker} from 'element-ui';
import {useUnit} from '@/utils/hooks/useUnit';
import MaintainTable from '../component/maintainTable.vue';
import MaintainSelect from '../component/MaintainSelect.vue';
// import DeriveCard from '../component/deriveCard.vue';
import SearchButtons from '../component/searchButtons.vue';
import dayjs from 'dayjs';
import {getDeviceFaultList, exportFaultExcel} from '@/api/equipment/equipmentdisplay.js';
import {brandDist, typeDict, systemDict, sourceDict, directionDict, faultReasonDict} from '@/config/maintain.js';

export default {
    name: 'Breakdown',
    components: {
        MyTable: MaintainTable,
        MaintainSelect,
        [Input.name]: Input,
        // DeriveCard,
        SearchButtons,
        [DatePicker.name]: DatePicker,
    },
    setup(props) {
        const times = ref('');
        const params = ref({
            startTime: '',
            endTime: '',
            systemId: '',
            source: '',
            deviceCondition: '',
            pageNumber: 1,
            pageSize: 10,
        });

        const {ratio} = useUnit();
        const column = ref([
            {
                prop: 'deviceName',
                label: '设备名称',
            },
            {
                prop: 'deviceType',
                label: '设备类型',
            },
            {
                prop: 'deviceIp',
                label: '设备IP',
                width: 180 * ratio.value,
            },
            {
                prop: 'direction',
                label: '方向',
                width: 180 * ratio.value,
            },
            {
                prop: 'source',
                label: '故障来源',
            },
            {
                prop: 'faultReason',
                label: '故障原因',
                width: 180 * ratio.value,
            },
            {
                prop: 'faultTime',
                label: '故障开始时间',
            },
            {
                prop: 'faultNum',
                label: '告警次数',
                width: 140 * ratio.value,
            },
        ]);
        const data = ref([]);

        // 修改时间
        const timeChangeFn = e => {
            if (!e) {
                params.value.startTime = '';
                params.value.endTime = '';
                return;
            }
            params.value.startTime = dayjs(e[0]).format('YYYY-MM-DD HH:mm:ss');
            params.value.endTime = dayjs(e[1]).format('YYYY-MM-DD HH:mm:ss');
        };

        const loading = ref(false);
        const total = ref(0);
        const searchFn = () => {
            loading.value = true;
            getDeviceFaultList(params.value).then(res => {
                total.value = res.data.total;
                data.value = res.data.records.map(item => {
                    return {
                        ...item,
                        deviceBrand: brandDist.get(item.deviceBrand),
                        deviceType: typeDict.find(e => e.value === item.deviceType)?.label || '暂无信息',
                        direction: directionDict.get(item.direction) || '暂无信息',
                        source: sourceDict.find(e => e.value === item.source)?.label || '暂无信息',
                        faultReason: faultReasonDict.get(item.faultReason),
                    };
                });
                loading.value = false;
            }).catch(err => {
                loading.value = false;
            });
        };
        searchFn();
        const resetFn = () => {
            params.value = {
                startTime: '',
                endTime: '',
                systemId: '',
                source: '',
                deviceCondition: '',
                pageNumber: 1,
                pageSize: 10,
            };
            times.value = '';
            searchFn();
        };

        // const visible = ref(false);

        // 分页器修改
        const handleSizeChange = e => {
            params.value.pageSize = e;
            searchFn();
        };
        const handleCurrentChange = e => {
            params.value.pageNumber = e;
            searchFn();
        };

        // 导出
        const exportExcel = e => {
            exportFaultExcel({
                deviceIdList: e,
            }).then(res => {
                // 下载
                const blob = new Blob([res]);
                const fileName = '故障处理清单.xlsx';
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = fileName;
                link.click();
                URL.revokeObjectURL(link.href);
            });
        };

        return {
            // visible,
            data,
            times,
            column,
            params,
            total,
            systemDict,
            sourceDict,
            loading,
            timeChangeFn,
            searchFn,
            resetFn,
            handleSizeChange,
            handleCurrentChange,
            exportExcel,
        };
    },
};
</script>

<style lang="less" scoped>
.breakdown {
    height: 100%;
}
.breakdown-list {
    width: 100%;
    height: 100%;

    .breakdown-list-title {
        width: 100%;
        height: 40px;
        line-height: 40px;
        font-size: 16px;
        font-weight: 500;
        color: #fff;
        padding-left: 16px;
        font-family: 'OPlusSans';
        border-bottom: 1px solid rgba(255, 255, 255, .2);
    }

    .breakdown-list-table {
        padding: 0 24px;
    }

    .list-table-search {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 80px;

        .list-table-search-left {
            display: flex;
            align-items: center;
            flex: 1;
            margin-right: 30px;

            > div {
                flex: 1;
                display: flex;
                align-items: center;
                margin-right: 24px;

                &:last-child {
                    margin-right: 0;
                }

                span {
                    display: inline-block;
                    width: 120px;
                    font-weight: 400;
                    font-family: 'OPlusSans';
                    font-size: 14px;
                    color: rgba(255, 255, 255, 0.6);
                }

                :deep(.el-date-editor) {
                    width: 100%;
                }

                :deep(.el-input__inner) {
                    height: 32px;
                    i, .el-range-separator {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 100%;
                    }
                }
            }
        }
    }
}
</style>