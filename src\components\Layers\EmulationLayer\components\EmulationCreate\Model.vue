<template>
    <div style="width: 100%;">
        <div
            v-for="item in model_list"
            :key="item.key"
            class="edit-item"
        >
            <span class="w-140">{{ item.label }}：</span>
            <el-input
                v-model="modelInfo[item.key]"
                class="f-1 h-40"
                placeholder="请输入"
            >
                <span
                    v-if="item.unit"
                    slot="suffix"
                    class="suffix-text"
                >{{ item.unit }}</span>
            </el-input>
        </div>
    </div>
</template>

<script>
import {computed} from 'vue';
import {Input} from 'element-ui';
import {model_list} from './config';

export default {
    name: 'ModelComponent',
    props: {
        data: {
            type: Object,
        },
    },
    components: {
        [Input.name]: Input,
    },
    inject: [],
    setup(props) {
        const modelInfo = computed(() => props.data);

        const validate = () => {
            for (let item of model_list) {
                const {key, label} = item;
                const val = modelInfo.value[key];
                // if (val > 0) {

                // } else {

                // }
            }
            model_list;
        };

        return {
            modelInfo,
            model_list,
            validate,
        };
    },

};
</script>

<style lang="less" scoped>
.edit-item {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    margin-left: 10px;

    >img {
        width: 18px;
        height: 28px;
        margin-left: 12px;
        cursor: pointer;
    }

    .point-btn {
        background: #49c3ff;
        color: #e5f1ff;
        display: inline-block;
        width: 52px;
        height: 28px;
        font-size: 14px;
        border-radius: 4px;
        margin-left: 8px;
        text-align: center;
        line-height: 28px;
    }

    >span {
        display: inline-block;
        width: 115px;
        font-size: 14px;
        color: #c4cfde;
        letter-spacing: 0;
        flex-shrink: 0;
    }

    .suffix-text {
        line-height: 40px;
        padding-right: 5px;
    }
}
</style>