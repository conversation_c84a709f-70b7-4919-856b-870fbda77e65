
<!-- 宏观视角图层 -->
<script>
import {ref, onMounted, onBeforeUnmount, watch} from 'vue';
export default {
    props: {
        // 基本信息 包括位置、名称 自定义数据 点击回调等
        info: {
            type: Object,
            default: () => null,
        },
        // 宏观管理实例
        macroInstace: {
            type: Object,
            default: () => null,
        },
    },
    setup(props, {slots}) {

        const marker = ref();
        const iconName = ref(null);
        const onClick = e => {
            props.info.clickCallback && props.info.clickCallback(e);
        };

        const addIcon = () => {
            if (!props.info.position) return;
            iconName.value = props.info.pointName;
            props.macroInstace.add(
                iconName.value,
                props.info.position,
                {
                    labelText: props.info.labelName,
                    text: props.info.number,
                    customData: props.info.customData,
                    clickCallback: onClick,
                    color: props.info.color || '#75FFCE',
                }
            );
        };

        const removeIcon = () => {
            iconName.value && props.macroInstace.removeByName(iconName.value);
        };

        const onHandle = () => {
            props.info.clickCallback(props.info);
        };

        watch(() => props.info, val => {
            if (val) {
                addIcon();
            }
            else {
                removeIcon();
            }
        }, {
            deep: true,
        });

        onMounted(() => {
            addIcon();
        });

        onBeforeUnmount(() => {
            removeIcon();
        });
        return {
            marker,
            onHandle,
        };
    },
};
</script>
