<template>
    <div>
        <macro-maker
            v-for="item in list"
            :key="item.pointId"
            :macro-instace="macroManager"
            :info="{
                pointId: item.pointId,
                position: item.position,
                pointName: item.pointId,
                number: item.pointValue,
                labelName: item.pointName,
                customData: item,
                clickCallback: () => callback(item),
            }"
        />
    </div>
</template>

<script>
import {macroManager} from '@MaintainPlanLayer/utils/Map/index';
import MacroMaker from '@/components/Common/MacroMaker/index.vue';
import {viewToMac} from '@/utils';
import {onMounted} from 'vue';
export default {
    props: {
        list: {
            type: Array,
            default: () => [],
        },
        callback: {
            type: Function,
            required: true,
        },
    },
    components: {
        MacroMaker,
    },
    setup() {

        onMounted(() => {
            viewToMac();
        });

        return {
            macroManager,
        };
    },
};
</script>