<template>
    <Card title="仿真方案概览" card-type="card-long-2">
        <template #content>
            <div class="overview-list">
                <div
                    v-for="card in getCardInfo"
                    :key="card.title"
                    class="overview-item"
                >
                    <div class="overview-item__top">
                        <div class="overview-item__icon">
                            <Icon :name="card.icon"/>
                        </div>
                        <div class="overview-item__title">
                            <p>{{ card.title }}</p>
                            <p>{{ card.enTitle }}</p>
                        </div>
                    </div>
                    <div class="overview-item__bottom">
                        <span>{{ card.value }}</span>
                        <span v-if="card.value !== undefined">{{ card.unit }}</span>
                    </div>
                </div>
            </div>
        </template>
    </Card>
</template>

<script setup>
import {Card, Icon} from '@/components/Common';
import {emulationTargetData} from '../index';
import {computed} from 'vue';

const cardConfig = [
    {
        title: '路网饱和度',
        enTitle: 'Saturation of road network',
        field: 'saturation',
        icon: 'saturation',
    },
    {
        title: '服务水平',
        enTitle: 'service level',
        field: 'avgSpeed',
        icon: 'service-level',
    },
    {
        title: '平均速度',
        enTitle: 'average speed',
        field: 'serviceLevel',
        icon: 'avg-speed',
        unit: 'km/h',
    },
];

const getCardInfo = computed(() => (
    cardConfig.map(card => ({
        ...card,
        value: emulationTargetData.value[card.field],
    }))
));

</script>

<style lang="less" scoped>
.overview {
    &-list {
        display: flex;
        justify-content: space-between;
    }

    &-item {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 384px;
        height: 172px;
        padding: 24px;
        background-color: rgba(18, 74, 166, .24);
        backdrop-filter: blur(10px);
        border-top: 1px solid;
        border-image: linear-gradient(to right, rgba(1, 255, 229, .5), rgb(36, 104, 242),) 1;

        &__icon {
            width: 36px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-image: url('@/assets/images/base/ranking-bg.svg');
            background-size: 100% 100%;
            margin-right: 10px;
        }

        &__title {
            padding-top: 4px;

            p:nth-child(1) {
                color: #fff;
                font-size: 20px;
            }

            p:nth-child(2) {
                font-size: 14px;
                font-family: RoboData;
                margin-top: 12px;
                text-transform: uppercase;
            }
        }

        &__top {
            display: flex;
        }

        &__bottom {
            text-align: right;
            font-size: 14px;

            span:nth-child(1) {
                color: #fff;
                font-family: RoboData;
                font-size: 42px;
            }

            span:nth-child(2) {
                margin-left: 4px;
            }
        }
    }
}
</style>