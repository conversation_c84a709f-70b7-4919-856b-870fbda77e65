# WeatheWarningLayer 恶劣天气预警图层

## 整体图层

### 介绍
提供了WeatheWarningLayer图层组件，能够快速构建恶劣气象监测图层并提供了默认组件，以及各子组件，可使用其中的子组件进行二次开发。

### 安装教程

1.  npm i apaas-maplayer
2. import {WeatheWarningLayer} from 'apaas-maplayer'

### 使用方法
> 先在外部初始化组件
```javascript
import {WeatheWarningLayer} from 'apaas-maplayer'

> 注册组件
```javascript
components: {
  WeatheWarningLayer
}
```
> template中声明

```html
<template>
  <section>
    <map-component
        ref="mapRef"
        :options="{
            center: [113.55, 23.5555],
            showSatelliteMap: true,
        }"
        class="map-box"
        :style="{
            height: height + 'px',
        }"
        @mapLoaded="mapLoaded"
    >
        <weathe-warning-layer
            v-if="mapShow"
        />
    </map-component>
  </section>
</template>
```

### 注意事项
需要先调用 `initWeatheWarningLayerConfig()` 方法进行初始化配置，地图初始化执行完毕后再让结构物健康监测图层渲染。示例如下：

```html
<template>
  <section>
     <map-component
        ref="mapRef"
        :options="{
            center: [113.55, 23.5555],
            showSatelliteMap: true,
        }"
        class="map-box"
        :style="{
            height: height + 'px',
        }"
        @mapLoaded="mapLoaded"
    >
        <weathe-warning-layer
            v-if="mapShow"
        />
    </map-component>
  </section>
</template>

<script setup>
    const mapShow = ref(false);

    // 地图加载完成后执行的回调函数（此处由使用方控制）
    const mapLoaded = () => {
        initWeatheWarningLayerConfig({
            engine: mapRef.value.map,
            apiHost,
        });
        mapShow.value = true;
    };

</script>
```

### 参数说明  props
|Prop name|Type|Default|Description|
|---|---|---|---|
|`engine`|`object`|`required`|地图实例|
|`apiHost`|`string`|`可选 默认为window.location.origin`|接口服务地址|
|`wsHost`|`string`|`可选 默认为window.location.host`|ws服务地址|
|`BaseVideoHost`|`string`|`可选 默认为window.location.origin`|视频组件服务地址|


## 恶劣气象监测子组件
### 天气实况组件 WeatherLive
#### 使用
```html
<script>
    import {WeatheWarning_WeatherLive} from'apaas-maplayer'

    components: {
        WeatheWarning_WeatherLive
    }
</script>

<template>
    <!-- 天气实况图层 直接调用此图层可直接展示默认数据和展示状态 也可自定义组合组件进行二次开发-->
    <weathe-warning_weather-live/>
</template>
```


## 恶劣气象监测子组件
### 天气实况组件二次开发 WeatheWarning_WeatherLive_Maker
#### 使用
```html
<script>
    import {WeatheWarning_WeatherLive_Maker} from'apaas-maplayer'

    components: {
        WeatheWarning_WeatherLive_Maker
    }
</script>

<template>
    <!-- 天气实况图层扎点组件-->
    <div
            v-for="item in liveData"
            :key="item.orgId"
        >
            <weathe-warning_weather-live-maker
                :info="{
                    position: [item.longitude, item.latitude],
                    pointName: item.adcode,
                    customData: item,
                }"
                @onHandle="onHandle"
            >
            <!-- 如果想自定义maker展示 可以传一个slot 如下 -->
            <div>这是一个自定义maker</div
            </weathe-warning_weather-live-maker>
        </div>
</template>
```

#### 所需参数
|参数名称|数据类型|是否必传|参数含义|
|---|---|---|---|
|domManagerInstace|`Object`|`可选`|dom图层管理器|
|info|`object`|`required`|maker所需数据options|
|onHandle|`function`|`required`|点击maker回调|

#### 参数详细说明
info:
```js

{
    "position": [113.1, 26.2], // 位置信息  必须
    "pointName": 'maker名称', // maker名称 必须唯一
    "customData": {a: 1, b: 2}, // 自定义数据项 主要用做点击事件回调数据
}
```
@onHandle:
```js
 // 点击回调
    const onHandle = async data => {
        // customData数据
        console.log(data);
    };
```
### 接口数据样例
```js
// 天气类型枚举
export const weatherTypeMap = {
    1: 'qing',
    2: 'yu',
    3: 'wu',
    4: 'xue',
    5: 'lei',
    6: 'shachen',
    7: 'bingbao',
    8: 'yun',
    9: 'yin',
};
liveData = [
    {
        "adcode": 440100, // 行政区划代码
        "name": "广州市", // 行政区划名称
        "temperature": 28, // 温度  
        "minimumTemperature": 23, // 最低温度
        "maximumTemperature": 32, // 最高温度
        "weatherCode": 3, // 天气代码
        "weather": "雾", // 天气名称
        "longitude": 113.2644, // 位置经度
        "latitude": 23.1291 // 位置纬度
    }
]
```


## 恶劣气象监测子组件
### 天气实况组件二次开发 WeatheWarning_WeatherLive_Popup
#### 使用
```html
<script>
    import {WeatheWarning_WeatherLive_Popup} from'apaas-maplayer'

    components: {
        WeatheWarning_WeatherLive_Popup
    }
</script>

<template>
    <!-- 天气实况图层扎点组件-->
 
        <WeatheWarning_WeatherLive_Popup
            :id="id"
            :detail="popupInfo"
            @close="onClose"
        >
        <!-- 如果想自定义弹窗展示 可以传一个slot 如下 -->
        <div>这是一个自定义content</div
        </WeatheWarning_WeatherLive_Popup>
</template>
```

#### 所需参数
|参数名称|数据类型|是否必传|参数含义|
|---|---|---|---|
|id|`number|string`|`可选`|唯一ID 可用于控制是否展示弹窗|
|detail|`object`|`required`|所需数据options|
|close|`function`|`required`|关闭回调|

#### 参数详细说明
detail:
```js

{
    "position": [113.1, 26.2], // 位置信息 必须
    'xxx' // 自定义数据项
}
```
@close:
```js
 // 点击回调
    const onclose = async () => {};
```

### 恶劣天气预警组件 WeatheWarning_Warning
#### 使用
```html
<script>
    import {WeatheWarning_Warning} from'apaas-maplayer'

    components: {
        WeatheWarning_Warning
    }
</script>

<template>
    <weathe-warning_warning/>
</template>
```

## 恶劣气象监测子组件
### 天气预警组件二次开发 WeatheWarning_Warning_Maker
#### 使用
```html
<script>
    import {WeatheWarning_Warning_Maker} from'apaas-maplayer'

    components: {
        WeatheWarning_Warning_Maker
    }
</script>

<template>
    <!-- 天气实况图层扎点组件-->
    <div
            v-for="item in warningData"
            :key="item.alertId"
        >
            <weathe-warning_warning-maker
                :info="{
                    position: [item.longitude, item.latitude],
                    pointName: item.adcode,
                    customData: item,
                }"
                @onHandle="onHandle"
            >
            <!-- 如果想自定义maker展示 可以传一个slot 如下 -->
            <div>这是一个自定义maker</div
            </weathe-warning_warning-maker>
        </div>
</template>
```

#### 所需参数
|参数名称|数据类型|是否必传|参数含义|
|---|---|---|---|
|domManagerInstace|`Object`|`可选`|dom图层管理器|
|info|`object`|`required`|maker所需数据options|
|onHandle|`function`|`required`|点击maker回调|

#### 参数详细说明
info:
```js

{
    "position": [113.1, 26.2], // 位置信息
    "pointName": 'maker名称', // maker名称 必须唯一
    "customData": {a: 1, b: 2}, // 自定义数据项 主要用做点击事件回调数据
}
```
@onHandle:
```js
 // 点击回调
    const onHandle = async data => {
        // customData数据
        console.log(data);
    };
```
### 接口数据样例
```js
warningData = [
    {
        "alertId": 1, // 预警ID
        "fbcc": "FBCC1", // 预警编号
        "forewarning": "台风蓝色预警", // 预警内容
        "type": 1, // 预警类型
        "level": 1, // 预警等级
        "longitude": 113.202557, // 预警经度
        "latitude": 23.783068 // 预警纬度
    },
]
```


## 恶劣气象监测子组件
### 天气实况组件二次开发 WeatheWarning_Warning_Popup
#### 使用
```html
<script>
    import {WeatheWarning_Warning_Popup} from'apaas-maplayer'

    components: {
        WeatheWarning_Warning_Popup
    }
</script>

<template>
    <!-- 天气实况图层扎点组件-->
 
        <weathe-warning_warning_popup
            :id="id"
            :detail="popupInfo"
            @close="onClose"
        >
        <!-- 如果想自定义弹窗展示 可以传一个slot 如下 -->
        <div>这是一个自定义content</div
        </weathe-warning_warning_popup>
</template>
```

#### 所需参数
|参数名称|数据类型|是否必传|参数含义|
|---|---|---|---|
|id|`number|string`|`可选`|唯一ID 可用于控制是否展示弹窗|
|detail|`object`|`required`|所需数据options|
|close|`function`|`required`|关闭回调|
|road|`function`|`required`|点击查看影响路段回调|

#### 参数详细说明
detail:
```js

{
    "position": [113.1, 26.2], // 位置信息 必须
    'xxx' // 自定义数据项
}
```
@close:
```js
 // 点击回调
    const onclose = async () => {};
```

@road:
```js
 // 点击回调
    const onroad = async () => {};
```


# 恶劣气象监测子组件
### 天气预警组件 路段基础信息卡片组件
#### 使用
```html
<script>
    import {WeatheWarning_Warning_BaseCard} from'apaas-maplayer'

    components: {
        WeatheWarning_Warning_BaseCard
    }
</script>

<template>
    <!-- 天气实况图层扎点组件-->
 
        <weathe-warning_warning_baseCard :info="info" :title="标题"/>

        <!-- 也可以通过slot自定义展示区域数据 -->

         <weathe-warning_warning_baseCard :info="info" :title="标题">
             <div>这是一个自定义展示区域</div
         </weathe-warning_warning_baseCard>
</template>
```

#### 所需参数
|参数名称|数据类型|是否必传|参数含义|
|---|---|---|---|
|title|`string`|`可选 默认为‘路段基本信息’`|卡片标题名称|
|info|`object`|`required`|所需数据options|

#### 参数详细说明
info:
```js

{
    "roadSectionName": "乐广高速附近,南向北", // 道路名称
    "startStake": "K260+000", // 起点桩号
    "endStake": "K266+000" // 终点桩号
}
```


# 恶劣气象监测子组件
### 天气预警组件 路段应急响应卡片组件
#### 使用
```html
<script>
    import {WeatheWarning_Warning_EmergencyCard} from'apaas-maplayer'

    components: {
        WeatheWarning_Warning_EmergencyCard
    }
</script>

<template>
    <!-- 天气实况图层扎点组件-->
 
        <WeatheWarning_Warning_EmergencyCard :info="info" :title="标题"/>

        <!-- 也可以通过slot自定义展示区域数据 -->

         <WeatheWarning_Warning_EmergencyCard :info="info" :title="标题">
             <div>这是一个自定义展示区域</div
         </WeatheWarning_Warning_EmergencyCard>
</template>
```

#### 所需参数
|参数名称|数据类型|是否必传|参数含义|
|---|---|---|---|
|title|`string`|`可选 默认为‘路段基本信息’`|卡片标题名称|
|info|`object`|`required`|所需数据options|

#### 参数详细说明
info:
```js

{
    "level": 1, // 预警等级
    "detourScheme": { // 绕行方案
        "tollName": "花庄收费站",
        "outStake": "K265+000",
        "direction": "上行",
        "startTime": "2023-12-06 18:00:00",
        "endTime": "2023-12-06 19:00:00",
        "bypassRatio": "21:78"
    },
    "controlScheme": { // 管控方案
        "direction": "上行",
        "tollName": "花庄收费站",
        "vehType": "货车"
    }
}
```

# 恶劣气象监测子组件
### 天气预警组件 路段天气预警卡片组件
#### 使用
```html
<script>
    import {WeatheWarning_Warning_RoadWarningCard} from'apaas-maplayer'

    components: {
        WeatheWarning_Warning_RoadWarningCard
    }
</script>

<template>
    <!-- 天气实况图层扎点组件-->
 
        <WeatheWarning_Warning_RoadWarningCard :info="info" :title="标题"/>

        <!-- 也可以通过slot自定义展示区域数据 -->

         <WeatheWarning_Warning_RoadWarningCard :info="info" :title="标题">
             <div>这是一个自定义展示区域</div
         </WeatheWarning_Warning_RoadWarningCard>
</template>
```

#### 所需参数
|参数名称|数据类型|是否必传|参数含义|
|---|---|---|---|
|title|`string`|`可选 默认为‘路段基本信息’`|卡片标题名称|
|info|`object`|`required`|所需数据options|

#### 参数详细说明
info:
```js

{
    "fbcc": "FBCC3", 
    "forewarning": "台风黄色预警",
    "type": 1,
    "level": 2,
    "startTime": "2023-12-06 13:15:00",
    "endTime": null,
    "longitude": 113.303793,
    "latitude": 23.580061
}
```

# 恶劣气象监测子组件
### 天气预警组件 实时视频卡片组件
#### 使用
```html
<script>
    import {WeatheWarning_Warning_VideoCard} from'apaas-maplayer'

    components: {
        WeatheWarning_Warning_VideoCard
    }
</script>

<template>
    <!-- 天气实况图层扎点组件-->
 
        <WeatheWarning_Warning_VideoCard :info="info" :title="标题"/>

        <!-- 也可以通过slot自定义展示区域数据 -->

         <WeatheWarning_Warning_VideoCard :info="info" :title="标题">
             <div>这是一个自定义展示区域</div
         </WeatheWarning_Warning_VideoCard>
</template>
```

#### 所需参数
|参数名称|数据类型|是否必传|参数含义|
|---|---|---|---|
|title|`string`|`可选 默认为‘路段基本信息’`|卡片标题名称|
|info|`Array`|`required`|所需数据options|

#### 参数详细说明
info:
```js
[
    {
        "assetsCode": "180702010100000030447", // 摄像头编号 必须
        "assetsType": "1807020101",
        "sectionId": "G04W3440011",
        "stake": "K250+800",
        "cameraName": "梯面通信站CCTV35K250+800"
    },
    {
        "assetsCode": "180702010100000029912",
        "assetsType": "1807020101",
        "sectionId": "G04W3440011",
        "stake": "K259+955",
        "cameraName": "花山隧道K259+955"
    }
]
```

# 恶劣气象监测子组件
###  台风路径组件
#### 使用
```html
<script>
    import {WeatheWarning_Typhoon} from'apaas-maplayer'

    components: {
        WeatheWarning_Typhoon
    }
</script>

<template>
    <!-- 台风路径组件-->
        <WeatheWarning_Typhoon/>
</template>
```

# 恶劣气象监测子组件
###  台风路径组件 
#### 使用台风路径maker组件
```html
<script>
    import {WeatheWarning_Typhoon_Maker} from'apaas-maplayer'

    components: {
        WeatheWarning_Typhoon_Maker
    }
</script>

<template>
    <!-- 台风路径组件-->
     <div
            v-for="item in typhoonData"
            :key="item.typhoonCode"
        >
            <WeatheWarning_Typhoon_Maker
                    :info="{
                        position: [item.longitude, item.latitude],
                        pointName: '历史台风路径' + item.id,
                        customData: item,
                        type: 'typhoonHistory',
                    }"
                    @onHandle="onHandleCallback"
            <WeatheWarning_Typhoon_Maker/>

        </div>

         <WeatheWarning_Typhoon_Maker
                :info="{
                    position: [item.longitude, item.latitude],
                    pointName: '历史台风路径' + item.id,
                    customData: item,
                    type: 'typhoonHistory',
                }"
                @onHandle="onHandleCallback">
                <!-- 可以自定义slot marker内容 -->
                <div>这是一个自定义展示区域</div
        <WeatheWarning_Typhoon_Maker/>
</template>
```

#### 所需参数
|参数名称|数据类型|是否必传|参数含义|
|---|---|---|---|
|domManagerInstace|`object`|`可选 默认为dom图层管理器`|dom图层管理器|
|info|`object`|`required`|所需数据options|
|onHandle|`function`|`可选`|点击maker回调 参数为传入的customData|

#### 参数详细说明
info:
```js
{
    position: [item.longitude, item.latitude], // 坐标
    pointName: '历史台风路径' + item.id, // 名称 必须唯一
    customData: item, // 自定义数据
    type: 'typhoonHistory', // 台风类型 历史台风typhoonHistory ｜｜ 中心点台风 typhoonCenter
}
```

### 接口数据样例
```js
 typhoonData = [
    {
        "typhoonCode": "201804", // 台风编号
        "typhoonName": "Ewiniar", // 台风名称
        "reportTime": "2018-06-09 05:00:00",
        "longitude": 113.5, // 台风中心点坐标
        "latitude": 23.6, // 台风中心点坐标
        "intensity": 6, // 风级
        "windSpeed": 7, // 风速
        "pressure": 1003,
        "centerCoordinates": [ // 台风历史路径 此数据可以用于画历史路径线和展示台风maker
            {
                "id": 85,
                "intensity": 6,
                "longitude": 113.3,
                "latitude": 23.6
            },
            {
                "id": 84,
                "intensity": 6,
                "longitude": 112.9,
                "latitude": 23.7
            },
        ]
    }
]
```

# 恶劣气象监测子组件
###  路面结冰组件
#### 使用
```html
<script>
    import {WeatheWarning_Freeze} from'apaas-maplayer'

    components: {
        WeatheWarning_Freeze
    }
</script>

<template>
    <!-- 路面结冰组件-->
        <WeatheWarning_Freeze/>
</template>
```



# 恶劣气象监测子组件
###  路面结冰组件 
#### 使用路面结冰maker组件
```html
<script>
    import {WeatheWarning_Freeze_Maker} from'apaas-maplayer'

    components: {
        WeatheWarning_Freeze_Maker
    }
</script>

<template>
    <!-- 台风路径组件-->
     <div
            v-for="item in freezeData"
            :key="item.typhoonCode"
        >
            <WeatheWarning_Freeze_Maker
                    :info="item?.info"
                    @onHandle="onHandleCallback"
            <WeatheWarning_Freeze_Maker/>

        </div>

         <WeatheWarning_Freeze_Maker
                :info="{
                    position: [item.longitude, item.latitude],
                    pointName: '历史台风路径' + item.id,
                    customData: item,
                    type: 'typhoonHistory',
                }"
                @onHandle="onHandleCallback"
            >
                <!-- 可以自定义slot marker内容 -->
                <div>这是一个自定义展示区域</div
        <WeatheWarning_Freeze_Maker/>
</template>
```

#### 所需参数
|参数名称|数据类型|是否必传|参数含义|
|---|---|---|---|
|domManagerInstace|`object`|`可选 默认为dom图层管理器`|dom图层管理器|
|info|`object`|`required`|所需数据options|
|onHandle|`function`|`可选`|点击maker回调 参数为传入的customData|

#### 参数详细说明
info:
```js
{
    position: [item.longitude, item.latitude], // 坐标
    pointName: '路面结冰' + item.id, // 名称 必须唯一
    customData: item, // 自定义数据
}
```

### 接口数据样例
```js
 typhoonData = [
    {
        "freezeId": 1, // 结冰id
        "highSpeedName": "广乐高速", // 道路名称
        "direction": "上行", // 道路方向
        "stakeNumber": "K249+900", // 桩号
        "iceThickness": 2, // 结冰厚度
        "impactLane": "1,2", // 受影响车道
        "impactLaneName": "一车道,二车道", // 受影响车道名称
        "longitude": 113.259, // 结冰点坐标
        "latitude": 23.595 // 结冰点坐标
    }
]
```