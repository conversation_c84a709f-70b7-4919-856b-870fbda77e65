<template>
    <div>
        <mesoscopic-maker
            v-for="item in list"
            :key="item.pointId + item.pointValue"
            :manager-instace="mesoscopicManager"
            :info="{
                pointId: item.pointId,
                position: item.position,
                pointName: item.pointId,
                number: item.pointValue,
                labelName: item.pointName,
                customData: item,
                clickCallback: () => callback(item),
            }"
        />
    </div>
</template>

<script>
import {mesoscopicManager} from '@MaintainPlanLayer/utils/Map/index';
import MesoscopicMaker from '@/components/Common/MesoscopicMaker/index.vue';
import {viewToMac} from '@/utils';
import {onMounted} from 'vue';
export default {
    props: {
        list: {
            type: Array,
            default: () => [],
        },
        callback: {
            type: Function,
            required: true,
        },
    },
    components: {
        MesoscopicMaker,
    },
    setup() {

        onMounted(() => {
            viewToMac();
        });

        return {
            mesoscopicManager,
        };
    },
};
</script>