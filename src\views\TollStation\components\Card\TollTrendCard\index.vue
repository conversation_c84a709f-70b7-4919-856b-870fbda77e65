<template>
    <Card title="月收费趋势" card-type="card-short-2">
        <template #content>
            <div class="toll-trend-card__content">
                <FokaiLine
                    y-name="收费趋势（万元）"
                    :data="getChartData"
                />
            </div>
        </template>
    </Card>
</template>

<script setup>
import {Card, FokaiLine} from '@/components/Common';
import {getMotherToll} from '@/api/tollStation';
import {computed, onMounted, ref} from 'vue';
import {tollStation} from '@/views/TollStation/store';
import dayjs from 'dayjs';

const cardData = ref([]);

const getChartData = computed(() => {
    return [
        {
            name: '金额',
            color: 'rgb(87, 255, 213)',
            colorStops: ['rgba(87, 255, 213, 0.35)', 'rgba(0, 255, 149, 0)'],
            data: cardData.value,
        },
    ];
});

async function fetchData() {
    const {data} = await getMotherToll({
        stationId: tollStation.value.stationId,
        stationName: tollStation.value.stationName,
    });
    cardData.value = data?.motherTollList.map(item => ({
        value: item.totalFee,
        name: dayjs(item.mother, 'YYYY-MM').format('M月'),
    }));
}

onMounted(() => {
    fetchData();
});

</script>

<style lang="less" scoped>
.toll-trend-card {
    &__content {
        height: 136px;
    }
}
</style>