<template>
    <div class="serviceArea">
        <collapse-panel :collapse.sync="collapse" class="popup">
            <div class="card-wrapper">
                <left :service-icon="serviceIcon" :equip-icon="equipIcon"/>

            </div>
        </collapse-panel>
        <map-comp
            class="map-comp" :line-name="lineName"
            :points-list="pointsList"
        >
            <map-icon
                :line-list="lineList" :map-filter-data="mapFilterData"
                :camera-list="cameraList"
                :kakou-list="kakouList" :qbb-list="qbbList"
                :fwq-list="fwqList" :close="close"
            />
            <div class="map-icon-filter">
                <map-icon-filter
                    :value="mapFilterData" multiple
                    :list="mapFilterList"
                    :class="{'close_right': collapseRight}"
                />
            </div>

        </map-comp>
        <tab-change v-model="currentTab" @click="changeTab"/>

        <collapse-panel direction="right" :collapse.sync="collapseRight">

            <right :service-icon="serviceIcon" :equip-icon="equipIcon"/>
        </collapse-panel>
    </div>
</template>

<script setup name='serviceArea'>
import MapComp from '../Map/index.vue';
import {onMounted, ref, computed, watch, shallowRef} from 'vue';

import MapIcon from './components/MapIcon.vue';
import {CollapsePanel, MapIconFilter} from '@/components/Common';
import Left from './Left/index.vue';
import Right from './Right/index.vue';
import tabChange from '@/views/ServiceManagement/Card/tabChange.vue';

import {
    getTollStationList,
    getServiceAreaList,
    getBridgeList,

} from '@/api/equipment/highspeed.js';
import {getSectionOverView, onlineOverView, getInfoBoardList, getCameraList} from '@/api/serviceManager/index.js';
import {viewToMicro, viewToFk} from '@/utils/map/methods/index.js';

import {equip, service} from '@/config/serviceMap.js';
import {serviceId} from '@/views/ServiceManagement/utils/index';
import {useCollapsePanel} from '@/hooks/useCollapsePanel';

// 服务区总览数据
const serviceIcon = ref({});
const equipIcon = ref({});
const currentTab = ref('SD101');
const tabList = ref([{name: '东区', key: 'SD101'},
                     {name: '西区', key: 'SD201'}]
);
const {collapse} = useCollapsePanel();
const {collapse: collapseRight} = useCollapsePanel();
const changeTab = item => {

    currentTab.value = item;
    //  "centerPoint": "112.9892101,22.71676053"xiqu
    // "centerPoint": "112.991059,22.71611355" d东区

    currentTab.value ===  'SD101' ? viewToMicro([112.991059, 22.71611355])
    : viewToMicro([112.9892101, 22.71676053]);
    serviceId.value = item;

};

//  情报板
const qbbList = ref([]);

// 服务区扎点
const fwqList = ref([]);
// 摄像枪扎点
const cameraList = ref([]);


const cameraIconList = ref([]);


// 线
const lineList = shallowRef([]);
const getData = () => {
    getSectionOverView().then(res => {
        const integratedData = [];
        // type
        const data = res.data;
        data.parkingSpaces.forEach(parkingSpace => {
            integratedData.push({
                ...parkingSpace,
                deviceType: parkingSpace.type,
                onlineRate: parkingSpace.onlineRate,
                info: service[parkingSpace.type],
            });
        });
        data.flow.forEach(flowItem => {
            const enexTypeName = flowItem.enexType === 1 ? 'out' : 'enter';
            integratedData.push({
                ...flowItem,
                deviceType: enexTypeName,
                total: flowItem.flow,
                remain: null,
                info: service[enexTypeName],

            });
        });
        serviceIcon.value = integratedData;
        console.log('output->serviceIcon.value ', serviceIcon.value);
    });

    onlineOverView().then(res => {
        equipIcon.value = res.data.map(item => {
            return {
                ...item,
                info: equip[item.deviceType],
            };
        });
        console.log('output->equipIcon', equipIcon.value);
    });
    const locationType = {1: '广州', 2: '开平'};
    getCameraList().then(res => {
        cameraList.value = res.data.map((item, index) => ({
            ...item,
            position: [item.longitude, item.latitude, item.altitude],
            id: item.deviceCode,
            title: item.deviceName,
            videoAddress: item.deviceCode,
            detailInfo: [
                {label: '桩号位置', value: item.stakeNumber},
                {label: '道侧路向', value: locationType[item.deviceLocation]},
                {label: '投入使用时间', value: item.useTime},
                {label: '预计报废时间', value: item.endTime},
            ],
        }));
    });
    const deviceType = {2: '门架情报板', 3: '限速标志', 4: '单立柱情报板', 5: '双立柱情报板', 20: 'F型情报板'};
    getInfoBoardList().then(res => {
        qbbList.value = res.data.map((item, index) => ({
            ...item,
            position: [item.longitude, item.latitude, item.altitude],
            title: item.infoBoardName,
            deviceName: item.infoBoardName,
            label: item.infoBoardName,
            id: item.deviceCode,
            type: deviceType[item.infoBoardType],
            detailInfo: [
                {label: '设备编号', value: item.deviceCode},
                {label: '设备种类', value: deviceType[item.infoBoardType]},
                {label: '设备位置', value: item.stakeNumber},

            ],
        }));
    });

    getServiceAreaList().then(res => {
        fwqList.value = res.data.map(item => {
            return {
                ...item,
                lng: item.centerPoint.split(',')[0],
                lat: item.centerPoint.split(',')[1],
                alt: item.centerPoint.split(',')[2] || 0,
                label: item.serviceAreaId,
                position: [+item.centerPoint.split(',')[0], +item.centerPoint.split(',')[1],
                           +item.centerPoint.split(',')[2] || 0],
            };
        });
    });

};
onMounted(() => {
    getData();
});



const mapFilterList = ref([
    {
        label: '相机',
        value: 'camera',
        icon: 'sheshi-shexiangtou',
    },
    {
        label: '服务区',
        value: 'fwq',
        icon: 'camera-fuwuqu',
    },
    {
        label: '情报板',
        value: 'qbb',
        icon: 'sun',
    },
]);
watch(() => serviceId.value, () => {
    getData();
});
const mapFilterData = ref(['fwq']);


</script>

<style lang="less" scoped>
.serviceArea {
    display: flex;
    justify-content: space-between;
    height: 100%;
    width: 100%;

    .map-icon-filter-wrapper {
        position: fixed;
        bottom: 85px;
        right: 1650px;
        z-index: 500;
        transition: all .35s;
        // background-color: red;
    }

    .close_right {
        right: 40px;
    }

    .serviceArea-left {
        display: flex;
        justify-content: space-between;
        overflow: scroll;
    }

    .map-comp {
        position: relative;

        .map-icon-filter {
            position: absolute;
            right: 16px;
            bottom: 190px;
            z-index: 19;
        }

        .tabbar {
            display: flex;
            justify-content: space-evenly;
            position: absolute;
            padding: 4px;
            left: calc(50% - 27px);
            top: 20px;
            z-index: 30;
            background-color: rgba(43, 43, 43);

            .tab-item {
                flex: 1;
                margin: 4px;
                line-height: 24px;
                font-size: 14px;
                width: 58px;
                height: 24px;
                text-align: center;
                cursor: pointer;
                color: rgba(167, 167, 167);

                &.active {
                    background-color: rgba(94, 191, 135);
                    color: #fff;
                }
            }
        }
    }
}
</style>