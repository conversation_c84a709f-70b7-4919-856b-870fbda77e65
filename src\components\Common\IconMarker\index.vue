
<script>
import {computed, watch, onMounted, onUnmounted} from 'vue';
export default {
    name: 'IconMarker',
    props: {
        iconManager: {
            type: Object,
            required: true,
        },
        info: {
            type: Object,
            default: () => ({}),
        },
    },
    setup(props) {
        const name = computed(() => props.info.name || 'IconMarker');
        const url = computed(() => props.info.url || '/maplayer/assets/image/position.png');
        const hasActive = computed(() => !!props.info.activeUrl || false);


        const onClick = e => {
            props.info.onClick && props.info.onClick(e);
        };

        const onMouseenter = e => {
            if (hasActive.value) {
                addIcon(true);
            }
            props.info.onMouseenter && props.info.onMouseenter(e);
        };

        const onMouseleave = e => {
            if (hasActive.value) {
                removeIcon(name.value + '-active');
            }
            props.info.onMouseleave && props.info.onMouseleave(e);
        };

        function addIcon(active) {
            if (!props.info.position) return;
            const nameVal = active ? name.value + '-active' : name.value;
            props.iconManager.addIcon(
                nameVal,
                props.info.position,
                {
                    ...props.info,
                    url: active ? props.info.activeUrl : url.value,
                    needBubble: active ? false : props.info.needBubble,
                    onClick: props.info?.onClick ? onClick : undefined,
                    onMouseenter: props.info?.onMouseenter ? onMouseenter : undefined,
                    onMouseleave: props.info?.onMouseleave ? onMouseleave : undefined,
                }
            );
        }

        function removeIcon(name) {
            props.iconManager.removeIconByName(name);
        }

        watch(() => props.info, (_, {name}) => {
            removeIcon(name);
            if (hasActive.value) {
                removeIcon(name + '-active');
            }
            addIcon();
        }, {deep: true});

        onMounted(() => {
            addIcon();
        });

        onUnmounted(() => {
            removeIcon(name.value);
            if (hasActive.value) {
                removeIcon(name.value + '-active');
            }
        });

        return {};
    },
};
</script>