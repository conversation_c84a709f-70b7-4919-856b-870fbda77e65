import {ref} from 'vue';

// 设备管理左下角点击查看详情按钮
export const activeIndex = ref(null);
export const detail = ref({
    name: '',
    data: [],
});
export const showDetail = ref(false);
export const width = ref(300);
export const top = ref('730px');
export const left = ref('620px');
export const right = ref('inherit');


// 设备管理地图扎点显示数组
export const mapFilterList = ref([
    {
        icon: 'sheshi-shexiangtou',
        value: 'sheshi-shexiangtou',
        label: '摄像头',
    },
    {
        icon: 'xianshiping',
        value: 'xianshiping',
        label: '显示屏',
    },
    {
        icon: 'lijiao',
        value: 'lijiao',
        label: '互通立交',
    },
    {
        icon: 'qiaoliang1',
        value: 'qiaoliang1',
        label: '桥梁',
    },
    {
        icon: 'toll-station',
        value: 'toll-station',
        label: '收费站',
    },
    {
        icon: 'camera-fuwuqu',
        value: 'camera-fuwuqu',
        label: '服务区',
    },
]);
export const mapFilterData = ref([
    'xianshiping',
    'lijiao',
    'qiaoliang1',
    'toll-station',
    'camera-fuwuqu',
]);