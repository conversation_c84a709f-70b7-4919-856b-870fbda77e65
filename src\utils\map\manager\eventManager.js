import {addBubble, removeBubble, addLabel, removeLabel, addIcon, removeIcon,
    addCircle, removeCircle, addText, removeText} from '../index';
import {getPointHeight} from '@/utils';

const nameMap = {
    '车辆故障': 'cheli<PERSON><PERSON><PERSON><PERSON>',
    '服务区事件': 'fuwu<PERSON><PERSON><PERSON><PERSON>',
    '重大事件': 'zhongdas<PERSON>jian',
    '重大社会活动': 'zhongdashehuihuodong',
    '养护施工': 'yanghushigong',
    '路面状况': 'lumianzhuangkuang',
    '交通灾害': 'jiaotongzaihai',
    '交通事故': 'jiaotongshigu',
    '交通气象': 'jiaotongqixiang',
    '路面裂缝': 'lumianliefeng',
    '异常停车': 'yichangtingche',
    '抛洒物': 'paosawu',
    '交通拥堵': 'jiaotongyongdu',
    '其他': 'qita',
    '事故多发地（当前有事故)': 'shigufasheng',
    '占道施工': 'yanghushigong',
};

const getIconUrl = (labelText, active) => {
    return nameMap[labelText]
        ? `maplayer/assets/image/event_warning/icons/${nameMap[labelText] || labelText}${active ? '_on' : ''}.png`
        : 'maplayer/assets/image/event_warning/icons/jiaotongshigu.png';
};

const getBubbleColor = (bubbleColor, active) => {
    return bubbleColor ? bubbleColor : (active ? 'rgba(252, 200, 50, 0.25)' : 'rgba(67, 36, 48, 1)');
};

// 事件告警管理器
class EventManager {
    constructor(engine) {
        this.eventeMap = new Map();
        this.setHaveMap = new Map();
        this.engine = engine;
    }
    async addEventPoint(name, point, options) {
        if (this.eventeMap.has(name)) {
            this.removeEventPointByName(name);
        }
        if (this.setHaveMap.has(name)) {
            this.setHaveMap.get(name)();
        }
        const next = await this.initHeight(name, point);
        if (!next) return;

        const {
            labelText,
            customData,
            bubbleColor,
            text = '',
            circleColor,
            active = false,
            circleBorderColor,
            clickCallback,
        } = options || {};
        const iconUrl = getIconUrl(labelText, active);
        const labelUrl = `maplayer/assets/image/event_warning/${
            labelText?.length <= 4 ? 'label_bg_4' : labelText?.length < 10 ? 'label_bg_5' : 'label_bg_6'
        }.png`;
        const labelWidth = labelText?.length <= 4 ? 72 : labelText?.length < 10 ? 101 : 170;
        const labelOffset = [labelWidth / 2 + 20, -50];
        // 气泡点
        let {bubble} = addBubble(point, {
            size: 40,
            color: getBubbleColor(bubbleColor, active),
            type: 'Wave',
            _engine: this.engine,
        });
        // 右侧文字label
        let {label} = addLabel(point, labelText, {
            offset: labelOffset,
            padding: [7, 8],
            width: labelWidth,
            height: 28,
            fontSize: 14,
            customData,
            background: labelUrl,
            _engine: this.engine,
        });

        // icon
        let {icon, _engine} = addIcon(point, iconUrl, {
            width: 52,
            height: 73,
            offset: [0, -40],
            customData,
            _engine: this.engine,
        });

        // 告警数字圆
        let {circle} = text && addCircle(point, {
            customData,
            color: circleColor,
            borderColor: circleBorderColor,
            _engine: this.engine,
        }) || {};
        // 告警数字
        let {_text} = text && addText(point, text, {
            customData,
            _engine: this.engine,
        }) || {};

        if (clickCallback && typeof clickCallback === 'function') {
            icon.receiveRaycast = true;
            label.receiveRaycast = true;
            _engine.event.bind(icon, 'click', clickCallback);
            _engine.event.bind(label, 'click', clickCallback);
        }

        this.eventeMap.set(name, {
            'Bubble': bubble, // 气泡点
            'Label': label, // 文字 label
            'Icon': icon, // icon
            'Text': _text, // 告警数字
            'Circle': circle, // 告警数字圆
        });
    }

    // 初始化高程
    async initHeight(name, point) {
        if (!point[2]) {
            // 解决请求height时await阻塞导致再次添加时无法清除扎点
            let have = true;
            this.setHaveMap.set(name, () => {
                have = false;
            });
            const height = await getPointHeight(point);
            point[2] = height?.data?.[0] || 0;
            if (have) {
                this.setHaveMap.delete(name);
            }
            return have;
        }
        return true;
    }

    removeEventPointByName(name) {
        const event = this.eventeMap.get(name);
        if (!event) return;
        removeBubble(event.Bubble, this.engine);
        removeLabel(event.Label, this.engine);
        removeIcon(event.Icon, this.engine);
        removeText(event.Icon, this.engine);
        removeCircle(event.Circle, this.engine);
        this.eventeMap.delete(name);
    }

    clear() {
        [...this.eventeMap.keys()].forEach(macro => {
            this.removeEventPointByName(macro);
        });
        this.eventeMap.clear();
    }
}

export {
    EventManager,
};