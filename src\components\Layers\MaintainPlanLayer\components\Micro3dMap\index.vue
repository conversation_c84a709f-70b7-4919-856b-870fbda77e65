<template>
    <div class="micro-3d-map">
        <!-- 扎点 -->
        <device-maker
            v-if="info.position && isPlan"
            :manager-instace="warningNextManager"
            :info="{
                position: info.position,
                labelName: planNameDictMap[info.type],
                pointName: info.planId,
                type: planTypeDictMap[info.type],
                size: 'normal',
                status: info.warning ? 'warning' : 'blue',
                bubbleColor: info.warning ? 'rgb(245, 92, 99)' : 'rgb(67, 148, 247)',
            }"
        />
        <transition
            leave-active-class="animate__animated animate__fadeOutLeft collapse-animate-duration"
            enter-active-class="animate__animated animate__fadeInLeft collapse-animate-duration"
        >
            <plan-flow
                v-if="isPlan && !collapse"
                :list="info.flowList"
                :loading="loading"
                @analysis="openAnalysis"
            />
        </transition>
        <template v-if="!isPlan">
            <template v-if="!isEmulationTwin">
                <!-- 仿真列表 -->
                <!-- platform-source参数先默认随便给一个 现在后端还没有定义出来 后面在改 -->
                <div v-show="!createConfig.show" class="create-emulation-content">
                    <emulation-list
                        platform-source="maintain-plan-emulation"
                        :emulation-type="3"
                        @add="createCase"
                        @edit="editCase"
                    />
                </div>
                <!-- 创建仿真方案组件 -->
                <!-- platform-source参数先默认随便给一个 现在后端还没有定义出来 后面在改 -->
                <div v-if="createConfig.show" class="create-emulation-content">
                    <emulation-create
                        :info="detailInfo"
                        :emulation-type="3"
                        platform-source="maintain-plan-emulation"
                        @close="onClose"
                    />
                </div>
            </template>

            <template v-else>
                <div class="emulation-twin-wrapper">
                    <emulation-twin
                        :emulation-type="3"
                        @goBack="goBack"
                    />
                </div>
            </template>
        </template>
    </div>
</template>

<script>
import {DeviceMaker} from '@/components/Common';
import {warningNextManager} from '@MaintainPlanLayer/utils/Map';
import PlanFlow from '@MaintainPlanLayer/components/PlanFlow/index.vue';
import {micro3dMapTypeDictMap, planTypeDictMap, planNameDictMap} from '@MaintainPlanLayer/config';
import {computed, onMounted, ref, unref, watch} from 'vue';
import {showToolBtn, collapse} from '@MaintainPlanLayer/store';
import {isEqual} from 'lodash';
import {viewToMicro} from '@/utils';
/** 仿真 */
import EmulationList from '@/components/Layers/EmulationLayer/components/EmulationList/index.vue';
import EmulationCreate from '@/components/Layers/EmulationLayer/components/EmulationCreate/index.vue';
import EmulationTwin from '@/components/Layers/EmulationLayer/components/EmulationTwin/index.vue';
import {strategyId, initUuid, init_road_info} from '@EmulationLayer/store/index';
import {createConfig} from '@EmulationLayer/store/emulationCreate';
import dayjs from 'dayjs';
import {getRoadInfoByLngAndLat} from '@EmulationLayer/api';

export default {
    components: {
        DeviceMaker,
        PlanFlow,
        EmulationCreate,
        EmulationList,
        EmulationTwin,
    },
    props: {
        type: {
            validator: val => [
                micro3dMapTypeDictMap.PLAN,
                micro3dMapTypeDictMap.EMULATION,
            ].includes(val),
            default: micro3dMapTypeDictMap.PLAN,
        },
        info: {
            type: Object,
            default: () => ({}),
        },
        collapse: Boolean,
    },
    setup(props, {emit}) {
        const showType = ref();
        const loading = ref(false);
        const isPlan = computed(() => unref(showType) === micro3dMapTypeDictMap.PLAN);
        let initEmulation = false;

        /**
         * 仿真
         */
        const detailInfo = ref(null);
        const isEmulationTwin = computed(() => strategyId.value);
        // 新增仿真方案
        const createCase = () => {
            createConfig.value.show = true;
            createConfig.value.type = 'add';
            createConfig.value.id = '';
            detailInfo.value = null;
        };

        // 修改仿真方案
        const editCase = (id, item) => {
            createConfig.value.show = true;
            createConfig.value.type = 'edit';
            createConfig.value.id = id;
            detailInfo.value = item;
        };

        // 取消创建仿真方案
        const onClose = () => {
            createConfig.value.show = false;
            createConfig.value.type = '';
            createConfig.value.id = '';
        };

        watch(
            () => isEmulationTwin.value,
            newVal => {
                showToolBtn.value = !newVal;
            }
        );

        watch(
            () => showType.value,
            newVal => {
                collapse.value = newVal === micro3dMapTypeDictMap.EMULATION;
            }
        );

        watch(
            () => props.type,
            (newVal, oldVal) => {
                if (isEqual(newVal, oldVal)) return;
                if (newVal === micro3dMapTypeDictMap.PLAN) {
                    resetDefault();
                }
                showType.value = newVal;
            },
            {
                immediate: true,
            }
        );

        watch(
            () => props.info,
            (newVal, oldVal) => {
                if (isEqual(newVal, oldVal)) return;
                newVal.position && viewToMicro(newVal.position);
            },
            {
                immediate: true,
                deep: true,
            }
        );

        async function fetchEmulationInfo() {
            const [lng, lat] = props.info?.position;
            const {data} = await getRoadInfoByLngAndLat(lng, lat);
            const {
                startTime,
                endTime,
                lane,
            } = props.info;
            const diffMinutes = dayjs(endTime).diff(dayjs(startTime), 'minute');
            detailInfo.value = {
                highSpeedName: data.roadName,
                sumoCity: data.sumoCity,
                eventPosition: `${lng},${lat}`,
                eventStartStake: data.stakeNumber,
                eventEndStake: data.stakeNumber,
                eventStartTime: startTime,
                eventEndTime: endTime,
                emulationStartStake: data.stakeNumber,
                emulationStartTime: startTime,
                emulationEndTime: endTime,
                closeLane: lane || '1',
                emulationType: 3,
                eventCategory: 1,
                eventType: 21,
                eventLocationType: 3,
                duration: diffMinutes,
                direction: 1,
                eventAltitude: 0,
                closeLaneNum: 1,
                closeLanePosition: '',
                visibility: 0,
                limitSpeed: 60,
                upstreamTransitionLength: 100,
                constructionLength: 100,
                downstreamTransitionLength: 100,

                tollName: null,
                tollStakeNumber: null,
                tollLongitude: null,
                tollLatitude: null,
                entranceLaneNum: null,
                entranceEtcLaneNum: null,
                entranceMtcLaneNum: null,
                entranceMixLaneNum: null,
                exitLaneNum: null,
                exitEtcLaneNum: null,
                exitMtcLaneNum: null,
                exitMixLaneNum: null,
            };
        }

        async function openAnalysis() {
            loading.value = true;
            try {
                await fetchEmulationInfo();
                if (!initEmulation) {
                    await initUuid();
                    await init_road_info();
                }
                createConfig.value.show = true;
                loading.value = false;
                showType.value = micro3dMapTypeDictMap.EMULATION;
                emit('update:type', micro3dMapTypeDictMap.EMULATION);
                emit('emulation', props.info);
                viewToMicro(props.info.position);
            }
            finally {
                loading.value = false;
            }
        }

        function goBack() {
            strategyId.value = 0;
            showType.value = micro3dMapTypeDictMap.EMULATION;
            showToolBtn.value = true;
        }

        function resetDefault() {
            strategyId.value = 0;
            createConfig.value = {
                show: false,
                type: 'add',
                id: null,
            };
            showType.value = micro3dMapTypeDictMap.PLAN;
        }

        onMounted(async () => {
            resetDefault();
            initEmulation = true;
            await initUuid();
            await init_road_info();
        });

        return {
            warningNextManager,
            openAnalysis,
            isPlan,
            planTypeDictMap,
            planNameDictMap,
            detailInfo,
            isEmulationTwin,
            goBack,
            createCase,
            editCase,
            onClose,
            createConfig,
            loading,
        };
    },
};
</script>
<style lang="less" scoped>
.micro-3d-map {
    .create-emulation-content {
        position: absolute;
        width: 472px;
        right: 20px;
        top: 120px;
    }
}

.emulation-twin-wrapper {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}
</style>