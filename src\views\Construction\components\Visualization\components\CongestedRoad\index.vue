<template>
    <div class="map-line">
        <div v-for="item in roadList" :key="item">
            <map-line
                :line-instace="lineManager"
                :list="item.pointList"
                :info="{
                    name: item,
                    color: item.color,
                    opacity: .8,
                }"
                dynamic-width
            />
            <BubbleMarker
                bubble-type="diamond"
                bubble-color="#FF4B4B"
                :show-label="true"
                :manager-instace="domManager"
                :icon-name="item.icon"
                :need-detail="false"
                :need-hover="true"
                :info="item"
            />
        </div>
    </div>
</template>

<script setup>
import {getCongestedRoad} from '@/api/construction';
import {MapLine, BubbleMarker} from '@/components/Common';
import {lineManager, domManager} from '@/views/TollStation/utils';
import {ref, watch} from 'vue';
import {projectType} from '../../store';
import {viewToMicro} from '@/utils';

const roadList = ref([]);

const colorMap = {
    2: '#fff8a0',
    3: '#e4aa3d',
    4: '#ed615c',
};

const iconMap = {
    2: 'yongdu-2',
    3: 'yongdu-3',
    4: 'yongdu-4',
};

const textMap = {
    2: '轻度拥堵',
    3: '中度拥堵',
    4: '重度拥堵',
};

async function fetchData() {
    const {data} = await getCongestedRoad({
        projectType: projectType.value,
    });
    roadList.value = data?.filter(item => item.level > 1).map(item => {
        const {linkState, level} = item;
        const {pointList} = linkState;
        const center = Math.floor(pointList.length / 2);
        const position = pointList[center];
        return {
            position,
            pointList,
            label: textMap[level],
            color: colorMap[level],
            icon: iconMap[level],
            clickCallback() {
                viewToMicro(position);
            },
        };
    });
}


watch(
    () => projectType.value,
    () => {
        fetchData();
    },
    {
        immediate: true,
    }
);

</script>