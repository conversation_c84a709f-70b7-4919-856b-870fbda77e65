import {request} from '@/utils/network-helper/index';
import {baseConfig} from '@/utils/common';
const {basePrefix} = baseConfig;

const baseUrl = () => {
    if (import.meta.env.MODE === 'test') return 'http://10.39.235.24:8751';
    return origin;
};

const baseCommon = `${basePrefix}/common`;

// 获取左侧框省内所有二级单位以及下属高速路段数量
export const getRelationNameAndNum = async () => {
    const {data, code} = await request.get(baseUrl() + baseCommon
    + '/company/showOrgNameAndNum', null, {
        extraInfo: {
            noGlobalLoading: true,
        },
    });
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 获取二级单位下属高速路段信息
export const getRelationById = async payload => {
    const {data, code} = await request.get(
        baseUrl() + baseCommon + '/company/showOrgSectionNameByOrgId', payload, {
            extraInfo: {
                noGlobalLoading: true,
            },
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 关键字搜索二级机构或高速
export const searchRelation = async payload => {
    const {data, code} = await request.get(
        baseUrl() + baseCommon + '/company/selectNameBykeyWord',
        payload
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};