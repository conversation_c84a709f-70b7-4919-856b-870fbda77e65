<template>
    <div class="icon-button-group">
        <div
            v-for="icon in list"
            :key="icon"
            class="icon-button"
            @click="handleClick(icon)"
        >
            <icon :size="18" :name="icon"/>
        </div>
    </div>
</template>
<script>
import Icon from '@/components/Common/Icon/index.vue';

export default {
    components: {
        Icon,
    },
    setup() {
        const list = ['notice', 'setting'];

        function handleClick(key) {
            console.log('🚀 ~ handleClick ~ key:', key);
        }

        return {
            list,
            handleClick,
        };
    },
};
</script>

<style lang="less" scoped>
.icon-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    cursor: pointer;
    background-color: rgb(40, 40, 40);
    font-size: 12px;

    &:active {
        opacity: .8;
    }

    &:not(:last-child) {
        margin-right: 8px;
    }

    &-group {
        display: flex;
    }
}
</style>