/* stylelint-disable */
/***** reset.css ******/
html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
    margin: 0;
    padding: 0;
    border: 0;
    font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'PingFang-SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial,
        sans-serif, 'FZLTZHJW--GB1-0';
    vertical-align: baseline;
    box-sizing: border-box;
}

/* make sure to set some focus styles for accessibility */
:focus {
    outline: 0;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
    width: 100% !important;
    display: block;
}

body {
    width: 100% !important;
    line-height: 1;
    overflow: hidden;
}

ol,
ul {
    list-style: none;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

textarea {
    overflow: auto;
    vertical-align: top;
    resize: vertical;
}

h1 {
    font-size: 20px;
    font-weight: 900;
    line-height: 1.7;
}

h2 {
    font-size: 18px;
    font-weight: 700;
    line-height: 1.5;
}

h3 {
    font-size: 16px;
    font-weight: 500;
    line-height: 1.3;
}

button {
    border: none;
    background: none;
    outline: none;
}

html,
body {
    padding: 0;
    margin: 0;
    height: 100%;
}

html {
    font-size: 10vh !important;
}

body {
    font-size: 14px;
}

#app {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 14px;
    color: #333;
    min-width: 1280px;
    font-weight: normal;
    height: 100%;
    width: 100% !important;
}

input::-webkit-credentials-auto-fill-button {
    display: none !important;
    visibility: hidden;
    pointer-events: none;
    position: absolute;
    right: 0;
}

input:focus::-webkit-contacts-auto-fill-button {
    opacity: 0;
}

::-webkit-input-placeholder {
    color: #999 !important;
}

*:not(.show-scroll, .show-scroll > *)::-webkit-scrollbar {
    //chrome 和Safari，电脑端微信浏览器
    width: 0 !important;
    height: 0 !important;
}


::-webkit-scrollbar {
    /* 滚动条整体样式 */
    width: 6px;
    /* 高宽分别对应横竖滚动条的尺寸 */
    height: 6px;
}


::-webkit-scrollbar-thumb {
    /* 滚动条里面小方块 */
    border-radius: 6px;
    background-color: rgb(0, 98, 167);
    background-clip: content-box;
    border-right: 2px solid transparent;
}

::-webkit-scrollbar-track {
    /* 滚动条里面轨道 */
    background-color: transparent;
}

@font-face {
    font-family: 'black';
    src: url('/maplayer/assets/fonts/black.ttf');
}

@font-face {
    font-family: 'ysbh';
    src: url('/maplayer/assets/fonts/ysbh.ttf');
}

@font-face {
    font-family: 'OPlusSans';
    src: url('/maplayer/assets/fonts/OPlusSans.ttf');
}

@font-face {
    font-family: 'Neue';
    src: url('/maplayer/assets/fonts/BebasNeue.ttf');
}

@font-face {
    font-family: 'PingFang';
    src: url('/maplayer/assets/fonts/PingFang-SC.otf');
}

@font-face {
    font-family: 'RoboData';
    src: url('/maplayer/assets/fonts/RoboDataRegular.otf');
}

@import url('./element.less');
@import url('./map.less');

.export-image {
    display: none;
}

.addPointer {
    cursor: pointer !important;
}

// 去除 Video 遮罩层. 2023-11-16 @by liuyy
#videoPlayOverlay{
    display: none !important;
}

// 两侧折叠 animate 过渡时长
.collapse-animate-duration {
    animation-duration: .35s !important;
}

// 自定义dom的chart图悬停tooltip样式设置
.tooltip-format {
    min-width: 140px;
    padding: 2px 8px;
    .title {
        color: rgba(#fff, .7);
        margin-bottom: 5px;
        &-pf {
            font-family: 'PingFang';
            font-size: 18px;
            font-weight: 500;
        }
        &-rb {
            font-family: 'RoboData';
            font-size: 16px;
            font-weight: 400;
        }
    }

    .info {
        .item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 3px 0;

            .item-name {
                color: rgba(#fff, .7);
                font-family: 'PingFang';
                font-size: 16px;
                font-weight: 400;

                &.fs-18 {
                    font-size: 18px;
                }
            }

            .item-info {
                font-family: 'RoboData';
                font-weight: 400;
                font-size: 16px;
                color: #fff;
            }
        }
    }
}


