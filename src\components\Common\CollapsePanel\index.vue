<template>
    <div
        :class="[
            'collapse-panel-wrapper',
            `collapse-panel-wrapper__${direction}`,
        ]"
    >
        <transition
            :leave-active-class="getTransitionName.leave"
            :enter-active-class="getTransitionName.enter"
        >
            <div
                v-show="!collapseState"
                :class="[
                    'collapse-panel__inner',
                    `collapse-panel-${direction}`,
                ]"
            >
                <slot></slot>
            </div>
        </transition>
        <div
            v-if="allowCollapse"
            :class="[
                'collapse-panel__icon',
                {collapse: collapseState},
                `collapse-panel__icon-${direction}`,
            ]"
            @click="handleClick"
        >
            <i :class="['el-icon', `el-icon-d-arrow-${direction}`]"></i>
        </div>
    </div>
</template>

<script setup>
import {computed, ref, watch} from 'vue';

const transitionNameMap = {
    left: {
        leave: 'animate__animated  animate__fadeOutLeft collapse-animate-duration',
        enter: 'animate__animated animate__fadeInLeft collapse-animate-duration',
    },
    right: {
        leave: 'animate__animated animate__fadeOutRight collapse-animate-duration',
        enter: 'animate__animated animate__fadeInRight collapse-animate-duration',
    },
};

const props = defineProps({
    collapse: Boolean,
    title: String,
    allowCollapse: {
        type: Boolean,
        default: true,
    },
    showHeader: {
        type: Boolean,
        default: false,
    },
    direction: {
        type: String,
        default: 'left',
    },
});

const emit = defineEmits(['update:collapse', 'collapse']);

const collapseState = ref(false);

const getTransitionName = computed(() => {
    return transitionNameMap[props.direction];
});

watch(
    () => props.collapse,
    (newVal, oldVal) => {
        if (newVal === oldVal) return;
        collapseState.value = newVal;
    },
    {
        immediate: true,
    }
);

function handleClick() {
    collapseState.value = !collapseState.value;
    emit('update:collapse', collapseState.value);
    emit('collapse', collapseState.value);
}

</script>

<style lang="less" scoped>
.collapse-panel {
    &-wrapper {
        position: fixed;
        height: 100%;
        top: 0;
        z-index: 20;
        padding: 160px 0 40px 40px;
        pointer-events: none;

        &__left {
            left: 0;
            padding: 160px 0 40px 40px;
        }

        &__right {
            right: 0;
            padding: 160px 40px 40px 0;
        }
    }

    &-left {
        &::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 1232px;
            height: 40px;
            background-image: url('@/assets/images/base/panel-bg-left.png');
            background-size: 100% 100%;
            z-index: 20;
        }

        &::before {
            content: '';
            position: absolute;
            top: -60px;
            left: -40px;
            width: calc(100% + 180px);
            max-width: 1400px;
            height: calc(100% + 60px);
            transition: none;
            background-image:
                linear-gradient(
                    to right,
                    rgba(0, 0, 0, .9) 0,
                    rgba(12, 13, 29, .8) calc(100% - 180px),
                    transparent,
                );
            pointer-events: none;
        }
    }

    &-right {
        &::after {
            content: '';
            position: absolute;
            right: 0;
            bottom: 0;
            width: 1232px;
            height: 40px;
            background-image: url('@/assets/images/base/panel-bg-right.png');
            background-size: 100% 100%;
            z-index: 20;
        }

        &::before {
            content: '';
            position: absolute;
            top: -60px;
            right: -40px;
            max-width: 1400px;
            width: calc(100% + 180px);
            height: calc(100% + 60px);
            transition: none;
            background-image:
                linear-gradient(
                    to left,
                    rgba(0, 0, 0, .9) 0,
                    rgba(12, 13, 29, .8) calc(100% - 180px),
                    transparent,
                );
            pointer-events: none;
        }
    }

    &__inner {
        position: relative;
        width: 100%;
        height: 100%;

        /deep/ & > * {
            pointer-events: auto;
        }
    }

    &__header {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 16px;
        padding: 12px;
        border-bottom: 2px solid rgba(255, 255, 255, .1);
    }

    &__body {
        flex: 1;
        overflow-y: auto;
        padding: 12px;
    }

    &__footer {
        flex-shrink: 0;
    }

    &__icon {
        position: absolute;
        top: 160px;
        width: 24px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #fff;
        background-image:
            linear-gradient(
                to bottom,
                rgb(36, 104, 242),
                rgba(1, 255, 229, .7)
            );
        transition: .2s linear;
        pointer-events: auto;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: rgba(#fff, .3);
        }

        &-left {
            right: -40px;
            clip-path:
                polygon(
                    0 0,
                    100% 0,
                    100% calc(100% - 10px),
                    calc(100% - 10px) 100%,
                    0 100%
                );

            &.collapse {
                right: calc(100% - 64px);

                .el-icon {
                    transform: rotate(180deg);
                }
            }
        }

        &-right {
            left: -40px;
            clip-path:
                polygon(
                    0 0,
                    100% 0,
                    100% 100%,
                    calc(100% - 10px) 100%,
                    0 calc(100% - 10px),
                    0 100%
                );

            &.collapse {
                left: calc(100% - 64px);

                .el-icon {
                    transform: rotate(180deg);
                }
            }
        }
    }
}

.collapse-animate-duration {
    animation-duration: .35s;
}
</style>