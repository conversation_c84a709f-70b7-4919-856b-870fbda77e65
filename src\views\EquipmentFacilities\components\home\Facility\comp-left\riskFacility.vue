<template>
    <div class="risk-facility">
        <div class="risk-facility__item">
            <div class="risk-facility__item__left">
                <div class="risk-facility__item__left__icon">
                    <icon :name="type === 1 ? 'qiaoliang1' : 'lijiao'"/>
                </div>
                <div class="risk-facility__item__left__info">
                    <div class="info__name">
                        <p class="info__name__cn">{{ type === 1 ? '桥梁' : '互通' }}风险点</p>
                        <p class="info__name__en">{{ type === 1 ? 'BRIDGE' : 'INTERFLOW' }}</p>
                    </div>
                    <div class="info__num">
                        <span class="info__num__value">{{ data.totalCount || 0 }}</span>
                        <span class="info__num__unit">个</span>
                    </div>
                </div>
            </div>

            <div class="risk-facility__item__right">
                <div class="risk-facility__item__right__high">
                    <p>高风险 <span>{{ data.highCount || 0 }}</span></p>
                    <div></div>
                </div>
                <div class="risk-facility__item__right__middle">
                    <p>中风险 <span>{{ data.centerCount || 0 }}</span></p>
                    <div></div>
                </div>
                <div class="risk-facility__item__right__low">
                    <p>低风险 <span>{{ data.lowCount || 0 }}</span></p>
                    <div></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import {ref, onMounted, watch} from 'vue';
import {Icon} from '@/components/Common/index';
import {facilityCountRisk} from '@/api/equipment/facilitydisplay';

const props = defineProps({
    type: {
        type: Number,
        default: 1,
    },
});

const data = ref({});

const init = () => {
    facilityCountRisk(props.type).then(res => {
        data.value = res.data;
    });
};

onMounted(() => init());

watch(() => props.type, () => {
    init();
});
</script>

<style lang="less" scoped>
.risk-facility {
    height: 172px;

    &__item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 172px;
        margin-bottom: 7px;
        background-color: rgba(18, 74, 166, 0.24);
        padding: 0 20px;

        &__right {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            width: 100%;
            margin-top: 24px;

            > div {
                min-width: 75px;
                margin-right: 12px;

                &:last-child {
                    margin-right: 0;
                }

                &.risk-facility__item__right__high {
                    span {
                        color: rgb(255, 85, 85);
                    }
                    div {
                        background-color: rgb(255, 85, 85);;
                    }
                }
                &.risk-facility__item__right__middle {
                    span {
                        color: rgb(233, 115, 18);
                    }
                    div {
                        background-color: rgb(233, 115, 18);
                    }
                }
                &.risk-facility__item__right__low {
                    span {
                        color: rgb(93, 228, 126);
                    }
                    div {
                        background-color: rgb(93, 228, 126);;
                    }
                }

                p {
                    font-size: 16px;
                    font-weight: 400;
                    font-family: 'PingFang';
                    color: rgba(#fff, .6);
                    span {
                        font-size: 20px;
                        font-weight: 400;
                        font-family: 'RoboData';
                    }
                }

                div {
                    width: 100%;
                    height: 2px;
                    margin-top: 13px;
                }
            }
        }

        &__left {
            display: flex;
            width: 100%;
            &__icon {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 42px;
                height: 32px;
                background: url('@/assets/images/base/iconBg.png') no-repeat center center / 100% 100%;
                margin-right: 12px;
            }

            &__info {
                display: flex;
                .info__name {
                    margin-right: 20px;
                    &__cn {
                        font-size: 18px;
                        font-weight: 500;
                        font-family: 'PingFang';
                        color: #fff;
                    }
                    &__en {
                        font-size: 16px;
                        font-weight: 400;
                        font-family: 'RoboData';
                        color: rgba(#fff, .5);
                        margin-top: 8px;
                    }
                }

                .info__num {
                    &__value {
                        font-size: 48px;
                        font-weight: 400;
                        font-family: 'RoboData';
                        color: #fff;
                    }
                    &__unit {
                        margin-left: 4px;
                        font-size: 16px;
                        font-weight: 400;
                        font-family: 'RoboData';
                        color: #fff;
                    }
                }
            }
        }

        &:last-child {
            margin-bottom: 0;
        }
    }
}
</style>