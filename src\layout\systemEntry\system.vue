<template>
    <div class="system-entry-box">
        <div class="perspective-box">
            <div
                v-for="(el, i) in systemList"
                :key="el.name"
                class="item"
                :class="{active: el.url && isActive(el)}"
                :style="{
                    '--ry': `${getRotateY(i)}Deg`,
                    '--opacity': 1 - Math.abs(getGap(activeIndex, i)) * unitOpacity * 1.7,
                }"
            >
                <a
                    :href="isActive(el) ? 'javascript:void(0)' : el.url"
                    :style="{transform: `rotateY(${-getRotateY(i) - rotationY}Deg)`}"
                >
                    <span>{{ el.name }}</span>
                </a>
            </div>
        </div>
    </div>
</template>

<script setup>
import {ref, computed, watch} from 'vue';
import {useRoute} from '@/utils';

const props = defineProps({
    systemList: {
        type: Array,
        default: () => [],
    },
});

const route = useRoute();

const unitDeg = computed(() => 360 / (props.systemList.length || 1));
const unitOpacity = computed(() => 1 / (props.systemList.length || 1));

const activeIndex = computed(e => props.systemList?.findIndex?.(el => el.url && isActive(el)) ?? 0);

const rotationY = ref(0);

const rotationComputed = computed(() => rotationY.value + 'deg');

const getGap = (l, r) => {
    let len = props.systemList.length;
    const gap = Math.abs(l - r);
    return Math.min(len - gap, gap);
};

const leftOrRightOrCenter = (activeIndex, index) => {
    let len = props.systemList.length;
    if (Math.abs(getGap(activeIndex, index)) ===  len / 2) {
        return 'center';
    }
    if (activeIndex < index) {
        if (index - activeIndex < len / 2) {
            return 'right';
        }
    }
    else if (index < activeIndex) {
        if (index + len - activeIndex < len / 2) {
            return 'right';
        }
    }
    return 'left';
};

function getRotateY(i) {
    let len = props.systemList.length;
    const l = len / 2 | 0;
    let rotationY = unitDeg.value * i - 1;
    const gap = getGap(activeIndex.value, i);
    const p = leftOrRightOrCenter(activeIndex.value, i);
    let a = Math.ceil(l / 2);
    if (p === 'center' || gap === 0 || gap > a) {
        return rotationY;
    }
    else if (p === 'left') {
        const unit = unitDeg.value / (gap + 1);
        return rotationY + gap * unit;
    }
    const unit = unitDeg.value / (gap + 1);
    return rotationY - gap * unit;
}


function isActive(el) {
    return route.path === el.realUrl || route.path === el.url;
}

watch(activeIndex, (n, o = 0) => {
    let len = props.systemList.length;
    const gap = getGap(n, o);
    const sign = ((o + gap) % len) === n ? -1 : 1;
    rotationY.value += sign * unitDeg.value * gap;
}, {
    immediate: true,
});

</script>

<style lang="less" scoped>
.system-entry-box {
    width: 966px;
    height: 276px;
    background: url('@/assets/images/entry/bg.png') no-repeat;
    background-size: 100% 149%;
    // overflow: hidden;
    perspective: 1200px;
    perspective-origin: 48% 0%;
    position: relative;

    .perspective-box {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0%;
        top: -20%;
        transform-style: preserve-3d;
        transition: transform 1s;
        transform: translateZ(0) rotateY(v-bind(rotationComputed));
    }

    .item {
        position: absolute;
        left: calc(50% - 50px);
        top: 40%;
        display: flex;
        width: 100px;
        height: 100px;
        border-radius: 50%;
        transform-style: preserve-3d;
        transform: rotateY(var(--ry)) translateZ(400px);

        &:hover {
            a {
                opacity: 1;
                margin-top: -50px;
            }
        }

        a {
            width: 100%;
            height: 100%;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            background: url('@/assets/images/entry/sys-bg.png') no-repeat;
            background-size: 100% 100%;
            transition: transform 1s, opacity 1s, margin-top .5s;
            opacity: var(--opacity);

            span {
                max-width: 3em;
                max-height: 56px;
                line-height: 24px;
                text-align: center;
                text-shadow: 0 3px 2px #054477;
            }
        }

        &.active {
            a {
                margin-top: 0 !important;
                background: url('@/assets/images/entry/sys-bg-active.png') no-repeat;
                background-size: 100% 100%;
            }
        }
    }
}
</style>