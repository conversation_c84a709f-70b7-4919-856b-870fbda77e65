<template>
    <div class="info">
        <div
            v-for="item in list " :key="item"
            class="item"
            @click="handItem(item)"
        >
            <div class="content">
                <div class="left">
                    <div class="top">
                        <p class="title">{{ item.title }}</p>
                        <div class="time"><span>{{ item.time }}</span></div>
                    </div>
                    <div class="plate" :style="{backgroundColor: item.color}">{{ item.plate }}</div>
                </div>
                <div class="right">
                    <div><span>事件位置: </span><p class="ext">{{ '雅姚服务区K150+626' }}</p></div>
                    <div><span>发生时间: </span><p class="ext">{{ '2024-01-09 00:28:25' }} </p></div>
                    <div><span>事件来源: </span><p class="ext">{{ 'K150+625上行Y' }}</p></div>
                    <div><span>车辆类型: </span><p class="ext">{{ '危险化品车' }} </p></div>
                </div>
            </div>
        </div>

    </div>
</template>

<script setup>
import {eventMonitor} from '@/config/serviceArea';

const props = defineProps({ // 获取父组件值
    list: {
        type: Array,
        default: () => (eventMonitor),
    },
});

const emit = defineEmits(['get']);
const handItem = item => {
    emit('handEventItem', item);
};


</script>


<style scoped lang='less'>
.item {
    margin: 6px 0;

    .content {
        padding: 16px;
        display: flex;
        justify-content: space-between;
background: rgb(41, 41, 41);
        span {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
        }

        .left {
            display: flex;
            flex-direction: column;
width: 112px;
            .time {
                margin-top: 8px;
            }

            .title {
                color: rgb(255, 255, 255);
width: 176px;
                font-size: 16px;
                font-weight: 500;
                line-height: 16px;

            }

            .plate {
                border-radius: 3px;
                font-size: 14px;
                font-weight: 500;
                color: #000;
                height: 20px;
                line-height: 20px;
                width: 80px;
            margin-top: 22px;

                text-align: center;
            }
        }

        .right {
margin-left: 24px;
            div {
                margin-bottom: 8px;
                display: flex;
                font-size: 12px;
span{
    // width: 65px;
    display:block;
    margin-right: 6px;
}

            }
.ext{
            color: rgba(255, 255, 255, 0.8);
flex: 1;

}
        }
    }
}
</style>