<template>
    <Card
        class="revenue-card" title="收费站营收"
        card-type="card-short-2"
    >
        <template #content>
            <div class="revenue-card__top">
                <div class="revenue-card__top-inner">
                    <div class="revenue-card__top-left">
                        <div>收费金额</div>
                        <div>Fee amount</div>
                    </div>
                    <div class="revenue-card__top-right">
                        <span>{{ cardData.totalFee }}</span>
                        <span>万元</span>
                    </div>
                </div>
                <img class="money-cover" src="@/assets/images/tollStation/money-cover.png">
            </div>
            <div class="revenue-card__bottom">
                <PieCharts
                    v-if="show"
                    :color="defaultColors"
                    :active-index="activeIndex"
                    :show-percentage="false"
                    :data="getChartData"
                />
            </div>
        </template>
        <template #titleContent>
            <div class="btn-group">
                <div
                    v-for="item in timeTypeList"
                    :key="item.value"
                    :class="[
                        'btn-default',
                        {'btn-default__active': item.value === timeType},
                    ]"
                    @click="changeTimeType(item.value)"
                >
                    {{ item.label }}
                </div>
            </div>
        </template>
    </Card>
</template>

<script setup>
import {Card} from '@/components/Common';
import {computed, ref, watch} from 'vue';
import PieCharts from '@/components/Common/Charts/PieCharts/LeftPieLi.vue';
import {getRevenue} from '@/api/tollStation';
import {formatCurrency} from '@/utils';

const activeIndex = ref(0);
// 0昨天 1近7天
const timeType = ref(0);
const cardData = ref({
    obuFee: '',
    cashFee: '',
    mobileFee: '',
    totalFee: '',
});

const chartConfig = [
    {
        name: 'ETC收费',
        field: 'obuFee',
        icon: 'chedao-etc',
    },
    {
        name: '现金收费',
        field: 'cashFee',
        icon: 'money',
    },
    {
        name: '移动支付',
        field: 'mobileFee',
        icon: 'phone',
    },
];
const defaultColors = [
    '#D0D0D0',
    '#55DADA',
    '#FCFF95',
    '#44DA1E',
    '#00B0FF',
];
const timeTypeList = [
    {label: '昨日', value: 0},
    {label: '近7日', value: 1},
];

const show = ref(true);

const getChartData = computed(() => {
    return chartConfig.map(item => {
        return {
            unit: '万元',
            // iconSize: 12,
            name: item.name,
            icon: item.icon,
            value: cardData.value[item.field],
        };
    });
});

async function fetchData() {
    show.value = false;
    const {data} = await getRevenue({
        timeType: timeType.value,
    });
    cardData.value = {
        obuFee: data.obuFee,
        cashFee: data.cashFee,
        mobileFee: data.mobileFee,
        totalFee: formatCurrency(data.totalFee),
    };
    show.value = true;
}

function changeTimeType(val) {
    timeType.value = val;
}

watch(
    () => timeType.value,
    () => {
        fetchData();
    },
    {
        immediate: true,
    }
);

</script>


<style lang="less" scoped>
.revenue-card {
    &__top {
        position: relative;

        &-inner {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 20px 20px 136px;
            height: 96px;
            background-image:
                linear-gradient(
                    to right,
                    #3377a4,
                    rgba(18, 74, 166, .8)
                );
            clip-path: polygon(0 0, 100% 0, 100% calc(100% - 8px), calc(100% - 8px) 100%, 0 100%);
        }

        &-left {
            flex-shrink: 0;
            font-size: 14px;
            color: rgba(#fff, .3);

            div:nth-child(1) {
                margin-bottom: 4px;
                font-size: 24px;
                color: #fff;
            }
        }

        &-right {
            font-size: 16px;
            color: rgba(#fff, .6);

            span:nth-child(1) {
                font-family: 'RoboData';
                font-size: 42px;
                color: #fff;
                margin-right: 4px;
            }
        }

        .money-cover {
            position: absolute;
            width: 138.5px;
            height: 99px;
            bottom: 8px;
            left: 24px;
        }
    }

    &__bottom {
        padding: 14px 16px;
        margin-top: 12px;
        height: 206px;
        background-image:
            linear-gradient(
                to right,
                #132e46,
                #173168
            );
        clip-path: polygon(0 0, 100% 0, 100% calc(100% - 8px), calc(100% - 8px) 100%, 0 100%);

        /deep/ main ul {
            padding-top: 8px;

            li {
                height: 48px !important;
                margin-bottom: 12px !important;
                padding-right: 20px;
            }
        }
    }
}
</style>
