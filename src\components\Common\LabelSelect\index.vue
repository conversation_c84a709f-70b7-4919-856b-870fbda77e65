<template>
    <div class="label-select">
        <span
            v-if="label"
            ref="labelRef"
            class="label"
        >{{ label }}：</span>
        <el-select
            v-model="model"
            v-bind="$attrs"
            :style="{'--pl': labelWidth + 'px'}"
            v-on="$listener"
        >
            <el-option
                v-for="el in list"
                :key="el.value"
                :label="el.label"
                :value="el.value"
            />
        </el-select>
    </div>
</template>

<script>
import {Select, Option} from 'element-ui';
import {computed, ref, onMounted, watch, nextTick} from 'vue';
export default {
    name: 'LabelSelect',
    props: {
        value: {
            type: [String, Number],
            default: '',
        },
        list: {
            type: Array,
            default: () => [],
        },
        label: {
            type: String,
            default: '',
        },
    },
    components: {
        [Select.name]: Select,
        [Option.name]: Option,
    },
    model: {
        prop: 'value',
        event: 'change',
    },
    setup(props, {emit}) {

        const labelRef = ref(null);

        const labelWidth = ref(15);

        const model = computed({
            get: () => props.value,
            set: e => emit('change', e),
        });

        const getLabelWidth = () => {
            labelWidth.value = labelRef.value?.offsetWidth || 15;
        };

        onMounted(() => {
            getLabelWidth();
        });

        watch(props.label, e => {
            nextTick(() => {
                getLabelWidth();
            });
        });

        return {
            model,
            labelRef,
            labelWidth,
        };
    },
};
</script>



<style lang="less" scoped>
.label-select {
    position: relative;
    height: auto;

    .label {
        left: 0;
        top: 0;
        height: 100%;
        color: #fff;
        z-index: 1;
        position: absolute;
        display: flex;
        align-items: center;
        padding-left: 12px;
        font-size: 12px;
        pointer-events: none;
    }

    /deep/ .el-input__inner {
        padding-left: var(--pl);
        height: 30px;
        font-size: 12px;
    }

    /deep/ .el-select__caret {
        line-height: 30px;
    }
}
</style>