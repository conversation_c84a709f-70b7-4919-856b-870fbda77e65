import {isEqual} from 'lodash';
import {getCurrentInstance, reactive, watch} from 'vue';

export function useRoute() {
    const instance = getCurrentInstance();
    if (!instance) throw new Error('useRoute需在setup中调用');

    const route = reactive({
        ...instance.proxy.$route,
    });

    watch(
        () => instance.proxy.$route,
        (newVal, oldVal) => {
            if (isEqual(newVal, oldVal)) return;
            Object.assign(route, {
                ...newVal,
            });
        },
        {
            deep: true,
            immediate: true,
        }
    );

    return route;
}

export function useRouter() {
    const instance = getCurrentInstance();
    if (!instance) throw new Error('useRouter需在setup中调用');

    return instance.proxy.$router;
}