<template>
    <div class="my-datepicker">
        <el-date-picker
            v-model="value"
            type="date"
            :placeholder="placeholder"
            @change="change"
        />
    </div>
</template>

<script>
import {DatePicker} from 'element-ui';
export default {
    name: 'MyDatePicker',
    props: {
        value: {
            type: String,
            default: '',
        },
        placeholder: {
            type: String,
            default: '请选择时间',
        },
    },
    components: {
        [DatePicker.name]: DatePicker,
    },
    model: {
        prop: 'value',
        event: 'update:value',
    },
    setup(props, {emit}) {
        const change = e => {
            emit('update:value', e);
            emit('change');
        };

        return {
            change,
        };
    },
};
</script>

<style scoped>

</style>