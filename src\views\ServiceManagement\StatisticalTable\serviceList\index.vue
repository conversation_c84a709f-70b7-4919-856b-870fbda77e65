
<template>
    <div class="health">
        <div class="health-list">
            <div class="health-list-title">
                <icon name="shebei"/>
                <span class="roadname"> 情报板列表查询-雅姚服务区</span>
            </div>
            <div class="health-list-table">
                <div class="list-table-search">
                    <div class="list-table-search-left">
                        <!-- <div class="search-input">
                            <span>设备查询：</span>
                            <el-input
                                v-model="params.deviceCondition" size="small"
                                placeholder="请输入内容"
                            />
                        </div> -->
                        <div class="search-input">
                            <maintain-select
                                v-model="params.infoBoardType" title="情报板类型："
                                :options="stakeOption"
                            />
                        </div>
                        <div class="search-input">
                            <maintain-select
                                v-model="params.infoBoardName" title="情报板名称："
                                :options="infoBoardNameOption"
                            />
                        </div>
                        <!-- <div class="search-input">
                            <span>发送时间：</span>
                            <el-date-picker
                                v-model="params.pushTimeStart" type="datetime"
                                @change="timeChangeFn"
                            />
                        </div> -->
                        <div class="search-input">
                            <span>时间范围：</span>
                            <el-date-picker
                                v-model="times" type="datetimerange"
                                start-placeholder="开始日期"
                                size="small"
                                end-placeholder="结束日期" @change="timeChangeFn"
                            />
                        </div>
                    </div>
                    <div class="list-table-search-right">
                        <search-buttons @search="searchFn" @reset="resetFn"/>
                    </div>
                </div>
                <my-table
                    :data="data" :column="column"
                    :loading="loading" :current-page="params.pageNumber"
                    :pages="{
                        pageSize: params.pageSize,
                    }" :total="total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange" @export-excel="exportExcel"
                />

            </div>
        </div>

    </div>
</template>

<script>
import {ref} from 'vue';
import {Input, DatePicker} from 'element-ui';
import {Icon} from '@/components/Common/index.js';

import {useUnit} from '@/utils/hooks/useUnit';
import MaintainTable from '../component/MaintainTable.vue';
import MaintainSelect from '../component/maintainSelect.vue';
import SearchButtons from '../component/SearchButtons.vue';
import {
    exportHealthExcel,
} from '@/api/equipment/equipmentdisplay.js';
import {
    getInfoBoard,

} from '@/api/serviceManager/index.js';


// import {brandDist, typeDict} from '@/config/maintain.js';
import {boardType, vehicleType, eventType, typeDict} from '@/config/serviceMap.js';
import {useRoute, useRouter} from '@/utils';
import dayjs from 'dayjs';

export default {
    components: {
        MyTable: MaintainTable,
        MaintainSelect,
        // DeriveCard,
        Icon,
        [Input.name]: Input,
        [DatePicker.name]: DatePicker,

        SearchButtons,
    },
    setup(props) {
        const times = ref([]);
        const params = ref({
            serviceId: 'SD101', //  服务区id, 可不传
            infoBoardName: null, // 情报板名称, 可不传
            infoBoardType: null, // 情报板类型, 可不传
            pushTimeStart: null, // 发送开始时间, 可不传
            pushTimeEnd: null, // 发送结束时间, 可不传
            pushStatus: null, // 发送状态, 可不传
            pageNumber: 1,
            pageSize: 5,
        });

        const route = useRoute();
        const info = route.params.info;
        // 设备桩号
        const stakeOption = ref([]);
        // 情报板名称选项
        const infoBoardNameOption = ref([]);

        // 品牌类型
        const brandOption = ref([]);


        // 列表获取
        const total = ref(0);
        const data = ref([]);
        const loading = ref(false);
        const searchFn = () => {
            loading.value = true;
            getInfoBoard(params.value).then(res => {
                total.value = res.data.totalCount;
                // 情报板类型
                // 创建一个临时对象来收集唯一类型及其对应的标签
                const uniqueBoardTypes = {};
                res.data.result.forEach(item => {
                    uniqueBoardTypes[item.infoBoardType] = boardType.get(item.infoBoardType);
                });

                // 重后的数组
                stakeOption.value = Object.entries(uniqueBoardTypes).map(([value, label]) => ({
                    value,
                    label,
                }));
                // 使用 Set 去重
                const uniqueName = new Set(res.data.result.map(item => item.infoBoardName));

                // 将去重后的 stake 值重新映射为含 value 和 label 的对象
                infoBoardNameOption.value = Array.from(uniqueName).
                    map(item => ({value: item, label: item}));
                data.value = res.data.result.map(item => {

                    return {
                        ...item,
                        boardType: boardType.get(item.infoBoardType),
                        stake: item.place,
                        pushStatus: item.pushStatus ? '成功' : '失败',
                    };
                });
                loading.value = false;
            }).catch(err => {
                loading.value = false;
            });
        };
        searchFn();
        const timeChangeFn = e => {




            if (!e) {
                params.pushTimeStart = null;
                params.pushTimeEnd = null;
                return;
            }
            console.log('output->', e);
            params.value.pushTimeStart = dayjs(e[0]).format('YYYY-MM-DD HH:mm:ss');
            params.value.pushTimeEnd = dayjs(e[1]).format('YYYY-MM-DD HH:mm:ss');
        };
        const resetFn = () => {
            params.value = {
                ...params.value,
                infoBoardName: null, // 情报板名称, 可不传
                infoBoardType: null, // 情报板类型, 可不传
                pushTimeStart: null, // 发送开始时间, 可不传
                pushTimeEnd: null, // 发送结束时间, 可不传
                pushStatus: null, // 发送状态, 可不传
                pushTime: null, // 发送状态, 可不传

            };
            times.value = null;
            searchFn();
        };

        const {ratio} = useUnit();
        const column = ref([
            {
                prop: 'infoBoardName',
                label: '情报板名称',
                width: 160 * ratio.value,

            },

            {
                prop: 'place',
                label: '位置',
                width: 120 * ratio.value,

            },
            {
                prop: 'boardType',
                label: '情报板类型',
                width: 120 * ratio.value,

            },
            {
                prop: 'publishContent',
                label: '发送内容',
            },
            {
                prop: 'pushTime',
                label: '发送时间',

            },

            // {
            //     prop: 'confirmRealNa',
            //     label: '影响路段',
            //     width: 120 * ratio.value,
            // },

        ]);



        // 分页器修改
        const handleSizeChange = e => {
            params.value.pageSize = e;
            searchFn();
        };
        const handleCurrentChange = e => {
            params.value.pageNumber = e;
            searchFn();
        };

        // 导出
        const exportExcel = e => {
            exportHealthExcel({
                deviceIdList: e,
            }).then(res => {
                // 下载
                const blob = new Blob([res]);
                const fileName = '情报板.xlsx';
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = fileName;
                link.click();
                URL.revokeObjectURL(link.href);
            });
        };

        return {
            // visible,
            data,
            params,
            eventType,
            typeDict,
            vehicleType,
            column,
            stakeOption,
            brandOption,
            total,
            loading,
            times,
            infoBoardNameOption,
            timeChangeFn,
            searchFn,
            resetFn,
            handleSizeChange,
            handleCurrentChange,
            exportExcel,
        };
    },
};
</script>

<style lang="less" scoped>
.health {
    height: 100%;
}

.health-list {
    width: 100%;
    height: 100%;
    border: 1px solid rgba(255, 255, 255, .2);

    .health-list-title {
        width: 100%;
        height: 40px;
        line-height: 40px;
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 500;
        color: #fff;
        padding-left: 16px;
        font-family: 'OPlusSans';
        border-bottom: 1px solid rgba(255, 255, 255, .2);

        .roadname {
            margin-left: 10px;
        }
    }

    .health-list-table {
        padding: 0 24px;
    }

    .list-table-search {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 80px;

        :deep(.el-range-editor.el-input__inner) {
            height: 32px;
        }

        .list-table-search-left {
            display: flex;
            align-items: center;
            flex: 1;

            >div {
                flex: 1;
                display: flex;
                align-items: center;
                margin-right: 24px;

                &:last-child {
                    margin-right: 0;
                }

                span {
                    flex-shrink: 0;
                    // display: inline-block;
                    // width: 100px;
                    margin-right: 8px;
                    font-weight: 400;
                    font-family: 'OPlusSans';
                    font-size: 14px;
                    color: rgba(255, 255, 255, .6);
                }

                :deep(.el-input) {
                    input {
                        background: transparent;

                        &::placeholder {
                            color: rgba(255, 255, 255, .6) !important;
                        }
                    }
                }

                :deep(.el-select) {
                    width: 100%;
                }
            }
        }

        .list-table-search-right {
            display: flex;
            justify-content: end;

            >div {
                margin-left: 16px;

                &:first-child {
                    margin-right: 0;
                }
            }

            width: 20%;

            :deep(.el-button) {
                font-size: 14px;
                font-weight: 400;
                font-family: 'OPlusSans';
            }

            :deep(.el-button--primary) {
                &:hover,
                &:focus {
                    background: #333;
                    border-color: #333;
                    color: #fff;
                }
            }

            :deep(.el-button--default) {
                background: transparent;
                color: #fff;
                border: 1px solid rgba(255, 255, 255, .2);
            }
        }
    }
}
</style>
