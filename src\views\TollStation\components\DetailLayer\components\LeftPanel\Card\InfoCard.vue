<template>
    <card class="card" title="收费站信息">
        <template #content>
            <div class="list">
                <div class="item">
                    <span class="label">高速名称：</span>
                    <span class="value">{{ info.sectionName }}{{ info.stationName }}</span>
                </div>
                <div class="item">
                    <span class="label">控制方向：</span>
                    <span class="value">{{ directionMap[info.direction] }}</span>
                </div>
                <div class="item">
                    <span class="label">入口车道数：</span>
                    <span class="value">{{ info.enterLaneNum }}个</span>
                </div>
                <div class="item">
                    <span class="label">入口ETC/MTC车道数：</span>
                    <span class="value">{{ info.enterEtcLaneNum }}个/{{ info. enterMtcLaneNum }}个</span>
                </div>
                <div class="item">
                    <span class="label">入口车道流量：</span>
                    <span class="value">{{ info.enterFlow }}辆/小时</span>
                </div>
                <div class="item">
                    <span class="label">排队长度：</span>
                    <span class="value">{{ info.queueLength }}米</span>
                </div>
                <div class="item">
                    <span class="label">所在高速：</span>
                    <span class="value">{{ info.sectionCode }}（{{ info.sectionName }}）</span>
                </div>
                <div class="item">
                    <span class="label">位置桩号：</span>
                    <span class="value">{{ info.stationStakeNumber }}</span>
                </div>
                <div class="item">
                    <span class="label">出口车道数：</span>
                    <span class="value">{{ info.exitLaneNum }}个</span>
                </div>
                <div class="item">
                    <span class="label">出口ETC/MTC车道数：</span>
                    <span class="value">{{ info.exitEtcLaneNum }}个/{{ info.exitMtcLaneNum }}个</span>
                </div>
                <div class="item">
                    <span class="label">入口车道流量：</span>
                    <span class="value">{{ info.exitFlow }}辆/小时</span>
                </div>
                <div class="item">
                    <span class="label">出口混合车道数</span>
                    <span class="value">{{ info.exitMixLaneNum }}个</span>
                </div>
            </div>
        </template>
    </card>
</template>

<script>
import {Card} from '@/components/Common';
import {directionMap} from '@/config';

export default {
    components: {
        Card,
    },
    props: {
        info: {
            type: Object,
            default: () => ({}),
        },
    },
    setup() {
        return {
            directionMap,
        };
    },
};
</script>