<!-- 设备设施 -->
<template>
    <map-comp class="structure">
        <collapse-panel class="prop-left" :collapse.sync="collapseLeft">
            <div style="display: flex;">
                <component :is="routeName + 'Left'"/>
                <div class="duty-card-info"><duty-card/></div>
            </div>
        </collapse-panel>
        <div
            v-if="routeName === 'Micro'" class="back"
            :class="{'active': !collapseLeft}"
            @click="handleBack"
        ><span class="el-icon-arrow-left"></span> 返回上一级</div>

        <collapse-panel
            :collapse.sync="collapseRight" direction="right"
        >
            <component :is="routeName + 'Right'"/>
        </collapse-panel>
        <div
            v-if="routeName === 'Micro' && alarmInfo.total" class="showMicro"
            :class="{'active': !collapseRight}"
            @click="showDetail"
        >告警详情</div>

        <template v-if="routeName === 'Meso'">
            <map-line
                v-for="item in baseList" :key="item.sectionId + item.direction"
                :list="item.sparseLoc"
                :info="{
                    name: item.sectionId + item.direction,
                    color: '#00FFA4',
                }"
            />
            <meso-maker @click="clickFn"/>
        </template>
        <template v-else>
            <micro-maker/>
        </template>

        <alarmInfoComp
            v-model="show"
            @micro="handleMicro"
        />

        <map-icon-filter
            :class="{'close_right': collapseRight}"
            :list="[]"
        />
    </map-comp>
</template>

<script>
import {ref, onMounted} from 'vue';
import {CollapsePanel, DetailList, MapIconFilter} from '@/components/Common';
import MapComp from './components/map/index.vue';
import MesoLeft from './components/home/<USER>/layer_left/index.vue';
import MesoRight from './components/home/<USER>/layer_right/index.vue';
import MicroLeft from './components/home/<USER>/layer_left/index.vue';
import MicroRight from './components/home/<USER>/layer_right/index.vue';
import {applySectionBySectionId, getCamera, selectWarningMessageToday} from '@/api/structure';
import {strToArr} from './utils/methods';
import MapLine from './components/map/line.vue';
import MesoMaker from './components/map/mesoMaker.vue';
import MicroMaker from './components/map/microMaker.vue';
import {
    structureId, markerInfo, alarmInfo,
    toMicroFn, toMesoFn, routeName,
} from './utils/index';
import {useCollapsePanel} from '@/hooks/useCollapsePanel';
import {DutyCard} from './components/common';
import alarmInfoComp from './components/home/<USER>/index.vue';
import {Message} from 'element-ui';

export default {
    name: 'Structure',
    components: {
        MesoLeft,
        CollapsePanel,
        MesoRight,
        MicroLeft,
        MicroRight,
        DetailList,
        MapIconFilter,
        MapComp, MapLine, MesoMaker, MicroMaker,
        DutyCard, alarmInfoComp,
    },
    setup(props) {
        const {collapse: collapseLeft} = useCollapsePanel();
        const {collapse: collapseRight} = useCollapsePanel();

        // 佛开高速基础高速线
        const baseList = ref([]);
        const getLine = () => {
            applySectionBySectionId().then(res => {
                baseList.value = res.data.map(item => ({
                    ...item,
                    sparseLoc: strToArr(item.sparseLoc),
                }));
            });
        };

        onMounted(() => {
            getLine();
        });

        // 点击告警扎点
        const show = ref(false);
        const init = async () => {
            const res = await selectWarningMessageToday({
                structureId: structureId.value,
                pageNumber: 1, // 当前页
                pageSize: 1, // 当前页
            });
            if (res.data.total === 0) Message.info('该桥梁今日暂无告警数据');
            alarmInfo.value = {
                ...alarmInfo.value,
                total: res.data.total,
                structureWainMessageVO: res.data.records[0].structureWainMessageVO,
                wainingLocationVO: res.data.records[0].wainingLocationVO,
            };
        };
        const initCamera = () => {
            getCamera({
                structureId: structureId.value,
                sectionDirection: markerInfo.value.sectionDirection || '2',
            }).then(res => {
                alarmInfo.value = {
                    ...alarmInfo.value,
                    videoInfo: res.data,
                };
            });
        };
        const showDetail = () => {
            show.value = true;
            init();
            initCamera();
        };

        const clickFn = e => {
            structureId.value = e.structureId;
            markerInfo.value = e;
            if (!e.sort) {
                toMicroFn();
            }
            else {
                showDetail();
            }
        };

        // 返回上一级
        const handleBack = () => {
            toMesoFn();
        };

        // 点击微观视角按钮
        const handleMicro = () => {
            toMicroFn();
        };

        return {
            collapseLeft,
            collapseRight,
            routeName,
            baseList,
            show, alarmInfo,
            clickFn, handleBack,
            handleMicro, showDetail,
        };
    },
};
</script>

<style lang="less" scoped>
.structure {
    position: absolute;
    display: flex;
    justify-content: space-between;

    .duty-card-info {
        height: 402px;
        margin-left: 24px;
    }

    .map-icon-filter-wrapper {
        position: fixed;
        bottom: 110px;
        right: 1300px;
        z-index: 500;
        transition: all .35s;
    }

    .close_right {
        right: 40px;
    }

    .showMicro{
        display: flex;
            justify-content: center;
            align-items: center;
            position: fixed;
            right: 96px;
            top: 155px;
            width: 140px;
            height: 40px;
            background: url('./images/back.png') no-repeat center / 100%;
            cursor: pointer;
            font-size: 18px;
            font-weight: 400;
            font-family: 'PingFang';
            z-index: 9000;

            &.active {
                right: 1340px;
            }
    }

        .back {
            display: flex;
            justify-content: center;
            align-items: center;
            position: fixed;
            left: 46px;
            bottom: 120px;
            width: 124px;
            height: 40px;
            background: url('./images/back.png') no-repeat center / 100%;
            cursor: pointer;
            font-size: 18px;
            font-weight: 400;
            font-family: 'PingFang';
            z-index: 9000;

            &.active {
                left: 1310px;
            }
        }
}
</style>