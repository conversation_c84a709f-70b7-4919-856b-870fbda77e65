<template>
    <card title="路况预测">
        <template #content>
            <div ref="scrollRef" class="card-content show-scroll">
                <div class="timeline-list">
                    <div
                        v-for="item in emulationPredictList"
                        :key="item"
                        class="timeline-item"
                    >
                        <div class="timeline-time">{{ item.time }}</div>
                        <div class="timeline-text">{{ item.predict }}</div>
                    </div>
                </div>
            </div>
        </template>
    </card>
</template>

<script>
import {Card} from '@/components/Common';
import {nextTick, ref, watch} from 'vue';
import {emulationPredictList} from '../EmulationTwin/index';

export default {
    components: {
        Card,
    },
    setup(props) {
        const scrollRef = ref();

        watch(
            () => emulationPredictList.value,
            () => {
                nextTick(() => {
                    scrollRef.value.scrollTo({
                        top: scrollRef.value.scrollHeight,
                        behavior: 'smooth',
                    });
                });
            },
            {
                deep: true,
            }
        );

        return {
            scrollRef,
            emulationPredictList,
        };
    },
};
</script>

<style lang="less" scoped>
.card-content {
    height: 180px;
    overflow-y: auto;
    margin-right: -12px;
    padding-right: 12px;
}

.timeline {
    &-item {
        position: relative;
        padding-left: 24px;

        &::before {
            content: '';
            position: absolute;
            top: 4px;
            left: 4px;
            width: 4px;
            height: 4px;
            background-color: #fff;
            border-radius: 50%;
            border: 3px solid rgba(40, 194, 130, .9);
        }

        &::after {
            content: '';
            position: absolute;
            width: 1px;
            height: calc(100% - 24px);
            top: 20px;
            left: 8px;
            background: rgba(196, 196, 196, .2);
            transform: translateX(50%);
        }
    }

    &-time,
    &-text {
        color: #fff;
    }

    &-text {
        padding-top: 12px;
        padding-bottom: 24px;
    }
}
</style>