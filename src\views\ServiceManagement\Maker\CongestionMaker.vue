<template>
    <!-- <Marker
        v-for="item in markerInfoList"
        :key="item.id"
        :info="item"
    >
        <template #default="{active, setActive}">
            <TextDetail
                v-if="active" :detail-info="item.detailInfo"
                :title="item.name"
                :cancel="() => setActive(!active)"
            />
        </template>
    </Marker> -->
    <div>
        <bubble-marker
            v-for="item in dataList " :key="item.id"
            :manager-instace="domManager"
            icon-name="yongdu"
            bubble-color="#ed5953" :info="item"
        >
            <DetailList
                v-model="show[item.eventId]"
                class="detail-card"
                :list="item.detailInfo"
                :title="item.title"
                width="328"
                @close="infoClose"
            />
        </bubble-marker>
    </div>
</template>
<script setup >
import {MapLine, BubbleMarker, DetailList} from '@/components/Common/index.js';
import {lineManager, domManager} from '@/views/ServiceManagement/Map/index.js';
import {computed, ref, onMounted} from 'vue';
import Vue from 'vue';
import {Ws} from '@/utils/ws';
const road_traffic_list = [
    {
        'code': 0,
        'name': '畅通',
        'maxSpeed': 999,
        'minSpeed': 90,
    },
    {
        'code': 1,
        'name': '基本畅通',
        'maxSpeed': 90,
        'minSpeed': 70,
    },
    {
        'code': 2,
        'name': '轻度拥堵',
        'maxSpeed': 70,
        'minSpeed': 50,
    },
    {
        'code': 3,
        'name': '中度拥堵',
        'maxSpeed': 50,
        'minSpeed': 30,
    },
    {
        'code': 4,
        'name': '严重拥堵',
        'maxSpeed': 30,
        'minSpeed': 0,
    },
];
import {viewToMicro, viewToFk} from '@/utils/map/methods/index.js';

import TextDetail from '@/components/Common/MarkerDetail/TextDetail.vue';
import _ from 'lodash';

const dataList = ref();
const roadList = ref([]);
const show = ref([]);
function clickCallback(item, index) {
    const info  = JSON.parse(item.cameraInfoList);
    console.log('output->item.position', item);
    viewToMicro([item.congestSourceLon, item.congestSourceLat, item.alt || 0]);

    show.value[item.eventId] = !show.value[item.eventId];
    console.log('clickCallback', item.title);
}
const wsData = ref();
const infoClose = e => {
    viewToFk();
};
const initWs = async e => {
    wsData.value = new Ws();
    // /congestion
    const url = `${import.meta.env.VITE_WS_URL}/congestion`;

    let ws = await wsData.value.connect(url, {});
    ws.onmessage = ({data}) => {
        if (data === 'ok' || data === '{}') {
            return;
        }
        const {trafficAlarmList: List} = JSON.parse(data);

        roadList.value = _.uniqBy([...roadList.value, ...List], 'id');
        roadList.value.forEach(item => {
            Vue.set(show.value, item.eventId, false);

        });

        dataList.value = roadList.value.map((item, index) => ({
            ...item,
            color: '#ed5953',
            title: road_traffic_list.find(i => +i.code === +item.jamType)?.name,
            position: [item.congestSourceLon, item.congestSourceLat, item.congestSourceAlt ?? 0],
            label: item.congestLocation,
            clickCallback: () => clickCallback(item, index),
            detailInfo: [
                {label: '拥堵位置', value: item.congestSourceStake},
                {label: '所属路段', value: item.congestLocation},
                {label: '开始时间', value: item.startTime},
                {label: '拥堵里程', value: item.congestLength},
                {label: '平均车速', value: item.speed},
                {label: '拥堵指数', value: item.congestIndex},
            ],
        }));
    };
    if (wsData.value.ws) {
        wsData.value.ws.send('refresh');
    }
};
onMounted(() => {
    initWs();
});

</script>
<style lang="less" scoped>
</style>