export const dataKey = {
    1: 'length',
    2: 'length',
    3: 'index',
    4: 'speed',
    5: 'flow',
    6: 'level',
    7: 'saturation',
    8: 'index',
    9: 'length',
};

export const areaColor = {
    1: [
        {
            offset: 0,
            color: 'rgba(1,255,229,0.41)', // 0% 处的颜色
        },
        {
            offset: 1,
            color: 'rgba(1,255,229,1) ', // 100% 处的颜色
        },
    ],
    2: [
        {
            offset: 0,
            color: 'rgba(0,119,255,0.41)', // 0% 处的颜色
        },
        {
            offset: 1,
            color: 'rgba(0,119,255,1) ', // 100% 处的颜色
        },
    ],
    3: [
        {
            offset: 0,
            color: 'rgba(207,204,127,0.41)', // 0% 处的颜色
        },
        {
            offset: 1,
            color: 'rgba(207,204,127,1) ', // 100% 处的颜色
        },
    ],
    4: [
        {
            offset: 0,
            color: 'rgba(132,47,255,0.41)', // 0% 处的颜色
        },
        {
            offset: 1,
            color: 'rgba(132,47,255,1) ', // 100% 处的颜色
        },
    ],
    5: [
        {
            offset: 0,
            color: 'rgba(1,255,229,0.41)', // 0% 处的颜色
        },
        {
            offset: 1,
            color: 'rgba(1,255,229,1) ', // 100% 处的颜色
        },
    ],
    6: [
        {
            offset: 0,
            color: 'rgba(207,204,127,0.41)', // 0% 处的颜色
        },
        {
            offset: 1,
            color: 'rgba(207,204,127,1) ', // 100% 处的颜色
        },
    ],
    7: [
        {
            offset: 0,
            color: 'rgba(207,204,127,0.41)', // 0% 处的颜色
        },
        {
            offset: 1,
            color: 'rgba(207,204,127,1) ', // 100% 处的颜色
        },
    ],
    8: [
        {
            offset: 0,
            color: 'rgba(132,47,255,0.41)', // 0% 处的颜色
        },
        {
            offset: 1,
            color: 'rgba(132,47,255,1) ', // 100% 处的颜色
        },
    ],
};

export const barOptions = data => {
    const {xData, yData1 = []} = data;
    const options = {
        tooltip: {
            trigger: 'axis',
        },
        legend: {
            show: true,
            // selectedMode: false,
            icon: 'diamond',
            right: '5%',
            top: 10,
            itemWidth: 8,
            itemHeight: 8,
            orient: 'horizontal',
            itemGap: 20,
            textStyle: {
                color: '#F9FBFF',
                fontSize: 12,
            },
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
        },
        dataZoom: [
            {
                // type: 'slider', // slider表示有滑动块的，inside表示内置的
                show: true,
                xAxisIndex: [0],
                height: 6,
                end: 35,
                bottom: 5,
                tyepe: 'inside',
                zoomOnMouseWheel: true,
                moveOnMouseMove: true,
                start: 0,
                handleIcon:
                    'path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z',
                handleSize: '100%',
                handleStyle: {
                    color: '#4A94DD',
                },
                textStyle: {
                    color: '#fff',
                    fontSize: 8,
                    fontWeight: 500,
                },
                fillerColor: '#4A94DD',
                borderColor: '#2B374D',
                backgroundColor: '#2B374D',

            },
        ],
        xAxis: {
            type: 'category',
            boundaryGap: false,
            axisLine: {
                show: true,
                lineStyle: {
                    type: 'solid',
                    color: 'rgba(78,141,237,0.7)',
                    width: '1',
                },
            },
            splitLine: {
                show: false,
            },
            axisLabel: {
                showMaxLabel: true, // x轴最后的显示
                textStyle: {
                    color: '#98B8DE',
                    fontSize: 12,
                    fontFamily: 'DINAlternate-Bold',
                },
            },
            axisTick: {
                show: true,
                inside: true,
                length: 3,
                lineStyle: {
                    width: 1,
                    type: 'solid',
                },

            },
            data: xData,
        },
        yAxis: {
            type: 'value',
            axisLine: {
                show: false,
            },
            nameTextStyle: {
                color: '#F9FBFF',
                fontSize: 12,
                fontFamily: 'FZLTZHJW--GB1-0',
            },
            axisTick: {
                show: false,
            },
            splitNumber: 4,
            splitLine: {
                show: true,
                lineStyle: {
                    color: 'rgba(78,141,237,0.2)',
                    type: 'dash',
                    width: '1',
                },
            },
            axisLabel: {
                textStyle: {
                    color: '#98B8DE',
                    fontSize: 12,
                    fontFamily: 'DINAlternate-Bold',
                },
            },
        },
        series: [
            {
                name: '上行',
                type: 'bar',
                smooth: true,
                symbol: 'none',
                data: yData1,
                color: '#00C1AD',
                barWidth: 8,
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: 'rgba(0,193,173,0.41)', // 0% 处的颜色
                            },
                            {
                                offset: 1,
                                color: 'rgba(0,193,173,1) ', // 100% 处的颜色
                            },
                        ],
                        global: false, // 缺省为 false
                    },
                    opacity: 0.2,
                },
            },
        ],
    };
    return options;

};