import {ref} from 'vue';
import {viewToFk, viewToMicro} from '@/utils';

// 结构物id
export const structureId = ref('');

// 微观三维info
export const markerInfo = ref({});

// 告警详情
export const alarmInfo = ref({
    total: 0,
    structureWainMessageVO: {},
    wainingLocationVO: {},
    videoInfo: {},
});

export const makerList = ref([]);

export const routeName = ref('Meso');
export const toMicroFn = () => {
    routeName.value = 'Micro';
    viewToMicro([markerInfo.value.lngRectify, markerInfo.value.latRectify, markerInfo.value.alt]);
};
export const toMesoFn = () => {
    structureId.value = '';
    routeName.value = 'Meso';
    markerInfo.value = {};
    viewToFk();
};