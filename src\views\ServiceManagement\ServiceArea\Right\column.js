import {h} from 'vue';
import {equipOverview, serviceOverview} from '@/config/serviceMap.js';
import {useUnit} from '@/utils';

const {ratio} = useUnit();

function getDir(v) {
    return equipOverview.get(v)?.title || '情报板';
}
function getState(v) {
    return v === 1 ? '在线' : '离线';
}

export default () => {
    return [

        {
            label: '设备类型',
            prop: 'deviceType',
            render(row, c, text, index) {
                const {deviceType} = row;
                const str = `${getDir(deviceType)}`;
                return h('span', {}, str);

            },
            width: `${120 * ratio.value}px`,

        },
        {
            label: '设备名称',
            prop: 'deviceName',
            render(row, c, text, index) {
                return h('span', {}, text || '0');
            },
            // width: `${112 * ratio.value}px`,

        },
        {
            label: '设备状态',
            prop: 'status',
            render(row, c, text, index) {
                const {status} = row;
                const str = `${getState(status)} `;
                return h('span', {}, str);
            },
            width: `${120 * ratio.value}px`,

        },
    ];
};