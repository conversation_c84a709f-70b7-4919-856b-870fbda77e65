<template>
    <div class="comp-left">
        <div class="comp-left-item">
            <card title="收费设施">
                <template #content>
                    <tollFacility/>
                </template>
            </card>
            <card title="设施健康状态">
                <template #titleContent>
                    <router-link to="/maintain" class="button">查看更多</router-link>
                    <div class="btn-group">
                        <div
                            v-for="item in monitorList"
                            :key="item.value"
                            :class="[
                                'btn-default',
                                {'btn-default__active': item.value === monitorType},
                            ]"
                            @click="changeMonitorType(item.value)"
                        >
                            {{ item.label }}
                        </div>
                    </div>
                </template>
                <template #content>
                    <facilityMonitoring :type="monitorType"/>
                </template>
            </card>
        </div>
        <div class="comp-left-item">
            <card class="card-long-1" title="设施列表">
                <template #titleContent>
                    <div class="btn-group">
                        <div
                            v-for="item in typeList"
                            :key="item.value"
                            :class="[
                                'btn-default',
                                {'btn-default__active': item.value === timeType},
                            ]"
                            @click="changeTimeType(item.value)"
                        >
                            {{ item.label }}
                        </div>
                    </div>
                </template>
                <template #content>
                    <facilitiesList :type="timeType"/>
                </template>
            </card>
        </div>
        <div class="comp-left-item">
            <card title="设施告警次数">
                <template #content>
                    <alarmTimes/>
                </template>
            </card>
            <card title="风险设施">
                <template #titleContent>
                    <div class="btn-group">
                        <div
                            v-for="item in riskTypeList"
                            :key="item.value"
                            :class="[
                                'btn-default',
                                {'btn-default__active': item.value === riskType},
                            ]"
                            @click="changeRiskTimeType(item.value)"
                        >
                            {{ item.label }}
                        </div>
                    </div>
                </template>
                <template #content>
                    <riskFacility :type="riskType"/>
                </template>
            </card>
        </div>
    </div>
</template>

<script>
import {ref} from 'vue';
import {Card} from '@/components/Common/index';
import tollFacility from './tollFacility.vue';
import facilityMonitoring from './facilityMonitoring.vue';
import alarmTimes from './alarmTimes.vue';
import facilitiesList from './facilitiesList.vue';
import riskFacility from './riskFacility.vue';
export default {
    name: '设施管理左',
    components: {
        Card,
        tollFacility,
        facilityMonitoring,
        alarmTimes,
        facilitiesList,
        riskFacility,
    },
    setup() {
        const timeType = ref('1');
        const monitorType = ref('1');
        const typeList = ref([
            {
                label: '桥梁',
                value: '1',
            },
            {
                label: '互通',
                value: '2',
            },
        ]);
        const monitorList = ref([
            {
                label: '桥梁',
                value: '1',
            },
            {
                label: '互通',
                value: '2',
            },
        ]);
        const riskTypeList = ref([
            {
                label: '桥梁',
                value: 1,
            },
            {
                label: '互通',
                value: 2,
            },
        ]);
        const riskType = ref(1);

        const changeTimeType = type => {
            timeType.value = type;
        };
        const changeMonitorType = type => {
            monitorType.value = type;
        };
        const changeRiskTimeType = type => {
            riskType.value = type;
        };

        return {
            timeType,
            monitorType,
            riskType,
            typeList,
            monitorList,
            riskTypeList,
            changeTimeType,
            changeMonitorType,
            changeRiskTimeType,
        };
    },
};
</script>

<style lang="less" scoped>
.comp-left {
    width: 100%;
    height: calc(100% - 152px);
    .comp-left-item {
        display: flex;
        margin-bottom: 18px;

        .button {
            border: 1px solid rgba(1, 255, 229, .8);
            color: rgba(1, 255, 229, .8);
            padding: 3px 8px;
            font-size: 16px;
            font-family: 'PingFang';
            margin-right: 20px;
        }

            :deep(.card) {
                .content {
                    height: 220px;
                }
            }

        &:first-child {
            margin-top: 0;
        }

        > div {
            &:first-child {
                margin-right: 24px;
            }
        }
    }
}
</style>