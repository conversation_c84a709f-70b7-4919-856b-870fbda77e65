
<template>
    <div class="health">
        <div class="health-list">
            <div class="health-list-title">
                <icon name="shebei"/>
                <span class="roadname">服务区车辆查询</span>
            </div>
            <div class="health-list-table">
                <div class="list-table-search">
                    <div class="list-table-search-left">
                        <!-- <div class="search-input">
                            <span>设备查询：</span>
                            <el-input
                                v-model="params.deviceCondition" size="small"
                                placeholder="请输入内容"
                            />
                        </div> -->
                        <div class="search-input">
                            <maintain-select
                                v-model="params.stake" title="位置："
                                :options="stakeOption"
                            />
                        </div>

                        <div class="search-input">
                            <span>车牌号：</span>
                            <el-input
                                v-model="params.plate" size="small"
                                placeholder="请输入内容"
                            />
                        </div>
                        <div class="search-input">
                            <maintain-select
                                v-model="params.vehicleType" title="车辆类型："
                                :options="vehicleTypeOption"
                            />
                        </div>
                        <div class="search-input">
                            <span>时间范围：</span>
                            <el-date-picker
                                v-model="params.times" type="datetimerange"
                                start-placeholder="开始日期"
                                size="small"
                                end-placeholder="结束日期" @change="timeChangeFn"
                            />
                        </div>
                    </div>
                    <div class="list-table-search-right">
                        <search-buttons @search="searchFn" @reset="resetFn"/>
                    </div>
                </div>
                <my-table
                    :data="dataList" :column="column"
                    :loading="loading" :current-page="params.pageNumber"
                    :pages="{
                        pageSize: params.pageSize,
                    }" :total="total"
                    button-title="车辆详情"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange" @export-excel="exportExcel"
                />
            </div>
        </div>

        <!-- <derive-card v-model="visible"/> -->
    </div>
</template>

<script>
import {ref, watch, onMounted} from 'vue';
import {Input, DatePicker} from 'element-ui';
import {Icon} from '@/components/Common/index.js';

import {useUnit} from '@/utils/hooks/useUnit';
import MaintainTable from '../component/MaintainTable.vue';
import MaintainSelect from '../component/MaintainSelect.vue';
import SearchButtons from '../component/SearchButtons.vue';
import {
    deviceStakeOption,
    deviceBrandOption,
    getDeviceHealthList,
    exportHealthExcel,
} from '@/api/equipment/equipmentdisplay.js';
import {
    getVehiclePage,

} from '@/api/serviceManager/index.js';


import {confirmResult, vehicleType, eventType, vehicleTypeOption} from '@/config/serviceMap.js';
import {useRoute, useRouter} from '@/utils';
import dayjs from 'dayjs';

export default {
    name: 'carFlow',
    components: {
        MyTable: MaintainTable,
        MaintainSelect,
        // DeriveCard,
        Icon,
        [Input.name]: Input,
        [DatePicker.name]: DatePicker,

        SearchButtons,
    },
    setup(props) {
        const params = ref({
            plate: null,
            vehicleType: null,
            stake: null,
            beginTime: null,
            endTime: null,
            pageNumber: 1,
            pageSize: 5,
            times: null,

        });

        const route = useRoute();
        const info = route.params.info;
        // 设备桩号
        const stakeOption = ref([]);
        // deviceStakeOption().then(res => {
        //     stakeOption.value = res.data.map(item => ({value: item, label: item}));
        // });

        // 品牌类型
        const brandOption = ref([]);
        // deviceBrandOption().then(res => {
        //     brandOption.value = res.data.map(item => ({value: item, label: brandDist.get(item) ?? '暂无信息'}));
        // });

        // 列表获取
        const total = ref(0);
        const dataList = ref([]);
        const loading = ref(false);
        const searchFn = async () => {
            loading.value = true;

            try {
                const {data} = await getVehiclePage(params.value);
                total.value = data.totalCount;
                // 使用 Set 去重
                const uniqueStakes = new Set(data.result.map(item => item.stake));
                stakeOption.value = Array.from(uniqueStakes).map(stake => ({value: stake, label: stake}));
                brandOption.value = data.result.map(item => ({
                    value: item.plate, label:
                        item.plate ?? '暂无信息',
                }));

                dataList.value = data.result.map(item => {
                    return {
                        ...item,
                        vehicleTypeText: vehicleType[item.vehicleType],
                        confirmResult: confirmResult[item.confirmResult],
                        eventTypeText: eventType[item.eventType]?.title,

                    };
                });
            }
            catch (error) {
                loading.value = false;

                console.log('error', error);
            }


            loading.value = false;
            console.log('output- dataList.value', dataList.value);
        };
        onMounted(() => {
            searchFn();

        });
        const resetFn = () => {
            params.value = {
                ...params.value,
                deviceCondition: null,
                deviceStake: null,
                deviceBrand: null,
                deviceType: null,
                vehicleType: null,
                stake: null,
                times: null,
            };
            searchFn();
        };
        const timeChangeFn = e => {

            if (!e) {
                params.beginTime = null;
                params.endTime = null;
                return;
            }
            console.log('output->', e);
            params.value.beginTime = dayjs(e[0]).format('YYYY-MM-DD HH:mm:ss');
            params.value.endTime = dayjs(e[1]).format('YYYY-MM-DD HH:mm:ss');
        };
        const {ratio} = useUnit();
        const column = ref([

            {
                prop: 'plate',
                label: '车牌号',

            },
            {
                prop: 'stake',
                label: '位置桩号',

            },
            {
                prop: 'vehicleTypeText',
                label: '车辆类型',

            },
            {
                prop: 'enterTime',
                label: '驶入时间',

            },
            {
                prop: 'exitTime',
                label: '驶出时间',
            },

            // {
            //     prop: 'operation',
            //     label: '操作',
            //     width: 180 * ratio.value,
            // },
        ]);

        // const visible = ref(false);

        // 分页器修改
        const handleSizeChange = e => {
            params.value.pageSize = e;
            searchFn();
        };
        const handleCurrentChange = e => {
            params.value.pageNumber = e;
            searchFn();
        };

        // 导出
        const exportExcel = e => {
            exportHealthExcel({
                deviceIdList: e,
            }).then(res => {
                // 下载
                const blob = new Blob([res]);
                const fileName = '服务区事件.xlsx';
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = fileName;
                link.click();
                URL.revokeObjectURL(link.href);
            });
        };
        watch(() => dataList.value, v => {
            console.log('output->v', v);
        }, {immediate: true});
        return {
            // visible,
            dataList,
            params,
            eventType,
            vehicleTypeOption,
            confirmResult, vehicleType,
            column,
            stakeOption,
            brandOption,
            total,
            loading,
            searchFn,
            timeChangeFn,
            resetFn,
            handleSizeChange,
            handleCurrentChange,
            exportExcel,
        };
    },
};
</script>

<style lang="less" scoped>
.health {
    height: 100%;
}

.health-list {
    width: 100%;
    height: 100%;
    border: 1px solid rgba(255, 255, 255, .2);

    .health-list-title {
        width: 100%;
        height: 40px;
        line-height: 40px;
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 500;
        color: #fff;
        padding-left: 16px;
        font-family: 'OPlusSans';
        border-bottom: 1px solid rgba(255, 255, 255, .2);

        .roadname {
            margin-left: 10px;
        }
    }

    .health-list-table {
        padding: 0 24px;
    }

    .list-table-search {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 80px;

        :deep(.el-range-editor.el-input__inner) {
            height: 32px;
        }

        .list-table-search-left {
            display: flex;
            align-items: center;
            flex: 1;

            >div {
                flex: 1;
                display: flex;
                align-items: center;
                margin-right: 24px;

                &:last-child {
                    margin-right: 0;
                }

                span {
                    flex-shrink: 0;
                    // display: inline-block;
                    // width: 100px;
                    margin-right: 8px;
                    font-weight: 400;
                    font-family: 'OPlusSans';
                    font-size: 14px;
                    color: rgba(255, 255, 255, .6);
                }

                :deep(.el-input) {
                    input {
                        background: transparent;

                        &::placeholder {
                            color: rgba(255, 255, 255, .6) !important;
                        }
                    }
                }

                :deep(.el-select) {
                    width: 100%;
                }
            }
        }

        .list-table-search-right {
            display: flex;
            justify-content: end;

            >div {
                margin-left: 16px;

                &:first-child {
                    margin-right: 0;
                }
            }

            width: 20%;

            :deep(.el-button) {
                font-size: 14px;
                font-weight: 400;
                font-family: 'OPlusSans';
            }

            :deep(.el-button--primary) {
                &:hover,
                &:focus {
                    background: #333;
                    border-color: #333;
                    color: #fff;
                }
            }

            :deep(.el-button--default) {
                background: transparent;
                color: #fff;
                border: 1px solid rgba(255, 255, 255, .2);
            }
        }
    }
}
</style>
