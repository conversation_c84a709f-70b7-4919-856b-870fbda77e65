<template>
    <div class="toll-facility">
        <div
            v-for="(item, index) in list" :key="index"
            class="facility__item"
        >
            <info-card
                width="274"
                height="106"
                class="infocard__item"
                :style-obj="styleObj"
                :info="item"
            />
            <progress-item :need-icon="false" :info="item"/>
        </div>
    </div>
</template>

<script>
import {ref, onMounted, set} from 'vue';
import {ProgressItem, InfoCard} from '@/views/EquipmentFacilities/components/common/index';
import {getStructureStatus, getInterchangeStatus} from '@/api/equipment/facilitydisplay';
export default {
    name: '收费设施',
    components: {
        ProgressItem,
        InfoCard,
    },
    setup(props) {
        const list = ref([
            {
                value: 0,
                unit: '座',
                cnTitle: '桥梁',
                enTitle: 'BRIDGE',
                icon: 'qiaoliang',
                name: '使用率/使用数：',
                point: 0,
                total: 0,
                type: 'bridge',
            },
            {
                value: 0,
                unit: '座',
                cnTitle: '互通立交',
                enTitle: 'OVERPASS',
                icon: 'lijiao',
                name: '使用率/使用数：',
                point: 0,
                total: 0,
                type: 'grade',
            },
        ]);

        const styleObj = {
            cnTitleFontSize: 20,
            enTitleFontSize: 16,
            numFontSize: 42,
        };

        const init = () => {
            getStructureStatus().then(res => {
                set(list.value, 0, {
                    ...list.value[0],
                    value: res.data?.bridgeList.structureNum,
                    point: res.data?.bridgeList.healthProportion,
                    total: res.data?.bridgeList.allDeviceNum,
                });
            });
            getInterchangeStatus().then(res => {
                set(list.value, 1, {
                    ...list.value[1],
                    value: res.data?.structureNum,
                    point: res.data?.healthProportion,
                    total: res.data?.allDeviceNum,
                });
            });
        };

        onMounted(() => init());

        return {
            list,
            styleObj,
        };
    },
};
</script>

<style lang="less" scoped>
.toll-facility {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 556px;

    .facility__item {
        width: 49%;
        height: 172px;
        background-image: linear-gradient(45deg, rgba(36, 104, 242, 0), rgba(36, 104, 242, 0.3));

        .info-card {
            background-color: transparent;
            border-top: none;
            backdrop-filter: none;
        }

        .progress-item {
            height: 65px;
            border-top: 1px solid;
            border-image: linear-gradient(to right, rgb(36, 104, 242), rgba(1, 255, 229, .5)) 1;
        }
    }

    :deep(.info-card__left__info) {
        width: 200px !important;
    }
}
</style>