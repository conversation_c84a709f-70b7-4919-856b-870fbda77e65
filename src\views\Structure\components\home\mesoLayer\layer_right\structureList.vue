<template>
    <div class="structure-list">
        <api-table
            :api="showStructureList"
            :columns="columns"
            :height="314"
            :pagination="false"
            :request-options="{
                listField: '',
            }"
            :request-params="{
                pageAble: true,
            }"
            @row-click="handleCellClick"
        />
    </div>
</template>

<script>
import {ApiTable} from '@/components/Common';
import {showStructureList} from '@/api/structure/index';
import {ref} from 'vue';
import {useUnit} from '@/utils';
import {
    structureId, markerInfo,
    toMicroFn, makerList,
} from '../../../../utils/index';
export default {
    name: '结构物列表',
    components: {
        ApiTable,
    },
    setup(props) {
        const {ratio} = useUnit();
        const columns = ref([
            {
                label: '结构物名称',
                prop: 'structureName',
                width: `${140 * ratio.value}px`,
                align: 'left',
            },
            {
                label: '位置',
                prop: 'sectionName',
                width: `${110 * ratio.value}px`,
                align: 'left',
            },
            {
                label: '桩号',
                prop: 'centerStake',
                width: `${110 * ratio.value}px`,
                align: 'center',
            },
            {
                label: '方向',
                prop: 'sectionDirection',
                width: `${110 * ratio.value}px`,
                align: 'center',
                format: e => {
                    return e.row.sectionDirection === '1' ? '广州方向' : '开平方向';
                },
            },
        ]);

        const handleCellClick = row => {
            structureId.value = row.structureId;
            markerInfo.value = makerList.value.find(item => item.structureId === row.structureId);
            toMicroFn();
        };

        return {
            showStructureList,
            columns, handleCellClick,
        };
    },
};
</script>

<style lang="less" scoped>
.structure-list {
    width: 100%;
    height: 314px;
}
</style>