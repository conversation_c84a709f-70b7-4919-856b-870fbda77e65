<template>
    <Card title="24小时车流量趋势" card-type="card-short-2">
        <template #content>
            <div class="traffic-trend-card__content">
                <FokaiLine
                    y-name="流量趋势（辆）"
                    :data="getChartData"
                />
            </div>
        </template>
    </Card>
</template>

<script setup>
import {Card, FokaiLine} from '@/components/Common';
import {computed, onMounted, ref} from 'vue';
import {tollStation} from '@/views/TollStation/store';
import {getTollHourFlow} from '@/api/tollStation';

const cardData = ref([]);

const getChartData = computed(() => {
    return [
        {
            name: '数量',
            color: 'rgb(87, 255, 213)',
            colorStops: ['rgba(87, 255, 213, 0.35)', 'rgba(0, 255, 149, 0)'],
            data: cardData.value,
        },
    ];
});

async function fetchData() {
    const {data} = await getTollHourFlow({
        stationId: tollStation.value.stationId,
        stationName: tollStation.value.stationName,
    });
    cardData.value = data?.hourFlowList.map(item => ({
        value: item.flow,
        name: item.time,
    }));
}

onMounted(() => {
    fetchData();
});

</script>

<style lang="less" scoped>
.traffic-trend-card {
    &__content {
        height: 136px;
    }
}
</style>