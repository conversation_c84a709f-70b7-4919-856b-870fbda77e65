import {GeoJSONDataSource, Icon, Label,
    Twin, Polygon, DOMOverlay, BubblePoint, TextMesh, FatLine, Circle, PathTracker} from '@baidu/mapv-three';
import {GLTFLoader} from 'bmap-three/examples/jsm/loaders/GLTFLoader.js';

import {engine} from '@/store/engine';
import {bindPointer, unBindPointer} from '../common';

export * from './methods/index';

// 添加icon
export const addIcon = (point, url, options) => {
    point = [point?.[0], point?.[1], point?.[2] || 0];
    const {
        width = 92,
        height = 118,
        offset = [0, -50],
        customData = {},
        geoData = [
            {
                'type': 'Feature',
                'geometry': {
                    'type': 'Point',
                    'coordinates': point,
                },
                'properties': {
                    'icon': url,
                    'size': 40,
                    'customData': customData,
                },
            },
        ],
        _engine = engine.value,
        renderOrder = 0,
    } = options || {};

    const icon = _engine.add(new Icon({
        width,
        height,
        vertexSizes: true,
        vertexIcons: true,
        transparent: true,
        offset: offset,
        depthTest: false, // 深度检测
    }));
    icon.renderOrder = renderOrder;

    GeoJSONDataSource.fromGeoJSON(geoData).then(data => {
        data.setAttribute('size').setAttribute('icon');
        icon.dataSource = data;
    });

    bindPointer(icon);

    return {
        icon,
        _engine,
    };
};

// 删除icon
export const removeIcon = (icon, _engine = engine.value) => {
    unBindPointer(icon);
    icon && icon.engine.remove(icon);
    icon.dispose?.();
};

// 添加label
export const addLabel = (point, text = '交通事故', options) => {
    point = [point?.[0], point?.[1], point?.[2] || 0];
    const {
        offset = [100, -65],
        padding = [20, -20, 20, 40],
        width = 120,
        height = 40,
        background = 'maplayer/assets/image/label.png',
        fontSize = 'auto',
        vertexOffsets = false,
        vertexBackgrounds = true,
        customData = {},
        geoData = [
            {
                'type': 'Feature',
                'geometry': {
                    'type': 'Point',
                    'coordinates': point,
                },
                'properties': {
                    'background': background,
                    'text': text,
                    'customData': customData,
                },
            },
        ],
        _engine = engine.value,
    } = options || {};
    const label = _engine.add(new Label({
        width,
        height,
        vertexBackgrounds,
        vertexOffsets,
        offset,
        fontSize,
        padding,
        transparent: true,
        depthTest: false, // 深度检测
    }));
    GeoJSONDataSource.fromGeoJSON(geoData).then(data => {
        data.setAttribute('text').setAttribute('background');
        label.dataSource = data;
    });
    bindPointer(label);

    return {label, _engine};
};

// 移除label
export const removeLabel = (label, _engine = engine.value) => {
    unBindPointer(label);
    label && label.engine.remove(label);
    label.dispose?.();
};

// 添加文字
export const addText = (point, text = '', options) => {
    point = [point?.[0], point?.[1], point?.[2] || 0];
    const {
        offset = [0, 0],
        padding = [4, 4],
        margin = [10, 10],
        fontSize = 16,
        collides = false,
        fillStyle = 'rgb(255,255,255)',
        fontFamily,
        renderOrder = 0,
        _engine = engine.value} = options || {};
    const geoData = [
        {
            'type': 'Feature',
            'geometry': {
                'type': 'Point',
                'coordinates': point,
            },
            'properties': {
                'Name': text.toString(),
                'Layer': text.toString(),
            },
        },
    ];
    const _text = _engine.add(new TextMesh({
        text,
        collides,
        fillStyle,
        fontFamily,
        fontSize,
        padding,
        margin,
    }));
    _text.pixelOffsetX = offset[0];
    _text.pixelOffsetY = offset[1];
    _text.flat = false;
    _text.renderOrder = renderOrder;

    GeoJSONDataSource.fromGeoJSON(geoData).then(data => {
        data.setAttribute('text', 'Name');
        _text.dataSource = data;
    });
    return {
        _text,
        _engine,
    };
};

// 删除文字
export const removeText = (text, _engine = engine.value) => {
    text && text.engine.remove(text);
    text?.dispose?.();
};

// 添加模型
export const addModel = (url = 'maplayer/assets/models/car-impact.glb', position, scale = 7, callback) => {
    const loader = new GLTFLoader();
    const point = engine.value.map.projectPointArr(position);
    let model = null;
    loader.load(url, gltf => {
        model = gltf.scene;
        model.position.set(point[0], point[1], 0);
        model.scale.setScalar(scale);
        model.rotation.x = Math.PI / 2;
        engine.value.add(model);
        callback && callback(model);
    });
};

// 删除模型
export const removeModel = model => {
    model && engine.value.remove(model);
};

// 添加孪生
export const addTwin = (showLabel = true, _engine = engine.value) => {
    const label = _engine.add(
        new Label({
            width: 124 / 32 / 2,
            // width: 180,
            height: 26 / 32,
            // height: 60,
            keepSize: false,
            fontSize: '8',
            fillStyle: '#fff',
            padding: [8, 0, 8, 8],
            offset: [0, -2],
            background: 'maplayer/assets/image/car_top_blue.png',
            depthTest: false,
        })
    );
    label.renderOrder = Infinity;
    const twin = _engine.add(new Twin({
        delay: 3 * 1000,
        modelConfig: {
            1: 'maplayer/assets/models/qiche.glb',
            2: 'maplayer/assets/models/kache.glb',
            3: 'maplayer/assets/models/uav.glb',
        },
        objectAttributes: {
            'text': 'plate',
        },
        enableColorList: ['car_body'],
        objects: showLabel ? [label] : null,
    }));
    twin.dataProvider
        .process('time', 'timestamp')
        .process('point', item => ([item.longitude, item.latitude, item.altitude]))
        // .process('point', item => ([item.longitude, item.latitude, -0.5]))
        .process('dir', item => (-item.heading) / 180 * Math.PI)
        .process('modelType', 'type')
        .process('id', item => item.id + 'id');
    return twin;
};

// 删除孪生
export const removeTwin = (twin, _engine = engine.value) => {
    twin?.modelClear();
    twin && _engine.remove(twin);
};

// 添加面
export const addPolygon = (points, _options) => {
    const {
        color = '#00ff00',
        opacity = 1,
        mapScale = 0.03,
        extrude = false,
        vertexColors = false,
        depthTest = false,
        _engine = engine.value,
    } = _options;
    points = points?.map(i => [i[0], i[1], i[2]]) || [];
    let options = {
        extrude, // 是否拉伸
        vertexColors,
        opacity,
        mapScale,
        color,
        depthTest,
        ..._options,
    };
    let p = new Polygon(options);
    const polygon = _engine.add(p);
    const geoData = [
        {
            type: 'Feature',
            geometry: {
                type: 'MultiPolygon',
                coordinates: [
                    [
                        points,
                    ],
                ],
            },
        },
    ];

    GeoJSONDataSource.fromGeoJSON(geoData).then(data => {
        polygon.dataSource = data;
    });

    return polygon;
};

// 删除面
export const removePolygon = polygon => {
    polygon && polygon.engine.remove(polygon);
    polygon.dispose?.();
};

// 添加dom label
export const addDOMOverlay = (point, dom, options) => {
    const {
        offset = [100, -65],
        _engine = engine.value,
    } = options || {};
    point = [point?.[0], point?.[1], point?.[2] || 0];
    if (!dom || !_engine) {
        return;
    }
    const domOverlay = _engine.add(new DOMOverlay({
        point: point,
        dom: dom,
        offset: offset,
        stopPropagation: true,
    }));
    return {domOverlay, _engine};
};

// 删除dom label
export const removeDOMOverlay = domOverlay => {
    domOverlay && domOverlay.engine.remove(domOverlay);
    domOverlay.dispose?.();
};


// 添加气泡点
export const addBubble = (point, options) => {
    const {
        type = '',
        color = 'rgba(117, 255, 206, .6)',
        size = 180,
        duration = 2000,
        keepSize = true,
        _engine = engine.value,
    } = options || {};
    point = [point?.[0], point?.[1], point?.[2] || 0];
    const geoData = [
        {
            'type': 'Feature',
            'geometry': {
                'type': 'Point',
                'coordinates': point,
            },
            'properties': {
                'type': type,
                'point': point,
            },
        },
    ];
    // wave类型的气泡点
    const bubble = _engine.add(new BubblePoint({
        color,
        size,
        type,
        duration,
        keepSize,
    }));

    GeoJSONDataSource.fromGeoJSON(geoData).then(data => {
        bubble.dataSource = data;
    });

    bindPointer(bubble);

    return {
        _engine,
        bubble,
    };
};


// 删除气泡点
export const removeBubble = (bubble, _engine = engine.value) => {
    unBindPointer(bubble);
    bubble && bubble.engine.remove(bubble);
    bubble?.dispose?.();
};

// 添加线
export const addLine = (positions, options, _engine = engine.value) => {
    const {lineWidth, color, opacity, transparent = false, customData, renderOrder = 0} = options || {};
    const line = _engine.add(new FatLine({
        vertexColors: true,
        lineWidth,
        opacity,
        keepSize: true,
        lineJoin: 'round',
        transparent,
        depthTest: false, // 深度检测
        ...options,
    }));
    if (transparent) {
        line.renderOrder = renderOrder;
    }
    const geojson = {
        type: 'Feature',
        geometry: {
            type: 'LineString',
            coordinates: positions,
        },
        properties: {
            color: color,
            customData: customData,
        },
    };
    GeoJSONDataSource.fromGeoJSON(geojson).then(geoData => {
        geoData.setAttribute('color');
        line.dataSource = geoData;
    });
    bindPointer(line);
    return {line, _engine};
};
// 删除线
export const removeLine = line => {
    unBindPointer(line);
    line && line.engine.remove(line);
    line.dispose?.();
};

// 添加圆
export const addCircle = (point, options) => {
    const {
        size = 30,
        color = '#381b29',
        borderColor = '#812d43',
        borderWidth = 1,
        opacity = 1,
        vertexSizes = false,
        keepSize = true,
        customData = {},
        _engine = engine.value,
    } = options || {};

    const geojson = {
        type: 'Feature',
        geometry: {
            type: 'Point',
            coordinates: point,
        },
        properties: {
            customData,
        },
    };

    const circle = _engine.add(new Circle({
        color,
        size,
        borderWidth,
        opacity,
        borderColor,
        vertexSizes,
        keepSize,
    }));

    GeoJSONDataSource.fromGeoJSON(geojson).then(data => {
        circle.dataSource = data;
    });

    return {
        circle,
        _engine,
    };
};
// 删除圆
export const removeCircle = circle => {
    circle && circle.engine.remove(circle);
    circle?.dispose?.();
};

// 添加视野漫游动画
export const addPathTracker = options => {
    const {
        viewMode = 'unlock',
        positions,
        model,
        duration = 10000,
        distance = 50,
        pitch = 70,
        _engine = engine.value,
    } = options;

    const pathTracker = _engine.add(new PathTracker());
    pathTracker.interpolateDirectThreshold = 50; // 进行方向插值的距离点的阈值
    pathTracker.track = positions; // 跟踪的路线,为坐标数组或LineString类型的geojson数据
    pathTracker._frameInfo = []; // 防止旧版本报错
    pathTracker.start({
        duration,
        distance,
        pitch,
        heading: 10,
    });
    pathTracker.object = model;
    pathTracker.viewMode = viewMode;

    return {
        pathTracker,
        _engine,
    };
};

// 删除视野漫游动画
export const removePathTracker = (name, _engine = engine.value) => {
    name && _engine.remove(name);
};