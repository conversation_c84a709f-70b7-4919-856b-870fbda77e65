<template>
    <div class="construction-visualization">
        <LeftPanel
            :collapse.sync="leftCollapse"
            @createScheme="handleCreateEmulation"
            @rowClick="handleEntryDetail"
        />
        <RightPanel
            :collapse.sync="rightCollapse"
        />
        <ToolButton class="toll-button"/>
        <template v-if="!createConfig.show">
            <map-line/>
            <!-- 施工扎点 -->
            <ConstructionMarker @clickMarker="handleEntryDetail"/>
            <template v-if="isDetail">
                <!-- 重点车辆扎点 -->
                <EmphasisCarMarker/>
                <!-- 事件扎点 -->
                <EventMarker/>
                <!-- 摄像头扎点 -->
                <VidiconMarker/>
                <!-- 拥堵路段 -->
                <CongestedRoad/>
                <!-- 情报板 -->
                <BoardMarker/>
            </template>
        </template>
    </div>
</template>

<script setup>
import MapLine from '@/views/TollStation/components/MapLine/index.vue';
import LeftPanel from './LeftPanel/index.vue';
import RightPanel from './RightPanel/index.vue';
import dayjs from 'dayjs';
import {openCreateScheme, showScheme, createConfig} from '../Emulation';
import {computed, onMounted} from 'vue';
import {useUnit, viewToFk, viewToMicro} from '@/utils';
import ConstructionMarker from './components/ConstructionMarker/index.vue';
import {activeProjectInfo} from './store';
import EmphasisCarMarker from './components/EmphasisCarMarker/index.vue';
import EventMarker from './components/EventMarker/index.vue';
import VidiconMarker from './components/VidiconMarker/index.vue';
import CongestedRoad from './components/CongestedRoad/index.vue';
import ToolButton from './components/ToolButton/index.vue';
import BoardMarker from './components/BoardMarker/index.vue';
import {useCollapsePanel} from '@/hooks/useCollapsePanel';
import {useWindowSize} from '@vueuse/core';

const {ratio} = useUnit();

const {collapse: leftCollapse} = useCollapsePanel();
const {collapse: rightCollapse} = useCollapsePanel();

const {width, height} = useWindowSize();

const isSmallScreen = computed(() => width.value / height.value < 3.6);

const isDetail = computed(() => activeProjectInfo.value);

const getOffsetRight = computed(() => {
    // 默认
    let offset = 1816;
    // 折叠右侧
    if (rightCollapse.value) {
        offset = 16;
    }
    // 收起仿真
    else if (!showScheme.value) {
        offset = 1286;
    }
    return `${offset * ratio.value}px`;
});

async function handleCreateEmulation(info) {
    // 小尺寸屏幕时，左侧面板收起
    if (isSmallScreen.value) {
        leftCollapse.value = true;
    }
    rightCollapse.value = false;
    showScheme.value = true;
    const {
        highSpeedName,
        closeLanePosition,
        eventLocationType,
        eventPosition,
        eventStartTime,
        duration,
        eventStartStake,
        direction,
        eventEndStake,
        eventEndTime,
        closeLane,
    } = info;
    const emulationEndTime = dayjs(eventStartTime)
        .add(duration, 'minute').format('YYYY-MM-DD HH:mm:ss');
    openCreateScheme({
        baseInfo: {
            highSpeedName,
            closeLanePosition,
            eventLocationType,
            eventPosition,
            eventStartStake,
            eventEndStake,
            eventEndTime,
            eventStartTime,
            duration,
            direction,
            emulationEndTime,
            closeLane: closeLane?.split(',').map(item => +item) || [],
            highSpeedName1: highSpeedName,
            emulationStartTime: eventStartTime,
        },
    });
}

function handleEntryDetail(info) {
    const {
        eventPosition,
        eventAltitude,
    } = info;
    const position = [
        ...eventPosition.split(','),
        eventAltitude,
    ];
    activeProjectInfo.value = info;
    viewToMicro(position);
}

onMounted(() => {
    viewToFk();
});

</script>

<style lang="less" scoped>
.toll-button {
    position: absolute;
    z-index: 30;
    right: v-bind(getOffsetRight);
    bottom: 85px;
    transition: right .16s linear;
}
</style>