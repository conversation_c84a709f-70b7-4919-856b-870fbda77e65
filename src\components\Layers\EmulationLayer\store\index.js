import {ref, shallowRef} from 'vue';
import {twinData} from '../mock/index';
import {fetchUuid, getstakenumberlist, getSumoStake, getHighSpeedInfo} from '@EmulationLayer/api';

const StakeNumberMap = new Map();
const SumoNumberMap = new Map();

// 仿真ws id列表
export const emulationIds = ref();

// 当前策略id
export const strategyId = ref();

// 当前仿真信息
export const emulationInfo = ref();

// uuid
export const uuid = ref(null);

// 仿真当前时刻
export const simulationTime = ref(null);





// 仿真缓存时间
export const simulationBufferTime = ref(null);

// 桩号列表
export const stakenumberlist = ref([]);
// 路段信息
export const roadInfo = ref({});
// sumo桩号
export const sumoStakeList = ref([]);
// 高速名称列表
export const highSpeedInfo = ref([]);

// 是否开启自定义区域
export const isCustomArea = ref(false);

// 添加自定义区域
export const addArea = ref();

// 更新自定义区域信息
export const updateAreaInfo = ref();

// 区域编辑管理器
export const polygonEditor = shallowRef();

// 矩形编辑管理器
export const reactEditor = shallowRef();

// 圆形编辑管理器
export const circleEditor = shallowRef();

// 是否折叠左侧面板
export const collapse = ref(false);

// 默认视角
export const defaultCenter = ref();

// 获取唯一身份uuid
export const initUuid = async () => {
    if (uuid.value) return;
    const {data} = await fetchUuid();
    uuid.value = data;
};

export const init_number_list = async (params = '') => {
    if (StakeNumberMap.has(params)) {
        const data = StakeNumberMap.get(params);
        stakenumberlist.value = data;
        return;
    }
    const {data} = await getstakenumberlist(params);
    Object.freeze(data);
    StakeNumberMap.set(params, data);
    stakenumberlist.value = data;

    return;
};

export const init_sumo_list = async (
    params = {
        highSpeedName: window.localStorage.getItem('highSpeedName') || 'S85',
    }) => {
    const {highSpeedName} = params;
    if (SumoNumberMap.has(highSpeedName)) {
        const data = SumoNumberMap.get(highSpeedName);
        sumoStakeList.value = data;
        return;
    }
    const {data} = await getSumoStake(params);
    Object.freeze(data[highSpeedName]);
    SumoNumberMap.set(highSpeedName, data[highSpeedName]);
    sumoStakeList.value = data[highSpeedName];
    return;
};

export const init_road_info = async () => {
    const {data} = await getHighSpeedInfo();
    highSpeedInfo.value = data;
};
