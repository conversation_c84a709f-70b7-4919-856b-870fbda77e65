# 折叠面板组件

## 所需参数
|参数名|含义|类型|默认值|说明|
|-|-|-|-|-|
|info|扎点信息|Object|-|必填|必填|
|managerInstace|扎点管理器(DOMManager)实例|Object|-|必填|
|showBadge|是否显示气泡右上角徽标|Boolean|false|-|
|showLabel|是否显示标签|Boolean|false|-|
|bubbleType|气泡类型|String|'rect'|'rect'-方形气泡，'circle'-圆形气泡，'diamond'-菱形气泡|
|bubbleColor|气泡颜色|String|'#009EFF'|-|
|iconName|气泡中间icon类型|String|'common'|与Icon组件type对应|
|iconName|气泡中间icon名称|String|'camera-4'|与Icon组件name对应|
|iconColor|气泡中间icon颜色|String|'#ffffff'|与Icon组件color对应|


### info数据格式说明
```javascript
{
    // 扎点坐标
    position: [122, 23, 20],
    // 扎点标签名称
    label: '测试扎点',
    // 扎点气泡右上角徽标数值
    badge: 10,
    // 点击扎点回调，e为该扎点信息
    clickCallback: (e) => {
        console.log('点击了扎点');
    }
    // 点击扎点回调，e为该扎点信息
    mouseenterCallback: (e) => {
        console.log('鼠标移入扎点');
    }
    // 点击扎点回调，e为该扎点信息
    mouseleaveCallback: (e) => {
        console.log('鼠标移出扎点');
    }
}
```