<template>
    <div class="item">
        <div class="type">
            <div class="top">
                <div class="exa">
                    <div class="top-icon mt-12">
                        <img :src="info.info?.icon" alt="">
                    </div>
                    <div class="text mt-12">
                        <p>{{ info.info?.title ?? '-' }}</p>

                    </div>
                    <div class="right mt-12">
                        <p>{{ info.onlineRate || '-' }}<span>%</span></p>
                    </div>
                </div>
                <div class="trans">
                    <p>{{ info.info?.translate }}</p>
                </div>
            </div>

            <div class="progress">
                <progress-c
                    :line-width="info?.onlineRate" line-color="rgba(0,255,114,0.5)"
                    :margin-left="1" background-color="rgba(0,0,0,.3)"
                />
            </div>

        </div>
        <div class="bottom">
            <p>在线<span class="tetxspan">{{ info.onlineCnt || '0' }}</span></p>
            <p> 离线
                <span class="tetxspan">{{ info.offlineCnt || '0' }}</span>
            </p>
        </div>
    </div>
</template>

<script>
import ProgressC from '@/components/Common/Progress/index.vue';
export default {
    components: {
        ProgressC,
    },
    props: {
        url: {
            type: String,
            default: '',
        },
        info: {
            type: Object,
            default: () => {},
        },
    },
    setup() {


    },

};
</script>

<style lang="less" scoped>
.item {
    display: flex;
    flex-direction: column;

    .type {
        border: 1px solid rgba(8, 74, 178, .35);
        backdrop-filter: blur(10.87px);
        background: rgba(8, 35, 79, .8);
    }

    .top {
        display: flex;
        flex-direction: column;
        width: 270px;
        height: 76px;
        font-size: 20px;
        padding: 0 18px;

        .exa {
            display: flex;
        }

        .top-icon {
            width: 32px;
            height: 32px;
            display: flex;
            justify-content: center;
            border: 1px solid rgba(255, 255, 255, .4);
            border-top: 4px solid rgba(255, 255, 255, .4);
            align-items: center;
            // background-image: url('@/assets/images/serviceM/icon-bg.png');
            // background-size: 100% 100%;
            padding: 6px;
            margin-right: 6px;
        }

        position: relative;

        .text {
            line-height: 32px;
            width: 229px;

            p {
                font-size: 20px;
                font-family: PingFang SC;
                font-weight: 500;
                line-height: 32px;
                letter-spacing: 0;
                text-align: justify;
            }

            .last {
                position: absolute;
                font-family: RoboData;
                font-size: 14px;
                font-weight: 400;
                line-height: 20px;
                letter-spacing: 0;
                text-align: justify;
            }
        }

        .trans {
            color: rgb(255, 255, 255);
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
            letter-spacing: 0;
            text-align: justify;
            margin-left: 36px;
            opacity: .3;
            font-family: RoboData-Beta 1.1;
            text-transform: uppercase;
        }

        .right {
            p {
                color: rgb(255, 255, 255);
                font-family: RoboData;
                font-size: 32px;
            }

            span {
                font-size: 14px;
                opacity: .8;
            }
        }

        img {
            width: 20px;
            height: 20px;
        }

        .a {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 42px;
            height: 42px;
            border: 1px solid rgba(255, 255, 255, .1);
            border-top: 1px solid #8e8e8e;
        }
    }

    .progress {
        margin-top: 9px;
        margin-bottom: 18px;
    }

    .bgd {
        height: 12px;
    }

    .bottom {
        box-sizing: border-box;
        // background: linear-gradient(119.36deg, rgb(36, 104, 242) 3.849%, rgba(1, 255, 229, .7) 99.795%);
        width: 270px;
        height: 31px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 9px;
        padding: 0 24px;
        background:
            linear-gradient(
                -45deg,
                transparent 8px,
                rgba(1, 255, 229, .7) 3.849%,
                rgb(36, 104, 242)   99.795%
            );

        p {
            opacity: .6;
            color: rgb(255, 255, 255);
            font-family: PingFang SC;
            font-size: 18px;
            font-weight: 500;
            line-height: 26px;

            .tetxspan {
                color: rgb(255, 255, 255);
                font-family: RoboData;
                font-size: 18px;
                line-height: 26px;
                margin-left: 8px;
            }
        }
    }
}
</style>