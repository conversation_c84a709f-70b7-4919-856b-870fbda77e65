<template>
    <div class="emulation-chart-wrapper">
        <chart
            :data-source="chartsData"
            :start-time="emulationStartTime"
            :end-time="emulationEndTime"
            :event-start-time="eventStartTime"
        />
    </div>
</template>

<script>
import Chart from './Chart/index.vue';
import {ref, watch} from 'vue';
import dayjs from 'dayjs';
import {useWebSocket} from '@vueuse/core';
import {emulationInfo, emulationTargetData} from '../../index';
import {ceil} from 'lodash-es';
import {emulationConfig} from '@/utils/common';

export default {
    components: {
        Chart,
    },
    props: {
        emulationIds: String,
        schemeId: String,
        uuid: String,
        emulationStartTime: String,
        emulationEndTime: String,
        eventStartTime: String,
    },
    setup(props) {

        const {
            basePrefix,
            baseWsHost,
        } = emulationConfig;

        // 0: 无策略, 1: 策略1, ....
        const chartsData = ref([[], []]);
        const wsMap = {};
        function initWs() {
            const {emulationIds, uuid, emulationStartTime} = props;
            for (let index = 0; index < emulationIds.length; index++) {
                const emulationId = emulationIds[index];
                if (wsMap[emulationId] || !emulationId) {
                    continue;
                }
                const startTime = dayjs(emulationStartTime).valueOf();
                const params = `emulationId=${emulationId}&startTime=${startTime}&uuid=${uuid}`;
                const url = `${baseWsHost}${basePrefix}/ws/imEmulationQueueSaturation?${params}`;
                const {
                    ws,
                } = useWebSocket(url, {
                    immediate: true,
                    onMessage(_, {data}) {
                        if (data === 'ok' || data === '{}') {
                            return;
                        };
                        data = JSON.parse(data);
                        if (index === 0) {
                            emulationInfo.value.emulationBufferTimeUnix = data.time;
                            emulationTargetData.value = {
                                saturation: ceil(data.saturation, 2),
                                avgSpeed: ceil(data.avgSpeed, 2),
                                serviceLevel: ceil(data.serviceLevel, 2),
                            };
                        }
                        chartsData.value[index].push({
                            queueLength: data.queueLength,
                            time: data.time,
                        });
                    },
                });
                wsMap[emulationId] = ws;
            }
        }

        watch(
            () => props.emulationIds,
            val => {
                val && initWs();
            },
            {
                immediate: true,
            }
        );

        return {
            chartsData,
        };
    },
};
</script>