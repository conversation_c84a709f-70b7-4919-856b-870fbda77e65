<template>
    <modal-card
        :visible="visible"
        :width="820"
        title="发布到情报板"
        @close="handleClose"
        @confirm="handleConfirm"
    >
        <div class="release-board scroll">
            <el-form
                ref="formRef"
                class="form"
                size="medium"
                :model="formModel"
                :rules="formRules"
                :label-width="labelWidth"
            >
                <el-row>
                    <el-col :span="11">
                        <el-form-item
                            label="发布方向："
                            prop="direction"
                            required
                        >
                            <el-select v-model="formModel.direction" size="small">
                                <el-option label="沈阳方向" value="沈阳方向"/>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="13">
                        <el-form-item label="发布桩号：" required>
                            <el-col :span="11">
                                <el-form-item prop="startStake">
                                    <el-select v-model="formModel.startStake" size="small">
                                        <el-option label="k123+123" value="k123+123"/>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="2">
                                <div style="text-align: center;">至</div>
                            </el-col>
                            <el-col :span="11">
                                <el-form-item prop="endStake">
                                    <el-select v-model="formModel.endStake" size="small">
                                        <el-option label="k123+123" value="k123+123"/>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-form-item>
                    </el-col>
                </el-row>
                <div class="board-collapse-wrapper scroll show-scroll">
                    <board-collapse
                        v-for="board in boardList"
                        :key="board.title"
                        :title="board.title"
                        :device-list="board.deviceList"
                    />
                </div>
            </el-form>
        </div>
    </modal-card>
</template>

<script>
import {ModalCard} from '@/components/Common';
import {useUnit} from '@/utils';
import {
    Form,
    FormItem,
    Select,
    Option,
    Row,
    Col,
    Input,
} from 'element-ui';
import {computed, ref} from 'vue';
import BoardCollapse from './BoardCollapse.vue';

export default {
    components: {
        ModalCard,
        [Form.name]: Form,
        [FormItem.name]: FormItem,
        [Select.name]: Select,
        [Option.name]: Option,
        [Row.name]: Row,
        [Col.name]: Col,
        [Input.name]: Input,
        BoardCollapse,
    },
    props: {
        visible: Boolean,
    },
    setup(props, {emit}) {
        const {ratio} = useUnit();

        const boardList = [
            {
                title: '门架式显示屏',
                deviceList: [
                    {
                        deviceName: 'K248+750-沈阳方向-门架式标志显示屏门架式情报板',
                        deviceType: '门架式情报板',
                        deviceStatus: 1,
                    },
                    {
                        deviceName: 'K248+750-沈阳方向-门架式标志显示屏门架式情报板',
                        deviceType: '门架式情报板',
                        deviceStatus: 1,
                    },
                ],
            },
            {
                title: '悬臂式显示屏',
                deviceList: [
                    {
                        deviceName: 'K248+750-沈阳方向-门架式标志悬臂式显示屏情报板',
                        deviceType: '悬臂式显示屏',
                        deviceStatus: 1,
                    },
                    {
                        deviceName: 'K248+750-沈阳方向-门架式标志悬臂式显示屏情报板',
                        deviceType: '悬臂式显示屏',
                        deviceStatus: 1,
                    },
                ],
            },
            {
                title: '站前屏',
                deviceList: [
                    {
                        deviceName: 'K248+750-沈阳方向-门架式标志站前屏情报板',
                        deviceType: '站前屏',
                        deviceStatus: 1,
                    },
                    {
                        deviceName: 'K248+750-沈阳方向-门架式标志站前屏情报板',
                        deviceType: '站前屏',
                        deviceStatus: 1,
                    },
                ],
            },
        ];

        const formRef = ref(null);
        const formModel = ref({
            direction: '沈阳方向',
            startStake: 'k123+123',
            endStake: 'k123+123',
        });
        const formRules = {
            direction: [{required: true, message: '请选择所属方向', trigger: 'change'}],
            startStake: [{required: true, message: '请选择起点桩号', trigger: 'change'}],
            endStake: [{required: true, message: '请选择终点桩号', trigger: 'change'}],
        };

        const labelWidth = computed(() => `${ratio.value * 100}px`);

        async function handleConfirm() {
            formRef.value.validate(async valid => {
                if (!valid) return;

                emit('confirm');
            });
        }

        function handleClose() {
            emit('update:visible', false);
        }

        return {
            labelWidth,
            formModel,
            formRef,
            handleConfirm,
            formRules,
            handleClose,

            boardList,
        };
    },
};
</script>

<style lang="less" scoped>
.release-board {
    .board-collapse-wrapper {
        height: 620px;
        overflow-y: auto;
        padding-right: 4px;
    }
}
</style>