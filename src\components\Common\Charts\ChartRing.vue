<template>
    <!-- 柱状图 -->
    <div ref="rootCharts" class="rootCharts"></div>
</template>
<script>

import {echarts} from './common';
import {BarChart} from 'echarts/charts';
import {onBeforeUnmount, onMounted, ref, watch} from 'vue';

echarts.use([BarChart]);

export default {
    data() {
        return {};
    },
    props: {
        value: {
            type: Number,
        },
        title: {
            type: String,
            default: '',
        },
        // 环背景颜色
        bgColor: {
            type: String,
            default: 'rgba(74, 91, 83, .3)',
        },
        // 环颜色
        ringColor: {
            type: String,
            default: 'rgba(247, 181, 1)',
        },
    },
    setup(props, {emit}) {

        let chart = null;
        const rootCharts = ref(null);

        const resize = () => {
            if (chart) {
                chart.resize();
            }
        };


        // 数据处理
        let max = 100;

        const update = () => {
            // 裁剪
            let value = (props.value + '').slice(0, 5);
            chart.setOption({
                title: [
                    {
                        text: '{a|' + value + '%}\n{b|' + props.title + '}',
                        show: true,
                        x: 'center',
                        y: 'center',
                        textStyle: {
                            rich: {
                                a: {
                                    fontSize: 24,
                                    color: 'rgba(255,255,255)',
                                    padding: [0, 0, 5, 0],
                                },
                                b: {
                                    fontSize: 14,
                                    color: 'rgba(255,255,255)',
                                    padding: [5, 0, 0, 0],
                                },
                            },
                        },
                    },
                ],
                polar: {
                    center: ['50%', '55%'],
                    radius: ['70%', '80%'],
                },
                angleAxis: {
                    max: max,
                    show: false,
                },
                radiusAxis: {
                    type: 'category',
                    show: false,
                    axisLabel: {
                        show: false,
                    },
                    axisLine: {
                        show: false,
                    },
                    axisTick: {
                        show: false,
                    },
                },
                series: [
                    {
                        name: '',
                        type: 'bar',
                        roundCap: true,
                        showBackground: true,
                        backgroundStyle: {
                            color: props.bgColor,
                        },
                        data: [props.value],
                        coordinateSystem: 'polar',
                        itemStyle: {
                            normal: {
                                color: props.ringColor,
                            },
                        },
                    },
                ],
            });
        };


        const initChart = () => {
            if (!rootCharts.value) {
                return;
            }
            chart = echarts.init(rootCharts.value, 'emulation');
            window.addEventListener('resize', resize);
        };

        watch(() => props.data, () => {
            update();
        }, {
            // imediate: true,
            // deep: true,
        });

        onMounted(() => {
            initChart();
            update();
        });

        onBeforeUnmount(() => {
            window.removeEventListener('resize', resize);
            chart.dispose();
        });

        return {
            rootCharts,
        };
    },
};

</script>

<style>
.rootCharts {
    width: 100%;
    height: 100%;
}
</style>