<template>
    <div class="structure-inspection">
        <div
            v-for="(item, index) in list" :key="index"
            class="structure-inspection__item"
        >
            <div class="structure-inspection__item--left">
                <icon :name="item.icon"/>
            </div>
            <div class="structure-inspection__item--right">
                <div class="item__right--title">
                    <span>{{ item.name }}</span>
                    <div>{{ item.num }}<p>座</p></div>
                </div>
                <div class="item__right--data">
                    <div class="item__right--data-total">{{ item.point }}</div>
                    <div class="item__right--data-unit">%</div>
                </div>
                <progress-comp
                    background-color="rgba(0, 0, 0, .5)"
                    line-color="rgba(0, 255, 114, .7)"
                    line-after-color="rgba(0, 255, 114, 1)"
                    :line-width="item.point"
                />
                <div class="item__right--point">{{ item.healthNum }}/{{ item.allDeviceNum }}</div>
            </div>
        </div>
    </div>
</template>

<script>
import {ref, onMounted, set} from 'vue';
import {ProgressComp, Icon} from '@/components/Common';
import {getStructureStatus, getInterchangeStatus} from '@/api/equipment/facilitydisplay';
export default {
    name: '结构物检测',
    components: {
        ProgressComp, Icon,
    },
    setup(props) {
        const list = ref([
            {
                icon: 'qiaoliang1',
                name: '桥梁设备在线率',
                num: 0,
                point: 0,
                type: 'bridge',
            },
            {
                icon: 'lijiao',
                name: '立交设备在线率',
                num: 0,
                point: 0,
                type: 'grade',
            },
        ]);

        const init = () => {
            getStructureStatus().then(res => {
                const data = res.data.bridgeList;
                set(list.value, 0, {
                    ...list.value[0],
                    ...data,
                    num: data.structureNum,
                    point: data.healthProportion,
                });
            });
            getInterchangeStatus().then(res => {
                set(list.value, 1, {
                    ...list.value[1],
                    ...res.data,
                    num: res.data.structureNum,
                    point: res.data.healthProportion,
                });
            });
        };

        onMounted(() => init());

        return {
            list,
        };
    },
};
</script>

<style lang="less" scoped>
.structure-inspection {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 172px;

    &__item {
        display: flex;
        justify-content: center;
        width: 270px;
        height: 172px;
        background-color: rgba(18, 74, 166, 0.3);
        backdrop-filter: blur(10px);
        border-top: 2px solid;
        border-image: linear-gradient(to right, rgb(36, 104, 242), rgba(1, 255, 229, 0.5));
        padding-top: 24px;

        &--left {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 28px;
            background: url('@/assets/images/base/iconBg.png') no-repeat center center / 100%;
            margin-right: 8px;
        }

        &--right {
            display: flex;
            flex-direction: column;

            .item__right--title {
                display: flex;
                align-items: center;

                span {
                    font-size: 20px;
                    font-weight: 500;
                    font-family: 'PingFang';
                    color: rgba(255,255,255,.9);
                    margin-right: 14px;
                }

                div {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background-image: linear-gradient(to right, rgb(36, 104, 242), rgba(1, 132, 255, 0.7));
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    padding: 2px 10px;
                    font-size: 14px;
                    font-family: 'RoboData';
                    color: rgb(255,255,255);

                    p {
                        font-size: 12px;
                        font-family: 'PingFang';
                        color: rgba(255,255,255,.6);
                    }
                }
            }

            .item__right--data {
                display: flex;
                align-items: flex-end;
                margin: 20px 0;

                &-total {
                    font-size: 32px;
                    font-weight: 400;
                    font-family: 'RoboData';
                    color: rgb(255,255,255);
                }

                &-unit {
                    font-size: 14px;
                    font-weight: 400;
                    font-family: 'RoboData';
                    color: rgba(255,255,255, .6);
                    margin-left: 5px;
                }
            }

            .item__right--point {
                text-align: right;
                margin-top: 10px;
                color: rgba(#fff, .7);
                font-size: 16px;
                font-weight: 400;
                font-family: 'PingFang';
            }
        }
    }
}
</style>