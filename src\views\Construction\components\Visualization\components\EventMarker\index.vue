<template>
    <div class="">
        <BubbleMarker
            v-for="item in list"
            :key="item"
            :manager-instace="domManager"
            icon-name="event"
            bubble-type="diamond"
            bubble-color="#FF4B4B"
            :info="item"
            :need-hover="false"
            :show-label="false"
        >
            <div class="content-wrapper">
                <div>{{ item.label, }}</div>
                <div class="btn-go" @click.stop="handleEvent(item.eventId)">去应急系统处置</div>
            </div>
        </BubbleMarker>
    </div>
</template>

<script setup>
import {getEventList} from '@/api/construction';
import {BubbleMarker} from '@/components/Common';
import {ref, watch} from 'vue';
import {domManager} from '@/views/Construction/utils';
import {projectType} from '../../store';

const list = ref([]);

async function fetchData() {
    const {data} = await getEventList({
        projectType: projectType.value,
    });
    list.value = data?.map(item => ({
        position: [item.lng, item.lat, item.alt],
        label: item.typeName,
        eventId: item.id,
    }));
}

function handleEvent(eventId) {
    // 跳转到道路保畅子系统
    window.open(`/x-emergency/${eventId}?action=1&flag=1`, '_self');
}

watch(
    () => projectType.value,
    () => {
        fetchData();
    },
    {
        immediate: true,
    }
);
</script>

<style lang="less" scoped>
.content-wrapper {
    display: flex;
    white-space: nowrap;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, .8);
    padding: 10px 8px;
    font-size: 16px;

    .btn-go {
        color: rgb(2, 232, 216);
        margin-left: 4px;
        cursor: pointer;

        &::before {
            content: '';
            display: inline-block;
            width: 1px;
            height: 8px;
            background-color: rgba(80, 80, 80);
            margin-right: 4px;
        }
    }
}
</style>