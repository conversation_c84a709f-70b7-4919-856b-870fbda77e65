import {defineConfig, loadEnv} from 'vite';
import createVuePlugin from '@vitejs/plugin-vue2'; // 使用Vue2版本
import requireContext from 'rollup-plugin-require-context'; // 处理兼容webpack工具require-context;
import {fileURLToPath} from 'node:url';
import {createHtmlPlugin} from 'vite-plugin-html';
import libCss from 'vite-plugin-libcss';
import viteCompression from 'vite-plugin-compression';
import {baseConfig} from './scripts/base.config.js';
import {docConfig} from './scripts/doc.config.js';
import {aliasMap} from './scripts/common.js';
import {splitConfig} from './scripts/split.config.js';
import myPluginNext from './plugins/vite-plugin-myPluginNext.js';
import {origincssVitePlugin} from '@baidu/origin.css';
import vueSetupExtend from 'unplugin-vue-setup-extend-plus/vite';
export default defineConfig(({mode}) => {
    const env = loadEnv(mode, process.cwd());

    // 根据当前模式定义构建选项
    let buildOptions = {};

    switch (mode) {
        case 'development':
            // 开发环境构建选项
            buildOptions = baseConfig();
            break;
        case 'live':
            // 开发环境构建选项
            buildOptions = baseConfig();
            break;
        case 'production':
            // 库模式构建选项
            buildOptions = baseConfig();
            break;
        case 'split':
            // 上架库模式构建选项
            buildOptions = splitConfig();
            break;
        default:
            // 文档模式构建选项
            buildOptions = docConfig();
    }

    return {
        plugins: [
            origincssVitePlugin(),
            myPluginNext({mode}),
            createVuePlugin(),
            requireContext(),
            createHtmlPlugin(),
            libCss(),
            vueSetupExtend(),
            // viteCompression({
            //     ext: '.gz',
            //     deleteOriginFile: false,
            // }),
        ],
        resolve: {
            alias: {
                'lodash': 'lodash-es',
                '@': fileURLToPath(new URL('./src', import.meta.url)),
                'examples': fileURLToPath(new URL('./examples', import.meta.url)),
                '@EmulationLayer': fileURLToPath(new URL(aliasMap['@EmulationLayer'], import.meta.url)),
                '@StructureLayer': fileURLToPath(new URL(aliasMap['@StructureLayer'], import.meta.url)),
                '@MaintainPlanLayer': fileURLToPath(new URL(aliasMap['@MaintainPlanLayer'], import.meta.url)),
                '@MaintainProjectLayer': fileURLToPath(new URL(aliasMap['@MaintainProjectLayer'], import.meta.url)),
                '@ThreeUrgentLayer': fileURLToPath(new URL(aliasMap['@ThreeUrgentLayer'], import.meta.url)),
                '@AccidentAreaLayer':
                    fileURLToPath(new URL(aliasMap['@AccidentAreaLayer'], import.meta.url)),
                '@LinkageRegionLayer':
                    fileURLToPath(new URL(aliasMap['@LinkageRegionLayer'], import.meta.url)),
                '@ManualInspectionLayer': fileURLToPath(new URL(aliasMap['@ManualInspectionLayer'], import.meta.url)),
                '@FacilityLayer':
                    fileURLToPath(new URL(aliasMap['@FacilityLayer'], import.meta.url)),
                // 影响element ui table 展示效果，勿动;
                vue: 'vue/dist/vue.esm.js',
            },
        },
        server: {
            hrm: true,
            port: 6688,
            open: true,
            hot: true,
            host: 'localhost',
            proxy: {
                '/ihs/api/v1/servicearea': { // 仿真
                    target: 'http://************:9016',
                    changeOrigin: true,
                },
                '/ihs/api': {
                    target: 'http://************:9016',
                    changeOrigin: true,
                },
                '/ihs/monitor': {
                    target: 'http://************:9016',
                    changeOrigin: true,
                },
                // '/ihs': {
                //     target: 'https://gjdt.private.gdcg.cn',
                //     changeOrigin: true,
                //     secure: false,
                //     ws: true,
                // },
                '/ihs/maplayer': {
                    target: mode === 'live' ? 'https://gjdt.private.gdcg.cn' : 'http://************:9016',
                    changeOrigin: true,
                },
                '/height': {
                    target: 'https://gjdt.private.gdcg.cn',
                    changeOrigin: true,
                    secure: false,
                },
                '/gjdt': {
                    // 利通api
                    target: 'https://gjdt.private.gdcg.cn',
                    secure: false,
                    changeOrigin: true,
                    rewrite: path => path.replace(/^\/gjdt/, ''),
                },
                '/gs-static': {
                    target: 'https://gjdt.private.gdcg.cn',
                    changeOrigin: true,
                    secure: false,
                },
            },
        },
        ...buildOptions,
    };
});