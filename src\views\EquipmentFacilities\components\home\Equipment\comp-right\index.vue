<template>
    <div class="comp-right">
        <div class="comp-right-item">
            <card class="card-long-2" title="正常运行设备数量图">
                <template #titleContent>
                    <cascadeSelect v-model="operat" @change="changeOperatFn"/>
                </template>
                <template #content>
                    <normalEquipment ref="normalEquipmentRef" :device-type="operat"/>
                </template>
            </card>
        </div>
        <div class="comp-right-item">
            <card class="card-long-2" title="设备数量空间分布图">
                <template #titleContent>
                    <cascadeSelect v-model="spatial" @change="changeSpatialFn"/>
                </template>
                <template #content>
                    <equipmentDistribution ref="equipmentDistributionRef" :device-type="spatial"/>
                </template>
            </card>
        </div>
        <div class="comp-right-item">
            <card class="card-long-2" title="设备故障率区间分布图">
                <template #titleContent>
                    <cascadeSelect v-model="failure" @change="changeFailureFn"/>
                </template>
                <template #content>
                    <failureDistribution ref="failureDistributionRef" :device-type="failure"/>
                </template>
            </card>
        </div>
    </div>
</template>

<script>
import {ref, nextTick} from 'vue';
import {Card} from '@/components/Common/index';
import normalEquipment from './normalEquipment.vue';
import equipmentDistribution from './equipmentDistribution.vue';
import failureDistribution from './failureDistribution.vue';
import cascadeSelect from './cascadeSelect.vue';

export default {
    name: '设备管理右',
    components: {
        Card,
        normalEquipment,
        equipmentDistribution,
        failureDistribution,
        cascadeSelect,
    },
    setup() {
        // 正常运行设备数量图
        const operat = ref('');
        const normalEquipmentRef = ref(null);
        const changeOperatFn = e => {
            nextTick(() => {
                normalEquipmentRef.value.init();
            });
        };

        // 设备数量空间分布图
        const spatial = ref('');
        const equipmentDistributionRef = ref(null);
        const changeSpatialFn = e => {
            nextTick(() => {
                equipmentDistributionRef.value.init();
            });
        };

        // 设备故障率区间分布图
        const failure = ref('');
        const failureDistributionRef = ref(null);
        const changeFailureFn = e => {
            nextTick(() => {
                failureDistributionRef.value.init();
            });
        };

        return {
            equipmentDistributionRef,
            normalEquipmentRef,
            failureDistributionRef,
            failure,
            spatial,
            operat,
            changeOperatFn,
            changeSpatialFn,
            changeFailureFn,
        };
    },
};
</script>

<style lang="less" scoped>
.comp-right {
    width: 1232px;

    :deep(.content) {
        padding: 16px 24px 14px;
    }

    &-item {
        width: 100%;
        margin-bottom: 24px;

        &:last-child {
            margin-bottom: 0;
        }

        .card {
            width: 1232px;
        }
    }
}
</style>