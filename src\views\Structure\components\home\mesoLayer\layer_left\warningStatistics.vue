<template>
    <div class="warning-statistics">
        <left-pie-li
            v-if="list.length" :color="color"
            :data="list"
        />
    </div>
</template>

<script>
import {ref, onMounted} from 'vue';
import {LeftPieLi} from '@/components/Common';
import {struWarnCount} from '@/api/structure/index';

export default {
    name: '结构物预警统计',
    components: {
        LeftPieLi,
    },
    setup(props) {
        const list = ref([]);

        const color = [
            'rgb(208, 208, 208)', 'rgba(85, 218, 218, .9)',
            'rgba(68, 218, 30, .9)', 'rgba(252, 255, 149, .9)', 'rgba(0, 176, 255, .9)',
        ];

        const warnTotal = ref(0);

        const init = () => {
            struWarnCount().then(res => {
                list.value = res.data.strWarnCountVO.map((item, index) => ({
                    name: item.structureName,
                    value: item.warnCount,
                    icon: 'qiaoliang1',
                    color: color[index],
                }));
                warnTotal.value = res.data.warnAll;
            });
        };

        onMounted(() => init());

        return {
            list,
            color,
            warnTotal,
        };
    },
};
</script>

<style lang="less" scoped>
.warning-statistics {
    width: 100%;
    height: 188px;
}
</style>