<template>
    <div class="event-card" @click="addEventIcon">
        <div class="header">
            <p>{{ info?.eventType | formatEventType }}</p>
            <div>
                <el-button
                    style="color: #fff;"
                    type="text"
                    @click="onEdit"
                >编辑</el-button>
                <el-button
                    type="text"
                    class="delete"
                    @click="onDelete"
                >删除</el-button>
            </div>
        </div>
        <div class="detail">
            <div class="detail-info">
                <span>事件发生时间：</span>
                <span>{{ info?.eventStartTime }}</span>
            </div>
            <div class="detail-info">
                <span>预计持续时长：</span>
                <span>{{ info?.duration }}</span>
            </div>
            <div class="detail-info">
                <span>位置桩：</span>
                <span>{{ info | formatePosition }} {{ info.eventStartStake }}</span>
            </div>
            <div class="detail-info">
                <span>行车方向：</span>
                <span>{{ info?.direction | formatDirection }}</span>
            </div>
            <div class="detail-info">
                <span>关闭车道数：</span>
                <span>{{ info?.closeLaneNum }}个</span>
            </div>
            <div class="detail-info">
                <span>事件位置类型：</span>
                <span>{{ info?.eventLocationType | formatEventLocation }}</span>
            </div>
        </div>
        <div class="footer-btn">
            <span @click="onCreate">创建仿真方案</span>
            <span v-if="moduleType === 1" @click="onImport">导入模板</span>
        </div>
        <el-dialog
            :visible.sync="visible"
            width="320px"
            class="del-modal"
            :modal-append-to-body="false"
        >
            <div class="content-box">
                <span>!</span>
                <span>确认删除该{{ moduleType === 1 ? '实时事件' : '事件模板' }}？</span>
            </div>
            <span slot="footer">
                <el-button @click="visible = false">取 消</el-button>
                <el-button type="primary" @click="onConfirm">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import {ref} from 'vue';
import {Button, Message, Dialog} from 'element-ui';
import {evnet_location_type} from '../config';
import {delEvent, importTemplate} from '@EmulationLayer/api';
import {road_direction} from '@EmulationLayer/config';
import {detailInfo, createConfig} from '@EmulationLayer/store/emulationCreate';
import {findEventType} from '@EmulationLayer/utils/index';


export default {
    props: {
        info: {
            type: Object,
            default: () => {},
        },
        moduleType: {
            type: Number,
            default: 1,
        },
    },
    components: {
        [Button.name]: Button,
        [Dialog.name]: Dialog,
    },
    filters: {
        formatEventType(val) {
            const cur = findEventType(val);
            return cur ? cur.name : '';
        },
        formatEventLocation(val) {
            const cur = evnet_location_type.find(info => info?.code === val);
            return cur ? cur.name : '';
        },
        formatDirection(val) {
            const cur = road_direction.find(info => info?.code === val);
            return cur ? cur.name : '';
        },
        formatePosition(info) {
            return info?.highSpeedNameCn ? info?.highSpeedNameCn + '-' : '' + info?.eventStartStake;
        },
    },
    methods: {

    },
    setup(props, {emit}) {
        const visible = ref(false);

        const onEdit = () => {
            emit('onEdit', props.info);
        };

        const onDelete = () => {
            visible.value = true;
        };

        // 导入模板
        const onImport = async () => {
            const id = props.info.id;
            const res = await importTemplate(id);
            if (res.code === 200) {
                Message.success('导入成功');
            }
        };

        // 删除
        const onConfirm = async () => {
            const id = props.info.id;
            await delEvent(id);
            Message.success('删除成功');
            emit('getData');
            visible.value = false;
        };

        const addEventIcon = () => {
            emit('addEvent', props.info);
        };

        const onCreate = () => {
            detailInfo.value = props.info;
            createConfig.value.show = true;
            createConfig.value.type = 'create';
            createConfig.value.id = '';
        };

        return {
            visible,
            onCreate,
            onEdit,
            onDelete,
            onImport,
            onConfirm,
            addEventIcon,
        };
    },
};
</script>

<style lang="less" scoped>
@import url('@EmulationLayer/assets/css/common.less');

.event-card {
    // background: rgba(123, 169, 238, .12);
    // border: 1px solid rgba(0, 231, 181, .25);
    padding-bottom: 24px;
    cursor: pointer;
    transition: border .2s ease-in-out;
    .background-card();

    &:hover {
        border-color: #08d6a5;
    }

    .header {
        height: 48px;
        background-color: rgba(7, 175, 135, .1);
        border-bottom: 1px solid #07af87;
        display: flex;
        justify-content: space-between;
        padding: 0 24px;
        font-family: "FZLTZHJW--GB1-0", sans-serif;
        cursor: pointer;
        align-items: center;

        > p {
            font-size: 16px;
            color: #e5f1ff;
        }
    }

    .detail {
        padding-bottom: 24px;

        .detail-info {
            margin-top: 24px;
            font-size: 14px;
            margin-left: 24px;

            > span:nth-child(1) {
                display: inline-block;
                width: 98px;
                margin-right: 8px;
                color: #93b4b7;
            }

            > span:nth-child(2) {
                color: #efffff;
            }
        }
    }

    .footer-btn {
        padding: 0 24px;
        display: flex;
        justify-content: center;
        cursor: pointer;

        span {
            display: flex;
            width: 132px;
            height: 36px;
            justify-content: center;
            border-radius: 2px;
            align-items: center;
            font-size: 14px;

            &:nth-child(1) {
                background-image: linear-gradient(180deg, rgb(26, 240, 147) .07%, rgb(20, 162, 178) 100%);
                color: #efffff;
            }

            &:nth-child(2) {
                border: 1px solid #08d6a5;
                color: #efffff;
                margin-left: 20px;
            }
        }
    }

    .del-modal {
        z-index: 2022;

        /deep/.el-dialog {
            background: rgba(32, 72, 63, 1);
            border-image: linear-gradient(180deg, #379084 0%, #5cf4c3 99%) 1.5 1.5 1.5 1.5;
            padding: 0 10px;
            border-radius: 6px;

            .el-dialog__body {
                .content-box {
                    color: #fff;
                    font-size: 16px;

                    >span:nth-child(1) {
                        display: inline-block;
                        width: 14px;
                        height: 14px;
                        margin-right: 8px;
                        text-align: center;
                        line-height: 14px;
                        background: #f9d087;
                        font-size: 14px;
                        color: rgba(9, 31, 67, .8);
                        border-radius: 50%;
                    }
                }
            }

            .el-dialog__footer {
                margin-top: 20px;
            }
        }
    }
}
</style>