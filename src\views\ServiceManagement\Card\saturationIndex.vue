<template>

    <card v-model="radio" title="饱和度指数演示">
        <template #content>
            <fokai-line
                v-if="list[0].data.length" y-name=""
                :data="list" :tooltip-formatter="tooltipFormatter"
                class="saturation-index"
            />
            <no-data v-else/>
        </template>
    </card>
</template>

<script>
import {FokaiLine, NoData, Card} from '@/components/Common';
import {
    getTrendHour,
} from '@/api/serviceManager/index.js';
import {ref, onMounted} from 'vue';
export default {
    name: '饱和度指数演示',
    props: {
        deviceType: {
            type: String,
            default: '',
        },
    },
    components: {
        NoData,
        FokaiLine,
        Card,
    },
    setup(props) {
        const list = ref([
            {
                name: '数量',
                color: 'rgb(87, 255, 213)',
                colorStops: ['rgba(87, 255, 213, 0.35)', 'rgba(0, 255, 149, 0)'],
                data: [],
            },
        ]);

        const init = () => {
            getTrendHour().then(res => {
                if (res.data && res.data.length) {
                    list.value[0].data = res.data.map(item => ({
                        ...item,
                        name: item.hour,
                        value: item.index,
                    }));
                }
                else {
                    list.value[0].data = [];
                }
            });
        };

        const tooltipFormatter = e => {
            // <div class="title-rb">${e[0].value}</div>
            return `<div class="tooltip-format">
                <div class="info">
                    <div class="item">
                        <div class="item-name">饱和度</div>
                        <div class="item-info">${e[0].value}</div>
                    </div>
                    <div class="item">
                        <div class="item-name">时间</div>
                        <div class="item-info">${e[0].name}</div>
                    </div>
                </div>
            </div>`;
        };
        onMounted(() => {
            init();

        });
        return {
            list,
            init,
            tooltipFormatter,
        };
    },
};
</script>

<style lang="less" scoped>
.saturation-index {
    height:310px;
}
</style>