<template>
    <SmallCard
        class="duty-info-card"
        title="值班信息"
    >
        <template #content>
            <div class="duty-info-card__content">
                <api-table
                    :columns="columns"
                    :api="getDuty"
                    :height="300"
                    :pagination="false"
                    :request-params="{
                        pageAble: false,
                    }"
                />
            </div>
        </template>
    </SmallCard>
</template>

<script setup>
import {ApiTable} from '@/components/Common';
import {useUnit} from '@/utils';
import {getDuty} from '@/api/tollStation';
import SmallCard from '@/components/Common/Card/smallCard.vue';

const {ratio} = useUnit();

const columns = [
    {
        label: '姓名',
        prop: 'dutyPersonName',
        width: `${70 * ratio.value}px`,
        align: 'center',
    },
    {
        label: '部门',
        prop: 'affiliatedDepartment',
        width: `${115 * ratio.value}px`,
        align: 'center',
    },
    {
        label: '职位',
        prop: 'position',
        width: `${105 * ratio.value}px`,
        align: 'center',
    },
    {
        label: '联系方式',
        prop: 'contactPhone',
        width: `${120 * ratio.value}px`,
        align: 'right',
    },
];

</script>

<style lang="less" scoped>
.duty-info-card {
    width: 460px;

    &__content {
        padding: 16px;
        backdrop-filter: blur(12px);
    }

    /deep/ .content {
        background-color: rgba(3, 31, 68, .3);
        backdrop-filter: blur(12px);
    }

    /deep/ .el-table {
        &__header {
            tr {
                font-size: 16px;
            }
        }

        &__body {
            font-size: 16px;
        }
    }
}
</style>