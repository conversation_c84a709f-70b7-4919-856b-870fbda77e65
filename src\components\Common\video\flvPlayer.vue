<template>
    <div v-loading="loading" class="videoBox">
        <video
            ref="videoElement" :autoplay="true"
            muted style="width: 100%; height: 100%;"
        >
        </video>
    </div>
</template>

<script setup>
import {ref, watch, onMounted, onUnmounted, computed} from 'vue';
import mpegts from 'mpegts.js';
import {
    getCameraInfo, getCameraClose,
} from '@/api/serviceManager/index.js';
// 使用环境变量获取视频接口前缀
const videoPath = import.meta.env.VITE_VIDEO_URL;
// 定义props
const props = defineProps({
    cameraInfoId: {
        type: String,
        required: true,
        default: '',
        // 视频id
    },
    // immediate: {
    //     type: Boolean,
    //     default: true,
    // },
});

const url = ref('');
const closeUrl = ref('');
const getData = async () => {
    try {
        if (!props.cameraInfoId) return;
        const {data} = await getCameraInfo({cameraIndexCode: props.cameraInfoId});
        if (!data || !data.list[0]) {
            throw new Error('获取video数据失败');
        }
        const {cameraFlowUrl, cameraInfoId} = data.list[0];
        url.value = `${videoPath}/api/video/flv/play?cameraInfoId=${cameraInfoId}&flowUrl=${cameraFlowUrl}`;
        closeUrl.value = cameraInfoId;
    }
    catch (error) {
        console.error('Error 获取video数据失败', error);
    }
};
// 初始化引用
const player = ref(null);
const videoElement = ref(null);
const loading = ref(true);

// 销毁播放器
const destroy =  () => {
    player.value?.pause();
    player.value?.unload();
    player.value?.detachMediaElement();
    player.value?.destroy();
    player.value = null;
};

// 创建播放器
const createVideo = async () => {
    if (mpegts.getFeatureList().mseLivePlayback && url.value) {
        player.value = mpegts.createPlayer({
            type: 'flv', // could also be mpegts, m2ts, flv
            isLive: true,
            url: url.value,
            hasAudio: false,
            hasVideo: true,
        });
        player.value.attachMediaElement(videoElement.value);
        player.value.load();
        loading.value = true;
        player.value.play();

        const option = {
            lastDecodeFrame: 0,
            count: 0,
            max_count: 30,
        };


        // 这些事件监听器主要用于监控视频播放的状态和性能，以及处理断流重连等情况。如果不需要这些功能，可以移除这些监听器
        /**
         * mpegts.Events.STATISTICS_INFO事件：
         * 用于监控视频解码帧数的变化。如果连续一段时间内解码帧数没有变化，则认为视频可能出现了断流。如果检测到断流，则会尝试重新连接。
         *
         * mpegts.Events.MEDIA_INFO
         * 用于记录媒体信息构建成功的日志。
            主要用于调试目的。
         *
         mpegts.Events.LOADING_COMPLETE
         用于记录加载完成的日志。
        主要用于调试目的。
         */
        player.value.on(mpegts.Events.STATISTICS_INFO, e => {
            if (option.lastDecodeFrame === 0) {
                option.lastDecodeFrame = e.decodedFrames;
                return;
            }
            if (option.lastDecodeFrame === e.decodedFrames) {
                option.count++;
                if (option.count > option.max_count) {
                    // 断流重连
                    destroy();
                    createVideo(videoElement.value);
                }
                else {
                    option.lastDecodeFrame = e.decodedFrames;
                    option.count = 0;
                }
            }
            if (option.count > option.max_count) {
                // 断流重连
                destroy();
                createVideo();
            }
        });

        // 构建成功的信息
        player.value.on(mpegts.Events.MEDIA_INFO, info => {
            console.log('[Mpegts 构建成功]', info);

        });

        player.value.on(mpegts.Events.LOADING_COMPLETE, () => {
            console.log('[LOADING_COMPLETE]');
            // destroy();
            // createVideo(videoElement.value);
        });
    }
};

const initVideoListener = () => {
    // videoElement.value.preLoad = true;
    videoElement.value.addEventListener('canplay', function () {
        console.log('视频已准备好播放');
        loading.value = false;
        videoElement.value.play();
    });

    videoElement.value.addEventListener('waiting', function () {
        console.log('视频正在缓冲');
        loading.value = true;
    });

    videoElement.value.addEventListener('loadstart', function () {
        loading.value = true;
        console.log('视频加载开始');
    });

    // videoElement.value.addEventListener('ended', function () {
    //     console.log('xx-ended');
    // });

    // videoElement.value.addEventListener('pause', function () {
    videoElement.value.addEventListener('seeking', function () {
        loading.value = true;
        console.log('视频正在寻找位置');
    });




    videoElement.value.addEventListener('play', function () {
        console.log('视频开始播放');
        loading.value = false;
    });

    videoElement.value.addEventListener('playing', function () {
        console.log('视频继续播放');
        loading.value = false;
    });
};

onMounted(() => {
    getData();
    // createVideo();
    initVideoListener();
});

// 在unmounted钩子中销毁
onUnmounted(async () => {
    destroy();
    await getCameraClose({cameraInfoId: closeUrl.value});

});
const reloadVideo = async () => {
    destroy(); // 销毁当前播放器
    createVideo(); // 创建新的播放器
};
watch(
    () => url.value,
    (newUrl, oldUrl) => {
        if (newUrl !== oldUrl) {
            reloadVideo(); // 当url.value变化时，重新加载视频
        }
    },
    {immediate: false}
);

watch(() => props.cameraInfoId, () => {
          getData();

      },
      {immediate: false});
</script>

<style lang="less" scoped>
.videoBox {
    width: 100%;
    height: 100%;
    pointer-events: none;
    background: #000;
    position: relative;

    video {
        width: 100%;
        height: 100%;
        position: relative;
        object-fit: cover;
        max-width: 100%;
        aspect-ratio: 16 / 9;
    }
}
</style>