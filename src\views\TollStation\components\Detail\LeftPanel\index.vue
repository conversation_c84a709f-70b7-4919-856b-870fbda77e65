<template>
    <collapse-panel :collapse="collapse" @collapse="$emit('update:collapse', !collapse)">
        <div class="panel-content">
            <div class="panel-content__left">
                <el-row :gutter="24 * ratio" class="card-row">
                    <el-col
                        v-for="card in cardList"
                        :key="card.key"
                        :span="card.span"
                    >
                        <component :is="card.component" :key="card.key"/>
                    </el-col>
                </el-row>
            </div>
            <div class="panel-content__right">
                <DutyInfoCard/>
            </div>
        </div>
    </collapse-panel>
</template>

<script setup>
import {CollapsePanel} from '@/components/Common';
import {Row as ElRow, Col as ElCol} from 'element-ui';
import {useUnit} from '@/utils';
import RevenueAnalysisCard from '../../Card/RevenueAnalysisCard/index.vue';
import TollStationInfoCard from '../../Card/TollStationInfoCard/index.vue';
import TollTrendCard from '../../Card/TollTrendCard/index.vue';
import TrafficTrendCard from '../../Card/TrafficTrendCard/index.vue';
import DutyInfoCard from '../../Card/DutyInfoCard/index.vue';

defineProps({
    collapse: Boolean,
});

const {ratio} = useUnit();

const cardList = [
    {component: RevenueAnalysisCard, key: 'RevenueAnalysisCard', span: 24},
    {component: TollStationInfoCard, key: 'TollStationInfoCard', span: 24},
    {component: TollTrendCard, key: 'TollTrendCard', span: 12},
    {component: TrafficTrendCard, key: 'TrafficTrendCard', span: 12},
];

</script>

<style lang="less" scoped>
.card-row {
    /deep/ &.el-row {
        > .el-col:not(:nth-last-child(-n+2)) {
            margin-bottom: 24px;
        }
    }
}

.panel-content {
    display: flex;
    align-items: flex-start;
    pointer-events: none;

    > * {
        pointer-events: auto;
    }

    &__left {
        width: 1232px;
    }

    &__right {
        margin-left: 24px;
    }
}
</style>