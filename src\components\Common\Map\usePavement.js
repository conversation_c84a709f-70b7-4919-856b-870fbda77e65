import {Default3DTiles, gltfLoaderEnhanced} from '@baidu/mapv-three';
import {onBeforeUnmount, shallowRef, watch} from 'vue';
import MaterialManager from './utils';

export function usePavement(map, props, emit) {

    const pavement = shallowRef();

    function addPavement() {
        const _gltfLoader = gltfLoaderEnhanced;
        //   const tilesetUrl = isOnline
        //     ? 'https://gjdt.private.gdcg.cn/twin/data/hdmap/guangdong_gcj02_flat/tiles-box-plane/top.json'
        //     : 'http://10.27.57.4:8600/data/hdmap/guangdong2w_02_flat/tiles-box-plane/top.json';
        const tiles = map.value.add(
            new Default3DTiles({
                url: import.meta.env.VITE_BMAP_URL,
                loaders: [[/\.gltf$/, _gltfLoader]],
                // loadSiblings: false,
                // displayActiveTiles: false,
            })
        );
        // tiles.position.z = -0.5;
        tiles.materialManager = new MaterialManager();

        pavement.value = tiles;

        map.value.event.bind(tiles, 'click', async e => {
            console.log('🚀 ~ e:', e);
            emit('clickMap', e);
        });
    }

    function removePavement() {
        if (!pavement.value) return;
        map.value.remove(pavement.value);
        pavement.value = null;
    }

    watch(
        map,
        v => {
            if (v && props.options?.showSatelliteMap) {
                addPavement();
            }
        },
        {
            immutable: true,
        }
    );

    onBeforeUnmount(() => {
        removePavement();
    });

    return {
        addPavement,
        removePavement,
    };
}