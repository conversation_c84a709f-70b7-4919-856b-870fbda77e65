<template>
    <div
        v-bind="$attrs"
        ref="cardRef"
        class="card"
    >
        <!-- 头部 -->
        <header class="header">
            <div class="title">
                <span class="icon-box">
                    <Icon :name="icon"/>
                </span>
                {{ title }}
                <div class="titleContent">
                    <slot name="titleContent"></slot>
                </div>
            </div>
            <div class="headerContent">
                <div
                    class="controller" :class="{'collapse': !!collapse}"
                    @click="() => collapse = !collapse"
                >
                </div>
            </div>
        </header>
        <!-- 内容区 -->
        <transition
            leave-active-class="animate__animated animate__fadeOutUp"
            enter-active-class="animate__animated animate__fadeInDown"
        >
            <div v-show="collapse" class="content">
                <slot name="content"></slot>
            </div>
        </transition>
    </div>
</template>

<script setup>
import {computed, ref} from 'vue';
import Icon from '@/components/Common/Icon/index.vue';

import useLightCard from './useLightCard.js';

const props = defineProps({
    title: {
        type: String,
        default: 'smallcard',
    },
    showLight: {
        type: Boolean,
        default: true,
        // 是否显示卡片hover特效
    },
});
const {cardRef} = useLightCard(props.showLight ? {
    light: {
        color: '#00aa00',
    },
} : null);

const collapse = ref(true);
</script>

<style scoped lang="less">

.card {
    position: relative;
    width: 300px;
    display: flex;
    flex-direction: column;
    gap: 2px;
    perspective-origin: 50% 50%;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // height: 54px;
        height: 36px;
        padding: 0 16px;
        background: url('@/assets/images/card/smallheader-bg.png') no-repeat;
        background-size: 100% 100%;

        .title {
            font-size: 20px;
            color: #fff;
            font-weight: 700;
            display: flex;
            align-items: center;
            font-family: OPlusSans 3;
            white-space: nowrap;

            .icon-box {
                width: 14px;
                height: 16px;
                background: url('@/assets/images/card/title-icon.png');
                background-size: 100% 100%;
                margin-right: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        .titleContent {
            padding-right: 16px;
        }

        .headerContent {
            .controller {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 22px;
                height: 22px;
                // border: 1px solid rgba(255, 255, 255, .3);
                background: url('@/assets/images/card/arrow.png');
                background-size: 100% 100%;
                cursor: pointer;
            }

            .collapse {
                transform: rotate(180deg);
            }
        }
    }

    .content {
        // padding-inline: 24px;
        position: relative;
        background: rgba(8, 35, 79, .8);
        box-shadow: inset 0 0 0 1px rgba(8, 74, 178, .35);
    }
}
</style>