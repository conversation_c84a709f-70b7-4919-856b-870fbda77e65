<template>
    <div id="my-select">

        <div class="search-input">
            <el-date-picker
                v-model="time"
                placeholder="选择日期" type="datetimerange"
                start-placeholder="开始日期" size="small"
                end-placeholder="结束日期"
                @change="timeChangeFn"
            />
        </div>
        <my-select
            v-model="stakeValue" :options="stakeOptions"
            placeholder="桩号区间" @change="changeStakeFn"
        />
        <my-select
            v-model="roadValue" :options="roadOptions"
            placeholder="所属路段" icon="road"
            @change="changeRoadFn"
        />
    </div>
</template>

<script>
import {ref, onMounted} from 'vue';
import MySelect from './select.vue';
import {DatePicker as ElDatePicker} from 'element-ui';
import {
    getStakeNumber, getRoadList,
} from '@/api/serviceManager/index.js';
export default {
    name: 'cascadeSelect',
    props: {
        placeholder: {
            type: String,
            default: '请选择',
        },
        value: {
            type: String,
            default: '',
        },
    },
    components: {
        MySelect,
        ElDatePicker,
    },
    model: {
        prop: 'value',
        event: 'update:value',
    },
    setup(props, {emit}) {
        const monitoring = ref('');
        const time = ref('');
        const roadValue = ref('');
        const stakeValue = ref('');
        const stakeOptions = ref('');
        const roadOptions = ref('');

        // 监控系统下拉框数组数据


        // 交调站下拉框数组数据
        const stationOptions = ref([]);

        const changeStakeFn = e => {
            emit('changeStakeFn', e);
        };
        const changeRoadFn = e => {
            emit('changeRoadFn', e);
        };
        const timeChangeFn = e => {
            emit('timeChangeFn', e);
        };

        onMounted(() => {

            getStakeNumber().then(res => {
                stakeOptions.value = res.data.map(item => ({
                    value: item,
                    label: item,
                }));
            });


            getRoadList().then(res => {
                roadOptions.value = res.data.map(item => ({
                    value: item.id,
                    label: item.name,
                }));
            });
            console.log('output->getRoadList', roadOptions.value);

        });
        return {
            monitoring,
            stationOptions,
            changeStakeFn, changeRoadFn,
            time, roadValue, stakeValue, stakeOptions, roadOptions, timeChangeFn,
        };
    },
};
</script>

<style lang="less" scoped>
    #my-select {
        display: flex;
        align-items: center;
        justify-content: end;

        > div {
            margin-left: 16px;
        }

        :deep(.el-select) {
            min-width: 164px;
        }
    }

    :deep(.el-date-editor .el-range-input) {
        color: #fff;

        .el-input__inner {
            height: 42px;
        }
    }
</style>