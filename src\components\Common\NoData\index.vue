<template>
    <div class="no-data" :style="style">
        <img
            src="@/assets/images/no-data.png"
        >
        <p>{{ message }}</p>
    </div>
</template>

<script>
import {useUnit} from '@/utils';
import {computed} from 'vue';
export default {
    props: {
        message: {
            type: String,
            default: () => '暂无信息',
        },
        width: {
            type: Number,
            default: () => null,
        },
        height: {
            type: Number,
            default: () => null,
        },
    },
    setup(props) {
        const {ratio} = useUnit();

        const style = computed(() => {
            return {
                width: props.width ? `${props.width * ratio.value}px` : '100%',
                height: props.height ? `${props.height * ratio.value}px` : '100%',
            };
        });

        return {
            style,
        };
    },
};
</script>

<style lang="less" scoped>
.no-data {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    align-content: center;
    justify-content: center;

    img {
        width: 152px;
        height: 82px;
    }

    p {
        width: 100%;
        text-align: center;
        color: #79ada4;
        font-size: 12px;
        line-height: 20px;
    }
}
</style>