<template>
    <div class="card-item">
        <div class="card-item__left">
            <div class="card-item__title">{{ info.vidiconType }}</div>
            <div class="card-item__num">
                <span>{{ info.total }}</span>
                <span>台</span>
            </div>
        </div>
        <div class="card-item__right">
            <div class="card-data-list">
                <div
                    v-for="c in config"
                    :key="c.field"
                    :class="[
                        'card-data-item',
                        {'card-data-item-col2': c.col === 2},
                    ]"
                >
                    <span>{{ c.title }}:</span>
                    <span>{{ info[c.field] }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>

defineProps({
    info: {
        type: Object,
        default: () => ({}),
    },
});

const config = [
    {
        title: '设备类型',
        field: 'deviceType',
    },
    {
        title: '在线率',
        field: 'runPercent',
    },
    {
        title: '故障数/率',
        field: 'faultPercent',
    },
    {
        title: '运行数量',
        field: 'runNum',
    },
    {
        title: '更新时间',
        field: 'time',
        col: 2,
    },
];

</script>

<style lang="less" scoped>
.card-item {
    height: 148px;
    background-color: rgba(18, 74, 166, .3);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: space-between;
    padding: 24px 24px 24px 32px;

    &__title {
        position: relative;
        height: 32px;
        line-height: 32px;
        padding: 0 8px;
        text-align: center;
        margin-left: 4.2px;
        background-image:
            linear-gradient(
                -45deg,
                rgb(36, 104, 242),
                rgba(1, 255, 229, .5),
            );

        &::before {
            content: '';
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: -4.2px;
            width: 2.2px;
            height: 32px;
            background-color: rgba(1, 255, 229, .5);
        }
    }

    &__num {
        margin-top: 16px;

        span:first-child {
            font-size: 42px;
            font-family: RoboData;
            color: #fff;
            margin-right: 2px;
        }
    }

    .card-data {
        &-list {
            width: 348px;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            font-size: 16px;
            color: rgba(#fff, .7);
        }

        &-item {
            width: calc((100% - 8px) / 2);
            height: 28px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: rgba(0, 98, 167, .3);
            padding: 0 8px;

            &:not(:nth-last-child(-n+2)) {
                margin-bottom: 8px;
            }

            &-col2 {
                width: 100%;
            }
        }
    }
}
</style>