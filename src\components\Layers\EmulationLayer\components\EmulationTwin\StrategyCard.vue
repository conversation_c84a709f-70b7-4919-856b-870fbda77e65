<template>
    <div class="strategy-card">
        <div class="title">{{ title }}</div>
        <!-- 开放应急车道 -->
        <template v-if="type === 1">
            <div class="item">
                <div>开放起始位置：</div>
                <div>{{ info.controlStake }}</div>
            </div>
            <div class="item">
                <div>管控距离：</div>
                <div>{{ info.controlLength }}m</div>
            </div>
            <div class="item">
                <div>应急车道限速值：</div>
                <div>{{ info.limitSpeed }}km/h</div>
            </div>
            <div class="item">
                <div>持续时间：</div>
                <div>{{ info.controlDuration }}分钟</div>
            </div>
        </template>
        <!-- 行车道管控 -->
        <template v-if="type === 2">
            <div class="item">
                <div>开始起始位置：</div>
                <div>{{ info.controlStake }}</div>
            </div>
            <div class="item">
                <div>管控距离：</div>
                <div>{{ info.controlLength }}m</div>
            </div>
            <div class="item">
                <div>行驶车道管控：</div>
                <div>
                    {{ laneList }}
                </div>
            </div>
            <div
                v-for="(info, index) in laneControlType"
                :key="index"
                class="item"
            >
                <div>{{ info[0] }}：</div>
                <div>
                    {{ info[1] }}
                </div>
            </div>
        </template>
        <!-- 出入口管控 -->
        <template v-if="type === 3">
            <div class="item">
                <div>出入口选择：</div>
                <div>{{ info.rampName }}</div>
            </div>
            <div class="item">
                <div>收费站上行：</div>
                <div>
                    <p v-for="(i, index) in entranceExitList" :key="index">{{ i }}</p>
                </div>
            </div>
        </template>
    </div>
</template>

<script>
import {computed} from 'vue';
import {strategy_list, vehicle_type_list} from '@EmulationLayer/config';

export default {
    props: {
        info: {
            type: Object,
            default: () => {},
        },
    },
    setup(props) {
        const {info} = props;

        const type = computed(() => info.type);
        const title = computed(() => strategy_list.find(i => i.key === type.value)?.name);

        // 行驶车道管控
        const laneList = computed(() => {
            return info?.lane?.split(',')?.map(lane => `${lane}车道`)?.toString();
        });

        // 管控详情
        const laneControlType = computed(() => {
            return info?.lane?.split(',')?.map(lane => {
                const laneControlType = info?.laneControlType?.split(',')?.[Number(lane) - 1];
                const limitSpeed = info?.limitSpeed?.split(',')?.[Number(lane) - 1];
                return [`${lane}车道`, `${+laneControlType === 1 ? `限速 ${limitSpeed}km/h` : '关闭'}`];
            });
        });

        // 收费站上行
        const entranceExitList = computed(() => {
            if (info.type !== 3 || !info.entranceExitType || info.entranceExitType === '') {
                return [];
            }

            const types = info.entranceExitType.split(',');

            return types.map(i => {
                if (Number(i) === 1) {
                    return '入口关闭';
                }

                if (Number(i) === 2) {
                    const entryConfig = info.entryConfig || {};
                    return `入口限流 持续时间${entryConfig.controlDuration || '-'}
                        分钟，入口限流量${entryConfig.flowLimit || '-'}
                        辆/小时，车辆限制
                        ${vehicle_type_list.find(i => i.code === +entryConfig.vehicleType)?.name || '-'}`;
                }

                if (Number(i) === 3) {
                    const exportConfig = info.exportConfig || {};
                    return `出口限流 持续时间${exportConfig.controlDuration || '-'}
                        分钟，绕行比例${exportConfig.bypassRate || '-'}%`;
                }
            });
        });

        return {
            type,
            title,
            entranceExitList,
            laneList,
            laneControlType,
        };
    },
};
</script>

<style lang="less" scoped>
.strategy-card {
    width: max-content;
    padding: 15px 15px 15px 30px;
    box-sizing: border-box;
    flex: 1;
    user-select: none;

    .title {
        font-family: FZLTZHJW--GB1-0;
        font-size: 14px;
        color: #e4f1ff;
        line-height: 22px;
        position: relative;

        &::before {
            position: absolute;
            left: -13px;
            top: 6px;
            content: '';
            width: 2px;
            height: 10px;
            background-color: #08d6a5;
        }
    }

    .item {
        display: flex;
        width: max-content;

        & > div {
            font-size: 12px;
            text-align: justify;
            line-height: 20px;
            padding-top: 6px;

            &:first-child {
                color: #93b4b7;
                width: max-content;
            }

            &:last-child {
                color: #cfd7f3;
                flex: 1;
            }
        }
    }
}
</style>