<template>
    <div class="meso-left">
        <div class="meso-left-item">
            <card title="轴载数据分析">
                <template #titleContent>
                    <my-date-time v-model="pickerTime"/>
                    <my-select v-model="bridgeType" :options="bridgeTypeOption"/>
                </template>
                <template #content>
                    <axleAnalysis
                        :date-time="dateTime" :bridge-type="bridgeType"
                        :bridge-type-option="bridgeTypeOption"
                    />
                </template>
            </card>
            <card class="short" title="风险结构物">
                <template #content>
                    <riskStructure/>
                </template>
            </card>
        </div>
        <div class="meso-left-item">
            <card
                title="结构物列表"
            >
                <template #content>
                    <structureList/>
                </template>
            </card>
            <card
                v-model="flowRadio" :radio-content="radioList"
                class="card-short-2" title="告警次数排名前10"
            >
                <template #titleContent>
                    <my-select v-model="alarmType" :options="alarmTypeOption"/>
                </template>
                <template #content>
                    <alarmRanking :type="flowRadio"/>
                </template>
            </card>
        </div>
    </div>
</template>

<script>
import {Card} from '@/components/Common/index';
import {MySelect, MyDateTime} from '../../../common/index';
import axleAnalysis from './axleAnalysis.vue';
import riskStructure from './riskStructure.vue';
import structureList from './structureList.vue';
import alarmRanking from './alarmRanking.vue';
import {ref, computed} from 'vue';
import dayjs from 'dayjs';

export default {
    name: '结构物中观左侧',
    components: {
        Card, MySelect,
        MyDateTime,
        axleAnalysis,
        riskStructure,
        structureList,
        alarmRanking,
    },
    setup() {
        const bridgeType = ref(null);
        const bridgeTypeOption = [
            {value: null, label: '全部'},
            {value: '1', label: '九江大桥与扩建九江大桥'},
            {value: '2', label: '北江大桥'},
            {value: '3', label: '吉利河大桥'},
            {value: '4', label: '潭州大桥'},
        ];
        const alarmType = ref('1');
        const alarmTypeOption = [
            {value: '1', label: '桥梁'},
        ];

        const flowRadio = ref('3');
        const radioList = [
            {label: '1', name: '年'},
            {label: '2', name: '季'},
            {label: '3', name: '月'},
        ];

        const pickerTime = ref('');
        const dateTime = computed(() => {
            console.log(pickerTime.value);
            return pickerTime.value ? dayjs(pickerTime.value).format('YYYY-MM-DD') : '';
        });

        return {
            bridgeType, bridgeTypeOption, pickerTime, dateTime,
            alarmType, alarmTypeOption,
            flowRadio, radioList,
        };
    },
};
</script>

<style lang="less" scoped>
.meso-left {
    width: 100%;
    height: calc(100% - 152px);

    .short :deep(.content) {
        padding: 16px 24px;
    }
    .meso-left-item {
        display: flex;

        > div:not(:last-child) {
            margin-bottom: 23px;
            &:first-child {
                margin-right: 24px;
            }
        }

        .my-select {
            margin-right: 8px;
        }

        :deep(.el-select) {
            width: 145px;
        }

        :deep(.el-date-editor) {
            width: 175px;
            margin-right: 8px;
        }
    }
}
</style>