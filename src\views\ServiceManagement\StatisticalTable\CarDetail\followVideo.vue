<template>
    <div class="VideoItem">
        <div class="video-container">
            <div v-if="showTitle" class="video-title">
                <span>{{ info.device_name }}</span>
                <span>{{ info.device_code }}</span>
            </div>
            <video-player v-if="true" :device-id="1212312"/>
        </div>
    </div>
</template>
<script>
import VideoPlayer from '@/components/Common/VideoPlayer/index.vue';
import {computed} from 'vue';
export default {
    name: 'VideoItem',
    props: {
        info: {
            type: Object,
            default: () => ({}),
        },
        plate: {
            type: String,
            default: '',
        },
        type: {
            type: String,
            default: 'real',
            validator: value => {
                return ['real', 'history'].includes(value);
            },
        },
        showTitle: {
            type: Boolean,
            default: true,
        },
    },
    components: {
        VideoPlayer,
    },
    setup(props) {
        const title = computed(() => (props.type === 'real' ? '实时视频' : '回放视频'));
        return {
            title,
        };
    },
};
</script>
<style lang="less" scoped>
.VideoItem {

    background: url('@TrafficLayer/assets/images/bg.svg') no-repeat;
    padding: 8px;
    // display: flex;
    backdrop-filter: blur(10px);

    .video-container {
        width: 100%;
        height: 100%;
        border: 1px solid #000;
        position: relative;

        .video-title {
            position: absolute;
            top: 0;
            left: 0;
            background: rgba(0, 1, 1, .5);
            width: 100%;
            height: 24px;
            z-index: 1;
            font-size: 14px;
            color: rgb(255, 255, 255);
            font-family: FZLanTingHeiS-R-GB;
            padding-inline: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
    }
}
</style>