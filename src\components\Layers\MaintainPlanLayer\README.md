# MaintainPlanLayer 养护计划管理图层

### 介绍

提供了MaintainPlanLayer图层组件，能够快速构建养护计划管理图层并提供了默认组件，以及各子组件，可使用其中的子组件进行二次开发。

### 安装教程

1. npm i apaas-maplayer

2. import {MaintainPlanLayer} from 'apaas-maplayer'

### 使用方法

> 先在外部初始化组件

```html
<template>
    <section>
      <div class="map" id="map"></div>
      {/* 此组件为整个养护计划管理图层 */}
      <MaintainPlanLayer />
    </section>
</template>

<script>
    import {MaintainPlanLayer} from 'apaas-maplayer'

    export default {
      components: {
        MaintainPlanLayer
      }
    }
</script>
```

## MaintainPlanLayer

### 注意事项

需要先调用 `initMaintainPlanLayerConfig()` 方法，初始化地图数据与接口参数，初始化操作执行完毕后再让应急事件态势分析图层渲染。示例如下：

```html
<template>
    <map-component
        ref="mapRef"
        :options="{
            center: [113.27324, 23.1579],
            showSatelliteMap: true,
        }"
        class="map-box"
        :style="{
            height: height + 'px',
        }"
        @mapLoaded="mapLoaded"
    >
        <maintain-plan-layer
            v-if="mapInitStatus"
        />
    </map-component>
</template>


<script>
import {Map, MaintainPlanLayer, initMaintainPlanLayerConfig} from 'apaas-maplayer';
import {ref, onMounted} from 'vue';

const apiHost = 'http://localhost';

export default {
    components: {
        MapComponent: Map,
        MaintainPlanLayer,
    },
    setup() {
        const mapRef = ref(null);
        const engine = ref(null);
        const height = ref(1080);
        const mapInitStatus = ref(false);
        // 地图加载完成后执行的回调函数
        const mapLoaded = () => {
            initMaintainPlanLayerConfig({
                engine: mapRef.value.map,
                apiHost,
            });
            mapInitStatus.value = true;
        };

        onMounted(() => {
            height.value = window.innerHeight;
            document.title = '养护计划管理图层';
        });

        return {
            mapRef,
            engine,
            height,
            mapInitStatus,
            mapLoaded,
            apiHost,
        };
    },
};
</script>

<style scoped>
.map-box {
    position: relative;
    width: 100vw !important;
}
</style>
```

## MaintainPlanMacroMap 宏观图层

|Prop name|Type|Default|Description|
|----------|----------|-------|-----------------------|
|`list`|`array`|`[]`|宏观扎点数据列表|
|`callback`|`function`|-|`必填`，点击扎点执行的函数，回调参数为该扎点数据|

### 宏观扎点数据格式

```javascript
[
    {
        // 扎点唯一id
        pointId: 'ABCDEFG',
        // 扎点名称
        pointName: '显示名称',
        // 扎点坐标
        position: [112, 22, 80],
        // 扎点联动区域数值
        pointValue: 10,
    }
]
```

## MaintainPlanMesoMap 中观图层

|Prop name|Type|Default|Description|
|----------|----------|-------|-----------------------|
|`list`|`array`|`[]`|中观扎点数据列表|
|`callback`|`function`|-|`必填`，点击扎点执行的函数，回调参数为该扎点数据|

### 中观扎点数据格式

```javascript
[
    {
        // 扎点唯一id
        pointId: 'ABCDEFG',
        // 扎点名称
        pointName: '显示名称',
        // 扎点坐标
        position: [112, 22, 80],
        // 扎点联动区域数值
        pointValue: 10,
    }
]
```
## MaintainPlanMicro2dMap 微观二维图层
|Prop name|Type|Default|Description|
|---|---|---|---|
|`list`|`array`|`[]`|微观二维数据列表|

### 微观二维数据格式

```javascript
[
    { 
        // 高速名称
        sectionName: "包茂高速",
        // 高速id
        sectionId: "G0065440010",
        // 高速路坐标集合，三维数组
        pointList: [
            [
                [
                    110.98523950360105,
                    22.63648047354496,
                    188.46439633333102
                ],
                [
                    110.98518010951707,
                    22.63611114514359,
                    189.48013727902435
                ],
                [
                    110.98490965613547,
                    22.635000306762702,
                    192.21364111367984
                ],
            ],
            [
                [
                    111.0324003446935,
                    21.618999979232967,
                    10.479862600119304
                ],
                [
                    111.03185943675737,
                    21.62038044285686,
                    12.061015347926514
                ],
                [
                    111.03169974282659,
                    21.62086061891917,
                    12.545071612645188
                ]
            ]
        ],
        // 该高速下的养护计划列表
        planList: [
            {
                // 养护计划扎点经纬度
                position: [
                    110.964103,
                    22.313127,
                    92.340862001052301
                ],
                // 养护计划id
                planId: "1",
                // 养护计划类型 1-路面 2-路基 3-桥梁 4-隧道 5-交安设施
                type: "1",
                // 是否为告警扎点
                warning: false
            },
            ...
        ]
    },
    ...
]
```

## MaintainPlanMicro3dMap 微观三维图层
|Prop name|Type|Default|Description|
|---|---|---|---|
|`type`|`String`|`"PLAN"`|可选值`"EMULATION"`,`"PLAN"`-显示计划信息，`"EMULATION"`-显示仿真|
|`info`|`Object`|`{}`|微观三维数据|

### 微观三维数据格式

```javascript
{
    //养护计划id
    planId: "1",
    // 养护计划扎点经纬度
    position: [
        110.964103,
        22.313127,
        92.340862001052301
    ],
    // 养护计划类型 1-路面 2-路基 3-桥梁 4-隧道 5-交安设施
    type: "1",
    // 是否为告警扎点
    warning: false,
    // 高速id
    sectionId: "G0065440010",
    // 养护计划流程集合
    flowList: [
        {   
            // 操作人
            operator: "操作人",
            // 审核时间
            auditTime: "2024-02-26 17:09:51",
            // 审核状态 0-待审核 1-审核通过 2-未通过
            auditStatus: 0,
            // 执行时间
            executeTime: "2024-02-26 17:09:51",
            // 执行状态 0-未执行 1-进行中 2-已执行
            executeStatus: 0,
            // 封闭车道
            closeLane: "2车道",
            // 是否执行计划 0-否 1-是
            executePlan: 0,
            // 计划结束时间
            planEndTime: "2024-02-26 17:09:51",
            // 养护计划类型 1-路面 2-路基 3-桥梁 4-隧道 5-交安设施
            maintainType: "1",
            // 计划开始时间
            planStartTime: "2024-02-22 17:09:48",
            // 调整时间
            adjustTime: "2024-02-22 17:00:11",
            // 创建时间
            createTime: "2024-02-22 17:00:11",
            // 计划流程状态，1-创建，2-调整，3-审核不通过，4-审核通过 5-计划执行中
            status: 1,
            // 养护计划施工路段
            maintainRoadName: "包茂高速 北向南 K2919+000至K2924+400"
        },
        ...
    ]
}
```


### 事件
|事件名|含义|返回参数|
|---|---|---|
|`emulation`|点击仿真按钮回调|计划信息|

## MaintainPlanPlanFlow 养护计划信息卡片组件
|Prop name|Type|Default|Description|
|---|---|---|---|
|`list`|`array`|`[]`养护计划信息数据列表|

### 养护计划信息数据格式
```javascript
[
    {   
        // 操作人
        operator: "操作人",
        // 审核时间
        auditTime: "2024-02-26 17:09:51",
        // 审核状态 0-待审核 1-审核通过 2-未通过
        auditStatus: 0,
        // 执行时间
        executeTime: "2024-02-26 17:09:51",
        // 执行状态 0-未执行 1-进行中 2-已执行
        executeStatus: 0,
        // 封闭车道
        closeLane: "2车道",
        // 是否执行计划 0-否 1-是
        executePlan: 0,
        // 计划结束时间
        planEndTime: "2024-02-26 17:09:51",
        // 养护计划类型 1-路面 2-路基 3-桥梁 4-隧道 5-交安设施
        maintainType: "1",
        // 计划开始时间
        planStartTime: "2024-02-22 17:09:48",
        // 调整时间
        adjustTime: "2024-02-22 17:00:11",
        // 创建时间
        createTime: "2024-02-22 17:00:11",
        // 计划流程状态，1-创建，2-调整，3-审核不通过，4-审核通过 5-计划执行中
        status: 1,
        // 养护计划施工路段
        maintainRoadName: "包茂高速 北向南 K2919+000至K2924+400"
    },
    ...
]
```

