import useCancelRequest from './useCancelRequest';

export default function useCancelRepeatRequest(request) {
    const map = new Map();
    const newRequest = useCancelRequest(request);
    return (url, params, config) => {
        // 取消请求
        if (map.has(url)) {
            map.get(url)();
        }
        const [data, cancel] = newRequest(url, params, config);
        const result = data.finally(() => {
            // 请求完成后清除
            map.delete(url);
        });
        map.set(url, cancel);
        return [
            result,
            cancel,
        ];
    };
}