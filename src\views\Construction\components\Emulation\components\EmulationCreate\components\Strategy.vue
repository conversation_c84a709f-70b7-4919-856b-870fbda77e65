<template>
    <div class="strategy-config">
        <el-form
            class="emulation-form"
            size="small"
            :label-width="labelWidth"
        >
            <!-- 策略输入方式 -->
            <el-form-item
                label="策略输入方式："
            >
                <el-radio-group
                    v-model="strategyInfo.strategyInputType"
                >
                    <el-radio
                        v-for="item in strategyInputOptions"
                        :key="item.value"
                        :label="item.value"
                    >
                        {{ item.label }}
                    </el-radio>
                </el-radio-group>
            </el-form-item>
            <!-- 策略输入方式-手动输入时显示 -->
            <template
                v-if="strategyInfo.strategyInputType === 2"
            >
                <el-form-item
                    label="策略选择："
                >
                    <el-checkbox-group v-model="strategyInfo.strategyType">
                        <el-checkbox
                            v-for="item in strategyOptions"
                            :key="item.value"
                            :label="item.value"
                        >
                            {{ item.label }}
                        </el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <div
                    class="emulation-form__sub"
                >
                    <!-- 开放应急车道 -->
                    <template v-if="strategyInfo.strategyType.includes(1)">
                        <div class="emulation-form__title">开放应急车道</div>
                        <div class="emulation-form__sub-item">
                            <el-form-item label="开放起始位置：">
                                <stake-select
                                    v-model="strategyInfo.emergencyConfig.controlStake"
                                    default-selected-middle
                                    :high-speed-name="highSpeedName"
                                    @change="handleChangeControlStake($event, 'emergencyConfig')"
                                />
                            </el-form-item>
                            <el-form-item label="管控距离：">
                                <el-input
                                    v-model="strategyInfo.emergencyConfig.controlLength"
                                    type="number"
                                    class="full-content"
                                    placeholder="请输入管控距离"
                                >
                                    <template #suffix>m</template>
                                </el-input>
                            </el-form-item>
                            <el-form-item label="应急车道限速值：">
                                <el-input
                                    v-model="strategyInfo.emergencyConfig.limitSpeed"
                                    type="number"
                                    class="full-content"
                                    placeholder="请输入应急车道限速值"
                                >
                                    <template #suffix>km/h</template>
                                </el-input>
                            </el-form-item>
                            <el-form-item label="持续时间：">
                                <el-input
                                    v-model="strategyInfo.emergencyConfig.controlDuration"
                                    type="number"
                                    class="full-content"
                                    placeholder="请输入持续时间"
                                >
                                    <template #suffix>分钟</template>
                                </el-input>
                            </el-form-item>
                        </div>
                    </template>
                    <!-- 行车道管控 -->
                    <template v-if="strategyInfo.strategyType.includes(2)">
                        <div class="emulation-form__title">行车道管控</div>
                        <div class="emulation-form__sub-item">
                            <el-form-item label="开放起始位置：">
                                <stake-select
                                    v-model="strategyInfo.laneControlConfig.controlStake"
                                    default-selected-middle
                                    :high-speed-name="highSpeedName"
                                    @change="handleChangeControlStake($event, 'laneControlConfig')"
                                />
                            </el-form-item>
                            <el-form-item label="管控距离：">
                                <el-input
                                    v-model="strategyInfo.laneControlConfig.controlLength"
                                    type="number"
                                    class="full-content"
                                    placeholder="请输入管控距离"
                                >
                                    <template #suffix>m</template>
                                </el-input>
                            </el-form-item>
                            <el-form-item label="行驶车道管控：">
                                <el-checkbox-group v-model="selectedLane">
                                    <el-checkbox
                                        v-for="lane in laneOptions"
                                        :key="lane.value"
                                        :label="lane.value"
                                        @change="handleSelectLane($event, lane.value)"
                                    >
                                        {{ lane.label }}
                                    </el-checkbox>
                                </el-checkbox-group>
                            </el-form-item>
                            <!-- 车道配置 -->
                            <el-form-item
                                v-for="item in strategyInfo.laneControlConfig.configList"
                                :key="`LANE_CONFIG__${item}`"
                                :label="item.lane === 99 ? '应急车道' : `${item.lane}车道`"
                            >
                                <el-radio-group v-model="item.laneControlType">
                                    <el-radio :label="1">限速</el-radio>
                                    <el-radio :label="0">关闭</el-radio>
                                </el-radio-group>
                                <el-input
                                    v-show="item.laneControlType === 1"
                                    v-model="item.limitSpeed"
                                    type="number"
                                    placeholder="请输入车道限速值"
                                >
                                    <template #suffix>km/h</template>
                                </el-input>
                            </el-form-item>
                        </div>
                    </template>
                    <!-- 出入口管控 -->
                    <template v-if="strategyInfo.strategyType.includes(3)">
                        <div class="emulation-form__title">出入口管控</div>
                        <div class="emulation-form__sub-item">
                            <el-form-item label="出入口选择：">
                                <api-select
                                    v-model="strategyInfo.entryAndExitConfig.rampName"
                                    default-selected-first
                                    :droup-down-fetch="false"
                                    :api="getTollList"
                                    :params="{
                                        highSpeedName,
                                        emulationStartStake,
                                        emulationEndStake,
                                    }"
                                    label-field="tollName"
                                    value-field="tollName"
                                />
                            </el-form-item>
                            <el-form-item
                                :label="`${strategyInfo.strategyType.rampName || ''}收费站 ${direction}：`"
                            >
                                <el-checkbox-group v-model="selectedTollConfig">
                                    <div
                                        v-for="tollConfig in tollConfigOptions"
                                        :key="tollConfig.value"
                                        style="display: flex; align-items: center;"
                                    >
                                        <el-checkbox
                                            :label="tollConfig.value"
                                            @change="handleSelectTollConfig($event, tollConfig.value)"
                                        >
                                            {{ tollConfig.label }}
                                        </el-checkbox>
                                        <div
                                            v-if="tollConfig.config"
                                            class="btn-config"
                                            @click="handleClickTollConfig(tollConfig.value)"
                                        >
                                            配置
                                        </div>
                                    </div>
                                </el-checkbox-group>
                            </el-form-item>
                        </div>
                    </template>
                </div>
            </template>
        </el-form>

        <!-- 入口限流、出口分流配置弹窗 -->
        <el-dialog
            :visible.sync="dialogVisible"
            class="config-dialog"
            :title="getDialogTitle"
            :modal-append-to-body="false"
        >
            <el-form
                class="emulation-form"
                size="small"
                label-width="0.9rem"
            >
                <el-form-item label="持续时间：">
                    <el-input
                        v-model="dialogInfo.controlDuration"
                        type="number"
                        class="full-content"
                        placeholder="请输入持续时间"
                    >
                        <template #suffix>分钟</template>
                    </el-input>
                </el-form-item>
                <!-- 入口限流配置 -->
                <template v-if="dialogType === 2">
                    <el-form-item label="入口限流量：">
                        <el-input
                            v-model="dialogInfo.flowLimit"
                            type="number"
                            class="full-content"
                            placeholder="请输入入口限流量"
                        >
                            <template #suffix>辆/小时</template>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="车辆限制：">
                        <api-select
                            v-model="dialogInfo.vehicleType"
                            :options="vehicleTypeOptions"
                            placeholder="请选择"
                            class="full-content"
                        />
                    </el-form-item>
                </template>
                <!-- 出口分流配置  -->
                <template v-else>
                    <el-form-item label="绕行比例：">
                        <el-input
                            v-model="dialogInfo.bypassRate"
                            type="number"
                            class="full-content"
                            placeholder="请输入绕行比例"
                        >
                            <template #suffix>%</template>
                        </el-input>
                    </el-form-item>
                </template>
            </el-form>
            <template #footer>
                <div class="config-dialog__footer">
                    <div @click="dialogVisible = false">取消</div>
                    <div @click="handleSaveConfig">保存</div>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import {
    Form,
    FormItem,
    Radio,
    RadioGroup,
    Input,
    Checkbox,
    CheckboxGroup,
    Select,
    Option,
    Dialog,
} from 'element-ui';
import ApiSelect from '@/components/Common/Form/ApiSelect.vue';
import {computed, ref, watch} from 'vue';
import {useUnit} from '@/utils';
import {
    strategyInputOptions,
    strategyOptions,
    laneOptions,
    tollConfigOptions,
    vehicleTypeOptions,
} from '../../../config';
import StakeSelect from './StakeSelect.vue';
import {isEqual, cloneDeep} from 'lodash';
import {getTollList} from '@/api/emulation';
import {strategyDefaultInfo} from '../config';

export default {
    components: {
        [Form.name]: Form,
        [FormItem.name]: FormItem,
        [Input.name]: Input,
        [Radio.name]: Radio,
        [RadioGroup.name]: RadioGroup,
        [Checkbox.name]: Checkbox,
        [CheckboxGroup.name]: CheckboxGroup,
        StakeSelect,
        [Select.name]: Select,
        [Option.name]: Option,
        [Dialog.name]: Dialog,
        ApiSelect,
    },
    props: {
        info: {
            type: Object,
            default: () => ({}),
        },
        highSpeedName: String,
        emulationStartStake: String,
        emulationEndStake: String,
        direction: String,
    },
    setup(props, {emit}) {
        const {ratio} = useUnit();
        // 缓存策略数据

        // 策略信息
        const strategyInfo = ref(cloneDeep(strategyDefaultInfo));
        const selectedLane = ref([]);
        const selectedTollConfig = ref([]);
        // 弹窗显隐状态
        const dialogVisible = ref(false);
        // 当天弹窗配置类型 2:入口限流 3:出口分流
        const dialogType = ref(2);
        // 弹窗配置信息
        const dialogInfo = ref({
            controlDuration: '',
            flowLimit: '',
            vehicleType: '',
            bypassRate: '',
        });
        const getDialogTitle = computed(() => (dialogType.value === 2 ? '入口限流配置' : '出口分流配置'));
        const labelWidth = computed(() => `${160 * ratio.value}px`);

        function handleChangeControlStake({position}, key) {
            strategyInfo.value[key].position = position?.join(',');
        }

        function handleSelectLane(flag, lane) {
            // 取消选中
            if (!flag) {
                const targetIndex = strategyInfo.value
                    .laneControlConfig.configList.findIndex(item => item.lane === lane);
                strategyInfo.value
                    .laneControlConfig.configList.splice(targetIndex, 1);
            }
            // 选中
            else {
                strategyInfo.value
                    .laneControlConfig.configList.push({
                        lane,
                        laneControlType: 0,
                        limitSpeed: '',
                    });
            }
        }

        function handleSelectTollConfig(flag, configType) {
            // 取消选中
            if (!flag) {
                const targetIndex = strategyInfo.value
                    .entryAndExitConfig.configList.findIndex(item => item.entranceExitType === configType);
                strategyInfo.value
                    .entryAndExitConfig.configList.splice(targetIndex, 1);
            }
            // 选中
            else {
                strategyInfo.value
                    .entryAndExitConfig.configList.push({
                        controlDuration: '',
                        flowLimit: '',
                        vehicleType: '',
                        bypassRate: '',
                        entranceExitType: configType,
                    });
            }
        }

        function handleClickTollConfig(type) {
            dialogType.value = type;
            dialogVisible.value = true;
            const targetConfig = strategyInfo.value
                .entryAndExitConfig.configList.find(item => item.entranceExitType === type) || {};
            dialogInfo.value = {
                controlDuration: '',
                flowLimit: '',
                vehicleType: '',
                bypassRate: '',
                entranceExitType: type,
                ...targetConfig,
            };
        }

        function handleSaveConfig() {
            const type = dialogType.value;
            const targetIndex = strategyInfo.value.entryAndExitConfig
                .configList.findIndex(item => item.entranceExitType === type);
            if (targetIndex >= 0) {
                strategyInfo.value.entryAndExitConfig
                    .configList[targetIndex] = dialogInfo.value;
            }
            else {
                strategyInfo.value.entryAndExitConfig
                    .configList.push(dialogInfo.value);
                selectedTollConfig.value.push(dialogInfo.value.entranceExitType);
            }
            dialogVisible.value = false;
        }

        // 监听外部信息变化，修改表单信息
        watch(
            () => props.info,
            (newVal, oldVal) => {
                // 值相等不处理
                if (isEqual(newVal, oldVal)) return;
                strategyInfo.value = newVal;
                selectedLane.value = strategyInfo.value.laneControlConfig
                    .configList.map(item => item.lane);
                selectedTollConfig.value = strategyInfo.value.entryAndExitConfig
                    .configList.map(item => item.entranceExitType);
            },
            {
                deep: true,
                immediate: true,
            }
        );

        // 监听表单信息变化，修改外部信息
        watch(
            () => strategyInfo.value,
            (newVal, oldVal) => {
                // 值相等不处理
                if (isEqual(newVal, oldVal)) return;
                emit('update:info', strategyInfo.value);
            },
            {
                deep: true,
                immediate: true,
            }
        );

        return {
            labelWidth,
            strategyInfo,
            strategyInputOptions,
            strategyOptions,
            handleChangeControlStake,
            laneOptions,
            handleSelectLane,
            getTollList,
            selectedTollConfig,
            tollConfigOptions,
            handleSelectTollConfig,
            handleClickTollConfig,
            dialogVisible,
            dialogType,
            getDialogTitle,
            dialogInfo,
            vehicleTypeOptions,
            handleSaveConfig,
            selectedLane,
        };
    },
};
</script>

<style lang="less" scoped>
.emulation {
    &-form {
        &__title {
            position: relative;
            margin-bottom: 18px;
            padding-left: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 20px;

            &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                margin-top: -9px;
                width: 3px;
                height: 18px;
                background-color: rgba(26, 228, 255, .9);
            }
        }

        &__sub {
            margin-bottom: 32px;

            &-item {
                padding-left: 12px;
            }
        }

        /deep/ .el-form {
            &-item {
                margin-bottom: 24px;

                &__label {
                    text-align: left;
                }
            }
        }

        /deep/ .el-radio {
            &:not(:last-of-type) {
                margin-right: 18px;
            }

            &__label {
                padding-left: 4px;
            }
        }

        /deep/ .el-input__inner {
            &[type="number"] {
                &::-webkit-inner-spin-button,
                &::-webkit-outer-spin-button {
                    -webkit-appearance: none;
                    margin: 0;
                }
            }
        }

        .full-content {
            width: 100%;
        }

        .btn-config {
            color: #28c282;
            cursor: pointer;
            margin-left: 4px;
            font-size: 14px;
        }
    }
}

.config-dialog {
    /deep/ .el-dialog {
        width: 400px;
        background-color: rgba(#2c2c2c, .8);
        backdrop-filter: blur(4px);

        &__header {
            display: flex;
            backdrop-filter: blur(2px);
            background-color: rgba(#fff, .05);

            &btn:hover .el-dialog__close {
                color: #fff;
            }
        }

        &__title {
            color: #fff;
        }
    }

    &__footer {
        display: flex;
        align-items: center;
        justify-content: center;

        div {
            padding: 8px 20px;
            text-align: center;
            border: 1px solid rgba(#fff, .2);
            cursor: pointer;
            font-weight: 500;

            &:active {
                opacity: .85;
            }

            &:last-child {
                border: none;
                color: #000;
                background-color: rgba(26, 228, 255, .9);
                margin-left: 24px;
            }
        }
    }
}
</style>