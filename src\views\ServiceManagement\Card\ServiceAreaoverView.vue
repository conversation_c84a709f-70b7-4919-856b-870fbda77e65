<template>
    <card
        title="服务区概览" class="service-list"
        :show-light="false"
    >
        <template #content>
            <div v-if="data.length" class="icon-list">
                <item
                    v-for="item in data" :key="item"
                    :info="item"
                />
            </div>
            <NoData v-else/>
        </template>

    </card>
</template>

<script setup lang="ts">
import Card from '@/components/Common/Card/smallCard.vue';
import {NoData} from '@/components/Common/index';

import {useUnit} from '@/utils';
import Item from './ServiceAreaOverviewItem.vue';
import {ref, onMounted, watch} from 'vue';
const {ratio} = useUnit();
const props = defineProps({
    data: {
        type: Array,
        default: () => ([]),
    },

});
watch(() => props.data, newVal => {
    console.log('as--------', newVal);
});
</script>
<style lang="less" scoped>


.icon-list {
    overflow-y: auto;
    max-height: 700px;
    padding: 14px;
}

</style>