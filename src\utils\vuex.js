// 普通vuex更新好书
export const createUpdateState = () => {
    return function (state, data) {
        Object.keys(data).forEach(key => {
            const value = data[key];
            if (Object.hasOwn(state, key)) {
                return Reflect.set(state, key, value);
            }
            for (const subState of Object.values(state)) {
                if (Object.hasOwn(subState, key)) {
                    return Reflect.set(subState, key, value);
                }
            }
            return Reflect.set(state, key, value);
        });
    };
};

//

export const initGetters = module => {
    const getters = module.getters ?? (module.getters = {});
    const state = module.state;

    Object.keys(state).forEach(key => {
        if (getters[key]) {
            return;
        };

        getters[key] = function (state) {
            return state[key];
        };
    });
    return module;
};
