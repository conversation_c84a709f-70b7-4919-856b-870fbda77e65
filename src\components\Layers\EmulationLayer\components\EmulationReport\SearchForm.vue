<template>
    <div class="search-form">
        <el-time-select
            v-model="searchInfo.startTime"
            style="width: 48%;"
            class="h-40"
            popper-class="emulation-el-popper"
            placeholder="起始时间"
            :picker-options="{
                start: emulationTime.startTime,
                step: '0:01',
                end: emulationTime.endTime,
            }"
            @change="timeChange"
        />
        <el-time-select
            v-model="searchInfo.endTime"
            style="width: 48%;"
            class="h-40"
            placeholder="结束时间"
            popper-class="emulation-el-popper"
            :picker-options="{
                start: emulationTime.startTime,
                step: '00:01',
                end: emulationTime.endTime,
                minTime: searchInfo.startTime,
            }"
            @change="timeChange"
        />
        <!-- <el-select
            v-model="searchInfo.sectionId"
            placeholder="请选择路段"
            style="width: 30%; height: 40px;"
            clearable
        >
            <el-option
                v-for="item in roadList"
                :key="item.code"
                :label="item.name"
                :value="item.code"
            />
        </el-select> -->

    </div>
</template>

<script>
import {TimeSelect, Option, Select} from 'element-ui';
export default {
    props: {
        emulationTime: {
            type: Object,
            default: () => {},
        },
        type: {
            type: String,
        },

    },
    components: {
        [TimeSelect.name]: TimeSelect,
        [Option.name]: Option,
        [Select.name]: Select,
    },
    watch: {
        emulationTime: {
            handler(val) {
                console.log(val);
                // this.searchInfo.startTime = val.startTime;
                // this.searchInfo.endTime = val.endTime;
            },
            // deep: true,
            immediate: true,
        },

    },
    data() {
        return {
            roadList: [],
            searchInfo: {},
        };
    },
    components: {
        [TimeSelect.name]: TimeSelect,
        [Select.name]: Select,
        [Option.name]: Option,
    },
    methods: {
        timeChange() {
            // console.log(typeof val);
            this.$forceUpdate();
            this.$emit('search', {
                evaluateType: this.type,
                ...this.searchInfo,
            });
        },

    },
    mounted() {
        // this.searchInfo.startTime = this.emulationTime.startTime;
        // this.searchInfo.endTime = this.emulationTime.endTime;
    },

};
</script>