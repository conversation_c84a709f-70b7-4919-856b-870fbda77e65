// 设备设施设备管理接口
import {request} from '@/utils/network-helper/index';
import {baseConfig} from '@/utils/common';
const {basePrefix} = baseConfig;

const baseStructure = `${basePrefix}/v1`;

// 2.1、设备总览静态数据展示
export const getDeviceOverView = async () => {
    const {data, code} = await request.get(
        baseStructure + '/deviceShow/getDeviceOverView'
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 2.2、正常运行设备数量
export const getDeviceNormal = async deviceType => {
    const {data, code} = await request.get(
        baseStructure + '/deviceShow/getDeviceNormal',
        {
            deviceType,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 2.3、设备空间分布
export const getDeviceSpace = async deviceType => {
    const {data, code} = await request.get(
        baseStructure + '/deviceShow/getDeviceSpace',
        {
            deviceType,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 2.4、故障设备分布
export const getDeviceFault = async deviceType => {
    const {data, code} = await request.get(
        baseStructure + '/deviceShow/getDeviceFault',
        {
            deviceType,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 2.5、设备健康指数-设备桩号下拉框选项
export const deviceStakeOption = async () => {
    const {data, code} = await request.get(
        baseStructure + '/deviceShow/deviceStakeOption'
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 2.6、设备健康指数-设备品牌下拉框选项
export const deviceBrandOption = async () => {
    const {data, code} = await request.get(
        baseStructure + '/deviceShow/deviceBrandOption'
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 2.7、设备健康指数列表查询
export const getDeviceHealthList = async params => {
    const {data, code} = await request.post(
        baseStructure + '/deviceShow/getDeviceHealthList',
        params
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 2.8、设备故障工单列表查询
export const getDeviceFaultList = async params => {
    const {data, code} = await request.post(
        baseStructure + '/deviceShow/getDeviceFaultList',
        params
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 2.9、导出设备健康指数列表
export const exportHealthExcel = async params => {
    const data = await request.post(
        baseStructure + '/deviceShow/exportHealthExcel',
        params,
        {
            exportFile: true,
            responseType: 'blob',
        }
    );
    if (data) {
        return data;
    }
    return {};
};

// 2.10、导出故障工单列表
export const exportFaultExcel = async params => {
    const data = await request.post(
        baseStructure + '/deviceShow/exportFaultExcel',
        params,
        {
            exportFile: true,
            responseType: 'blob',
        }
    );
    if (data) {
        return data;
    }
    return {};
};