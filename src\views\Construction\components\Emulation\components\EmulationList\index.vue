<template>
    <div class="emulation-list-wrapper">
        <div class="emulation-list__header">
            <div class="emulation-list__title">交通仿真方案列表</div>
            <div class="ct">
                <div class="emulation-btn-add" @click="handleCreate">新增</div>
                <span
                    :class="[
                        'emulation-create-close',
                        {show: showScheme},
                    ]"
                    @click="toggle"
                >
                    <i class="el-icon-caret-bottom"></i>
                </span>
            </div>
        </div>
        <el-collapse-transition>
            <div v-show="showScheme" class="emulation-list__main">
                <div class="emulation-list__body show-scroll">
                    <no-data v-if="isEmpty"/>
                    <div
                        v-for="item in schemeList"
                        :key="item"
                        class="emulation-item"
                    >
                        <div class="emulation-item__left">
                            <div>
                                <!-- 名称 -->
                                <div class="emulation-name" :title="item.name">{{ item.name }}</div>
                                <!-- 时间 -->
                                <div class="emulation-time">{{ item.emulationStartTime }}</div>
                            </div>
                            <!-- 状态 -->
                            <div
                                :class="[
                                    'emulation-tag',
                                    `${emulationStatusMap[item.status].className}`,
                                ]"
                            >
                                {{ emulationStatusMap[item.status].text }}
                            </div>
                            <!-- 查看仿真报表 -->
                            <i
                                class="emulation-document el-icon-document"
                                title="查看仿真报表"
                                @click="handleReport(item)"
                            >
                            </i>
                        </div>
                        <div class="emulation-item__right">
                            <button
                                v-if="item.status === 1"
                                type="button"
                                class="emulation-btn"
                                @click="handleRun(item)"
                            >
                                运行
                            </button>
                            <button
                                v-else
                                type="button"
                                :disabled="item.status === 2"
                                :class="['emulation-btn']"
                                @click="handleView(item)"
                            >
                                查看
                            </button>
                            <button
                                type="button"
                                :disabled="[2, 3].includes(item.status)"
                                class="emulation-btn"
                                @click="handleEdit(item)"
                            >
                                修改
                            </button>
                            <button
                                type="button"
                                class="emulation-btn"
                                @click="handleCopy(item)"
                            >
                                复制
                            </button>
                            <el-popconfirm
                                icon="el-icon-info"
                                icon-color="red"
                                title="确定删除该仿真方案?"
                                @onConfirm="handleDelete(item)"
                            >
                                <button
                                    slot="reference"
                                    type="button"
                                    class="emulation-btn emulation-btn-del"
                                >
                                    删除
                                </button>
                            </el-popconfirm>
                        </div>
                    </div>
                </div>
                <div v-if="total" class="emulation-list__footer">
                    <div>共搜索到{{ total }}条数据</div>
                    <div class="emulation-pagination">
                        <!-- 上一页 -->
                        <i
                            :class="[
                                'el-icon-arrow-left',
                                {
                                    'emulation-btn-disabled': pageNo === 1,
                                },
                            ]"
                            @click="handlePrevPage"
                        >
                        </i>
                        <span>{{ pageNo }}</span>
                        <span>/</span>
                        <span>{{ pageTotal }}</span>
                        <!-- 下一页 -->
                        <i
                            :class="[
                                'el-icon-arrow-right',
                                {
                                    'emulation-btn-disabled': pageNo === pageTotal,
                                },
                            ]"
                            @click="handleNextPage"
                        >
                        </i>
                    </div>
                </div>
            </div>
        </el-collapse-transition>

        <emulation-report
            :scheme-id="emulationReport.schemeId"
            :visible.sync="emulationReport.visible"
            :emulation-time="emulationReport.emulationTime"
        />
    </div>
</template>

<script>
import {NoData} from '@/components/Common';
import {Popconfirm, Message} from 'element-ui';
import {computed, ref, watch} from 'vue';
import {emulationStatusMap} from '../../config';
import {getSchemeList, copyScheme, deleteScheme} from '@/api/emulation';
import EmulationReport from '../EmulationReport/index.vue';
import dayjs from 'dayjs';
import CollapseTransition from 'element-ui/lib/transitions/collapse-transition';
import {showScheme} from '../..';

export default {
    components: {
        NoData,
        [Popconfirm.name]: Popconfirm,
        EmulationReport,
        [CollapseTransition.name]: CollapseTransition,
    },
    props: {
        emulationType: {
            type: Number,
            default: 3,
        },
        emulationPlatform: {
            type: String,
            default: 'emulation',
        },
    },
    setup(props, {emit}) {

        const pageNo = ref(1);
        const pageSize = 10;
        const pageTotal = ref(0);
        const total = ref(0);
        const schemeList = ref(null);
        const collapse = ref(false);
        const emulationReport = ref({
            schemeId: null,
            visible: false,
            emulationTime: null,
        });

        const isEmpty = computed(() => schemeList.value && schemeList.value.length === 0);

        async function fetchSchemeList() {
            const {data} = await getSchemeList({
                pageNo: pageNo.value,
                mapLayer: props.emulationPlatform,
                emulationType: props.emulationType,
                pageSize,
            });
            total.value = data.total;
            schemeList.value = data.items;
            pageTotal.value = Math.ceil(data.total / pageSize);
        }

        watch(
            () => pageNo.value,
            () => {
                fetchSchemeList();
            },
            {
                immediate: true,
            }
        );

        function handlePrevPage() {
            if (pageNo.value === 1) return;
            pageNo.value--;
        }

        function handleNextPage() {
            if (pageNo.value === pageTotal.value) return;
            pageNo.value++;
        }

        function handleCreate() {
            emit('create');
        }
        function handleView(e) {
            emit('view', e);
        }

        function handleRun(e) {
            emit('run', e);
        }

        function handleEdit(e) {
            emit('edit', e);
        }

        async function handleCopy({id}) {
            await copyScheme(id);
            await fetchSchemeList();
            Message.success('复制成功');
        }

        async function handleDelete({id}) {
            await deleteScheme(id);
            await fetchSchemeList();
            Message.success('删除成功');
        }

        function handleReport(e) {
            const {id, emulationStartTime, emulationEndTime, status} = e;
            if (status === 1) return Message.warning('仿真方案未进行仿真，不存在仿真评价!');
            if (status === 2) return Message.warning('仿真运行中，请结束后查看仿真评价!');
            emulationReport.value = {
                schemeId: id,
                visible: true,
                emulationTime: {
                    startTime: dayjs(emulationStartTime).format('HH:mm'),
                    endTime: dayjs(emulationEndTime).format('HH:mm'),
                },
            };
        }

        // 切换折叠
        function toggle() {
            showScheme.value = !showScheme.value;
        }


        return {
            handleCreate,
            handleRun,
            handleView,
            handleEdit,
            handleCopy,
            handleDelete,
            handleReport,
            total,
            schemeList,
            pageTotal,
            pageNo,
            emulationStatusMap,
            handlePrevPage,
            handleNextPage,
            isEmpty,
            emulationReport,
            showScheme,
            toggle,
        };
    },
};
</script>

<style lang="less" scoped>
.emulation {
    &-list {
        &-wrapper {
            width: 508px;
            backdrop-filter: blur(4px);
        }

        &__header {
            flex-shrink: 0;
            height: 36px;
            padding: 0 10px 0 42px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-image: url('@/assets/images/base/modal-head-bg.svg');
            background-size: 100% 100%;
            font-size: 22px;
        }

        &__main {
            height: 800px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        &__body {
            margin-top: 4px;
            width: 100%;
            flex: 1;
            overflow-y: scroll;
            background-color: rgba(3, 31, 68, .5);
            backdrop-filter: blur(8px);
            border: 1px	solid rgba(72, 100, 146, .35);
            border-bottom-color: rgb(41, 86, 157);
        }

        &__footer {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            background-color: rgba(3, 31, 68, .65);
            backdrop-filter: blur(20px);
        }
    }

    &-item {
        position: relative;
        padding: 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: rgba(#fff, .8);
        font-size: 18px;

        &:hover {
            background-image:
                linear-gradient(
                    to right,
                    rgba(36, 104, 242, .6),
                    rgba(1, 255, 229, .6)
                );
        }

        &::before {
            content: '';
            position: absolute;
            bottom: 1px;
            left: 50%;
            transform: translateX(-50%);
            width: calc(100% - 32px);
            height: 1px;
            background-color: rgba(#fff, .1);
        }

        &__left {
            display: flex;
            align-items: center;
        }

        &__right {
            display: flex;
        }
    }

    &-name {
        width: 152px;
        height: 24px;
        line-height: 24px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        cursor: default;
        font-size: 18px;
    }

    &-time {
        margin-top: 8px;
        font-size: 16px;
    }

    &-tag {
        margin-left: 16px;
        width: 58px;
        line-height: 22px;
        text-align: center;
        border-radius: 4px;
        font-size: 16px;

        &-gray {
            background-color: rgba(#606060, .3);
        }

        &-orange {
            color: #eea80f;
            background-color: rgba(#eea80f, .24);
        }

        &-blue {
            color: rgb(0, 255, 229);
            background-color: rgba(0, 255, 229, .28);
        }
    }

    &-document {
        margin-left: 16px;
        cursor: pointer;
        padding: 4px;
        color: rgb(0, 255, 229);
    }

    &-btn {
        padding: 4px 6px;
        cursor: pointer;
        color: rgb(0, 255, 229);
        user-select: none;
        font-size: 18px;

        &:last-child {
            padding-right: 0;
        }

        &-del {
            color: #f55;
        }

        &[disabled] {
            cursor: not-allowed !important;
            color: rgba(#fff, .5);
        }
    }

    &-btn-add {
        width: 64px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: rgb(0, 255, 229);
        cursor: pointer;
        border: 1px solid rgb(0, 255, 229);
        font-size: 16px;
        margin-right: 16px;

        > * {
            vertical-align: middle;
        }

        span {
            margin-left: 4px;
        }
    }

    &-pagination {
        user-select: none;

        span {
            padding: 2px 4px;
        }

        i {
            cursor: pointer;
            padding: 2px 6px;
        }
    }
}

.emulation-create-close {
    color: #fff;
    width: 12px;
    height: 12px;
    line-height: 12px;
    text-align: center;
    border: 1px	solid rgba(46, 210, 255, .3);
    font-size: 10px;
    cursor: pointer;

    &.show i {
        transform: rotate(-180deg);
    }

    i {
        transform-origin: center;
        transition: .16s linear;
    }
}
</style>