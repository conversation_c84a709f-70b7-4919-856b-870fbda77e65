
<!-- 事件扎点 -->
<script>
import {ref, onMounted, onBeforeUnmount, computed} from 'vue';
export default {
    props: {
        // 基本信息 包括位置、名称 自定义数据 点击回调等
        info: {
            type: Object,
            default: () => null,
        },
        // 事件扎点管理实例
        managerInstace: {
            type: Object,
            default: () => null,
        },
    },
    setup(props) {
        const iconName = ref(null);

        const active = computed(() => {
            return props?.info?.active || false;
        });

        const addIcon = () => {
            if (!props.info.position) {
                return;
            }
            if (!props.managerInstace) {
                return;
            }
            iconName.value = props.info.name || props.info.type;
            props.managerInstace.addEventPoint(
                iconName.value,
                props.info.position,
                {
                    labelText: props.info.type,
                    bubbleColor: props.info.bubbleColor,
                    text: props.info.text,
                    circleColor: props.info.circleColor,
                    circleBorderColor: props.info.circleBorderColor,
                    active: active.value,
                    customData: props.info.customData,
                    clickCallback: props.info.clickCallback,
                }
            );
        };

        const removeIcon = () => {
            iconName.value && props.managerInstace.removeEventPointByName(iconName.value);
        };

        onMounted(() => {
            addIcon();
        });

        onBeforeUnmount(() => {
            removeIcon();
        });
    },
};
</script>