<!-- 四维仿真图层 -->
<template>
    <div class="emulation-layer">
        <!-- 左侧导航 -->
        <template>
            <slider-nav
                :emulation-type="curEmulation"
                @change="changeEmulationType"
            />
        </template>
        <!--  -->
        <template v-if="!isEmulationTwin">
            <!-- 左侧实时事件列表 -->
            <div v-show="!isCustomArea" class="left-content">
                <transition
                    leave-active-class="animate__animated animate__fadeOutLeft collapse-animate-duration"
                    enter-active-class="animate__animated animate__fadeInLeft collapse-animate-duration"
                >
                    <event-list v-show="!collapse" :emulation-type="curEmulation"/>
                </transition>
                <tool-button-bar
                    :class="['tool-button-bar', {collapse}]"
                    :show-back="false"
                    :collapse.sync="collapse"
                    @recover="handleRecover"
                />
            </div>
            <!-- 中间主体 主要是为了占满中间部分 -->
            <div v-show="!isCustomArea" class="center-content"></div>
            <!-- 右侧仿真方案部分 -->
            <div v-show="!isCustomArea" class="right-content">
                <emulation-list
                    v-show="!createConfig.show && emulationListVisible"
                    :emulation-type="curEmulation"
                    @add="createCase"
                    @edit="editCase"
                />
                <emulation-create
                    v-if="createConfig.show"
                    :emulation-type="curEmulation"
                    @close="onClose"
                />
                <div v-if="curEmulation !== 6" class="right-menu">
                    <div
                        class="menu-item" @click="createConfig.show = false;
                                                  emulationListVisible = !emulationListVisible;"
                    >
                        <img src="@/assets/images/fangzhen.png" alt="">
                        <span>仿真方案</span>
                    </div>
                </div>
            </div>
            <!-- 自定义区域 -->
            <div v-show="isCustomArea" class="custom-area-content">
                <custom-area/>
            </div>
        </template>
        <template v-else>
            <emulation-twin
                :emulation-type="curEmulation"
                style="position: fixed;"
            />
        </template>

    </div>
</template>

<script>
import {ref, watch, onMounted, computed} from 'vue';
import {strategyId, initUuid, init_road_info,
        isCustomArea, polygonEditor, reactEditor, circleEditor,
        collapse,
        defaultCenter} from '@EmulationLayer/store/index';

import {detailInfo, createConfig} from '@EmulationLayer/store/emulationCreate';
import {engine} from '@/store/engine';
import {
    PolygonEditor,
    RectEditor,
    CircleEditor,
} from '@baidu/mapv-three';

import EventList from './components/EventList/index.vue';
import EmulationList from './components/EmulationList/index.vue';
import EmulationCreate from './components/EmulationCreate/index.vue';
import EmulationTwin from './components/EmulationTwin/index.vue';
import CustomArea from './components/CustomArea/index.vue';
import SliderNav from './components/SliderNav/index.vue';
import {ToolButtonBar} from '@/components/Common';
import {viewTo} from '@/utils';

export default {
    name: 'EmulationLayer',
    components: {
        EventList,
        EmulationList,
        EmulationCreate,
        EmulationTwin,
        CustomArea,
        SliderNav,
        ToolButtonBar,
    },
    props: {
        emulationType: {
            type: [String, Number],
            default: 1,
        },
    },
    setup(props) {

        const emulationListVisible = ref(true);
        const curEmulation = ref(props.emulationType || 1);

        const isEmulationTwin = computed(() => strategyId.value);

        watch(
            () => props.emulationType,
            (newVal, oldVal) => {
                if (newVal === oldVal) return;
                curEmulation.value = newVal;
            }
        );

        // 新增仿真方案
        const createCase = () => {
            createConfig.value.show = true;
            createConfig.value.type = 'add';
            createConfig.value.id = '';
            detailInfo.value = null;
        };

        // 修改仿真方案
        const editCase = (id, item) => {
            createConfig.value.show = true;
            createConfig.value.type = 'edit';
            createConfig.value.id = id;
            detailInfo.value = item;
        };

        const onClose = () => {
            createConfig.value.show = false;
            createConfig.value.type = '';
            createConfig.value.id = '';
        };

        const changeEmulationType = e => {
            curEmulation.value = e;
            createConfig.value = {
                show: false,
                type: 'add',
                id: null,
            };
            strategyId.value = undefined;
        };

        // 地图实例
        watch(() => engine.value, val => {
                  if (val) {
                      //   engine.value = val;
                      //   testAddLine();
                      // 添加区域编辑管理器
                      polygonEditor.value = engine.value.add(new PolygonEditor(engine.value, {
                          polygonColor: 'rgba(73,195,255,0.08)',
                          borderColor: '#49C3FF',
                          borderWidth: 6,
                      }));
                      polygonEditor.value.addEventListener('change', e => {
                          console.log('eee--', e);
                      });

                      // 添加矩形区域编辑管理器
                      reactEditor.value = engine.value.add(new RectEditor(engine.value, {
                          rectColor: 'rgba(73,195,255,0.08)',
                          borderColor: '#49C3FF',
                          borderWidth: 6,
                      }));

                      // 添加圆形区域编辑管理器
                      circleEditor.value = engine.value.add(new CircleEditor(engine.value, {
                          circleColor: 'rgba(73,195,255,0.08)',
                          borderColor: '#49C3FF',
                          borderWidth: 6,
                      }));
                  }
              },
              {
                  immediate: true,
              }
        );

        function handleRecover() {
            createConfig.value.show = false;
            createConfig.value.type = '';
            createConfig.value.id = '';
            const center = defaultCenter.value;
            center && viewTo({
                zoom: 15,
                center,
            }, 1000);
        }

        onMounted(() => {
            // 初始化全局数据
            initUuid();
            init_road_info();
        });

        return {
            curEmulation,
            createConfig,
            emulationListVisible,
            strategyId,
            isEmulationTwin,
            isCustomArea,
            createCase,
            editCase,
            onClose,
            changeEmulationType,
            collapse,
            handleRecover,
        };
    },
};
</script>

<style lang='less'>
.emulation-layer {
    display: flex;
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 10;
    top: 0;
    pointer-events: none;
    padding: 30px 0;

    .el-date-editor {
        background: transparent;
        border: none;

        .el-input__prefix {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 0;
            background-color: transparent;
            border: none;
        }

        .el-range__icon {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: transparent;
            border: none;
            margin-left: 4px;
        }
    }

    .el-input__inner {
        border-color: transparent;
        background:
            linear-gradient(
                -66.16deg,
                rgba(34, 120, 105, .58) 8.087%,
                rgba(15, 103, 74, .62) 93.804%
            );
        border: 1.5px solid;
        border-image: linear-gradient(180deg, #379084 0%, #5cf4c3 99%) 1.5 1.5 1.5 1.5;
        clip-path: inset(0 round 2px);
    }

    .el-range-input {
        color: #fff;
    }

    .left-content {
        position: relative;
        display: flex;
        margin-left: 20px;
        width: 410px;
        pointer-events: auto;

        .tool-button-bar {
            position: absolute;
            left: 430px;
            transition: .3s linear;

            &.collapse {
                left: 0;
            }
        }
    }

    .center-content {
        flex: 1;
    }

    .right-content {
        margin-right: 114px;
        width: 472px;
        pointer-events: auto;
    }

    .custom-area-content {
        flex: 1;
    }
}

.emulation-el-popper.el-popper {
    border-color: rgba(#07af87, .3);
    background-color: rgba(32, 72, 63, .78);

    .el-date-range-picker__content.is-left {
        border-right-color: rgba(#07af87, .3);
    }

    .el-time-panel.el-popper {
        border-color: rgba(#07af87, .3);
        background-color: rgb(32, 72, 63);

        .el-time-panel__btn.confirm {
            color: #fff;
        }
    }

    .available {
        &.current,
        &.start-date,
        &.end-date {
            &:not(.disabled) span {
                color: #fff;
                background-color: #07af87;
            }
        }

        &.today span {
            color: #07af87;
        }

        &.in-range {
            div {
                background-color: rgba(#07af87, .3);
            }
        }
    }

    .el-input__inner {
        border-color: transparent;
        background:
            linear-gradient(
                -66.16deg,
                rgba(34, 120, 105, .58) 8.087%,
                rgba(15, 103, 74, .62) 93.804%
            );
        border: 1.5px solid;
        border-image: linear-gradient(180deg, #379084 0%, #5cf4c3 99%) 1.5 1.5 1.5 1.5;
        clip-path: inset(0 round 2px);
    }

    .time-select-item {
        color: #fff;

        &.disabled {
            color: #ccc;
            cursor: not-allowed !important;
        }

        &:not(.disabled):hover {
            color: #fff;
            background-color: rgba(#07af87, .3);
        }

        &.selected {
            color: #07af87;
        }
    }
}

.right-menu {
    width: 78px;
    height: 78px;
    background-image: linear-gradient(180deg, #07af87 0%, rgba(0, 121, 199, .45) 100%);
    padding: 4px;
    cursor: pointer;
    position: absolute;
    right: 0;
    top: 30px;

    .menu-item {
        background: #07af87;
        border: 1px solid #00e7b5;
        width: 70px;
        height: 70px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        img {
            display: block;
            width: 36px;
            height: 36px;
            padding-bottom: 10px;
        }

        span {
            font-family: 'FZLTZHJW--GB1-0', sans-serif;
            font-size: 14px;
            color: #fff;
        }
    }
}
</style>