<template>
    <div class="board-collapse">
        <div :class="['board-collapse__head', {collapse}]" @click="toggle">
            <i class="el-icon-arrow-down"></i>
            <div class="board-collapse__title">
                {{ title }}
                <span class="board-collapse__num">（共 {{ deviceList.length }} 条）</span>
            </div>
        </div>
        <el-collapse-transition>
            <div v-if="!collapse" class="board-collapse__body">
                <!-- 表格 -->
                <fk-table
                    :columns="columns"
                    :data="deviceList"
                    @selection-change="selectionChange"
                >
                    <template #deviceStatus="{row}">
                        <div class="device-status">
                            <span :class="['dot', row.deviceStatus === 1 ? 'dot-green' : 'dot-red']"></span>
                            <span>{{ row.deviceStatus === 1 ? '正常运行中' : '离线' }}</span>
                        </div>
                    </template>
                </fk-table>
                <!-- 输入框 -->
                <div class="input-area">
                    <el-input
                        v-model="textSubType"
                        type="textarea"
                        resize="none"
                        placeholder="请输入"
                        :rows="5"
                        :disabled="inputDisabled"
                    />
                    <div class="btns">
                        <div class="btn" @click="handleClear">清屏</div>
                        <div class="btn" @click="handleInput">文字</div>
                    </div>
                </div>
                <!-- 图文/文字 tabs -->
                <el-tabs v-model="currentTab" type="card">
                    <!-- <el-tab-pane label="图文" name="tab1">
                        图文
                    </el-tab-pane> -->
                    <el-tab-pane label="文字" name="text">
                        <el-row>
                            <el-col :span="4">
                                <el-select v-model="textType">
                                    <el-option
                                        v-for="option in options"
                                        :key="option.value"
                                        :label="option.label"
                                        :value="option.value"
                                    />
                                </el-select>
                            </el-col>
                            <el-col :style="{marginLeft: `${12 * ratio}px`}" :span="8">
                                <el-select v-model="textSubType" style="width: 100%;">
                                    <el-option
                                        v-for="option in getSubOptions"
                                        :key="option.value"
                                        :label="option.label"
                                        :value="option.value"
                                    />
                                </el-select>
                            </el-col>
                        </el-row>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </el-collapse-transition>
    </div>
</template>

<script>
import CollapseTransition from 'element-ui/lib/transitions/collapse-transition';
import {ref, computed} from 'vue';
import Table from '@/components/Common/Table/index.vue';
import {
    Input,
    Button,
    Tabs,
    TabPane,
    Select,
    Option,
    Row,
    Col,
} from 'element-ui';
import {useUnit} from '@/utils';

export default {
    components: {
        [CollapseTransition.name]: CollapseTransition,
        FkTable: Table,
        [Input.name]: Input,
        [Button.name]: Button,
        [Tabs.name]: Tabs,
        [TabPane.name]: TabPane,
        [Select.name]: Select,
        [Option.name]: Option,
        [Row.name]: Row,
        [Col.name]: Col,
    },
    props: {
        title: {
            type: String,
            default: '门架式显示屏',
        },
        deviceList: {
            type: Array,
            default: () => ([]),
        },
        options: {
            type: Array,
            default: () => ([
                {
                    label: '拥堵',
                    value: '拥堵',
                    children: [
                        {
                            label: '前方K140处拥堵请秩序行驶',
                            value: '前方K140处拥堵请秩序行驶',
                        },
                    ],
                },
            ]),
        },
    },
    setup(props) {
        const {ratio} = useUnit();

        const columns = [
            {
                type: 'selection',
                width: 40,
            },
            {
                label: '设备名称',
                prop: 'deviceName',
                width: 360,
                align: 'center',
            },
            {
                label: '设备类型',
                prop: 'deviceType',
                width: 120,
                align: 'center',
            },
            {
                label: '设备状态',
                prop: 'deviceStatus',
                width: 100,
                align: 'center',
                slotName: 'deviceStatus',
            },
        ];

        const collapse = ref(true);
        const inputDisabled = ref(true);
        const currentTab = ref('text');

        const textType = ref('拥堵');
        const textSubType = ref('前方K140处拥堵请秩序行驶');

        const getSubOptions = computed(() => {
            const list = props.options?.find(i => i.value === textType.value);

            return list?.children || [];
        });

        function toggle() {
            collapse.value = !collapse.value;
        }

        function selectionChange(selection) {

        }

        function handleClear() {
            textSubType.value = '';
            inputDisabled.value = false;
        }

        function handleInput() {
            inputDisabled.value = false;
        }

        return {
            collapse,
            toggle,
            columns,
            selectionChange,
            inputDisabled,
            handleClear,
            handleInput,
            currentTab,
            ratio,
            textType,
            textSubType,
            getSubOptions,
        };
    },
};
</script>

<style lang="less" scoped>
.board-collapse {
    border-bottom: 1px solid #c4c4c4;

    &__head {
        display: flex;
        align-items: center;
        height: 48px;
        cursor: pointer;

        .el-icon-arrow-down {
            transition: transform .2s linear;
            transform: rotate(0deg);
        }

        &.collapse {
            .el-icon-arrow-down {
                transform: rotate(-90deg);
            }
        }
    }

    &__title {
        font-size: 16px;
        margin-left: 16px;
    }

    &__num {
        color: rgba(#fff, .6);
        font-size: 14px;
    }

    &__body {
        padding: 16px 0;

        /deep/ .el-table {
            &__header-wrapper {
                display: none;
            }
        }
    }

    .device-status {
        display: flex;
        align-items: center;

        .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 6px;

            &-green {
                background-color: #27b279;
            }

            &-red {
                background-color: #f55;
            }
        }
    }

    .btns {
        display: flex;
        justify-content: end;
        margin-top: 16px;

        .btn {
            line-height: 32px;
            padding: 0 10px;
            border: 1px solid rgba(#fff, .6);
            cursor: pointer;
            user-select: none;

            &:active {
                opacity: .85;
            }

            &:last-child {
                margin-left: 16px;
            }
        }
    }
}
</style>