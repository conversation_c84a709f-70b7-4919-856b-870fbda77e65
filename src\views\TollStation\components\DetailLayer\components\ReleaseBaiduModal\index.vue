<template>
    <modal-card
        :visible="visible"
        :width="1042"
        title="发布到百度地图"
        @close="handleClose"
        @confirm="handleConfirm"
    >
        <div class="release-baidu">
            <div class="release-baidu__left">
                <el-form
                    ref="formRef"
                    class="form"
                    size="medium"
                    :model="formModel"
                    :rules="formRules"
                    :label-width="labelWidth"
                >
                    <el-form-item
                        label="事件类型："
                        required
                    >
                        <el-col :span="11">
                            <el-form-item prop="eventType1">
                                <el-select v-model="formModel.eventType1" size="small">
                                    <el-option label="施工管制" value="施工管制"/>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" :offset="1">
                            <el-form-item prop="eventType2">
                                <el-select v-model="formModel.eventType2" size="small">
                                    <el-option label="养护施工" value="养护施工"/>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-form-item>
                    <el-form-item
                        label="所属方向："
                        prop="direction"
                        required
                    >
                        <el-select v-model="formModel.direction" size="small">
                            <el-option label="100" value="100"/>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="起点桩号：" required>
                        <el-col :span="11">
                            <el-form-item prop="startStakeKM">
                                <el-select v-model="formModel.startStakeKM" size="small">
                                    <el-option label="k123+123" value="k123+123"/>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" :offset="1">
                            <el-form-item prop="startStakeM">
                                <el-select v-model="formModel.startStakeM" size="small">
                                    <el-option label="30.1231245" value="30.1231245"/>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-form-item>
                    <el-form-item label="终点桩号：" required>
                        <el-col :span="11">
                            <el-form-item prop="endStakeKM">
                                <el-select v-model="formModel.endStakeKM" size="small">
                                    <el-option label="k123+123" value="k123+123"/>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" :offset="1">
                            <el-form-item prop="endStakeM">
                                <el-select v-model="formModel.endStakeM" size="small">
                                    <el-option label="30.1231245" value="30.1231245"/>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-form-item>
                    <el-form-item
                        label="事件开始时间："
                        prop="startTime"
                        required
                    >
                        <el-date-picker
                            v-model="formModel.startTime"
                            size="mini"
                            type="datetime"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            placeholder="选择事件开始时间"
                        />
                    </el-form-item>
                    <el-form-item
                        label="事件结束时间："
                        prop="endTime"
                        required
                    >
                        <el-date-picker
                            v-model="formModel.endTime"
                            size="mini"
                            type="datetime"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            placeholder="选择事件结束时间"
                        />
                    </el-form-item>
                    <el-form-item label="备注：" props="remark">
                        <el-input
                            v-model="formModel.remark"
                            type="textarea"
                            size="mini"
                            :rows="3"
                            resize="none"
                        />
                    </el-form-item>
                </el-form>
            </div>
            <div class="release-baidu__right">
                <div class="map">
                    <map-component @loaded="loaded"/>
                </div>
            </div>
        </div>
    </modal-card>
</template>

<script>
import {ModalCard} from '@/components/Common';
import {useUnit} from '@/utils';
import {Form, FormItem, Select, Option, Col, DatePicker, Input} from 'element-ui';
import {computed, ref} from 'vue';
import MapComponent from '@/views/TollStation/components/Map/index.vue';

export default {
    components: {
        ModalCard,
        [Form.name]: Form,
        [FormItem.name]: FormItem,
        [Select.name]: Select,
        [Option.name]: Option,
        [Col.name]: Col,
        [DatePicker.name]: DatePicker,
        [Input.name]: Input,
        MapComponent,
    },
    props: {
        visible: Boolean,
    },
    setup(props, {emit}) {
        let engine = null;
        const {ratio} = useUnit();

        const formRef = ref(null);
        const formModel = ref({
            eventType1: '施工管制',
            eventType2: '养护施工',
            direction: '上行',
            startStakeKM: 'k123+123',
            startStakeM: '30.1231245',
            endStakeKM: 'k123+123',
            endStakeM: '30.1231245',
            startTime: '2023-01-23 13:12:23',
            endTime: '2023-01-23 14:12:23',
            remark: '',
        });
        const formRules = {
            eventType1: [{required: true, message: '请选择事件类型', trigger: 'change'}],
            eventType2: [{required: true, message: '请选择事件类型', trigger: 'change'}],
            direction: [{required: true, message: '请选择所属方向', trigger: 'change'}],
            startStakeKM: [{required: true, message: '请选择起点桩号', trigger: 'change'}],
            startStakeM: [{required: true, message: '请选择起点桩号', trigger: 'change'}],
            endStakeKM: [{required: true, message: '请选择终点桩号', trigger: 'change'}],
            endStakeM: [{required: true, message: '请选择终点桩号', trigger: 'change'}],
            startTime: [{required: true, message: '请选择事件开始时间', trigger: 'change'}],
            endTime: [{required: true, message: '请选择事件结束时间', trigger: 'change'}],
        };

        const labelWidth = computed(() => `${ratio.value * 120}px`);

        function loaded(map) {
            engine = map;
        }

        async function handleConfirm() {
            formRef.value.validate(async valid => {
                if (!valid) return;

                emit('confirm');
            });
        }

        function handleClose() {
            emit('update:visible', false);
        }

        return {
            labelWidth,
            loaded,
            formModel,
            formRef,
            handleConfirm,
            formRules,
            handleClose,
        };
    },
};
</script>

<style lang="less" scoped>
.release-baidu {
    display: flex;
    width: 100%;

    &__left {
        width: 360px;
        flex-shrink: 0;
    }

    &__right {
        flex: 1;
        padding-left: 16px;
    }

    .map {
        width: 100%;
        height: calc(100% - 20px);
        background: #fff;
    }
}

.form {
    & /deep/ .el-select,
    & /deep/ .el-date-editor.el-input {
        width: 100%;
    }

    & /deep/ .el-date-editor .el-input__inner {
        height: 36px;
    }
}

</style>