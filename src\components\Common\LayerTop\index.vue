<template>
    <div :class="['top', {'gap-top': hideHeader}]">
        <!-- 标题与logo -->
        <div v-if="!hideHeader" class="background">
            <div class="left">
                <!-- <img
                    src="@/assets/images/logo.png"
                    alt=""
                > -->
                <div class="title">{{ title }}</div>
            </div>

            <div class="right">
                <div class="time">{{ time }}</div>
                <div class="date">
                    <div class="format">{{ week }}</div>
                    <div class="now">{{ date }}</div>
                </div>
            </div>
        </div>

        <!-- 插槽：是否要下拉卡片 -->
        <div class="slot">
            <div class="left">
                <slot name="select"></slot>
            </div>
            <div class="right">
                <slot name="right"></slot>
            </div>
        </div>
    </div>
</template>

<script>
import {useTimeNow} from '@/utils';
import {onMounted, onUnmounted, ref} from 'vue';
import qs from 'query-string';

export default {
    name: 'LayerTop',
    props: {
        title: {
            type: String,
            default: '图层',
        },
    },
    setup() {
        const {time, week, date, pause, resume, isActive} = useTimeNow();

        const hideHeader = ref(false);

        const pauseTimer = () => isActive.value && pause();

        const resumeTimer = () => !isActive.value && resume();

        function handleChangeUrl() {
            const url = location.href;
            const index = url.indexOf('?');
            if (index === -1) {
                hideHeader.value = false;
                resumeTimer();
                return;
            }
            const {
                hideTop,
            } = qs.parse(url.substring(index + 1));
            const hide = hideTop === 'true';
            hide ? pauseTimer() : resumeTimer();
            hideHeader.value = hide;
        }

        onMounted(() => {
            handleChangeUrl();
            window.addEventListener('popstate', handleChangeUrl);
        });

        onUnmounted(() => {
            window.removeEventListener('popstate', handleChangeUrl);
        });

        return {
            time,
            week,
            date,
            hideHeader,
        };
    },
};
</script>

<style lang="less" scoped>
.top {
    position: relative;

    &.gap-top {
        padding-top: 80px;
    }

    .background {
        display: flex;
        justify-content: space-between;
        padding: 0 30px;
        width: 100vw;
        height: 80px;
        padding-top: 20px;
        background:
            // url('@/assets/images/title_background.png') no-repeat
            center/110% 110%;

        .left {
            display: flex;

            img {
                width: 38px;
                height: 34px;
                margin-right: 12px;
            }

            .title {
                text-align: center;
                font-size: 34px;
                font-weight: 400;
                letter-spacing: 2.83px;
                color: #fff;
                font-family: 'black';
                // 适配性文字渐变
                @supports (-webkit-background-clip: text) or (background-clip: text) {
                    background:
                        linear-gradient(
                            to bottom,
                            #fff,
                            rgb(122, 251, 255)
                        );
                    -webkit-background-clip: text;
                    background-clip: text;
                    color: transparent;
                }
            }
        }

        .right {
            display: flex;
            color: #fff;

            .time {
                font-size: 30px;
                font-weight: 700;
                letter-spacing: 1px;
                margin-right: 14px;
            }

            .date {
                display: flex;
                flex-direction: column;
                color: #fff;
                font-weight: 700;
                letter-spacing: 1px;
                font-size: 12px;
                padding-top: 3px;

                .format {
                    font-weight: 500;
                    font-family: 'PingFang SC', 'PingFang-SC';
                }
            }
        }
    }

    .slot {
        display: flex;
        justify-content: space-between;

        .left,
        .right {
            display: flex;
            align-items: center;
            padding: 0 26px;
        }
    }
}
</style>
