import {addLabel, removeLabel} from '../index';
import {getPointHeight} from '@/utils';
// label管理器
class LabelManager {
    constructor(engine) {
        this.labelMap = new Map();
        this.setHaveMap = new Map();
        this.engine = engine;
    }
    async addLabel(name, point, text = '交通事故', options) {
        if (this.labelMap.has(name)) {
            this.removeLabelByName(name);
        }
        if (this.setHaveMap.has(name)) {
            this.setHaveMap.get(name)();
        }
        const next = await this.initHeight(name, point);
        if (!next) return;

        const {offset, padding, width, height, customData, clickCallback} = options || {};
        let {label, _engine} = addLabel(point, text, {
            offset,
            padding,
            width,
            height,
            customData,
            _engine: this.engine,
        });
        this.labelMap.set(name, label);
        if (clickCallback && typeof clickCallback === 'function') {
            label.receiveRaycast = true;
            _engine.event.bind(label, 'click', clickCallback);
        }
    }

    // 初始化高程
    async initHeight(name, point) {
        if (!point[2]) {
            // 解决请求height时await阻塞导致再次添加时无法清除扎点
            let have = true;
            this.setHaveMap.set(name, () => {
                have = false;
            });
            const height = await getPointHeight(point);
            point[2] = height?.data?.[0] || 0;
            if (have) {
                this.setHaveMap.delete(name);
            }
            return have;
        }
        return true;
    }

    removeLabelByName(name) {
        const label = this.labelMap.get(name);
        label && removeLabel(label, this.engine);
        this.labelMap.delete(name);
    }

    clear() {
        [...this.labelMap.keys()].forEach(label => {
            this.removeLabelByName(label);
        });
        this.labelMap.clear();
    }
}

export {
    LabelManager,
};