<template>
    <map-component @loaded="mapLoaded">
        <page-wrapper>
            <home v-if="isHome" @clickMarker="handleEnterTollStation"/>
            <detail v-else @back="handleBack"/>
        </page-wrapper>
        <map-line/>
    </map-component>
</template>

<script setup>
import {computed, ref} from 'vue';
import {viewToFk, viewToMicro} from '@/utils';
import {showTypeMap} from './config';
import MapComponent from './components/Map/index.vue';
import {PageWrapper} from '@/components/Common';
import MapLine from './components/MapLine/index.vue';
import Home from './components/Home/index.vue';
import Detail from './components/Detail/index.vue';
import {tollStation} from './store';

const showType = ref(showTypeMap.HOME);

// 显示全部收费站
const isHome = computed(() => showType.value === showTypeMap.HOME);


function handleBack() {
    showType.value = showTypeMap.HOME;
    tollStation.value = {};
    viewToFk();
}

function handleEnterTollStation(info) {
    viewToMicro(info.position);
    tollStation.value = info;
    showType.value = showTypeMap.DETAIL;
}

function mapLoaded() {
    viewToFk();
}
</script>

<style lang="less" scoped>
.map {
    position: relative;
    flex: 1;
    transition: .35s linear;
    overflow: hidden;
}
</style>