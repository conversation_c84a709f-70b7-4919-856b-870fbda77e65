import {BaseApiHost} from '@/store/common';
import {request} from '@/utils/network-helper/index';

// 根据经纬度获取坐标高程信息 单点
export const getPointHeight = async payload => {
    const origin = ['test', 'development'].includes(import.meta.env.MODE) ? 'http://**********:8211' : BaseApiHost.value;
    const {data}
        = await fetch(`${origin}/height/?x=${payload?.[0]}&y=${payload?.[1]}`,
            {
                method: 'GET',
            }).then(response => response.json());
    if (data) {
        return {
            data: data || 0,
        };
    }
    return {};
};

// 根据经纬度获取坐标高程信息 批量
export const getPointHeights = async payload => {
    const origin = ['test', 'development'].includes(import.meta.env.MODE) ? 'http://**********:8211' : BaseApiHost.value;
    const response = await request.post(`${window.origin}/points_height/`, JSON.stringify(payload), {
        headers: {
            'Content-Type': 'application/json',
        },
        mode: 'no-cors',
    });
    return response.data || [];
};

// 获取高程
// 根据经纬度获取坐标高程信息
export const getAlt = async payload => {
    const origin = ['test', 'development'].includes(import.meta.env.MODE) ? 'http://**********:8211' : BaseApiHost.value;
    const {data}
        = await fetch(`${origin}/height/?x=${payload.x}&y=${payload.y}`,
            {
                method: 'GET',
            }).then(response => response.json());
    if (data) {
        return data?.[0];
    }
    return Promise.reject(null);
};