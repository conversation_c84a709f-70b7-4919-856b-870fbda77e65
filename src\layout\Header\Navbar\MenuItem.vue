<template>
    <el-submenu
        v-if=" !!info?.children?.length"
        :index="info.name"
        popper-class="app-menu-poper"
        :show-timeout="100"
    >
        <template #title>{{ info.meta.title }}</template>
        <app-menu-item
            v-for="item in info.children"
            :key="item.path"
            :info="item"
        />
    </el-submenu>
    <el-menu-item v-else :index="info.path">{{ info.meta.title }}</el-menu-item>
</template>

<script>
import {MenuItem, Submenu} from 'element-ui';

export default {
    name: 'AppMenuItem',
    components: {
        [Submenu.name]: Submenu,
        [MenuItem.name]: MenuItem,
    },
    props: {
        info: Object,
    },
    mounted() {
        // 处理el-submenu的bug，报错 Maximum call stack size exceeded
        this.$children[0].$parent = this.$parent;
    },
    setup() {
    },
};
</script>

<style lang="less" >
.app-menu-poper {
    &.el-menu--horizontal {
        // reset选中样式
        .el-submenu__title,
        .el-menu-item {
            color: rgba(255, 255, 255, .6) !important;

            &:hover,
            &.is-active,
            &.is-opened {
                color: #fff !important;
                font-weight: bold;
            }
        }
        // reset选中样式
        .el-submenu {
            &.is-opened,
            &.is-active {
                > .el-submenu__title {
                    color: #fff !important;
                    font-weight: bold;
                }
            }
        }

        // reset样式
        .el-menu--popup {
            padding: 0;
            min-width: 132px;

            > li {
                border-top: 1px solid rgba(255, 255, 255, .2);
            }
        }
    }
}
</style>