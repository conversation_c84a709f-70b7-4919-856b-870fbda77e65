<template>
    <div class="structure-alarm">
        <single-data-bar
            v-if="list.length"
            y-name="数量" :data="list"
            :legend-data="['告警数量']"
        />
        <no-data v-else/>
    </div>
</template>

<script>
import {ref, onMounted} from 'vue';
import {SingleDataBar, NoData} from '@/components/Common';
import {selectWarningTypeCountByStructureId} from '@/api/structure/index';
import {structureId} from '@/views/Structure/utils/index';
export default {
    name: '结构物报警类型统计',
    components: {
        SingleDataBar, NoData,
    },
    setup(props) {
        const list = ref([]);

        const init = () => {
            selectWarningTypeCountByStructureId({
                structureId: structureId.value,
            }).then(res => {
                list.value = res.data.map(item => ({
                    name: item.warningType,
                    value: item.warningCount,
                    ...item,
                    color: 'rgba(87,255,213,.6)',
                }));
            });
        };

        onMounted(() => init());

        return {
            list,
        };
    },
};
</script>

<style lang="less" scoped>
.structure-alarm {
    width: 100%;
    height: 314px;
}
</style>