<template>
    <main>
        <c-table
            :data="list"
            :columns="columns" :loading="loading"
        />
    </main>
</template>

<script>
import CTable from '@/components/Common/Table/index.vue';
import getColumns from './column';



export default {
    name: 'DangerousDoing',
    components: {
        CTable,
    },
    props: {
        list: {
            type: Array,
            default: () => [],
        },
        loading: {
            type: Boolean,
            default: false,
        },
    },
    setup(props) {
        console.log('output->list', props.list);
        const columns = getColumns();

        return {
            columns,
        };
    },
};
</script>


<style lang="less" scoped>
main{
    overflow-y: auto;
}
/deep/  .el-table__header{
    background-color: rgba(255, 255, 255);

  }


/deep/ .el-table th, .el-table td{
    padding: 0;

}
/deep/ .cell{
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>

