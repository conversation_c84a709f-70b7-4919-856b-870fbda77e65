import {
    IconManager,
    LabelManager,
    MacroManager,
    MesoscopicManager,
    LineManager,
    WarningManager,
    WarningNextManager,
    MaintainPlanManager,
} from '@/utils/map/manager';
export const iconManager = new IconManager();

export const labelManager = new LabelManager();

export const macroManager = new MacroManager();
export const mesoscopicManager = new MesoscopicManager();
export const warningNextManager = new WarningNextManager();
export const warningManager = new WarningManager();
export const maintainPlanManager = new MaintainPlanManager();

export const lineManager = new LineManager();

