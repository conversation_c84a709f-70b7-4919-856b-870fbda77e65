<template>
    <bubble-marker
        bubble-color="#009EFF"
        icon-name="sheshi-shexiangtou"
        :manager-instace="managerInstace"
        :info="{
            ...info,
            clickCallback: handleClick,
        }"
    >
        <modal-card
            :visible.sync="visible"
            :fullscreen-center="false"
            :title="info.name"
            :show-foot="false"
            :width="384"
        >
            <div class="v-list">
                <div class="v-item ct jsb">
                    <span class="v-item__label">所属高速：</span>
                    <span class="v-item__value">{{ info.sectionName }}</span>
                </div>
                <div class="v-item ct jsb">
                    <span class="v-item__label">设备桩号：</span>
                    <span class="v-item__value">{{ info.deviceStake }}</span>
                </div>
                <div class="v-item">
                    <flv-player v-if="visible" :camera-info-id="deviceCode"/>
                </div>
            </div>
        </modal-card>
    </bubble-marker>
</template>

<script>
import BubbleMarker from '@/components/Common/BubbleMarker/index.vue';
import ModalCard from '@/components/Common/ModalCard/index.vue';
import FlvPlayer from '@/components/Common/video/flvPlayer.vue';
import {ref} from 'vue';

export default {
    components: {
        BubbleMarker,
        ModalCard,
        FlvPlayer,
    },
    props: {
        info: {
            type: Object,
            default: () => ({}),
        },
        managerInstace: {
            type: Object,
            required: true,
        },
        deviceCode: String,
    },
    setup() {
        const visible = ref(false);

        function handleClick() {
            visible.value = true;
        }

        return {
            visible,
            handleClick,
        };
    },
};
</script>

<style lang="less" scoped>
.v-list {
    .v-item {
        padding: 6px 12px;
        background-color: rgba(0, 98, 167, .15);
        font-size: 16px;

        &:not(:last-child) {
            margin-bottom: 4px;
        }

        &__label {
            color: rgb(231, 239, 255);
        }

        &__value {
            color: #fff;
        }
    }
}
</style>