// 高速路字符串转数组方法，默认起携带高程
export const roadLineFormatted = (arr, size = 3) => {
    const result = [];
    let tempArr = [];

    for (let i = 0; i < arr.length; i++) {
        tempArr.push(+arr[i] || 0);

        if (tempArr.length === size) {
            result.push(tempArr);
            tempArr = [];
        }
    }
    return result;
};

export function toChineseNumber(num) {
    // 数字裁剪
    const numStr = num
        .toString()
        .replace(/(?=(\d{4})+$)/g, ',')
        .split(',')
        .filter(Boolean);

    // 单位映射表
    const chars = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
    const units = ['', '十', '百', '千'];

    function handleZero(str) {
        return str.replace(/零{2,}/g, '零').replace(/零+$/g, '');
    }

    // 四个数字各自添加单位
    function _transform(n) {
        // 如果 0000 ，返回零
        if (n === '0000') {
            return chars[0];
        }

        let res = '';
        for (let i = 0; i < n.length; i++) {
            const c = chars[+n[i]];
            let u = units[n.length - 1 - i];
            // 如果是零，不设置单位
            if (c === chars[0]) {
                u = '';
            }
            res += c + u;
        }
        // 去除重复零以及末尾的零
        res = handleZero(res);
        return res;
    }

    // 裁剪后的字符串循环遍历每一项的四个数字
    const bigUnits = ['', '万', '亿'];
    let result = '';
    for (let i = 0; i < numStr.length; i++) {
        const part = numStr[i];
        const c = _transform(part);
        let u = bigUnits[numStr.length - 1 - i];
        // 如果是零，不设置单位
        if (c === chars[0]) {
            u = '';
        }
        result += c + u;
    }
    result = handleZero(result);
    return result;
}