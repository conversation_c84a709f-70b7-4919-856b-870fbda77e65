<template>
    <div class="layout-header">
        <div class="layout-header__left">
            <div class="layout-header__logo">
                <img src="@/assets/images/base/logo.svg">
                <img src="@/assets/images/base/logo-text.svg">
            </div>
            <span></span>
        </div>
        <div class="layout-header__middle">
            <div class="layout-header__title">佛开高精度数字底图高分智能综合监控平台</div>
        </div>
        <div class="layout-header__right">
            <div class="layout-header__right-item">
                <Icon
                    name="time"
                    color="#fff"
                    size="22"
                />
                <span>{{ currentDateTime }}</span>
            </div>
        </div>
    </div>
</template>

<script setup>
import {Icon} from '@/components/Common';
import {useDateFormat, useNow} from '@vueuse/core';

const currentDateTime = useDateFormat(useNow(), 'YYYY-MM-DD HH:mm:ss');

</script>

<style lang="less" scoped>
.layout-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 172px;
    color: #fff;
    background-image: url('@/assets/images/base//header-bg.png');
    background-size: 100% 100%;
    padding: 24px 40px;
    pointer-events: none;

    & > * {
        pointer-events: auto;
    }

    &__logo {
        display: flex;
        align-items: center;
        height: 25px;

        img:nth-child(1) {
            width: 42px;
            height: auto;
        }

        img:nth-child(2) {
            width: 204px;
            height: auto;
            margin-left: 12px;
        }
    }

    &__title {
        font-size: min(68px, 1.25vw);
        font-weight: 720;
        letter-spacing: 4px;
        white-space: nowrap;
    }

    &__left {
        width: 440px;
        flex-shrink: 0;
    }

    &__middle {
        position: absolute;
        left: 50%;
        top: 36px;
        transform: translateX(-50%);
    }

    &__right {
        &-item {
            position: relative;
            display: flex;
            align-items: center;
            font-size: 20px;

            &::after {
                content: '';
                position: absolute;
                left: 0;
                bottom: -4px;
                width: 100%;
                height: 1px;
                background-color: rgba(#fff, .2);
            }

            span {
                margin-left: 12px;
                line-height: 32px;
                color: rgba(#fff, .7);
            }
        }
    }
}
</style>