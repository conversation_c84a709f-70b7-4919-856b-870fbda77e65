<template>
    <div class="micro-left">
        <div class="micro-left-item">
            <card
                class="card-long-2" title="传感器实时监测数据"
            >
                <template #content>
                    <sensorsMonitor/>
                </template>
            </card>
        </div>
        <div class="micro-left-item">
            <card
                class="card-long-2" title="结构物报警类型统计"
            >
                <template #content>
                    <structureAlarm/>
                </template>
            </card>
        </div>
    </div>
</template>

<script>
import {Card} from '@/components/Common/index';
import sensorsMonitor from './sensorsMonitor.vue';
import structureAlarm from './structureAlarm.vue';

export default {
    name: '结构物微观左侧',
    components: {
        Card,
        sensorsMonitor,
        structureAlarm,
    },
    setup() {
    },
};
</script>

<style lang="less" scoped>
.micro-left {
    width: 100%;
    height: calc(100% - 152px);

    &-item:not(:last-child) {
        margin-bottom: 24px;
    }
}
</style>