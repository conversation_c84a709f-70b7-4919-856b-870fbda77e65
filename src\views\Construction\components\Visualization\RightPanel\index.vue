<template>
    <CollapsePanel
        class="collapse-panel"
        direction="right"
        :collapse="collapse"
        @collapse="$emit('update:collapse', !collapse)"
    >
        <div class="panel-content">
            <div class="panel-content__left">
                <Emulation/>
            </div>
            <div class="panel-content__right">
                <HighFrequencyStatisticsCard/>
                <DistributionStatisticsCard class="mt-24"/>
            </div>
        </div>
    </CollapsePanel>
</template>

<script setup>
import {CollapsePanel} from '@/components/Common';
import HighFrequencyStatisticsCard from '../../Card/HighFrequencyStatisticsCard/index.vue';
import DistributionStatisticsCard from '../../Card/DistributionStatisticsCard/index.vue';
import Emulation from '../../Emulation/index.vue';

defineProps({
    collapse: Boolean,
});
</script>

<style lang="less" scoped>
.panel-content {
    display: flex;
    align-items: flex-start;
    pointer-events: none;

    > * {
        pointer-events: auto;
    }

    &__left {
        margin-right: 24px;
    }
}

.collapse-panel {
    /deep/ .collapse-panel__inner {
        &::before {
            max-width: none;
        }
    }
}
</style>