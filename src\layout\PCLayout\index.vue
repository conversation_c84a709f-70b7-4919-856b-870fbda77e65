<template>
    <div class="app-layout">
        <div class="app-layout__header">
            <layout-header/>
        </div>
        <div class="app-layout__body">
            <layout-breadcrumb/>
            <div class="app-layout__content">
                <router-view/>
            </div>
        </div>
    </div>
</template>

<script>
import Header from './Header/index.vue';
import Breadcrumb from './Breadcrumb/index.vue';

export default {
    components: {
        LayoutHeader: Header,
        LayoutBreadcrumb: Breadcrumb,
    },
};
</script>

<style lang="less" scoped>
.app-layout {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100vh;
    overflow: hidden;

    &__header {
        flex-shrink: 0;
        width: 100%;
    }

    &__body {
        flex: 1;
        background-color: rgb(7, 21, 45);
        color: #fff;
        display: flex;
        flex-direction: column;
        overflow-y: hidden;
    }

    &__content {
        position: relative;
        flex: 1;
        overflow-y: auto;
        padding: 0 24px 24px 24px;
    }
}
</style>