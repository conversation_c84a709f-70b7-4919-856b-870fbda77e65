<template>
    <div class="base-config">
        <el-form
            class="emulation-form"
            :model="formInfo"
            size="small"
            :label-width="labelWidth"
        >
            <div class="emulation-form__title">施工基本信息</div>
            <!-- 区域选择 -->
            <el-form-item label="区域选择：">
                <el-radio-group v-model="formInfo.areaChoose" @change="changeArea">
                    <el-radio
                        v-for="item in areaSelectOption"
                        :key="item.value"
                        :label="item.value"
                    >
                        {{ item.label }}
                    </el-radio>
                </el-radio-group>
            </el-form-item>
            <!-- 高速、桩号选择 -->
            <el-row v-if="formInfo.areaChoose === 1">
                <el-col :span="8">
                    <el-form-item
                        label-width="0px"
                        prop="highSpeedName"
                    >
                        <api-select
                            v-model="formInfo.highSpeedName"
                            filterable
                            default-selected-first
                            :api="getHighSpeedInfoCache"
                            result-field=""
                            label-field="highSpeedNameCn"
                            value-field="highSpeedName"
                            @change="formInfo.emulationStartStake = ''"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="15" :offset="1">
                    <el-form-item
                        label-width="0px"
                        prop="emulationStartStake"
                    >
                        <stake-select
                            v-model="formInfo.emulationStartStake"
                            :high-speed-name="formInfo.highSpeedName"
                        />
                    </el-form-item>
                </el-col>
            </el-row>
            <!-- 仿真时间 -->
            <el-form-item label="仿真开始时间：" prop="emulationStartTime">
                <el-date-picker
                    v-model="formInfo.emulationStartTime"
                    class="full-content"
                    type="datetime"
                    placeholder="请选择仿真开始时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                />
            </el-form-item>
            <el-form-item label="仿真结束时间：" prop="emulationEndTime">
                <el-date-picker
                    v-model="formInfo.emulationEndTime"
                    class="full-content"
                    type="datetime"
                    placeholder="请选择仿真结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                />
            </el-form-item>
            <el-form-item label="出车点距离：">
                <el-input
                    v-model="formInfo.flowDistance"
                    type="number"
                    :min="500"
                    :max="5000"
                    class="full-content"
                    placeholder="请输入500-5000之间的出车点距离"
                >
                    <template #suffix>米</template>
                </el-input>
            </el-form-item>
            <div class="emulation-form__title">施工区域方案信息</div>
            <el-form-item label="施工类型：">
                <api-select
                    v-model="formInfo.eventType"
                    class="full-content"
                    default-selected-first
                    :options="eventTypeOptions"
                />
            </el-form-item>
            <el-form-item label="施工位置类型：">
                <api-select
                    v-model="formInfo.eventLocationType"
                    class="full-content"
                    default-selected-first
                    :options="evnetLocationTypeOptions"
                />
            </el-form-item>
            <el-form-item label="施工开始时间：">
                <el-date-picker
                    v-model="formInfo.eventStartTime"
                    class="full-content"
                    type="datetime"
                    placeholder="请选择施工开始时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                />
            </el-form-item>
            <el-form-item label="预计持续时长：">
                <el-input
                    v-model="formInfo.duration"
                    class="full-content"
                    placeholder="请输入预计持续时长"
                >
                    <template #suffix>分钟</template>
                </el-input>
            </el-form-item>
            <el-form-item label="高速名称：">
                <api-select
                    v-model="formInfo.highSpeedName1"
                    filterable
                    class="full-content"
                    default-selected-first
                    :api="getHighSpeedInfoCache"
                    result-field=""
                    label-field="highSpeedNameCn"
                    value-field="highSpeedName"
                    @change="formInfo.eventStartStake = ''"
                />
            </el-form-item>
            <el-form-item label="施工位置：">
                <stake-select
                    :value="formInfo.eventStartStake"
                    default-selected-middle
                    :high-speed-name="formInfo.highSpeedName1"
                    @change="handleEventStakeChange"
                />
            </el-form-item>
            <!-- 事件经纬度 -->
            <el-form-item>
                <div class="form-position">
                    <el-input
                        v-model="formInfo.eventPosition"
                        :disabled="!isOpenPosition"
                        class="form-position__input"
                    />
                    <div
                        v-if="!isOpenPosition"
                        class="form-position__btn"
                        @click="handleOpenPosition"
                    >
                        <icon name="dingwei" color="#fff"/>
                    </div>
                    <div
                        v-else
                        class="form-position__btn"
                        @click="handleConfirmPosition"
                    >
                        <span>确定</span>
                    </div>
                </div>
            </el-form-item>
            <!--  -->
            <el-form-item label="影响方向：">
                <api-select
                    v-model="formInfo.direction"
                    class="full-content"
                    default-selected-first
                    :props="{
                        disabled: true,
                    }"
                    :options="evnetDirectionOptions"
                />
            </el-form-item>
            <el-form-item label="封闭行车道：">
                <el-checkbox-group v-model="formInfo.closeLane">
                    <el-checkbox
                        v-for="item in closeLaneOptions"
                        :key="item.value"
                        :label="item.value"
                    >
                        {{ item.label }}
                    </el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="施工限速：">
                <el-input
                    v-model="formInfo.limitSpeed"
                    class="full-content"
                    placeholder="请输入施工限速"
                >
                    <template #suffix>km/h</template>
                </el-input>
            </el-form-item>
            <el-form-item label="上游过渡区长度：">
                <el-input
                    v-model="formInfo.upstreamTransitionLength"
                    class="full-content"
                    placeholder="请输入上游过渡区长度"
                >
                    <template #suffix>米</template>
                </el-input>
            </el-form-item>
            <el-form-item label="作业区长度：">
                <el-input
                    v-model="formInfo.constructionLength"
                    class="full-content"
                    placeholder="请输入作业区长度"
                >
                    <template #suffix>米</template>
                </el-input>
            </el-form-item>
            <el-form-item label="下游过渡区长度：">
                <el-input
                    v-model="formInfo.downstreamTransitionLength"
                    class="full-content"
                    placeholder="请输入下游过渡区长度"
                >
                    <template #suffix>米</template>
                </el-input>
            </el-form-item>
        </el-form>

        <!-- 事件扎点 -->
        <bubble-marker
            :manager-instace="domManager"
            bubble-type="diamond"
            bubble-color="#FF4B4B"
            icon-name="construction"
            :info="{
                position: getMarkerPosition,
            }"
        />
        <!-- 定位扎点 -->
        <icon-marker
            v-if="isOpenPosition"
            :icon-manager="iconManager"
            :info="{
                width: 20,
                height: 25.4,
                position: getIconPosition,
            }"
        />
        <!-- 施工区域 -->
        <construction-area
            v-for="item in getConstructionList"
            :key="item"
            :list="item"
        />
    </div>
</template>

<script>
import {
    Form,
    FormItem,
    Radio,
    RadioGroup,
    Row,
    Col,
    Select,
    Option,
    DatePicker,
    Input,
    Checkbox,
    CheckboxGroup,
} from 'element-ui';
import {computed, ref, watch} from 'vue';
import StakeSelect from './StakeSelect.vue';
import {Icon, BubbleMarker, IconMarker} from '@/components/Common';
import {useUnit, viewTo} from '@/utils';
import ApiSelect from '@/components/Common/Form/ApiSelect.vue';
import {getHighSpeedInfoCache} from '../index.js';
import {engine} from '@/store/engine';
import {isEqual, cloneDeep} from 'lodash-es';
import {
    domManager,
    iconManager,
} from '@/views/Construction/utils';
import {
    eventTypeOptions,
    evnetLocationTypeOptions,
    evnetDirectionOptions,
    closeLaneOptions,
    areaSelectOption,
} from '../../../config';
import {baseDefaultInfo} from '../config';
import {
    getPointsElevation,
    getStakeNumber,
    getEmulationRange,
    getConstructionGeo,
} from '@/api/emulation';
import Area from '@/views/Construction/components/Visualization/components/ConstructionMarker/Area.vue';

export default {
    components: {
        [Form.name]: Form,
        [FormItem.name]: FormItem,
        [Radio.name]: Radio,
        [RadioGroup.name]: RadioGroup,
        [Row.name]: Row,
        [Col.name]: Col,
        [Select.name]: Select,
        [Option.name]: Option,
        StakeSelect,
        [DatePicker.name]: DatePicker,
        [Input.name]: Input,
        Icon,
        [Checkbox.name]: Checkbox,
        [CheckboxGroup.name]: CheckboxGroup,
        ApiSelect,
        BubbleMarker,
        IconMarker,
        ConstructionArea: Area,
    },
    props: {
        // 表单信息，支持sync修饰符
        info: {
            type: Object,
            default: () => ({}),
        },
        emulationType: {
            type: Number,
            default: 2,
        },
    },
    setup(props, {emit}) {
        const {ratio} = useUnit();
        let resetPosition = true;
        // 表单信息
        const formInfo = ref(cloneDeep(baseDefaultInfo));
        // 是否开启定位
        const isOpenPosition = ref(false);
        const labelWidth = computed(() => `${160 * ratio.value}px`);
        const eventMarkerHeight = ref([]);
        const tempPosition = ref([]);
        const getMarkerPosition = computed(() => {
            const {eventPosition} = formInfo.value;
            if (!eventPosition.length) return;
            return [...eventPosition.split(','), eventMarkerHeight.value];
        });
        const getIconPosition = computed(() => {
            if (!tempPosition.value.length) return;
            return [...tempPosition.value, eventMarkerHeight.value];
        });
        // 施工区域坐标点集合
        const getConstructionList = computed(() => {
            const {constructionGeoList = []} = formInfo.value;
            return constructionGeoList.map(item => {
                const formatList = item.map(item => item.split(','));
                const [first] = formatList;
                first && formatList.push(first);
                return formatList;
            });
        });

        // 监听外部信息变化，修改表单信息
        watch(
            () => props.info,
            newVal => {
                // 值相等不处理
                if (isEqual(newVal, formInfo.value)) return;
                if (newVal.eventPosition) {
                    resetPosition = false;
                }
                formInfo.value = newVal;
            },
            {
                deep: true,
                immediate: true,
            }
        );

        // 监听表单信息变化，修改外部信息
        watch(
            () => formInfo.value,
            newVal => {
                // 值相等不处理
                if (isEqual(newVal, props.info)) return;
                emit('update:info', newVal);
            },
            {
                deep: true,
                immediate: true,
            }
        );

        // 监听事件位置变化，获取高程并切换视角
        watch(
            () => formInfo.value.eventPosition,
            async newVal => {
                if (!newVal) return;
                const position = newVal.split(',');
                // 获取高程
                const {data} = await getPointsElevation(position);
                eventMarkerHeight.value = data[0];
                viewTo({
                    zoom: 15,
                    center: getMarkerPosition.value,
                }, 1000);
            },
            {
                immediate: true,
            }
        );

        // 获取施工区域
        watch(
            () => [
                formInfo.value.eventPosition,
                formInfo.value.closeLane,
                formInfo.value.upstreamTransitionLength,
                formInfo.value.constructionLength,
                formInfo.value.downstreamTransitionLength,
            ],
            async (newVal, oldVal) => {
                // 参数为空或者相等不处理
                if (!newVal.length || isEqual(newVal, oldVal)) return;
                // 有效参个数
                const effectiveSize = newVal.filter(i => {
                    return i !== undefined && i !== '';
                }).length;
                if (newVal.length !== effectiveSize) return;
                const [
                    location,
                    closeLane,
                    upstreamTransitionLength,
                    constructionLength,
                    downstreamTransitionLength,
                ] = newVal;
                const {data} = await getConstructionGeo({
                    location,
                    closeLane: closeLane.join(','),
                    upstreamTransitionLength,
                    constructionLength,
                    downstreamTransitionLength,
                });
                formInfo.value = {
                    ...formInfo.value,
                    constructionCornerGeoList: data.constructionCornerGeoList,
                    constructionGeoList: data.constructionGeoList,
                };
            },
            {
                deep: true,
                immediate: true,
            }
        );

        async function handleEventStakeChange({position, value}) {
            const {data} = await getEmulationRange({
                highSpeedName: formInfo.value.highSpeedName1,
                stakeNumber: value,
            });
            const newInfo = {
                ...formInfo.value,
                highSpeedName: data.highSpeedName,
                emulationStartStake: data.emulationStartStake,
                emulationEndStake: data.emulationEndStake,
                eventStartStake: value,
            };
            if (resetPosition) {
                newInfo.eventPosition = position.join(',');
            }
            else {
                resetPosition = true;
            }
            formInfo.value = newInfo;
        }

        // 点击地图高精路面
        function handleClickMap3DRoad(e) {
            if (e.object?.is3DTiles) {
                const {point} = e;
                const [lng, lat] = point;
                tempPosition.value = [lng, lat];
            }
        }

        // 打开自定义定位
        function handleOpenPosition() {
            const {eventPosition} = formInfo.value;
            eventPosition
                && viewTo({
                    zoom: 15,
                    center: eventPosition.split(','),
                }, 1000); ;
            isOpenPosition.value = true;
            document.querySelector('.mapv-canvas').style.cursor = 'crosshair';
            // 绑定点击地图高精路面事件
            engine.value.event.bind('click', handleClickMap3DRoad);
        }

        // 点击确定定位
        async function handleConfirmPosition() {
            isOpenPosition.value = false;
            document.querySelector('.mapv-canvas').style.cursor = 'default';
            engine.value.event.unbind('click', handleClickMap3DRoad);
            if (!tempPosition.value.length) return;
            const {data = {}} = await getStakeNumber(...tempPosition.value);
            formInfo.value = {
                ...formInfo.value,
                direction: data?.querySumoEdgeDto.direction || 1,
                highSpeedName1: data.roadName,
                eventStartStake: data?.stakeNumber,
                eventPosition: tempPosition.value.join(','),
            };
            tempPosition.value = [];
        }

        function changeArea(e) {
            if (e === 2) {
                viewTo({
                    zoom: 15,
                    pitch: 15,
                    center: getMarkerPosition.value,
                }, 1000);
                emit('customeArea');
            }
        }

        return {
            formInfo,
            areaSelectOption,
            labelWidth,
            getHighSpeedInfoCache,
            eventTypeOptions,
            evnetLocationTypeOptions,
            handleEventStakeChange,
            isOpenPosition,
            handleOpenPosition,
            handleConfirmPosition,
            evnetDirectionOptions,
            closeLaneOptions,
            domManager,
            iconManager,
            getMarkerPosition,
            getIconPosition,
            getConstructionList,
            changeArea,
        };
    },
};
</script>

<style lang="less" scoped>
.emulation {
    &-form {
        &__title {
            position: relative;
            margin-bottom: 18px;
            padding-left: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 20px;

            &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                margin-top: -9px;
                width: 3px;
                height: 18px;
                background-color: rgba(26, 228, 255, .9);
            }
        }

        /deep/ .el-form {
            &-item {
                margin-bottom: 24px;

                &__label {
                    text-align: left;
                }
            }
        }

        /deep/ .el-radio {
            &:not(:last-of-type) {
                margin-right: 18px;
            }

            &__label {
                padding-left: 2px;
            }
        }

        /deep/ .el-input__inner {
            &[type="number"] {
                &::-webkit-inner-spin-button,
                &::-webkit-outer-spin-button {
                    -webkit-appearance: none;
                    margin: 0;
                }
            }
        }

        .full-content {
            width: 100%;
        }

        .form-position {
            display: flex;

            &__input {
                flex: 1;
            }

            &__btn {
                display: flex;
                align-items: center;
                height: 32px;
                padding: 0 8px;
                border: 1px solid rgba(#fff, .5);
                margin-left: 8px;
                color: #fff;
                cursor: pointer;
                user-select: none;
            }
        }

        .mb-0 {
            margin-bottom: 0 !important;
        }
    }
}
</style>