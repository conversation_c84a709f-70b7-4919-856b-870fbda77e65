<template>
    <div class="relict-statistics">
        <center-pie-dom
            v-if="show" :list="list"
            :normal="false"
        />
    </div>
</template>

<script>
import {CenterPieDom} from '@/components/Common';
import {ref, onMounted} from 'vue';
import {scatteredCount} from '@/api/equipment/facilitydisplay';
export default {
    name: '路面遗撒物统计',
    components: {
        CenterPieDom,
    },
    setup() {
        const list = ref([
            {name: '塑料袋子', value: 0, ratio: 0, color: 'rgb(208, 208, 208)', type: 'Bag'},
            {name: '石子', value: 0, ratio: 0, color: 'rgb(85, 218, 218)', type: 'Stones'},
            {name: '盒子', value: 0, ratio: 0, color: 'rgb(68, 218, 30)', type: 'Box'},
            {name: '瓶子', value: 0, ratio: 0, color: 'rgb(252, 255, 149)', type: 'Bottle'},
            {name: '纸屑', value: 0, ratio: 0, color: 'rgb(85, 218, 218)', type: 'Paper'},
            {name: '树叶', value: 0, ratio: 0, color: 'rgb(0, 176, 255)', type: 'Leaf'},
        ]);

        const show = ref(false);

        const init = () => {
            scatteredCount().then(res => {
                list.value = list.value.map(item => {
                    return {
                        ...item,
                        value: item.type === 'Paper'
                            ? res.data['scattered' + item.type] + res.data.scatteredBits
                            : res.data['scattered' + item.type],
                        ratio: item.type === 'Paper'
                            ? res.data[item.type.toLowerCase() + 'Proportion'] / 100 + res.data.bitsProportion / 100
                            : res.data[item.type.toLowerCase() + 'Proportion'] / 100,
                    };
                });
                show.value = true;
            });
        };

        onMounted(() => {
            init();
        });

        return {
            list,
            show,
        };
    },
};
</script>

<style lang="less" scoped>
.relict-statistics {
    width: 100%;
    height: 316px;

    :deep(.m) {
        width: 140px;
    }
}
</style>