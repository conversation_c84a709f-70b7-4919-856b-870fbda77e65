<template>
    <div
        v-if="value" class="detail-list"
        :style="{'--width': width * ratio + 'px'}"
    >
        <div class="detail-list-header">
            <div v-if="!$slots.title" class="detail-list-header-title">{{ title }}</div>
            <slot v-else name="title"></slot>
            <div
                v-if="showClose" class="detail-list-header-close"
                @click.stop="close"
            >
                <template v-if="!$slots.close">
                    <i class="el-icon-close"></i>
                    <span>关闭</span>
                </template>
                <template v-else>
                    <slot name="close"></slot>
                </template>
            </div>
        </div>

        <div class="detail-list-content">
            <div class="detail-list-content-list">
                <div
                    v-for="(item,index) in list" :key="index"
                    class="detail-list-content-item"
                >
                    <div class="icon-label">
                        <div v-if="item.icon" class="item-icon">
                            <icon :name="item.icon"/>
                        </div>
                        <span class="label">{{ item.label }}</span>
                    </div>
                    <span class="value" :class="{...item.ortherClass}">{{ item.value }}</span>
                </div>
            </div>

            <div
                v-if="showVideo" class="detail-list-content-video"
                :style="{'--height': height + 'px'}"
            >
                <flvPlayer v-if="videoAddress" :camera-info-id="videoAddress"/>
                <no-data v-else/>
            </div>
        </div>
    </div>
</template>

<script>
import {useUnit} from '@/utils';
import flvPlayer from '../video/flvPlayer.vue';
import NoData from '../NoData/index.vue';
import Icon from '../Icon/index.vue';
import {computed} from 'vue';
export default {
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: '',
        },
        width: {
            type: [String, Number],
            default: '629',
        },
        list: {
            type: Array,
            default: () => ([]),
        },
        videoAddress: {
            type: String,
            default: '',
        },
        showVideo: {
            type: Boolean,
            default: false,
        },
        showClose: {
            type: Boolean,
            default: true,
        },
    },
    components: {
        flvPlayer,
        Icon,
        NoData,
    },
    model: {
        prop: 'value',
        event: 'update:value',
    },
    setup(props, {emit}) {
        const {ratio} = useUnit();
        const height = computed(() => {
            return props.list.length * 32 * ratio.value;
        });

        const close = () => {
            emit('update:value', false);
            emit('close');
        };
        return {
            ratio,
            height,
            close,
        };
    },
};
</script>

<style lang="less" scoped>
.detail-list {
    width: var(--width);
    background-color: rgba(8, 35, 79, .8);
    backdrop-filter: blur(10px);

    .detail-list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: 'OPlusSans';
        font-weight: 500;
        background-color: rgba(36, 104, 242, .3);
        height: 58px;
        border-top: 10px solid rgba(77, 135, 255, .3);
        border-bottom: 1px solid rgba(255, 255, 255, .1);
        padding: 0 20px;

        &::before {
            content: '';
            position: absolute;
            top: 3px;
            left: 8px;
            width: 16px;
            height: 4px;
            background-image: linear-gradient(to right, rgb(46, 210, 255) 4px, transparent 2px);
            background-size: 6px 100%;
        }

        &::after {
            content: '';
            position: absolute;
            top: 4px;
            right: 8px;
            width: 72px;
            height: 2px;
            background-color: rgb(46, 210, 255);
        }

        .detail-list-header-title {
            font-size: 20px;
        }

        .detail-list-header-close {
            font-size: 12px;
            color: rgba(255, 255, 255, .6);
            cursor: pointer;

            span {
                margin-left: 6px;
            }
        }
    }

    .detail-list-content {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        padding: 8px;

        .detail-list-content-list {
            width: 100%;

            .detail-list-content-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 36px;
                font-size: 16px;
                padding: 0 12px;
                background-color: rgba(0, 98, 167, .15);
                margin-bottom: 4px;

                &:last-child {
                    margin-bottom: 0;
                }

                .icon-label {
                    display: flex;
                    align-items: center;

                    .item-icon {
                        width: 31px;
                        height: 24px;
                        line-height: 21px;
                        text-align: center;
                        margin-right: 9px;
                        background: url('../../../assets/images/base/iconBg.png') no-repeat center center / 100%;
                    }
                }

                span {
                    font-family: 'OPlusSans';
                    font-size: 14px;

                    &.label {
                        font-weight: 500;
                        color: rgba(255, 255, 255, .6);
                    }

                    &.value {
                        font-weight: 400;

                        &.stake {
                            font-family: 'Neue';
                            font-size: 16px;
                        }

                        &.operation {
                            background-color: rgba(77, 232, 0, .6);
                            padding: 4px 8px;
                        }
                    }
                }
            }
        }

        .detail-list-content-video {
            width: 100%;
            height: 200px;
            padding: 14px;
            margin-top: 10px;
            background-color: rgba(255, 255, 255, .08);
        }
    }
}
</style>