export const tooltipFormatter = params => {
    const name = params[0].name;
    const dom = params.reduce((fragment, e) => {
        return fragment + `
            <div class="fs-12">
                <div class="fr ac gap-4">
                    <p class="dib h-12 o-60 lh-12">${e.seriesName}</p>
                    <p class="dib h-12 b lh-12">${e.value}</p>
                </div>
            </div>`;
    }, '');
    return `
        <div class="fc gap-4">
            <p class="h-13 fs-14 b lh-13">${name}</p>
            ${dom}
        </div>
    `;
};

export const singleTooltipFormatter = params => {
    const name = params[0].name;
    const dom = params.reduce((fragment, e) => {
        return fragment + `
            <div class="fr ac gap-4 lh-26 fs-18">
                <p class="label dib o-70 mb-0">${e.seriesName}</p>
                <p class="value dib b mb-0">${e.value}</p>
            </div>`;
    }, '');
    return `
        <div class="fc gap-4">
            <p class="label fs-18 lh-26 b o-70 mb-0">${name}</p>
            ${dom}
        </div>
    `;
};