import {addText, removeText} from '../index';
import {getPointHeight} from '@/utils';
// 文字管理器
class TextManager {
    constructor() {
        this.textMap = new Map();
        this.setHaveMap = new Map();
    }
    async addText(name, point, text, options = {}) {
        if (this.textMap.has(name)) {
            this.removeTextByName(name);
        }
        if (this.setHaveMap.has(name)) {
            this.setHaveMap.get(name)();
        }
        const next = await this.initHeight(name, point);
        if (!next) return;
        let {_text, _engine} = addText(point, text, options);
        this.textMap.set(name, _text);
    }

    // 初始化高程
    async initHeight(name, point) {
        if (!point[2]) {
            // 解决请求height时await阻塞导致再次添加时无法清除扎点
            let have = true;
            this.setHaveMap.set(name, () => {
                have = false;
            });
            const height = await getPointHeight(point);
            point[2] = height?.data?.[0] || 0;
            if (have) {
                this.setHaveMap.delete(name);
            }
            return have;
        }
        return true;
    }

    removeTextByName(name) {
        const text = this.textMap.get(name);
        text && removeText(text);
        this.textMap.delete(name);
    }

    clear() {
        [...this.textMap.keys()].forEach(text => {
            this.removeTextByName(text);
        });
        this.textMap.clear();
    }
}

export {
    TextManager,
};