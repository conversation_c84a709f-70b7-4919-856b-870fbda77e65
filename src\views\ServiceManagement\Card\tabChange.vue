<template>
    <div class="tab-change">
        <div :class="{'tab-change__left': true, 'active': value === 'SD101'}" @click="changeTab('SD101')">
            东区
        </div>
        <div :class="{'tab-change__right': true, 'active': value === 'SD201'}" @click="changeTab('SD201')">
            西区
        </div>
    </div>
</template>

<script>
import {
    mapFilterData, mapFilterList,
} from '@/views/EquipmentFacilities/utils';

export default {
    name: '',
    props: {
        value: {
            type: String,
            default: 'Equipment',
        },
    },
    model: {
        prop: 'value',
        event: 'update:value',
    },
    setup(props, {emit}) {
        const changeTab = type => {
            emit('update:value', type);
            emit('click', type);
        };

        return {
            changeTab,
        };
    },
};
</script>

<style lang="less" scoped>
.tab-change {
    display: flex;
    align-items: center;
    position: fixed;
    top: 180px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 50;

    &__left,
    &__right {
        display: flex;
        align-items: center;
        width: 180px;
        height: 40px;
        margin-right: 6px;
        font-size: 24px;
        font-weight: 500;
        font-family: 'PingFang';
        color: rgba(#fff, .7);
        text-shadow: 0 2px 0 0 rgba(0, 0, 0, .15);
        cursor: pointer;
    }

    &__left {
        background: url('@/views/EquipmentFacilities/images/left_inactive.png') no-repeat center center / 100%;
        justify-content: flex-end;
        padding-right: 32px;

        &.active {
            background-image: url('@/views/EquipmentFacilities/images/left_active.png');
            color: rgba(#fff, 1);
        }
    }

    &__right {
        background: url('@/views/EquipmentFacilities/images/right_inactive.png') no-repeat center center / 100%;
        padding-left: 32px;

        &.active {
            background-image: url('@/views/EquipmentFacilities/images/right_active.png');
            color: rgba(#fff, 1);
        }
    }
}
</style>