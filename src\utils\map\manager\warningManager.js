import {addBubble, removeBubble, addLabel, removeLabel, addIcon, removeIcon,
    addCircle, removeCircle, addText, removeText} from '../index';
import {getPointHeight} from '@/utils';
// 告警管理器 包含事件 构造物等告警展示
class WarningManager {
    constructor(engine) {
        this.warningeMap = new Map();
        this.setHaveMap = new Map();
        this.engine = engine;
    }
    async addWarningPoint(name, point, options) {
        if (this.warningeMap.has(name)) {
            this.removeWarningPointByName(name);
        }
        if (this.setHaveMap.has(name)) {
            this.setHaveMap.get(name)();
        }
        const next = await this.initHeight(name, point);
        if (!next) return;

        const {
            labelText,
            iconType,
            labelUrl = 'maplayer/assets/image/structure_warning/label.png',
            customData,
            bubbleColor = 'rgba(67, 36, 48, 1)',
            text = '',
            circleColor,
            circleBorderColor,
            clickCallback,
        } = options || {};

        // 气泡点
        let iconUrl = 'maplayer/assets/image/structure_warning/' + iconType + '.png';
        let {bubble} = addBubble(point, {
            size: 60,
            color: bubbleColor,
            type: 'Wave',
            _engine: this.engine,
        });
        // 右侧文字label
        let {label} = addLabel(point, labelText, {
            offset: [130, -68],
            padding: [13, 0, 2, 40],
            width: 201,
            height: 42,
            fontSize: 17,
            customData,
            background: labelUrl,
            _engine: this.engine,
        });

        // icon
        let {icon, _engine} = addIcon(point, iconUrl, {
            width: 48,
            height: 83,
            offset: [0, -50],
            customData,
            _engine: this.engine,
        });

        // 告警数字圆
        let {circle} = text && addCircle(point, {
            customData,
            color: circleColor,
            borderColor: circleBorderColor,
            _engine: this.engine,
        }) || {};
        // 告警数字
        let {_text} = text && addText(point, text, {
            customData,
            _engine: this.engine,
        }) || {};

        if (clickCallback && typeof clickCallback === 'function') {
            icon.receiveRaycast = true;
            label.receiveRaycast = true;
            _engine.event.bind(icon, 'click', clickCallback);
            _engine.event.bind(label, 'click', clickCallback);
        };

        this.warningeMap.set(name, {
            'Bubble': bubble, // 气泡点
            'Label': label, // 文字 label
            'Icon': icon, // icon
            'Text': _text, // 告警数字
            'Circle': circle, // 告警数字圆
        });
    }

    // 初始化高程
    async initHeight(name, point) {
        if (!point[2]) {
            // 解决请求height时await阻塞导致再次添加时无法清除扎点
            let have = true;
            this.setHaveMap.set(name, () => {
                have = false;
            });
            const height = await getPointHeight(point);
            point[2] = height?.data?.[0] || 0;
            if (have) {
                this.setHaveMap.delete(name);
            }
            return have;
        }
        return true;
    }

    removeWarningPointByName(name) {
        const warning = this.warningeMap.get(name);
        warning && removeBubble(warning.Bubble, this.engine);
        warning && removeLabel(warning.Label, this.engine);
        warning && removeIcon(warning.Icon, this.engine);
        warning && removeText(warning.Text, this.engine);
        warning && removeCircle(warning.Circle, this.engine);
        this.warningeMap.delete(name);
    }

    clear() {
        [...this.warningeMap.keys()].forEach(macro => {
            this.removeWarningPointByName(macro);
        });
        this.warningeMap.clear();
    }
}

export {
    WarningManager,
};