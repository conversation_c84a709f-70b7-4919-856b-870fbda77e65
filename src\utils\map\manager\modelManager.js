import {addModel, removeModel} from '../index';
import {getPointHeight} from '@/utils';
// 模型管理器
class ModelManager {
    constructor() {
        this.modelMap = new Map();
        this.setHaveMap = new Map();
    }
    async addModel(name, url = 'maplayer/assets/models/car-impact.glb', position, scale = 7, callback) {
        if (this.setHaveMap.has(name)) {
            this.setHaveMap.get(name)();
        }
        const next = await this.initHeight(name, position);
        if (!next) return;
        addModel(url, position, scale, model => {
            this.modelMap.set(name, model);
            callback && callback(model);
        });
    }

    // 初始化高程
    async initHeight(name, point) {
        if (!point[2]) {
            // 解决请求height时await阻塞导致再次添加时无法清除扎点
            let have = true;
            this.setHaveMap.set(name, () => {
                have = false;
            });
            const height = await getPointHeight(point);
            point[2] = height?.data?.[0] || 0;
            if (have) {
                this.setHaveMap.delete(name);
            }
            return have;
        }
        return true;
    }

    removeModelByName(name) {
        const mmodel = this.modelMap.get(name);
        mmodel && removeModel(mmodel);
        this.modelMap.delete(name);
    }
    getModelByName(name) {
        return this.modelMap.get(name);
    }

    clear() {
        [...this.modelMap.keys()].forEach(text => {
            this.removeModelByName(text);
        });
        this.modelMap.clear();
    }
}

export {
    ModelManager,
};