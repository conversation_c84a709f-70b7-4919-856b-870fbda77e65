<template>
    <div ref="emulationChartRef" class="emulation-chart"></div>
</template>

<script>
import {nextTick, ref, watch} from 'vue';
import {useEcharts} from '@/hooks/useEcharts';
import {useUnit} from '@/utils';
import dayjs from 'dayjs';

export default {
    props: {
        dataSource: Array,
        startTime: String,
        endTime: String,
        eventStartTime: String,
    },
    setup(props) {
        const emulationChartRef = ref();

        const {ratio} = useUnit();

        const {
            setOptions,
            echarts,
        } = useEcharts(emulationChartRef);

        function updateCharts() {
            const {
                dataSource,
                startTime,
                endTime,
                eventStartTime,
            } = props;
            const startTimeUnix = dayjs(startTime).valueOf();
            const endTimeUnix = dayjs(endTime).valueOf();
            const eventTimeUnix = dayjs(eventStartTime).valueOf();
            const options = {
                tooltip: {
                    trigger: 'axis',
                    textStyle: {
                        fontSize: 12 * ratio.value,
                    },
                    padding: 4 * ratio.value,
                    formatter(params) {
                        const {data, seriesName} = params[0];
                        const [timeUnix, length] = data;
                        const timeDayjs = dayjs(timeUnix).format('HH:mm:ss');
                        return `
                            <div style="font-family: 'OPlusSans'; color: #fff;">
                                <div style="font-size: ${12 * ratio.value}px; font-weight: 700;">
                                    ${timeDayjs}
                                </div>
                                <div style="font-size: ${10 * ratio.value}px;">
                                    <span style="color: rgba(255,255,255, .6);">${seriesName}</span>
                                    <span style="font-weight: 700; margin-left: ${4 * ratio.value}px">${length}</span>
                                </div>
                            </div>
                        `;
                    },
                    position(point) {
                        return point.map(item => item + 16 * ratio.value);
                    },
                },
                legend: {
                    data: ['优化前排队长度', '优化后排队长度'],
                    top: 2 * ratio.value, // 设置图例显示在顶部
                    left: 'right', // 设置图例水平居中
                    textStyle: {
                        color: '#fff',
                        fontSize: 12 * ratio.value,
                        fontWeight: 400,
                        fontFamily: 'OPlusSans',
                    },
                    itemWidth: 25 * ratio.value,
                    itemHeight: 14 * ratio.value,
                },
                grid: {
                    left: '0%',
                    right: '0%',
                    top: 40 * ratio.value,
                    bottom: '0%',
                    containLabel: true,
                },
                xAxis: {
                    min: startTimeUnix,
                    max: endTimeUnix,
                    type: 'time',
                    boundaryGap: false,
                    axisLabel: {
                        fontSize: 12 * ratio.value,
                        formatter: {
                            hour: '{hour|{HH}:{mm}}',
                        },
                        rich: {
                            hour: {
                                fontSize: 12 * ratio.value,
                            },
                        },
                    },
                },
                yAxis: {
                    type: 'value',
                    minInterval: 1, // 设置y轴的最小间隔为1
                    axisLabel: {
                        fontSize: 12 * ratio.value,
                    },
                },
                series: [
                    {
                        name: '优化前排队长度',
                        type: 'line',
                        stack: 'Total',
                        data: dataSource?.[0]?.map(item => [item.time, item.queueLength]),
                        itemStyle: {
                            color: '#FF9B3D',
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {
                                    offset: 0,
                                    color: 'rgba(255, 155, 61, 0.3)',
                                },
                                {
                                    offset: 1,
                                    color: 'rgba(255, 155, 61, 0.1)',
                                },
                            ]),
                        },
                        markLine: {
                            label: {
                                show: true,
                                color: '#fff',
                                borderColor: 'rgb(98, 255, 191)',
                                borderType: 'solid',
                                borderWidth: 2 * ratio.value,
                                height: 22 * ratio.value,
                                lineHeight: 24 * ratio.value,
                                fontSize: 12 * ratio.value,
                                padding: [0, 12 * ratio.value],
                                distance: 0,
                            },
                            lineStyle: {
                                type: 'solid',
                                width: 2 * ratio.value,
                            },
                            symbolSize: 0,
                            data: [
                                // [
                                //     {
                                //         name: '方案下发',
                                //         coord: [eventTimeUnix, 0],
                                //         lineStyle: {
                                //             color: '#3AD482',
                                //         },
                                //         label: {
                                //             borderColor: '#3AD482',
                                //         },
                                //     },
                                //     {
                                //         coord: [eventTimeUnix, 60],
                                //     },
                                // ],
                                // [
                                //     {
                                //         name: '事件发生',
                                //         coord: [eventTimeUnix, 0],
                                //         lineStyle: {
                                //             color: '#FF4040',
                                //         },
                                //         label: {
                                //             borderColor: '#FF4040',
                                //         },
                                //     },
                                //     {
                                //         coord: [
                                //             eventTimeUnix,
                                //             Math.ceil(Math.max(...dataSource?.[0]?.map(item => item.queueLength))),
                                //         ],
                                //     },
                                // ],
                            ],
                        },
                    },
                    dataSource?.[1].length && {
                        name: '优化后排队长度',
                        type: 'line',
                        stack: 'Total',
                        data: dataSource?.[1]?.map(item => [item.time, item.queueLength]),
                        itemStyle: {
                            color: '#3AD482',
                        },
                        // areaStyle: {
                        //     color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        //         {
                        //             offset: 0,
                        //             color: 'rgba(58, 212, 130, 0.3)',
                        //         },
                        //         {
                        //             offset: 1,
                        //             color: 'rgba(58, 212, 130, 0.1)',
                        //         },
                        //     ]),
                        // },
                    },
                ],
            };
            setOptions(options);
        }

        watch(
            () => props.dataSource,
            () => {
                nextTick(() => {
                    updateCharts();
                });
            },
            {
                immediate: true,
                deep: true,
            }
        );

        return {
            emulationChartRef,
        };
    },
};
</script>

<style lang="less" scoped>
.emulation-chart {
    height: 150px;
}
</style>