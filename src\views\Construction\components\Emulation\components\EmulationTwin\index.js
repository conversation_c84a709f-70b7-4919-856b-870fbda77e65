import {ref} from 'vue';

export const emulationInfo = ref({
    uuid: '',
    schemeId: '',
    emulationIds: [],
    emulationStartTime: '',
    emulationEndTime: '',
    emulationStartStake: '',
    emulationEndStake: '',
    eventStartStake: '',
    eventStartTime: '',
    eventPosition: '',
    highSpeedName: '',
    constructionGeoList: [],
    emulationNowTimeUnix: 0,
    emulationBufferTimeUnix: 0,
});

export const emulationTargetData = ref({
    saturation: '-',
    avgSpeed: '-',
    serviceLevel: '-',
});

export const emulationPredictList = ref([]);

export const emulationStrategyList = ref([]);

export function resetEmulationData() {
    emulationInfo.value = {
        uuid: '',
        schemeId: '',
        emulationIds: [],
        emulationStartTime: '',
        emulationEndTime: '',
        eventStartStake: '',
        eventStartTime: '',
        eventPosition: '',
        highSpeedName: '',
        constructionGeoList: [],
        emulationNowTimeUnix: 0,
        emulationBufferTimeUnix: 0,
    };
    emulationTargetData.value = {
        saturation: '-',
        avgSpeed: '-',
        serviceLevel: '-',
    };
    emulationPredictList.value = [];
    emulationStrategyList.value = [];
}