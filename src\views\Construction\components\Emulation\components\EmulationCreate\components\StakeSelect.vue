<template>
    <div class="stake-select-wrapper">
        <el-select
            v-model="kmStake"
            size="small"
            class="stake-select"
            @change="handleKmStakeChange"
        >
            <el-option
                v-for="item in getKmStakeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
        </el-select>
        <el-select
            v-model="mStake"
            size="small"
            class="stake-select"
        >
            <el-option
                v-for="item in getMStakeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
        </el-select>
    </div>
</template>

<script>
import {
    Select,
    Option,
} from 'element-ui';
import {computed, ref, watch} from 'vue';
import {getStakenumberlistCache} from '..';

export default {
    components: {
        [Select.name]: Select,
        [Option.name]: Option,
    },
    props: {
        // k251+300形式
        value: String,
        highSpeedName: String,
        // 选项变化时，是否默认选中中间的桩号
        defaultSelectedMiddle: Boolean,
    },
    setup(props, {emit}) {
        // 千米桩
        const kmStake = ref('');
        // 米桩
        const mStake = ref('');
        const stakeOptions = ref([]);

        const getKmStakeOptions = computed(() => {
            return stakeOptions.value.reduce((acc, cur) => {
                const [km, m] = cur.stake.split('+');
                const find = acc.find(item => item.value === km);
                if (find) {
                    find.children.push({
                        label: m,
                        value: m,
                        position: cur.position,
                    });
                }
                else {
                    acc.push({
                        label: km,
                        value: km,
                        children: [
                            {
                                label: m,
                                value: m,
                                position: cur.position,
                            },
                        ],
                    });
                }
                return acc;
            }, []);
        });
        const getMStakeOptions = computed(() => {
            return getKmStakeOptions.value.find(item => item.value === kmStake.value)?.children || [];
        });
        const getStake = computed(() => {
            if (!kmStake.value || !mStake.value) return '';
            return `${kmStake.value}+${mStake.value}`;
        });

        async function fetchStakeOptions() {
            const highSpeedName = props.highSpeedName;
            if (!highSpeedName) return;
            const data = await getStakenumberlistCache(highSpeedName);
            stakeOptions.value = data.map(item => ({
                stake: item.stakeNumber,
                position: [item.longitude, item.latitude],
            }));
        }

        function handleKmStakeChange(value) {
            if (!value) return;
            mStake.value = getMStakeOptions.value?.[0].value;
        }

        // 监听prop.highSpeedName变化，更新选项
        watch(
            () => props.highSpeedName,
            () => {
                fetchStakeOptions();
            },
            {
                immediate: true,
            }
        );

        // 监听选项变化，props.defaultSelectedFirst为true时，默认选中中间的桩号
        watch(
            () => stakeOptions.value,
            newVal => {
                if (!props.defaultSelectedMiddle || props.value) return;
                if (!newVal?.length) return;
                const middle = ~~(newVal.length / 2);
                const {stake} = newVal[middle];
                const [km, m] = stake.split('+');
                kmStake.value = km;
                mStake.value = m;
            },
            {
                deep: true,
                immediate: true,
            }
        );

        // 内部kmStake和mStake变化，更新props.value
        watch(
            () => getStake.value,
            async (newVal, oldVal) => {
                // 相等不处理
                if (newVal === oldVal || !newVal) return;
                await fetchStakeOptions();
                const target = stakeOptions.value.find(item => item.stake === newVal);
                if (!target) return;
                emit('input', target.stake);
                emit('change', {
                    position: target.position,
                    value: target.stake,
                });
            }
        );

        // 监听props.value变化，更新kmStake和mStake
        watch(
            () => props.value,
            (newVal, oldVal) => {
                if (newVal === oldVal) return;
                const [km, m] = newVal.split('+');
                kmStake.value = km;
                mStake.value = m;
            },
            {
                immediate: true,
            }
        );

        return {
            kmStake,
            mStake,
            getKmStakeOptions,
            getMStakeOptions,
            handleKmStakeChange,
        };
    },
};
</script>

<style lang="less" scoped>
.stake-select {
    width: calc((100% - 16px) / 2);

    &-wrapper {
        display: flex;
        justify-content: space-between;
    }
}
</style>