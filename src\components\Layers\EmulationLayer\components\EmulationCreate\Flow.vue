<template>
    <div class="box">
        <div class="select-area">
            <span>流量输入方式：</span>
            <div class="item">
                <div
                    v-for="mode in modeList"
                    :key="mode.value"
                    class="select-item"
                    :class="{'active-item': inputMode === mode.value}"
                    @click="changeMode(mode.value)"
                >
                    <span class="ratio-icon"></span>
                    <span>{{ mode.name }}</span>
                </div>
            </div>
        </div>
        <div
            v-if="inputMode === 2"
            class="edit-item gap-8"
            :style="{
                'flex-wrap': 'wrap',
            }"
        >
            <span style="width: 100%;">查询时间区间：</span>
            <el-date-picker
                v-model="dateTimeRange"
                class="f-1 h-40"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="pickerOptions"
                @change="onDateChange"
            />
        </div>
        <div v-for="(item, index) in flowList" :key="item.seq">
            <div class="small-title">
                <span>{{ item.rampName }}({{ item.direction | formatDirection }})</span>
                <div>
                    <span
                        v-if="item.isEdit"
                        @click="addFlow(index)"
                    >
                        添加
                    </span>
                    <span @click="handleEdit(item)">{{ item.isEdit ? '保存' : '编辑' }}</span>
                    <span v-if="item.isEdit" @click="cancel(item)">取消</span>
                </div>
            </div>
            <div v-for="(i, j) in item.carInfoList" :key="i.seq">
                <div class="edit-item mt-16">
                    <span>流量(veh/h)：</span>
                    <div class="item">
                        <template v-if="item.isEdit">
                            <el-input-number
                                v-model="i.carFlow" :min="0"
                                label="描述文字"
                                style="width: 100%;"
                            />
                            <!-- <i class="el-icon-delete" @click="delFlow(index, j.seq ?? j)"></i> -->
                            <span class="icon-delete" @click="delFlow(index, j.seq ?? j)">删除</span>
                        </template>
                        <el-input
                            v-else
                            v-model="i.carFlow"
                            readonly
                        />
                    </div>
                </div>
                <div class="edit-item">
                    <span>开始时间：</span>
                    <el-date-picker
                        v-model="i.startTime"
                        type="datetime"
                        :readonly="!item.isEdit"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        popper-class="emulation-el-popper"
                        class="f-1 h-40"
                        placeholder="请选择事件开始时间"
                    />
                </div>
                <div
                    v-if="hasETC" class="edit-item mt-16"
                >
                    <span>ETC车辆比例：</span>
                    <div class="item">
                        <el-input
                            v-model="i.etcRatio"
                            :readonly="!item.isEdit"
                        >
                            <span slot="suffix" class="suffix-text">%</span>
                        </el-input>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import dayjs from 'dayjs';
import {cloneDeep} from 'lodash';
import {road_direction} from '@EmulationLayer/config';
import {Input, InputNumber, DatePicker} from 'element-ui';

const Mode = [
    {
        name: '在线生成',
        value: 1,
    },
    {
        name: '手动输入',
        value: 2,
    },
];
export default {
    name: 'FlowInfo',
    components: {
        [DatePicker.name]: DatePicker,
        [InputNumber.name]: InputNumber,
        [Input.name]: Input,
    },
    filters: {
        formatDirection(val) {
            const cur = road_direction.find(item => item.code === val);
            return cur ? cur.name : '';
        },
    },
    props: {
        hasETC: {
            type: Boolean,
            default: false,
        },
        flowInfo: {
            type: Array,
            default: () => [],
        },
        inputMode: {
            type: Number,
            default: 1,
        },
        dateRange: {
            type: Array,
            default: () => [
                dayjs().subtract(1, 'hours').format('YYYY-MM-DD HH:mm:ss'),
                dayjs().format('YYYY-MM-DD HH:mm:ss'),
            ],
        },
    },
    data() {
        return {
            dateTimeRange: this.dateRange,
            pickerOptions: {
                disabledDate(time) {
                    const maxToday = new Date();
                    maxToday.setHours(23, 59, 59, 999);
                    return time.getTime() > maxToday.getTime();
                },
            },
            flowList: [],
            editFLowItem: null,
            modeList: Object.freeze(Mode),
        };
    },
    watch: {
        flowInfo: {
            handler(v) {
                if (v !== this.flowList) {
                    this.flowList = v;
                }
            },
            immediate: true,
        },
    },
    methods: {
        onDateChange(value) {
            this.$emit('onDateChange', value);
        },
        changeMode(mode) {
            this.$emit('onChangeMode', mode);
        },
        cancel(item) {
            Object.assign(item, this.editFLowItem, {isEdit: false});
        },
        handleEdit(item) {
            this.editFLowItem = cloneDeep(item);
            this.$set(item, 'isEdit', !item.isEdit);
            if (!item.isEdit) {
                this.$emit('change', this.flowList);
            }
        },
        delFlow(index, flowIndex) {
            this.flowList[index].carInfoList.splice(flowIndex, 1);
        },
        addFlow(index) {
            this.flowList[index].carInfoList.push({
                seq: this.flowList[index].carInfoList.length - 1 || 1,
                carFlow: 0,
                startTime: '',
            });
        },
    },
};
</script>

<style lang="less" scoped>

.box {
    width: 100%;

    .small-title {
        font-size: 14px;
        color: #e5f1ff;
        position: relative;
        padding-left: 8px;
        display: flex;
        justify-content: space-between;

        &::before {
            content: '';
            width: 4px;
            height: 12px;
            background: #08d6a5;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translate(0, -50%);
        }

        > div {
            display: flex;
            gap: 8px;

            span {
                cursor: pointer;
                color: #08d6a5;
            }

            span:nth-child(1) {
                color: #08d6a5;
            }
        }
    }

    .add-btn {
        width: 100%;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid rgba(178, 214, 255, 1);
        border-radius: 2px;
        color: #b2d6ff;
        margin-bottom: 16px;
        cursor: pointer;
    }

    .select-area {
        padding: 22px 0;
        display: flex;
        margin-left: 10px;
        padding-top: 0 !important;
        align-items: center;

        > span {
            display: block;
            color: #c4cfde;
            width: 98px !important;
        }

        .item {
            flex: 1;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            // justify-content: space-between;

            .select-item {
                display: flex;
                align-items: center;
                cursor: pointer;
                color: #c4cfde;

                &.active-item {
                    .ratio-icon {
                        border: 1px solid #49c3ff;

                        &::before {
                            transform: scale(1);
                            transform: translate(-50%, -50%);
                        }
                    }
                }

                .ratio-icon {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 14px;
                    height: 14px;
                    border: 1px solid #98b8de;
                    border-radius: 50%;
                    margin-right: 8px;
                    margin-left: 16px;
                    transition: border .1s;
                    left: 0;
                    top: 0;

                    &::before {
                        content: '';
                        width: 8px;
                        height: 8px;
                        background: #49c3ff;
                        border-radius: 50%;
                        transform: scale(0);
                        transition: transform .1s ease-out;
                    }
                }
            }
        }
    }

    .edit-item {
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        margin-left: 10px;

        > span {
            display: inline-block;
            width: 98px;
            font-size: 14px;
            color: #c4cfde;
            letter-spacing: 0;
        }

        .edit-con {
            flex: 1;
            display: flex;
            // align-items: center;
            flex-wrap: wrap;

            > div {
                margin: 0 12px;
                display: flex;
                align-items: center;
                // margin-top: 16px;

                span:nth-child(1) {
                    display: inline-block;
                    width: 14px;
                    height: 14px;
                    border: 1px solid #98b8de;

                    &.selected {
                        background: #49c3ff;
                        position: relative;

                        &::before {
                            content: '✓';
                            color: rgba(10, 28, 53, 1);
                            position: absolute;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%, -50%);
                        }
                    }
                }

                span:nth-child(2) {
                    font-family: 'FZLTZHJW--GB1-0', sans-serif;
                    font-size: 14px;
                    color: #c4cfde;
                    margin-left: 8px;
                }

                .config {
                    margin-left: 5px !important;
                    color: #49c3ff !important;
                }
            }
        }

        .item {
            flex: 1;
            height: 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .reduce,
            .add {
                display: flex;
                width: 40px;
                height: 40px;
                background: rgba(26, 132, 255, .8);
                border: 1px solid rgba(52, 146, 255, 1);
                justify-content: center;
                align-items: center;
                font-size: 20px;
                color: #e5f1ff;
            }

            .reduce {
                border-radius: 2px 0 0 2px;
            }

            .add {
                border-radius: 0 2px 2px 0;
            }

            .icon-delete {
                font-size: 14px;
                color: #ff6565;
                cursor: pointer;
                width: 50px;
                padding-left: 10px;
            }
        }
    }
}

</style>