import {addPolygon, removePolygon} from '../index';
// Polygon管理器
class PolygonManager {
    constructor(engine) {
        this.polygonMap = new Map();
        this.engine = engine;
    }
    addPolygon(name, points, options) {
        if (this.polygonMap.has(name)) {
            this.removePolygonByName(name);
        }
        let _polygon = addPolygon(points, {
            ...options,
            _engine: this.engine,
        });
        this.polygonMap.set(name, _polygon);
    }
    removePolygonByName(name) {
        const polygon = this.polygonMap.get(name);
        polygon && removePolygon(polygon);
        this.polygonMap.delete(name);
    }

    clear() {
        [...this.polygonMap.keys()].forEach(text => {
            this.removePolygonByName(text);
        });
        this.polygonMap.clear();
    }
}

export {
    PolygonManager,
};