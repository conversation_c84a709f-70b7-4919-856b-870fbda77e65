export class Mitt {
    #map;

    constructor() {
        this.#map = new Map();
    }

    emit(event, ...args) {
        const callbackList = this.#map.get(event) || [];
        callbackList.forEach(cb => cb(...args));
    }

    on(event, callback) {
        const callbackList = this.#map.get(event) || [];
        callbackList.push(callback);
        this.#map.set(event, callbackList);
    }

    off(event, callback) {
        const callbackList = this.#map.get(event) || [];
        const findIndex = callbackList.findIndex(cb => cb === callback);
        callbackList.splice(findIndex, 1);
        this.#map.set(event, callbackList);
    }

    one(event, callback) {
        const callbackList = this.#map.get(event) || [];
        const self = this;
        const oneCallback = function (...args) {
            callback.apply(this, args);
            setTimeout(() => {
                self.off(event, oneCallback);
            });
        };
        callbackList.push(oneCallback);
        this.#map.set(event, callbackList);
    }
}
