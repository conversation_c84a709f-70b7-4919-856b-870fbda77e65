import {apiHost} from '@EmulationLayer/store/common';
import {request} from '@/utils/network-helper/index';
import {emulationConfig} from '@/utils/common';
const {basePrefix, baseApiHost} = emulationConfig;
const baseUrl = () => {
    return apiHost.value || baseApiHost || '';
};


// 高速的基础信息
export const getHighSpeedInfo = async _ => {
    const {data, code} = await request.get(`${baseUrl()}${basePrefix}/emulation/getHighSpeedInfo`);
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 获取桩号下拉列表
export const getstakenumberlist = async highSpeedName => {
    const {data, code} = await request.get(
        `${baseUrl()}${basePrefix}/emulation/getStakeNumberList?toCoordinateType=gcj02&highSpeedName=${highSpeedName}`
    );
    if (+code === 200 && data) {
        return {data, code};
    }
    return {};
};

// 交通事件列表查询
export const fetchList = async payload => {
    const {data, code} = await request.post(`${baseUrl()}${basePrefix}/event/list`, payload);
    if (+code === 200 && data) {
        return {data, code};
    }
    return {};
};

// 删除事件事件或者模板
export const delEvent = async payload => {
    const {data, code} = await request.get(`${baseUrl()}${basePrefix}/event/delete?id=${payload}`);
    if (+code === 200 && data) {
        return {data, code};
    }
    return {};
};


// 导入模板
export const importTemplate = async payload => {
    const {data, code} = await request.get(`${baseUrl()}${basePrefix}/event/importTmp?id=${payload}`);
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 编辑事件或者事件模板
export const updateEvent = async payload => {
    const {data, code} = await request.post(`${baseUrl()}${basePrefix}/event/modify`, payload);
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 新增事件模板
export const createEvent = async payload => {
    const {data, code} = await request.post(`${baseUrl()}${basePrefix}/event/addTmp`, payload);
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 通过经纬度信息获取桩号
export const getStakeInfo = async ({longitude, latitude, highSpeedName}) => {
    const {data, code} = await request.get(
        // eslint-disable-next-line max-len, vue/max-len
        `${baseUrl()}${basePrefix}/stakeNumber/getStakeNumber?longitude=${longitude}&latitude=${latitude}&highSpeedName=${highSpeedName}`
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 根据经纬度获取坐标高程信息
export const getPointsElevation = async payload => {
    const _baseUrl = ['test'].includes(import.meta.env.MODE) ? 'http://10.27.57.4:8211' : baseUrl();
    const {data}
        = await fetch(`${_baseUrl}/height/?x=${payload[0]}&y=${payload[1]}`,
            {
                method: 'GET',
            }).then(response => response.json());
    if (data) {
        return {
            data,
        };
    }
    return {};
};

// 唯一身份id
export const fetchUuid = async () => {
    const {data, code} = await request.get(`${baseUrl()}${basePrefix}/emulation/getUuid`);
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 仿真运行
export const emulationStart = async payload => {
    const {data, code} = await request.post(`${baseUrl()}${basePrefix}/scheme/run`, payload);
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 查看仿真策略
export const seeTravelResult = async payload => {
    const {id, uuid} = payload;
    const {data, code} = await request.get(`${baseUrl()}${basePrefix}/scheme/seeTravelResult?id=${id}&uuid=${uuid}`);
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 结束仿真
export const emulationEnd = async payload => {
    const {emulationId, schemeId, uuid} = payload;
    const url = `${baseUrl()}${basePrefix}/scheme/end?emulationId=${emulationId}&schemeId=${schemeId}&uuid=${uuid}`;
    const {data, code} = await request.get(url);
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 仿真评价查询
export const evaluateQuery = async payload => {
    const {data, code} = await request.post(`${baseUrl()}${basePrefix}/emulation/evaluateQuery`, payload);
    if (+code === 200) {
        return {data, code};
    }
    return {};
};


// 仿真列表查询
export const emulationList = async payload => {
    const {data, code} = await request.post(`${baseUrl()}${basePrefix}/emulation/schemeList`, payload);
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 新增仿真方案
export const createScheme = async payload => {
    const {data, code} = await request.post(`${baseUrl()}${basePrefix}/scheme/add`, payload);
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 删除仿真方案
export const delScheme = async payload => {
    const {data, code} = await request.get(`${baseUrl()}${basePrefix}/scheme/delete?id=${payload}`);
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 编辑仿真方案
export const updateScheme = async payload => {
    const {data, code} = await request.post(`${baseUrl()}${basePrefix}/scheme/modify`, payload);
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 复制仿真方案

export const copyScheme = async payload => {
    const {data, code} = await request.get(`${baseUrl()}${basePrefix}/scheme/copy?id=${payload}`);
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 仿真评价统计

export const evaluateStatistics = async (payload = 197) => {
    // eslint-disable-next-line max-len, vue/max-len
    const {data, code} = await request.get(`${baseUrl()}${basePrefix}/emulation/evaluateStatistics?schemeId=${payload}`);
    if (+code === 200) {
        return {data, code};
    }
    return {};
};


// 重置仿真方案的状态
export const resetEmulation = async payload => {
    const {data, code} = await request.get(`${baseUrl()}${basePrefix}/dev/resetScheme?id=${payload}`);
    if (+code === 200) {
        return {data, code};
    };
    return {};
};

// 仿真流量查询
export const fetchFlow = async payload => {
    const {data, code} = await request.post(`${baseUrl()}${basePrefix}/emulation/flowList`, payload);
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

export const getSumoStake = async ({highSpeedName}) => {
    // eslint-disable-next-line max-len, vue/max-len
    const {data, code} = await request.get(`${baseUrl()}${basePrefix}/emulation/getSumoStakeList?highSpeedName=${highSpeedName}`);
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 获取仿真桩号范围
export const getEmulationRange = async payload => {
    const {data, code} = await request.post(`${baseUrl()}${basePrefix}/emulation/getEmulationRange`, payload);
    if (+code === 200) {
        return {data, code};
    }
    return {};
};


// 出入口下拉选择
export const fetchGate = async payload => {
    const {data, code} = await request.post(`${baseUrl()}${basePrefix}/emulation/getTollList`, payload);
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 获取策略详情
export const getSchemeInfoById = async payload => {
    const {data, code} = await request.get(`${baseUrl()}${basePrefix}/emulation/schemeById?schemeId=${payload}`);
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 通过自定义区域获取仿真高速信息
export const customHighInfo = async payload => {
    const {data, code} = await request.post(`${baseUrl()}${basePrefix}/emulation/getCustomHighInfo`, {
        areaLocation: payload,
    });
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 获取仿真评价
export const getEvaluate = async payload => {
    // eslint-disable-next-line max-len, vue/max-len
    const {data, code} = await request.get(`${baseUrl()}${basePrefix}/emulation/statistics?schemeId=${payload.id}&stepIndex=${payload.step}`, {}, {
    });
    if (+code === 200) {
        return {data, code};
    }
    return {};
};


// 获取仿真报告
export const getEvaluateReport = async payload => {
    // eslint-disable-next-line max-len, vue/max-len
    const {data, code} = await request.post(`${baseUrl()}${basePrefix}/emulation/statistics`, payload, {
    });
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 获取施工渲染路段信息
export const getConstructionGeo = async payload => {
    // eslint-disable-next-line max-len, vue/max-len
    const {data, code} = await request.post(`${baseUrl()}${basePrefix}/emulation/getConstructionGeo`, payload, {
    });
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 根据经纬度获取高速路信息
export const getRoadInfoByLngAndLat = async (longitude, latitude) => {
    const params = `longitude=${longitude}&latitude=${latitude}`;
    const {data, code} = await request.get(
        `${baseUrl()}${basePrefix}/stakeNumber/getStakeNumber?${params}`,
        {},
        {
            extraInfo: {
                noGlobalLoading: true,
            },
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};