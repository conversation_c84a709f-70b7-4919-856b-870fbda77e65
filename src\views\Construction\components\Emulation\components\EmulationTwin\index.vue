<template>
    <div v-if="emulationInfo.emulationIds.length" class="emulation-twin">
        <!-- left -->
        <collapse-panel :collapse.sync="leftCollapse">
            <emulation-overview-card/>
            <emulation-run-card
                class="mt-24"
                @play="handlePlay"
                @pause="handlePause"
                @stop="handleStop"
                @changeTime="handleTimeChange"
            />
        </collapse-panel>
        <!-- right -->
        <collapse-panel direction="right" :collapse.sync="rightCollapse">
            <div style="display: flex;">
                <road-situation-card/>
                <suggest-card class="ml-24"/>
            </div>
        </collapse-panel>
        <!-- 事件扎点 -->
        <bubble-marker
            :manager-instace="domManager"
            bubble-type="diamond"
            bubble-color="#FF4B4B"
            icon-name="construction"
            :info="{
                position: emulationInfo.eventPosition,
            }"
        />
        <!-- 施工区域 -->
        <construction-area
            v-for="item in getConstructionList"
            :key="item"
            :list="item"
        />
    </div>
</template>

<script>
import {ref, onMounted, watch, computed, onBeforeUnmount} from 'vue';
import {
    getSchemeInfoById,
    seeTravelResult,
    getPointsElevation,
    runEmulation,
    endEmulation,
} from '@/api/emulation';
import {twinManager, domManager} from '@/views/Construction/utils';
import {viewTo} from '@/utils';
import {useWebSocket} from '@vueuse/core';
import dayjs from 'dayjs';
import {colorBaseArr1, colorBaseArr2} from '../../config';
import {engine} from '@/store/engine';
import {geojsonUtils} from '@baidu/mapv-three';
import {debounce} from 'lodash-es';
import {BubbleMarker, CollapsePanel} from '@/components/Common';
import
    ConstructionArea
    from '@/views/Construction/components/Visualization/components/ConstructionMarker/Area.vue';
import {emulationInfo, emulationPredictList, emulationStrategyList, resetEmulationData} from './index';
import {emulationConfig, messageTip} from '@/utils/common';
import EmulationOverviewCard from './EmulationOverviewCard/index.vue';
import EmulationRunCard from './EmulationRunCard/index.vue';
import RoadSituationCard from './RoadSituationCard/index.vue';
import SuggestCard from './SuggestCard/index.vue';
import {schemaInfo} from '../..';
import {useCollapsePanel} from '@/hooks/useCollapsePanel';

export default {
    components: {
        BubbleMarker,
        ConstructionArea,
        CollapsePanel,
        EmulationOverviewCard,
        EmulationRunCard,
        RoadSituationCard,
        SuggestCard,
    },
    setup(_, {emit}) {
        const EMULATION_TWIN_NAME = 'EMULATION_TWIN_NAME';

        const {
            basePrefix,
            baseWsHost,
        } = emulationConfig;

        twinManager.addTwin(EMULATION_TWIN_NAME);
        let twin = twinManager.getTwinByName(EMULATION_TWIN_NAME);

        const {collapse: leftCollapse} = useCollapsePanel();
        const {collapse: rightCollapse} = useCollapsePanel();

        const stakeList = ref([]);
        const cameraPosition = ref();

        // 施工区域坐标点集合
        const getConstructionList = computed(() => {
            const {constructionGeoList = []} = emulationInfo.value;
            return constructionGeoList?.map(item => {
                const formatList = item?.map(item => item.split(','));
                const [first] = formatList;
                first && formatList.push(first);
                return formatList;
            });
        });

        // 仿真ws
        const getWsUrl = computed(() => {
            const {
                emulationStartTime,
                emulationIds,
                uuid,
            } = emulationInfo.value;
            const startTime = dayjs(emulationStartTime).valueOf();
            const params = `emulationId=${emulationIds?.[0]}&startTime=${startTime}&uuid=${uuid}`;
            return `${baseWsHost}${basePrefix}/ws/imEmulationScheme?${params}`;
        });
        const {
            send: emulationSend,
        } = useWebSocket(getWsUrl, {
            immediate: false,
            onMessage(ws, {data}) {
                if (data === 'ok' || data === '{}') {
                    return;
                }
                try {
                    data = JSON.parse(data);
                    emulationInfo.value.emulationNowTimeUnix = data.timestamp;
                    let result = [];
                    data.data.forEach(item => {
                        let resultItem = {
                            timestamp: data.timestamp,
                        };
                        if (item.type === 1) {
                            item.color = colorBaseArr1[item.id.charAt(item.id.length - 1)];
                        }
                        else if (item.type === 2) {
                            item.color = colorBaseArr2[item.id.charAt(item.id.length - 1)];
                        }
                        result.push(resultItem);
                    });

                    twin?.push(data.data);
                }
                catch (e) {
                    messageTip(data);
                }
            },
        });


        // 路况预测ws
        const getPredictWsUrl = computed(() => {
            const {
                emulationIds,
                uuid,
            } = emulationInfo.value;
            const params = `emulationId=${emulationIds[0]}&uuid=${uuid}`;
            return `${baseWsHost}${basePrefix}/ws/imEmulationPredict?${params}`;
        });
        useWebSocket(getPredictWsUrl, {
            immediate: false,
            onMessage(ws, {data}) {
                if (data === 'ok' || data === '{}') {
                    return;
                }
                const {predict, strategyList} = JSON.parse(data);
                predict && emulationPredictList.value.push(predict);
                if (strategyList?.length) {
                    emulationStrategyList.value = strategyList;
                };
                emit('predict', predict);
            },
        });


        function handlePlay() {
            twin.start();
            emulationSend('normal');
        }

        function handlePause() {
            twin.pause();
            emulationSend('pause');
        }

        async function handleStop() {
            const {
                emulationIds,
                uuid,
                schemeId,
            } = emulationInfo.value;
            // twin.dispose();
            await endEmulation({
                emulationIds: emulationIds[0],
                uuid: uuid,
                schemeId: schemeId,
            });
            emit('stop');
        }

        function resetTwin() {
            twinManager.removeTwinByName(EMULATION_TWIN_NAME);
            twinManager.addTwin(EMULATION_TWIN_NAME);
            twin = twinManager.getTwinByName(EMULATION_TWIN_NAME);
        }

        function removeTwin() {
            twinManager.removeTwinByName(EMULATION_TWIN_NAME);
            twin = null;
        }

        async function initEmulation() {
            resetEmulationData();
            const {schemeId, uuid} = schemaInfo.value;
            const {data: schemeData} = await getSchemeInfoById(schemeId);
            const {eventList} = schemeData;
            const eventInfo = eventList[0];
            const eventPosition = eventInfo.eventPosition.split(',');
            // 获取高程
            try {
                const {data: heightData} = await getPointsElevation(eventPosition);
                eventPosition[2] = heightData[0];
            }
            catch {
                eventPosition[2] = 80;
            }
            // 获取仿真方案id，1待开始，3已完成
            const fetchEmulationIdApi = schemeData.status === 1 ? runEmulation : seeTravelResult;
            const {data: emulationData} = await fetchEmulationIdApi({
                uuid,
                id: schemeId,
            });
            const emulationIds = emulationData.emulationId || [];
            // 将无策略仿真id放置第一位
            emulationIds.unshift(emulationIds.pop());
            emulationInfo.value = {
                ...emulationInfo.value,
                uuid,
                schemeId,
                eventPosition,
                emulationIds,
                emulationStartTime: schemeData.emulationStartTime,
                emulationEndTime: schemeData.emulationEndTime,
                emulationStartStake: schemeData.emulationStartStake,
                emulationEndStake: schemeData.emulationEndStake,
                eventStartStake: eventInfo.eventStartStake,
                eventStartTime: eventInfo.eventStartTime,
                highSpeedName: eventInfo.highSpeedName,
                constructionGeoList: eventInfo.constructionGeoList,
            };
            // 切换视角至当前事件坐标
            viewTo({
                zoom: 15,
                center: eventPosition,
            });
        }

        function handleTimeChange(time) {
            // twin.reset();
            // twin.start();
            resetTwin();
            emulationSend(JSON.stringify({
                drag: time,
            }));
        }

        watch(
            () => cameraPosition.value,
            debounce(val => {
                if (!val) return;
                let params = JSON.stringify({
                    vehicleScope: {
                        longitude: val[0],
                        latitude: val[1],
                        scope: 1000,
                    },
                });
                emulationSend(params);
            }, 500)
        );

        function listenMapView(e) {
            if (e.rendering.renderState.viewChanged) {
                const {x, y, z} = e.camera.position;
                cameraPosition.value = geojsonUtils.unprojectPointArr([x, y, z]);
            }
        }

        onMounted(() => {
            initEmulation();
            engine.value.rendering.addPrepareRenderListener(listenMapView);
        });

        onBeforeUnmount(() => {
            removeTwin();
            engine.value.rendering.removePrepareRenderListener(listenMapView);
        });

        return {
            handlePlay,
            handlePause,
            handleStop,
            stakeList,
            domManager,
            getConstructionList,
            emulationInfo,
            handleTimeChange,
        };
    },
};
</script>

<style lang="less" scoped>
.emulation-twin {
    position: relative;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none !important;

    > * {
        pointer-events: auto;
    }

    &__bottom {
        position: absolute;
        bottom: 0;
        width: 100%;
    }

    &__charts {
        padding: 16px 20px;
        background-color: rgba(44, 44, 44, .8);
        backdrop-filter: blur(4px);
    }
}
</style>