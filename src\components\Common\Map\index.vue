<template>
    <div style="position: relative; width: 100%; height: 100%; overflow: hidden;">
        <div ref="map_container" style="width: 100%; height: 100%;"></div>
        <slot></slot>
    </div>
</template>

<script setup>
import {
    Engine,
    geojsonUtils,
} from '@baidu/mapv-three';
import {onMounted, ref, shallowRef, onBeforeUnmount} from 'vue';
import useAssets from './useAssets';
import useTimeAndSky from './useTime';
import useCamera from '@/utils/hooks/useCamera.js';
import useSatellite from './useSatellite.js';
import useTollStation from './useTollStation.js';
import {cameraHeight, mapCenter} from '@/store/engine';
import {usePavement} from './usePavement';

const props = defineProps({
    options: {
        type: Object,
        default: () => ({
            showAssetsScene: true,
        }),
    },
});

const emit = defineEmits(['mapClick', 'mapLoaded']);

const viewPosition = ref();
const map_container = shallowRef();

const map = shallowRef();
const sky = shallowRef();

const {center: cameraCenter} = useCamera(map, v => {
    cameraHeight.value = v.z;
});

// const {initWeather, setWeather} = useWeather();
const {initSky} = useTimeAndSky();

// 添加天空&天气
const addSkyAndWeather = () => {
    sky.value = initSky();
    window.sky = sky.value;
    // initWeather(window.ENGINE, sky.value);
};

// 是否加载设备资产
useAssets(map, props);
// 卫星影像
useSatellite(map, props);
// 收费站&服务区
useTollStation(map, props, cameraCenter);
// 高精路面
usePavement(map, props, emit);

// 初始化地图
const initMap = () => {
    if (!map_container.value) {
        return;
    }
    const engine = new Engine(map_container.value, {
        rendering: {
            enableAnimationLoop: true,
            preserveDrawingBuffer: true, // 只有开启后才可导出场景图片
        },
        controls: {
            // zoom: true, // 开启地图缩放控件
            // fullscreen: true, // 开启全屏控件
            // geoLocate: true, // 开启定位控件
            // exportImage: false, // 开启导出图片控件
            // map3dcontrol: true,
            compass: false, // 开启视角控件
            // mouseLocation: true, // 开启地理坐标显示
        },
        map: {
            is3DControl: true, // 移动端可控制
        },
    });
    const center = mapCenter.value;
    props.options?.center && engine.map.setCenter(center.slice(0, 2));
    const yaw = center[3] || 0;
    engine.map.setHeading(yaw);
    const pitch = center[4] || 50;
    engine.map.setPitch(pitch);
    const zoom = center[5] || 12;
    engine.map.setZoom(zoom);

    engine.rendering.shadow.enabled = true;
    engine.rendering.enableAnimationLoop = true;

    engine.rendering.addPrepareRenderListener(e => {
        if (e.rendering.renderState.viewChanged) {
            const {x, y, z} = e.camera.position;
            const transPosition = geojsonUtils.unprojectPointArr([x, y, z]);
            viewPosition.value = transPosition;
        }
    });


    map.value = engine;
    window.ENGINE = engine;

    // 加载天空 & 天气
    addSkyAndWeather();

    emit('mapLoaded', engine);
};

onMounted(() => {
    initMap();
});

onBeforeUnmount(() => {
    map.value?.dispose();
    map.value = null;
});

defineExpose({
    map,
});
</script>

<style lang="less" scoped>
    #map-effect {
        position: absolute;
        pointer-events: none;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        z-index: 11;
    }
</style>