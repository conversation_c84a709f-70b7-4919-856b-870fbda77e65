<template>
    <div>
        <!-- <BubbleMarker
            v-for="board in boardList"
            :key="board.assetsCode"
            :info="board"
            bubble-color="rgb(57, 182, 0)"
            icon-name="xianshiping"
            icon-color="#fff"
            :manager-instace="domManager"
        /> -->
    </div>
</template>

<script setup>
import {getAllInfoboard, getInfoboardContent} from '@/api/tollStation';
// import {BubbleMarker} from '@/components/Common';
// import {domManager} from '@/views/TollStation/utils';
import {onBeforeUnmount, onMounted, ref} from 'vue';
import {viewToPoint} from '@/utils';
import {assetsScene} from '@/store/engine';
import {useIntervalFn} from '@vueuse/core';

const props = defineProps({
    assetsScene: {
        type: Object,
        default: () => assetsScene.value,
    },
});

// 轮询间隔
const POLL_GAP_TIME = 1000 * 60 * 2;

const boardList = ref([]);

const boardContentMap = {};

const {resume} = useIntervalFn(
    () => {
        const models = props.assetsScene.models;
        // 轮询不使用缓存
        setBoardContent(models, false);
    },
    POLL_GAP_TIME,
    {
        immediate: false,
    }
);

async function fetchData() {
    const {data} = await getAllInfoboard();
    boardList.value = data.map(board => {
        const position = [board.longitudeRectify, board.latitudeRectify, board.alt];
        return {
            position,
            assetsCode: board.assetsCode,
            name: board.infoBoardName,
            infoBoardWidth: board.infoBoardWidth,
            infoBoardHeight: board.infoBoardHeight,
            fontSize: Math.max(board.infoBoardHeight / 3, 12),
            uuid: board.uuid,
            clickCallback: () => {
                viewToPoint(position);
            },
        };
    });
}

function getBoardObj3D(models, uuid) {
    for (const model of models) {
        const obj3d = model.children.find(m => m.uuid === uuid);
        if (obj3d) {
            return obj3d;
        }
    }
}

async function getContent(assetsCode, useCache) {
    const content = boardContentMap[assetsCode];
    if (content && useCache) return content;
    const {data} = await getInfoboardContent(assetsCode);
    boardContentMap[assetsCode] = data;
    return data;
}

function setContent(obj3d, info, content) {
    let repeat = 20;
    let timer = setInterval(() => {
        if (!repeat--) {
            clearInterval(timer);
            timer = null;
        }
        // 情报板模型已渲染
        if (obj3d.loaded) {
            clearInterval(timer);
            timer = null;
            props.assetsScene.handleActiveDevice(
                info.uuid,
                'guidanceScreen',
                {
                    text: content,
                    fontSize: info.fontSize,
                    fontColor: 'red',
                    layout: 'center',
                    screenLength: info.infoBoardWidth,
                    screenHeight: info.infoBoardHeight,
                }
            );
        }
    }, 100);
}

function setBoardContent(models, useCache) {
    if (!models.length) return;
    for (const board of boardList.value) {
        const obj3d = getBoardObj3D(models, board.uuid);
        if (obj3d) {
            getContent(board.assetsCode, useCache).then(content => {
                setContent(obj3d, board, content);
            });
        }
    }
}

function sceneViewAddCallback({models}) {
    // sceneViewAdd回调优先使用缓存
    setBoardContent(models, true);
}

function addSceneViewAddListener() {
    props.assetsScene.addEventListener('sceneViewAdd', sceneViewAddCallback);
}

function removeSceneViewAddListener() {
    props.assetsScene.removeEventListener('sceneViewAdd', sceneViewAddCallback);
}

onMounted(() => {
    fetchData();
    addSceneViewAddListener();
    resume();
});

onBeforeUnmount(() => {
    removeSceneViewAddListener();
});


</script>