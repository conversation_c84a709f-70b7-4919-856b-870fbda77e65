<template>
    <main>
        <pie-chart
            class="pieChartBox"
            v-bind="$attrs"
            :data="events"
            :active-index="curActiveIndex"
            :colors="colors"
            @mouseout="mouseout"
            @mouseover="mouseover"
        />

        <ul class="show-scroll">
            <li
                v-for="(e, i) in events"
                :key="e.name"
                :class="{
                    active: i === curActiveIndex,
                }"
                :style="{'--c': colors[i % colors.length]}"
                @mouseover="mouseover(i)"
                @mouseout="mouseout(i)"
            >
                <i class="color-icon"></i>
                <slot
                    v-if="slots.li" name="li"
                    :item="e"
                    :index="i"
                ></slot>
                <template v-else>
                    <div class="left">
                        <span
                            v-if="e.icon"
                            class="icon-wrapper"
                        >
                            <icon
                                :name="e.icon"
                                :size="e.iconSize"
                            />
                        </span>
                        {{ e.name }}
                    </div>

                    <div class="right n">
                        <div class="value">
                            {{ e.value }}
                            <span>{{ e.unit }}</span>
                        </div>
                        <template v-if="showPercentage">
                            <i class="line"></i>
                            <div>
                                <b>{{ e.ratio ? (e.ratio * 100).toFixed(1) : 0 }}</b>
                                <span class="unit">%</span>
                            </div>
                        </template>
                    </div>
                </template>
            </li>
        </ul>
    </main>
</template>
<script>
import {computed, ref} from 'vue';
import {useIntervalFn} from '@vueuse/core';
import PieChart from './LeftPie.vue';
import Icon from '../../Icon/index.vue';
import {round} from 'lodash';

export default {
    props: {
        data: {
            type: Array,
            default: () => [],
        },
        activeIndex: {
            type: Number,
            default: 0,
        },
        color: {
            type: Array,
            default: () => [],
        },
        showPercentage: {
            type: Boolean,
            default: true,
        },
    },
    components: {
        PieChart, Icon,
    },
    setup(props, {slots}) {
        const events = computed(() => {
            const sum = props.data.reduce((acc, cur) => (acc + cur.value), 0);
            let ratioSum = 0;
            return props.data.map((item, index) => {
                let ratio;
                if (index === props.data.length - 1) {
                    ratio = 1 - ratioSum;
                }
                else {
                    ratio = round(item.value / sum, 5);
                    ratioSum += ratio;
                }
                return {
                    ...item,
                    ratio,
                };
            });
        });
        const curActiveIndex = ref(props.activeIndex);
        const defaultColors = [
            '#D0D0D0',
            '#55DADA',
            '#FCFF95',
            '#44DA1E',
            '#00B0FF',
        ];
        const colors = computed(() => {
            return props.color.length ? props.color : defaultColors;
        });
        const activeIndexChange = () => {
            const len = events.value.length;
            curActiveIndex.value = (curActiveIndex.value + 1) % len;
        };
        const {pause, resume} = useIntervalFn(
            () => {
                activeIndexChange();
            },
            5 * 1000,
            {
                immediate: false,
                immediateCallback: false,
            }
        );

        const mouseout = e => {
            resume();
        };


        const mouseover = e => {
            pause();
            curActiveIndex.value = e;
        };

        return {
            events, curActiveIndex, colors, mouseout, mouseover,
            activeIndexChange,
            slots,
        };
    },
};
</script>

<style lang="less" scoped>
    main {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        .pieChartBox {
            width: 180px;
            height: 180px;
        }

        ul {
            flex: 1;
            height: 188px;
            overflow-y: auto;
            padding-left: 30px;
            margin-right: 6px;

            li {
                position: relative;
                width: 100%;
                height: 32px;
                padding-inline: 6px;
                padding-left: 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                cursor: pointer;
                background: rgba(18, 74, 166, .24);
                transition: background .2s;
                margin-bottom: 7px;
                backdrop-filter: blur(10px);
                clip-path:
                    polygon(
                        0 0,
                        100% 0,
                        100% calc(100% - 6px),
                        calc(100% - 6px) 100%,
                        0 100%
                    );

                &:last-child {
                    margin-bottom: 0;
                }

                &.active {
                    background: linear-gradient(-25.62deg, rgb(36, 104, 242) .326%, rgba(1, 255, 229, .5) 133.391%);
                    border: 1px solid transparent;
                }

                .color-icon {
                    position: absolute;
                    width: 6px;
                    height: 6px;
                    aspect-ratio: 1;
                    top: 50%;
                    left: 6px;
                    transform: translateY(-50%);
                    background: var(--c);
                }

                .icon-wrapper {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-sizing: content-box;
                    width: 20px;
                    height: 20px;
                    margin-right: 12px;
                    border: 2px solid rgba(255, 255, 255, .2);
                }

                .left {
                    display: flex;
                    align-items: center;
                    height: 20px;
                    gap: 4px;
                    font-size: 18px;
                    font-weight: 500;
                    color: #e7efff;
                }

                .right {
                    font-size: 20px;
                    display: flex;
                    align-items: center;

                    .value {
                        font-family: RoboData;
                        color: rgba(255, 255, 255, .7);

                        span {
                            font-size: 12px;
                        }
                    }

                    b {
                        font-family: RoboData;
                        margin-left: 12px;
                        color: #fff;
                    }

                    .unit {
                        font-size: 10px;
                        font-weight: 500;
                        margin-left: 4px;
                        color: rgba(255, 255, 255, .5);
                    }

                    .line {
                        display: inline-block;
                        width: 1px;
                        height: 12px;
                        margin-left: 12px;
                        background: rgba(255, 255, 255, .3);
                    }
                }
            }
        }

        .active .right .value {
            color: #fff;
        }
    }

</style>