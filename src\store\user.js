import {request} from '@/utils/network-helper/index';
import {ref} from 'vue';
import router from '@/router';

export const user = ref(null);
let isLoadingUser = false;
export function useUserInfo() {
    if (!user.value) {
        if (isLoadingUser) {
            return user;
        }
        let token = new URLSearchParams(window.location.search)?.get('token') ?? null;
        if (token) {
            // 清除地址栏的 token
            router.replace({path: router.currentRoute.value.path});
            localStorage.setItem('token', token);
        }
        else {
            token = localStorage.getItem('token');
        }
        // 获取用户信息，失败后跳转登录
        // 成功后保存 token 到 localStorage
        isLoadingUser = true;
        request.post('/ihs/admin/auth/myInfo', null, {
            headers: {Platform: 37},
        }).then(res => {
            user.value = res.data;
        }).finally(() => {
            isLoadingUser = false;
        });
    }

    return user.value;
};