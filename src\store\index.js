import Vue from 'vue';
import Vuex from 'vuex';
import getters from './getters';
import {createUpdateState, initGetters} from '@/utils/vuex.js';


Vue.use(Vuex);

const modulesFiles = require.context('./modules', true, /\.js$/);

const modules = modulesFiles.keys().reduce((modules, modulePath) => {
    const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1');
    const value = modulesFiles(modulePath);
    modules[moduleName] = initGetters(value.default || value);
    return modules;
}, {});

const store = new Vuex.Store({
    namespace: true,
    mutations: {
        UPDATE_STATE: createUpdateState(),
    },
    modules,
    getters,
});

export default store;
