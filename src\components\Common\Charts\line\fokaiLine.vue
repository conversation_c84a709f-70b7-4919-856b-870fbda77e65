<template>
    <div class="fc gap-12 c-50 fs-14 pr full">
        <div v-if="data.length" class="fr jsb pr pt-2">
            <span>{{ yName }}</span>
            <div
                v-if="data.length > 1"
                class="fr gap-15"
            >
                <div
                    v-for="item in data" :key="item.name"
                    class="fr ac gap-5"
                >
                    <span
                        class="dib w-10 h-10 b-50%"
                        :style="{
                            backgroundColor: item.color,
                        }"
                    ></span>
                    <span>{{ item.name }}</span>
                </div>
            </div>
        </div>
        <div class="pr f-1">
            <ul
                v-if="data.length"
                ref="yAxisRef"
                class="yAxis pa t-0 l-0 b-11 fcr jsb"
            >
                <li
                    v-for="i in (splitNumber + 1)" :key="i"
                    class="yAxis-item h-16 px-7 ct"
                >
                    {{ transformStep(i) }}
                </li>
            </ul>
            <div
                v-show="data.length"
                ref="chartRef" style="position: absolute;"
                class="l-0 t-0 r-0 b-0"
            ></div>
        </div>
        <a-empty v-if="!data.length"/>
    </div>
</template>
<script setup>
import {ref, watch} from 'vue';
import {useResizeObserver} from '@vueuse/core';
import useChartCommon from '../useChartCommon';
import {singleTooltipFormatter} from '../util';
import {echarts} from '../common';
import {useUnit} from '@/utils/hooks/useUnit';
const {ratio} = useUnit();

const props = defineProps({
    yName: {
        type: String,
    },
    data: {
        type: Array,
        default: () => [],
    },
    boundaryGap: {
        type: Boolean, // 两边是否留白
        default: true,
    },
    tooltipFormatter: {
        type: Function,
        default: singleTooltipFormatter,
    },
});

const style = {
    fontColor: 'rgba(255, 255, 255, .7)',
    white: 'rgb(255, 255, 255)',
    transparent: 'rgba(255, 255, 255, .0)',
};

const splitNumber = 5;
const step = ref(5);
const chartRef = ref(null);
const yAxisRef = ref(null);
const highlightXName = ref('');
let xAxis = [];

const transformStep = v => {
    const s = step.value;
    const n = (s > 1 || s === 1) ? (v - 1) * s : ((v - 1) * s).toFixed(1);
    return n === '0.0' ? '0' : n;
};

useResizeObserver(yAxisRef, entries => {
    const entry = entries[0];
    const {width} = entry.contentRect;
    chartRef.value.style.left = width + 8 * ratio.value + 'px';
});

const getOptions = () => {
    const {step: s, xAxis: x, series} = transformData(props.data);
    step.value = s;
    xAxis = x;
    return {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                lineStyle: {
                    color: style.white,
                    width: 2,
                    type: 'solid',
                },
            },
            className: 'statistics-chart-tooltip',
            position: function (point, params, dom, rect, size) {
                const {contentSize, viewSize} = size;
                let x;
                if (point[0] + contentSize[0] < viewSize[0]) {
                    x = Math.min(point[0], viewSize[0] - contentSize[0]);
                }
                else {
                    x = point[0] - contentSize[0];
                }
                // 固定在顶部
                return [x, 3];
            },
            formatter: props.tooltipFormatter,
        },
        grid: {
            top: 0,
            right: 0,
            bottom: 0,
            left: 0,
            containLabel: true,
        },
        xAxis: {
            type: 'category',
            show: true,
            boundaryGap: props.boundaryGap, // 两边是否留白
            offset: -3,
            axisTick: {
                show: false,
            },
            axisLabel: {
                interval: 'auto',
                formatter: (_, index) => {
                    return xAxis[index];
                },
                fontSize: 14 * ratio.value,
                color: style.fontColor,
                fontFamily: 'RoboData',
            },
            axisLine: {
                show: false,
            },
            axisPointer: {
                type: 'shadow',
                shadowStyle: {
                    color: new echarts.graphic.LinearGradient(
                        0,
                        0,
                        0,
                        1,
                        [
                            {
                                offset: 0,
                                color: style.white,
                            },
                            {
                                offset: 1,
                                color: style.transparent,
                            },
                        ],
                        false
                    ),
                    opacity: 0.3,
                },
            },
            data: xAxis.map(item => {
                if (item === highlightXName.value) {
                    return {
                        value: item,
                        textStyle: {
                            color: style.white,
                            padding: [2 * ratio.value, 6 * ratio.value],
                            borderWidth: 1 * ratio.value,
                            borderRadius: 1 * ratio.value,
                            borderColor: style.fontColor,
                            backgroundColor: new echarts.graphic.LinearGradient(
                                0,
                                0,
                                0,
                                1,
                                [
                                    {
                                        offset: 0,
                                        color: 'rgb(36, 104, 242)',
                                    },
                                    {
                                        offset: 1,
                                        color: 'rgba(1, 255, 229, 0.5)',
                                    },
                                ],
                                false
                            ),
                        },
                    };
                }
                return {
                    value: item,
                    textStyle: {
                        padding: [2 * ratio.value, 6 * ratio.value],
                        borderWidth: 1 * ratio.value,
                    },
                };
            }),
        },
        yAxis: {
            type: 'value',
            min: 0,
            max: splitNumber * step.value,
            interval: step.value,
            splitNumber,
            splitLine: {
                show: true,
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.1)',
                },
            },
            axisLabel: {
                show: false,
                fontSize: 14 * ratio.value,
                color: style.fontColor,
            },
            splitArea: {
                show: true,
                areaStyle: {
                    color: ['rgba(0, 58, 134, 0.3)'],
                },
            },
        },
        series,
    };
};

const getSeries = (data, type, color) => {
    return [...data.map(item => {
        const {name, color, colorStops, data} = item;
        return {
            name,
            type: 'line',
            symbol: 'circle',
            showSymbol: true,
            smooth: true, // 平滑
            data,
            connectNulls: true, // 连续空值
            lineStyle: {
                color,
                width: 2 * ratio.value,
            },
            itemStyle: {
                color: 'transparent',
            },
            areaStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: colorStops.map((color, index) => ({
                        offset: index,
                        color,
                    })),
                    global: false,
                },
            },
        };
    })];
};

function transformData(data) {
    const max = Math.ceil(Math.max(...data.map(items => items.data.map(item => {
        const {value} = item;
        return value ?? item;
    })).flat(), 1));
    const s = Number((max / splitNumber).toFixed(1));
    const step = s > 1 ? Math.ceil(s) : s;
    const xAxis = data[0] ? data[0].data.map(item => item.name) : [];
    const series = getSeries(data);
    return {
        max,
        step,
        xAxis,
        series,
    };
};

const {forceRender, chart} = useChartCommon(chartRef, {
    getOptions,
});

const setName = name => {
    highlightXName.value = name;
};

watch(chart, () => {
    if (chart.value) {
        chart.value.on('highlight', ({batch: [{dataIndex}]}) => {
            const name = xAxis[dataIndex];
            setName(name);
        });
        chart.value.on('downplay', () => setName(''));
    }
});

watch(() => [
    props.data,
    props.tooltipFormatter,
    props.boundaryGap,
    highlightXName.value,
    ratio.value,
], () => {
    forceRender();
});
</script>
<style lang="less" scoped>
.c-50 {
    color: rgba(255, 255, 255, .7);
}

.yAxis {
    display: flex;
    flex-direction: column-reverse;
    height: calc(100% - 24px);

    .yAxis-item {
        font-family: 'RoboData';
    }
}

:deep(.ant-empty) {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}
</style>