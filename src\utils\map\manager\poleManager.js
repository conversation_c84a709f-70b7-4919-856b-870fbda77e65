import {addIcon, removeIcon, addDOMOverlay, removeDOMOverlay, addBubble, removeBubble} from '../index';
import {getTextWidth} from '@/utils';

const removeMap = {
    Icon: removeIcon,
    Label: removeDOMOverlay,
    Bubble: removeBubble,
};

// 支持绑定的事件
const eventNameEnum = {
    click: 'clickCallback',
    mouseenter: 'onMouseenter',
    mouseleave: 'onMouseleave',
};

// 标杆图标管理器
class PoleManager {
    constructor(engine) {
        this.poleMap = new Map();
        this.engine = engine;
    }
    /**
     * 添加标杆扎点
     * @param name {string}
     * @param point {Array} 经纬度
     * @param options {Object} 配置项
     */
    async addPole(name, point, options = {}) {
        if (this.poleMap.has(name)) {
            this.removePoleByName(name);
        }

        const {
            size = 'small',
            bubbleColor = '#5DE47E',
            labelDom,
            customData,
            iconName = 'green',
        } = options;
        const iconUrl = `maplayer/assets/image/pole_facilitie/pole_${iconName}.png`;
        // 气泡点
        let {bubble} = addBubble(point, {
            size: size === 'normal' ? 60 : 40,
            color: bubbleColor,
            type: 'Wave',
            _engine: this.engine,
        });

        let {icon, _engine} = addIcon(point, iconUrl, {
            width: 6,
            height: 66,
            offset: [0, -30],
            customData,
            _engine: this.engine,
        });

        // 右侧label dom
        let {domOverlay} = addDOMOverlay(point, labelDom, {
            _engine: this.engine,
            offset: [0, -58],
        });

        // 绑定事件
        Object.keys(eventNameEnum).forEach(eventType => {
            const eventName = eventNameEnum[eventType];
            const callback = options[eventName];
            if (callback && typeof callback === 'function') {
                domOverlay.receiveRaycast = true;
                icon.receiveRaycast = true;
                _engine.event.bind(domOverlay, eventType, callback);
                _engine.event.bind(icon, eventType, callback);
            }
        });

        this.poleMap.set(name, {
            'Bubble': bubble, // 气泡点
            'Icon': icon, // 图标
            'Label': domOverlay, // 文字 label
        });
    }
    /**
     * 移除指定名称的标杆扎点及其相关引用。
     * @param name - 标杆扎点唯一标识
     */
    removePoleByName(name) {
        const macro = this.poleMap.get(name);
        if (!macro) return;
        Object.keys(macro).forEach(key => {
            const remove = removeMap[key];
            remove(macro[key]);
        });
        this.poleMap.delete(name);
    }

    clear() {
        [...this.poleMap.keys()].forEach(macro => {
            this.removePoleByName(macro);
        });
        this.poleMap.clear();
    }
}

export {
    PoleManager,
};