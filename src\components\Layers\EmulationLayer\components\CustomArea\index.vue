<!-- 自定义区域组件 -->
<template>
    <div v-if="curEdit" class="area-page">
        <div class="map-legend">
            <div
                v-for="item in edit_legend"
                :key="item.code"
                :class="{
                    'item': true,
                    'active': curEdit === item.code,
                }"
                :title="item.title"
                @click="onTabType(item)"
            >
                <img
                    v-if="item.code !== 4"
                    :src="`${curEdit === item.code ? item.active : item.default}`"
                    alt=""
                >
            </div>
        </div>
        <div
            class="edit-btn" title="编辑完成"
            @click="onComplete"
        >
            <i class="el-icon-circle-check"></i>
        </div>
        <div
            class="exit-btn" title="退出编辑"
            @click="onExit"
        >
            <i class="el-icon-switch-button"></i>
        </div>
    </div>
</template>

<script>
import {computed, reactive, ref, watch} from 'vue';
import {edit_legend} from './config';
import {customHighInfo} from '@EmulationLayer/api';
import {messageTip, viewTo} from '@/utils';
import {addArea, isCustomArea, updateAreaInfo,
        polygonEditor, reactEditor, circleEditor} from '@EmulationLayer/store/index';
import {engine} from '@/store/engine';
import {geojsonUtils, FatLine, GeoJSONDataSource} from '@baidu/mapv-three';
import {Color} from 'bmap-three';


export default {
    name: 'CustomArea',
    components: {
    },
    props: {
        editStatus: {
            type: Number,
            default: 1,
        },
    },
    setup(props, {emit}) {
        const curEdit = ref(3);
        const editor = ref('polygonEditor');
        let lineMap = new Map();

        const onTabType = item => {
            curEdit.value = item.code;
            switch (item.code) {
                case 1:
                    messageTip('正在开发中，请等待！', 'info');
                    break;
                case 2:
                    editor.value = 'reactEditor';
                    // eslint-disable-next-line no-use-before-define
                    startReactEditor();
                    break;
                case 3:
                    editor.value = 'polygonEditor';
                    // eslint-disable-next-line no-use-before-define
                    startPolygonEditor();
                default:
                    break;
            }
        };

        const curEditor = computed(() => {
            if (editor.value === 'polygonEditor') return polygonEditor.value;
            if (editor.value === 'reactEditor') return reactEditor.value;
            return undefined;
        });

        const drawLine = async (data, lineKey, width = 3) => {
            // eslint-disable-next-line no-use-before-define
            clearLine(lineKey); // 清除其他的路径
            let line = engine.value.add(new FatLine({
                vertexColors: true,
                lineWidth: width,
                keepSize: true,
                lineJoin: 'round',
            }));
            lineMap.set(lineKey, line);
            let geoData = await GeoJSONDataSource.fromGeoJSON(data);
            geoData.setAttribute('color');
            lineMap.get(lineKey).dataSource = geoData;
        };

        const onComplete = async () => {
            curEditor.value?.complete?.();
            const drawed = curEditor.value?.drawedGraph[0];
            if (!drawed) {
                messageTip('请先进行区域框选');
                return;
            }
            const point = drawed.dataItem.attributes;
            let geoData = geojsonUtils.convertPolygon2LineString(curEditor.value.exportToGeoJSON());
            geoData[0].properties.color = '#49C3FF';

            drawLine(geoData, 'area');
            const editData = point.map(item => {
                return [item.point[0], item.point[1]];
            }).join(';');
            const {data} = await customHighInfo(editData);
            if (data && data.length > 0) {
                messageTip('获取仿真区域成功', 'success');
                updateAreaInfo.value = {data, type: 2};
                curEditor.value.enabled = false;
                isCustomArea.value = false;
                engine.value.map.setPitch(70);
                engine.value.map.setZoom(15);
                engine.value.map.setHeading(190);
            }
            else {
                curEdit.value = 3;
                editor.value = 'polygonEditor';
                // eslint-disable-next-line no-use-before-define
                startPolygonEditor();
            }
        };

        const onExit = () => {
            curEditor.value.enabled = false;
            isCustomArea.value = false;
            updateAreaInfo.value = {data: null, type: 1};
            engine.value.map.setPitch(70);
            engine.value.map.setZoom(15);
            engine.value.map.setHeading(190);
        };

        const clearLine = lineKey => {
            [...lineMap.keys()].forEach(key => {
                if (lineKey) {
                    let line = lineMap.get(lineKey);
                    line && engine.value.remove(line);
                    lineMap.delete(lineKey);
                }
                else {
                    let line = lineMap.get(key);
                    line && engine.value.remove(line);
                    lineMap.delete(key);
                }
            });
        };

        const clear = () => {
            clearLine();
            polygonEditor.value.clearAll();
            reactEditor.value.clearAll();
            circleEditor.value.clearAll();
            polygonEditor.value.enabled = false;
            reactEditor.value.enabled = false;
            circleEditor.value.enabled = false;
        };

        const startPolygonEditor = () => {
            clear();
            polygonEditor.value.enabled = true;
        };

        // 矩形区域编辑
        const startReactEditor = () => {
            clear();
            reactEditor.value.enabled = true;
        };


        watch(() => props.editStatus, val => {
            if (val) {
                curEdit.value = val;
            }
        });

        watch(() => addArea.value, val => {
            if (!val) return;
            curEdit.value = val;
            messageTip('请开始绘制区域', 'info');
            editor.value = 'polygonEditor';
            startPolygonEditor();
        });

        return {
            editor,
            curEdit,
            edit_legend,
            onTabType,
            onExit,
            onComplete,
        };
    },

};
</script>

<style lang="less" scoped>
.area-page {
    position: relative;
}

.map-legend {
    width: 144px;
    height: 40px;
    background: rgba(7, 175, 135, .5);
    border: 1px solid rgb(95, 107, 113, 1);
    position: absolute;
    right: 16px;
    display: flex;
    cursor: pointer;
    top: 120px;
    pointer-events: auto;

    .item {
        width: 48px;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;

        .el-icon-circle-check {
            font-size: 18px;
        }

        .default {
            color: #98b8de;
        }

        .active {
            color: #fff;
        }

        &.active {
            background: rgb(7, 175, 135);
            border: 1px solid rgb(0, 255, 186);
        }

        &:nth-child(2) {
            border-left: 1px solid rgba(239, 255, 255, .25);
            border-right: 1px solid rgba(239, 255, 255, .25);
        }

        img {
            width: 18px;
            height: 18px;
        }
    }
}

.edit-btn,
.exit-btn {
    width: 48px;
    height: 40px;
    background: rgba(7, 175, 135, .5);
    border: 1px solid rgb(95, 107, 113, 1);
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    cursor: pointer;
    pointer-events: auto;
    right: 16px;

    &:hover {
        background: rgb(7, 175, 135);
        border: 1px solid rgb(0, 255, 186);

        .el-icon-circle-check,
        .el-icon-switch-button {
            color: #fff;
        }
    }

    .el-icon-circle-check,
    .el-icon-switch-button {
        font-size: 22px;
        color: rgba(229, 241, 255, .6);
    }
}

.edit-btn {
    top: 170px;
}

.exit-btn {
    top: 220px;
}
</style>
