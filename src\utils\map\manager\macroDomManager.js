import {
    addIcon,
    removeIcon,
    addDOMOverlay,
    removeDOMOverlay,
    addText,
    removeText,
} from '../index';
import EventCenter from './event.js';

const removeMap = {
    Icon: removeIcon,
    DomOverlay: removeDOMOverlay,
    Text: removeText,
};

// 支持绑定的事件
const eventNameEnum = {
    click: 'clickCallback',
    mouseenter: 'onMouseenter',
    mouseleave: 'onMouseleave',
};

// 宏观图层管理器
class MacroDomManager extends EventCenter {
    constructor(engine) {
        super(eventNameEnum);
        this.macroMap = new Map();
        this.engine = engine;
    }
    /**
     * 添加气泡到地图上
     * @param name 气泡名称
     * @param point 经纬度
     * @param options 配置项
     */
    addBubble(name, point, options = {}) {
        this.options = options;
        if (this.macroMap.has(name)) {
            this.removeBubbleByName(name);
        }

        const {
            text,
            customData,
            labelDom,
            iconOffset = [0, 0],
            textOffset,
            iconUrl = 'maplayer/assets/image/base/icons/normal.png',
            renderOrder = 0,
        } = options;

        const {
            width = 180,
            height = 110,
            fontSize,
        } = customData || {};

        const map = {};
        const {icon, _engine} = addIcon(point, iconUrl, {
            width,
            height,
            renderOrder,
            offset: iconOffset,
            customData,
            _engine: this.engine,
        });

        const {_text} = addText(point, text, {
            _engine: this.engine,
            offset: textOffset,
            fontSize,
            renderOrder,
        });

        if (labelDom) {
            const {domOverlay} = addDOMOverlay(point, labelDom, {
                _engine: this.engine,
                offset: [0, -54],
            });
            map.DomOverlay = domOverlay;
        }

        map.Icon = icon;
        map.Text = _text;

        // 绑定事件
        this.bind(icon);
        this.macroMap.set(name, map);
    }

    /**
     * 从宏中移除指定名称的气泡
     * @param name 气泡的名称
     */
    removeBubbleByName(name) {
        const macro = this.macroMap.get(name);
        if (!macro) return;
        this.unbind(macro.Icon);
        Object.keys(macro).forEach(key => {
            const remove = removeMap[key];
            remove(macro[key], this.engine);
        });
        this.macroMap.delete(name);
    }

    clear() {
        [...this.macroMap.keys()].forEach(macro => {
            this.removeBubbleByName(macro);
        });
        this.macroMap.clear();
    }
}

export {
    MacroDomManager,
};