<template>
    <div class="list-table-search-right">
        <el-button
            size="small" type="primary"
            icon="el-icon-search"
            @click.stop="searchFn"
        >
            查询
        </el-button>
        <el-button
            size="small"
            plain
            icon="el-icon-refresh"
            @click.stop="resetFn"
        >重置</el-button>
    </div>
</template>

<script>
import {Button} from 'element-ui';
export default {
    components: {
        [Button.name]: Button,
    },
    model: {
        prop: 'visible',
        event: 'update:visible',
    },
    setup(prop, {emit}) {
        // 点击导出按钮
        const exportFn = () => {
            emit('update:visible', true);
        };

        const searchFn = () => {
            emit('search');
        };

        const resetFn = () => {
            emit('reset');
        };

        return {
            exportFn,
            searchFn,
            resetFn,
        };
    },
};
</script>

<style lang="less" scoped>
.list-table-search-right {
    display: flex;
    justify-content: end;
    width: 100%;

    > div {
        margin-left: 16px;
        &:first-child {
            margin-left: 0;
        }
    }

    :deep(.el-button) {
        font-size: 14px;
        font-weight: 400;
        font-family: 'OPlusSans';
        border-radius: 0;
    }

    :deep(.el-button--default) {
                color: #fff;
            }
}
</style>