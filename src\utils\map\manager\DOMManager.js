import {addDOMOverlay, removeDOMOverlay} from '../index';
// dom label管理器
class DOMManager {
    constructor(engine) {
        this.domMap = new Map();
        this.engine = engine;
    }
    async addDOM(name, position, dom, options) {
        if (this.domMap.has(name)) {
            this.removeDOMByName(name);
        }
        const domOverlay = addDOMOverlay(position, dom,
            {_engine: this.engine, ...options});
        this.domMap.set(name, domOverlay);
    }
    removeDOMByName(name) {
        const dom = this.domMap.get(name);
        dom && removeDOMOverlay(dom.domOverlay, this.engine);
        this.domMap.delete(name);
    }

    clear() {
        [...this.domMap.keys()].forEach(dom => {
            this.removeDOMByName(dom);
        });
        this.domMap.clear();
    }
};

export {
    DOMManager,
};