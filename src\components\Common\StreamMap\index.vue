<template>
    <div class="container">
        <div id="player" :loading="loading"></div>
        <slot></slot>
    </div>
</template>

<script>
import {onMounted, ref} from 'vue';
import {CloudRenderEngine} from '@baidu/cloudrenderengine';

import {isOnline, getUrlParams} from '@/utils/common';

import useAssets from './useAssets.js';

export default {
    name: 'StreamMap',
    setup(props, {emit}) {
        const loading = ref(true);
        const map = ref(null);
        const urlParams = getUrlParams();

        // 地图内部的模型资源
        useAssets({
            map,
        });

        const getEnginPath = () => {
            const {ip = '***********', port = '8889', url, proto = 'ws'} = urlParams;
            // 是否为https
            const isProtocol = location.protocol === 'https:';
            // 构建可能传入的地图地址
            const ws = `${proto}://${ip}:${port}`;
            // https下的地图地址
            const wss = 'wss://gjdt.private.gdcg.cn/webrtc/';
            // 判断使用哪个地址
            const onlineUrl = isProtocol ? wss : ws;
            return url || wss;
        };

        const initMap = () => {
            const url = getEnginPath();
            let engine = new CloudRenderEngine({
                // 若为本地的话使用本地地图地址;
                wsUrl: url,
                connect_on_load: true,
                isShowTestUI: false,
                shouldShowPlayOverlay: false,
            });
            engine.load();
            let time = null;

            engine?.addEventListener('videoInitialised', e => {
                clearTimeout(time);
                time = setTimeout(() => {
                    map.value = engine;
                    console.log('mapmapmap', map);
                    loading.value = false;
                    emit('mapLoaded');
                }, 200);
            });
        };

        onMounted(() => {
            initMap();

            window.addEventListener('beforeunload', () => {
                localStorage.setItem('abc', 567);
                map.value.restartGame();
            });
        });

        return {
            loading,
            map,
        };
    },
};

</script>

<style lang="less" scoped>

.container {
    width: 100%;
    height: 100%;
    overflow: hidden;

    #player {
        position: relative;
        width: 100%;
        aspect-ratio: 16 / 9;
        height: auto;
        overflow: hidden;
        opacity: 0;
        object-fit: fill;
        transition: opacity .4s;

        /deep/video {
            // object-fit: cover;
        }

        &:not([loading]) {
            opacity: 1;
        }
    }
}
</style>