<template>
    <el-table
        ref="tableRef"
        v-bind="$attrs"
        :loading="loading"
        :data="data"
        :sticky="sticky"
        style="width: 100%;"
        :class="{'show-scroll': sticky, scroll: sticky}"
        v-on="$listeners"
        @cell-click="handleCellClick"
    >
        <el-table-column
            v-for="col in columns"
            v-bind="col"
            :key="col.prop"
            :label="col.label"
            :prop="col.prop"
            :formatter="col.render"
        >
            <template v-if="col.slotName" #default="scope">
                <slot
                    :name="col.slotName"
                    v-bind="scope"
                ></slot>
            </template>
        </el-table-column>
        <slot></slot>
    </el-table>
</template>

<script>
import {TableColumn, Table, Button} from 'element-ui';
import {onMounted, ref, nextTick} from 'vue';

export default {
    name: 'CarInfoTable',
    components: {
        [Table.name]: Table,
        [TableColumn.name]: TableColumn,
        [Button.name]: Button,
    },
    props: {
        data: {
            type: Array,
            default: () => ([]),
        },
        columns: {
            type: Array,
            default: () => ([]),
        },
        loading: {
            type: Boolean,
            default: false,
        },
        sticky: {
            type: Boolean,
            default: false,
        },
    },
    setup(props, {emit}) {
        const tableRef = ref(null);
        onMounted(() => {
            nextTick(() => {
                tableRef.value?.doLayout?.();
            });
        });
        const handleCellClick =  row => {
            emit('handlerCellClick', row);
        };
        return {
            handleCellClick,
            tableRef,
        };
    },
};
</script>

<style lang="less" scoped>
@text-color: #E7EFFF;

.el-table {
    background: transparent !important;
    color: @text-color;
    overflow: visible;

    .el-table__row:hover {
        cursor: pointer;
    }

    &[sticky=true] {
        height: 100%;
        display: flex;
        flex-direction: column;

        .el-table__body-wrapper {
            scrollbar-gutter: stable both-edges;
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            // padding-left: 6px;
        }
    }

    .cell {
        height: 32px;
        display: flex;
        align-items: center;

        .btn {
            color: #ffd6d3;
            font-size: 12px;
            cursor: pointer;
            transition: filter .3s;

            &:hover {
                filter: contrast(1.2);
            }

            &:active {
                filter: contrast(1.5);
            }
        }
        // line-height: 32px;
    }

    &::before,
    .el-table__fixed-right::before {
        content: '';
        display: none;
    }

    thead {
        color: @text-color;
        font-weight: normal;
    }

    tr {
        background: transparent;
    }

    .el-table__cell {
        background: transparent !important;
        height: 32px !important;
        padding: 0 !important;
        border: none !important;
    }

    .el-table__body-wrapper {
        overflow: visible;
        // padding-left: 2px;
    }

    .el-table__row {
        position: relative;

        &::after {
            content: '';
            width: 1px;
            left: -2px;
            top: 0;
            height: 32px;
            background-color: #79d4b8;
            position: absolute;
        }
    }

    // .el-table__fixed-right {
    //     tr {
    //         background: transparent !important;
    //     }
    // }

    .el-table__body {
        border-collapse: separate;
        border-spacing: 0 8px;
        // width: 100% !important;

        tr {
            background: rgba(255, 255, 255, .15);
        }

        .el-table_1_column_1 .cell {
            // border-left: 1px solid #29569d;
            height: 100%;
            display: flex;
            align-items: center;
        }
    }

    .el-button--text {
        // color: @active-text-color;
    }

    .el-table__empty-block {
        height: calc(100% - 8px) !important;

        .el-table__empty-text {
            color: #e7efffb4;
        }
    }
}

</style>