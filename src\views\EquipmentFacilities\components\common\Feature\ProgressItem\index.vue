<template>
    <div class="progress-item">
        <div class="progress-item__info">
            <div class="progress-item__info--title">{{ info.name }}</div>
            <div class="progress-item__info--data">
                <div class="data-point">{{ info.point }}%</div>
                <div class="data-num">{{ info.total }}</div>
                <div
                    v-if="needIcon" class="data-detail"
                    @click.stop="($event) => handleDetail($event)"
                >查看详情</div>
            </div>
        </div>
        <progress-bar :percentage="info.point"/>
    </div>
</template>

<script>
import {ProgressBar} from '@/components/Common';

export default {
    name: 'progress-item',
    props: {
        needIcon: {
            type: Boolean,
            default: false,
        },
        info: {
            type: Object,
            default: () => ({}),
        },
    },
    components: {
        ProgressBar,
    },
    setup(props, {emit}) {
        const handleDetail = event => {
            emit('click', props.info, event);
        };

        return {
            handleDetail,
        };
    },
};
</script>

<style lang="less" scoped>
.progress-item {
    width: 100%;
    background-color: rgba(22, 48, 86, 0.3);
    box-shadow: 0 1px 0 0 rgba(0, 47, 121, 0.35);
    padding: 10px 15px 10px 24px;

    &__info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        margin-bottom: 6px;

        &--title {
            font-size: 16px;
            color: rgba(#fff, .7);
            font-family: 'PingFang';
            font-weight: 500;
        }

        &--data {
            display: flex;
            align-items: center;
            font-size: 16px;
            font-family: 'RoboData';
            font-weight: 400;

            .data-point {
                color: rgba(#fff, .6);
                margin-right: 12px;

                &::after {
                    content: '';
                    display: inline-block;
                    width: 2px;
                    height: 12px;
                    background-color: rgba(255, 255, 255, .6);
                    margin-left: 12px;
                }
            }

            .data-num {
                color: #fff;
            }

            .data-detail {
                font-family: 'PingFang';
                color: rgb(0, 255, 229);
                padding: 2px 8px;
                margin-left: 12px;
                border: 1px solid rgb(1, 255, 229);
                cursor: pointer;
            }
        }
    }
}
</style>