<template>
    <div class="construction-marker-wrapper">
        <div
            v-for="item in list"
            :key="item.id"
            class="construction-marker"
        >
            <bubble-marker
                bubble-type="circle"
                bubble-color="#07EDDE"
                icon-name="project"
                icon-color="#333"
                :show-label="true"
                :manager-instace="domManager"
                :need-detail="!isActive(item)"
                :need-hover="!isActive(item)"
                :info="{
                    ...item,
                    label: item.projectName,
                    position: item.position,
                    clickCallback: handleClickMarker,
                }"
            >
                <!-- <div v-if="isActive(item)" class="content-wrapper">
                    <div>{{ item.projectName, }}</div>
                    <div class="btn-go">去应急系统处置</div>
                </div> -->
            </bubble-marker>
            <construction-area :list="item.positionList"/>
        </div>
    </div>
</template>

<script setup>
import {BubbleMarker} from '@/components/Common';
import {domManager} from '@/views/Construction/utils';
import ConstructionArea from './Area.vue';
import {getProjectList} from '@/api/construction';
import {watch, ref} from 'vue';
import {projectType, activeProjectInfo} from '../../store';

const emit = defineEmits(['clickMarker']);

const list = ref([]);

function handleClickMarker(e) {
    console.log('🚀 ~ handleClickMarker ~ e:', e);
    emit('clickMarker', e);
}

async function fetchData() {
    const {data} = await getProjectList(projectType.value);

    list.value = data.map(item => {
        const position = [
            ...item.eventPosition.split(','),
            item.eventAltitude,
        ];
        return {
            ...item,
            position: position,
            lng: position[0],
            lat: position[1],
            alt: position[2],
        };
    });
}

function isActive(info) {
    return info?.id === activeProjectInfo.value?.id;
}

watch(
    () => projectType.value,
    () => {
        fetchData();
    },
    {
        immediate: true,
    }
);
</script>

<style lang="less" scoped>
.content-wrapper {
    display: flex;
    white-space: nowrap;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, .8);
    padding: 10px 8px;
    font-size: 16px;

    .btn-go {
        color: rgb(2, 232, 216);
        margin-left: 4px;
        cursor: pointer;

        &::before {
            content: '';
            display: inline-block;
            width: 1px;
            height: 8px;
            background-color: rgba(80, 80, 80);
            margin-right: 4px;
        }
    }
}
</style>