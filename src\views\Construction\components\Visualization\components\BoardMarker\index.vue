<template>
    <div>
        <BubbleMarker
            v-for="board in boardList"
            :key="board.key"
            :info="board"
            bubble-color="rgb(57, 182, 0)"
            icon-name="xianshiping"
            icon-color="#fff"
            :manager-instace="domManager"
        />
    </div>
</template>

<script setup>
import {getInfoboard} from '@/api/construction';
import {BubbleMarker} from '@/components/Common';
import {domManager} from '@/views/Construction/utils';
import {watch, ref} from 'vue';
import {viewToPoint} from '@/utils';
import {activeProjectInfo} from '../../store';

const boardList = ref([]);

async function fetchData() {
    const {lng, lat} =  activeProjectInfo.value;
    const {data} = await getInfoboard({
        lng,
        lat,
    });
    boardList.value = data.map(item => ({
        position: [item.lng, item.lat, item.alt],
        key: item.assetsCode,
        name: item.infoBoardName,
        clickCallback: () => {
            viewToPoint([item.lng, item.lat, item.alt]);
        },
    }));
}

watch(
    () => activeProjectInfo.value,
    val => {
        if (!val) return;
        fetchData();
    },
    {
        immediate: true,
    }
);

</script>