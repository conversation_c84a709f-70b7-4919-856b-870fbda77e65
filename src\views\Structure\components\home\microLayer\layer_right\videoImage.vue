<template>
    <div class="video-image">
        <div class="video-image__top">
            <span class="top__name">{{ markerInfo.sectionName }}</span>
            <span class="top__stake">{{ structureData.centerStake }}</span>
            <span class="top__direction">{{ type === '1' ? '上行' : '下行' }}</span>
        </div>

        <div class="video-image__bottom">
            <flvPlayer v-if="videoData[direction + 'Camera']" :camera-info-id="videoData[direction + 'Camera']"/>
            <no-data v-else/>
        </div>
    </div>
</template>

<script>
import {flvPlayer, NoData} from '@/components/Common';
import {markerInfo, structureId} from '@/views/Structure/utils/index';
import {getCamera, getVideoByStructureId} from '@/api/structure/index';
import {onMounted, ref, computed} from 'vue';
export default {
    name: '视频图像',
    props: {
        type: {
            type: String,
            default: '',
        },
    },
    components: {
        flvPlayer, NoData,
    },
    setup(props) {
        const videoData = ref({});
        const structureData = ref({});
        const init = () => {
            getVideoByStructureId({
                structureId: structureId.value,
                sectionDirection: markerInfo.value.sectionDirection || '1',
            }).then(res => {
                structureData.value = res.data;
            });
            getCamera({
                structureId: structureId.value,
                sectionDirection: markerInfo.value.sectionDirection || '2',
            }).then(res => {
                videoData.value = res.data;
            });
        };
        onMounted(() => init());

        const direction = computed(() => {
            return props.type === '1' ? 'up' : 'down';
        });
        return {
            markerInfo, videoData, direction, structureData,
        };
    },
};
</script>

<style lang="less" scoped>
.video-image {
    width: 100%;
    height: 316px;

    &__top {
        display: flex;
        align-items: center;
        width: 100%;
        height: 48px;
        padding-left: 24px;
        font-size: 20px;
        font-family: 'PingFang';
        font-weight: 500;
        color: rgba(#fff, .7);
        background-color: rgba(18, 74, 166, 0.24);
        border-left: 2px solid rgb(36, 104, 242);
        border-image: linear-gradient(to bottom, rgb(36, 104, 242), rgba(1, 255, 229, 0.5));

        .top__stake {
            margin: 0 10px;
        }
    }

    &__bottom {
        width: 100%;
        height: 249px;
        margin-top: 18px;
        padding: 14px 8px 8px;
        background-color: rgba(18, 74, 166, 0.24);
        border-top: 2px solid rgb(41, 86, 157);
    }
}
</style>