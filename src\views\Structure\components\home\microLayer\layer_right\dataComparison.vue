<template>
    <div class="data-comparison">
        <fokai-line
            v-if="apiType" :data="list"
            y-name="数量"
        />
        <no-data v-else/>
    </div>
</template>

<script>
import {ref, watch} from 'vue';
import {FokaiLine, NoData} from '@/components/Common';
import {selectSensorListDay} from '@/api/structure/index';
import {structureId} from '@/views/Structure/utils/index';
export default {
    name: '实时与历史数据对比',
    props: {
        sensorName: {
            type: String,
            default: '',
        },
        measureType: {
            type: String,
            default: '',
        },
    },
    components: {
        FokaiLine, NoData,
    },
    setup(props) {
        const list = ref([
            {
                name: '历史监测数据',
                color: 'rgb(36, 104, 242)',
                colorStops: ['rgba(89, 180, 245, .38)', 'rgba(89, 180, 245, 0)'],
                data: [
                    {
                        'name': '00:00',
                        'value': 0,
                    },
                    {
                        'name': '01:00',
                        'value': 0,
                    },
                    {
                        'name': '02:00',
                        'value': 0,
                    },
                    {
                        'name': '03:00',
                        'value': 0,
                    },
                    {
                        'name': '04:00',
                        'value': 0,
                    },
                    {
                        'name': '05:00',
                        'value': 0,
                    },
                    {
                        'name': '06:00',
                        'value': 0,
                    },
                    {
                        'name': '07:00',
                        'value': 0,
                    },
                    {
                        'name': '08:00',
                        'value': 0,
                    },
                    {
                        'name': '09:00',
                        'value': 0,
                    },
                    {
                        'name': '10:00',
                        'value': 0,
                    },
                    {
                        'name': '11:00',
                        'value': 0,
                    },
                    {
                        'name': '12:00',
                        'value': 0,
                    },
                    {
                        'name': '13:00',
                        'value': 0,
                    },
                    {
                        'name': '14:00',
                        'value': 0,
                    },
                    {
                        'name': '15:00',
                        'value': 0,
                    },
                    {
                        'name': '16:00',
                        'value': 0,
                    },
                    {
                        'name': '17:00',
                        'value': 0,
                    },
                    {
                        'name': '18:00',
                        'value': 0,
                    },
                    {
                        'name': '19:00',
                        'value': 0,
                    },
                    {
                        'name': '20:00',
                        'value': 0,
                    },
                    {
                        'name': '21:00',
                        'value': 0,
                    },
                    {
                        'name': '22:00',
                        'value': 0,
                    },
                    {
                        'name': '23:00',
                        'value': 0,
                    },
                ],
            },
            {
                name: '实时监测数据',
                color: 'rgb(0, 255, 149)',
                colorStops: ['rgba(88, 255, 220, .3)', 'rgba(88, 255, 220, 0.1)'],
                data: [
                    {
                        'name': '00:00',
                        'value': 0,
                    },
                    {
                        'name': '01:00',
                        'value': 0,
                    },
                    {
                        'name': '02:00',
                        'value': 0,
                    },
                    {
                        'name': '03:00',
                        'value': 0,
                    },
                    {
                        'name': '04:00',
                        'value': 0,
                    },
                    {
                        'name': '05:00',
                        'value': 0,
                    },
                    {
                        'name': '06:00',
                        'value': 0,
                    },
                    {
                        'name': '07:00',
                        'value': 0,
                    },
                    {
                        'name': '08:00',
                        'value': 0,
                    },
                    {
                        'name': '09:00',
                        'value': 0,
                    },
                    {
                        'name': '10:00',
                        'value': 0,
                    },
                    {
                        'name': '11:00',
                        'value': 0,
                    },
                    {
                        'name': '12:00',
                        'value': 0,
                    },
                    {
                        'name': '13:00',
                        'value': 0,
                    },
                    {
                        'name': '14:00',
                        'value': 0,
                    },
                    {
                        'name': '15:00',
                        'value': 0,
                    },
                    {
                        'name': '16:00',
                        'value': 0,
                    },
                    {
                        'name': '17:00',
                        'value': 0,
                    },
                    {
                        'name': '18:00',
                        'value': 0,
                    },
                    {
                        'name': '19:00',
                        'value': 0,
                    },
                    {
                        'name': '20:00',
                        'value': 0,
                    },
                    {
                        'name': '21:00',
                        'value': 0,
                    },
                    {
                        'name': '22:00',
                        'value': 0,
                    },
                    {
                        'name': '23:00',
                        'value': 0,
                    },
                ],
            },
        ]);

        const apiType = ref(false);

        watch(() => [props.sensorName, props.measureType], () => {
            if (!props.sensorName || !props.measureType) return;
            apiType.value = false;
            selectSensorListDay({
                structureId: structureId.value,
                sensorName: props.sensorName,
                sensorDataName: props.measureType,
            }).then(res => {
                apiType.value = true;
                if (res.data.historyList.length) {
                    list.value[0].data = res.data.historyList.map(item => ({
                        name: item.time,
                        value: item.num,
                    }));
                }
                if (res.data.todayList.length) {
                    list.value[1].data = res.data.todayList.map(item => ({
                        name: item.time,
                        value: item.num,
                    }));
                }
                console.log('list.value', list.value);
            });
        }, {immediate: true});

        return {
            list,
            apiType,
        };
    },
};
</script>

<style lang="less" scoped>
.data-comparison {
    width: 100%;
    height: 322px;
}
</style>