// import {useRoute, useRouter} from '@/utils';

// const router = useRouter();

// export const handleClick = e => {
//     router.push({name: 'CarFlow', params: {id: e.id, info: e}}); // 使用router.push进行路由跳转
// };
import {ref, computed} from 'vue';
export const getColor = val => {
    if (val > 70) {
        return 'rgb(255, 105, 105,.7)';
    }
    if (val <= 20) {
        return 'rgba(0, 255, 114,.7)';
    }
    else if (val <= 70 && val > 20) {
        return 'rgba(255, 249, 147.7)';
    }
};
// SD101 雅瑶服务区 - 东区
// SD201 雅瑶服务区 - 西区
export const serviceId = ref('SD101');

