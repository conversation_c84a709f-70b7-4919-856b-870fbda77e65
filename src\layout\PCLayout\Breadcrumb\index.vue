<template>
    <div class="app-breadcrumb">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item
                v-for="item in getList"
                :key="item"
            >
                {{ item }}
            </el-breadcrumb-item>
        </el-breadcrumb>
    </div>
</template>

<script>
import {
    Breadcrumb,
    BreadcrumbItem,
} from 'element-ui';
import {computed, onMounted, ref} from 'vue';
import {useRoute} from '@/utils';
import {pcMenuList} from '@/config/menu';

export default {
    components: {
        [Breadcrumb.name]: Breadcrumb,
        [BreadcrumbItem.name]: BreadcrumbItem,
    },
    setup() {
        const breadcrumbMap = ref({});

        const route = useRoute();

        const getList = computed(() => {
            return breadcrumbMap.value[route.path] || [];
        });

        function initBreadcrumbMap() {
            const map = {};
            const stash = pcMenuList.map(menu => ({
                ...menu,
                prevBreadcrumb: [],
            }));
            while (stash.length) {
                const current = stash.shift();
                if (!current) continue;
                const breadcrumb = [...current.prevBreadcrumb, current.name];
                map[current.path] = breadcrumb;
                if (current.children?.length) {
                    current.children.forEach(child => {
                        stash.push({
                            ...child,
                            prevBreadcrumb: breadcrumb,
                        });
                    });
                }
            }
            breadcrumbMap.value = map;
        }

        onMounted(() => {
            initBreadcrumbMap();
        });

        return {
            getList,
        };
    },
};
</script>

<style lang="less" scoped>
.app-breadcrumb {
    height: 46px;
    padding: 16px 24px;

    & /deep/ .el-breadcrumb__item {
        .el-breadcrumb__inner {
            color: rgba(#fff, .6);
        }

        &:last-child {
            .el-breadcrumb__inner {
                color: #fff;
            }
        }
    }
}
</style>