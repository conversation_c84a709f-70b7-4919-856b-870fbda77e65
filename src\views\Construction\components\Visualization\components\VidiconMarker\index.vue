<template>
    <div class="vidicon-marker-wrapper">
        <vidicon-marker
            v-for="item in list"
            :key="item"
            :manager-instace="domManager"
            :info="item"
            :device-code="item.deviceCode"
        />
    </div>
</template>

<script setup>
import {VidiconMarker} from '@/components/Common';
import {domManager} from '@/views/Construction/utils';
import {ref, watch} from 'vue';
import {activeProjectInfo} from '../../store';
import {getDeviceByLngLat} from '@/api/construction';

const list = ref([]);

async function fetchData() {
    const {lng, lat} =  activeProjectInfo.value;
    const {data} = await getDeviceByLngLat({
        lng,
        lat,
    });
    list.value = data?.map(item => ({
        position: [item.longitudeGcj, item.latitudeGcj, item.altitudeGcj],
        sectionName: '佛开高速',
        deviceStake: item.stake,
        name: item.name,
        deviceCode: item.cameraIndexCode,
    }));
}

watch(
    () => activeProjectInfo.value,
    val => {
        if (!val) return;
        fetchData();
    },
    {
        immediate: true,
    }
);

</script>