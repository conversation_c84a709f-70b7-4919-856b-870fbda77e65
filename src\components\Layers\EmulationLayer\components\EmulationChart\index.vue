<!-- 仿真指标图表 -->
<template>
    <div class="content-box">
        <div class="tip">
            <div v-for="item in evaluateTextData" :key="item">
                {{ item }}
            </div>
        </div>
        <div class="echarts-content-box">
            <draggable
                v-model="chartList"
                class="echarts-content"
                animation="200"
                group="module"
            >
                <div
                    v-for="item in chartList" :key="item.name"
                    class="item"
                    :class="['module', item.key]"
                    :style="{'--duration': -Math.random() + 's'}"
                >
                    <div class="echarts-box">
                        <div class="small-title">
                            {{ item.name }}
                        </div>
                        <echarts-items
                            :key="item.key"
                            :data-props="item.data"
                            :type="item.code"
                            :name="item.key"
                            :color="item.color"
                            :echarts-data="evaluateData[item.key]"
                        />
                    </div>
                </div>
            </draggable>
        </div>
    </div></template>

<script>
import EchartsItems from './Charts/EchartsItem.vue';
import draggable from 'vuedraggable';
import {onMounted, ref, watch, computed, onBeforeUnmount} from 'vue';
import {emulationIds, emulationInfo, uuid,
        simulationBufferTime, strategyId} from '@EmulationLayer/store/index';
import {getEvaluate} from '@EmulationLayer/api';
import {wsHost} from '@EmulationLayer/store/common';

import {Ws} from '@/utils/ws';
import dayjs from 'dayjs';
import {throttle} from 'lodash';

import {emulationConfig} from '@/utils/common';

export default {
    components: {EchartsItems, draggable},
    props: {
        id: {
            type: String,
            default: () => null,
        },
    },
    setup(props, {emit}) {
        const {basePrefix, baseWsHost} = emulationConfig;
        const wsBaseUrl = computed(() => {
            return `${wsHost.value || baseWsHost}${basePrefix}`;
        });

        const chartList = [
            {
                code: 1,
                name: '排队长度',
                key: 'queueInfos',
                data: 'maxQueueLen',
                color: ['#01FFE5', '#0077FF', '#CFCC7F'],
            },
            {
                code: 2,
                name: '拥堵里程',
                key: 'congestLengthInfos',
                data: 'maxQueueLen',
                color: ['#01FFE5', '#0077FF', '#CFCC7F'],

            },
            {
                code: 3,
                name: '拥堵指数',
                key: 'congestIndexInfos',
                data: 'jamIndex',
                color: ['#01FFE5', '#0077FF', '#CFCC7F'],

            },
            {
                code: 4,
                name: '平均速度',
                key: 'avgSpeedInfos',
                data: 'avgSpeed',
                color: ['#01FFE5', '#0077FF', '#CFCC7F'],

            },
            {
                code: 5,
                name: '流量趋势',
                key: 'flowTrendInfos',
                data: 'flow',
                color: ['#01FFE5', '#0077FF', '#CFCC7F'],

            },
            {
                code: 6,
                name: '服务水平',
                key: 'serveLevelInfos',
                data: 'serviceLevel',
                color: ['#01FFE5', '#0077FF', '#CFCC7F'],

            },
        ];

        const startTime = emulationInfo.value.emulationStartTime;
        const endTime = emulationInfo.value.emulationEndTime;
        // const startTimeUnix = dayjs(startTime).valueOf();
        // const endTimeUnix = dayjs(endTime).valueOf();
        const evaluateData = ref({
            queueInfos: {data: [[], [], []]},
            congestLengthInfos: {data: [[], [], []]},
            congestIndexInfos: {data: [[], [], []]},
            avgSpeedInfos: {data: [[], [], []]},
            flowTrendInfos: {data: [[], [], []]},
            serveLevelInfos: {data: [[], [], []]},
        });

        const evaluateTextData = ref(['暂无仿真评价']);

        const wsMap = new Map();

        // 清除ws
        const closeWs = () => {
            if (wsMap.value.size > 0) {
                [...wsMap.value.keys()].forEach(itemWs => {
                    itemWs.dispose();
                });
                wsMap.value.clear();
            };
        };

        // 监听仿真实例id变化 为空说明关闭了仿真
        watch(() => emulationIds.value, val => {
            if (!val.length) {
                closeWs();
            }
        });

        // 监听仿真时间变化
        watch(() => simulationBufferTime.value, throttle(async val => {
            const currentTime =  dayjs(val).valueOf();
            const startTime =  dayjs(emulationInfo.value.emulationStartTime).valueOf();
            // 计算开始时间和当前时间之间的差值（单位：毫秒）
            const timeDifference = currentTime - startTime;
            const step = 1;

            // 判断差值是否是1分钟的整数倍
            const isOneMinuteInterval =  Math.floor(timeDifference / (step * 60 * 1000));
            if (isOneMinuteInterval >= 1) {
                const res = await getEvaluate({
                    id: strategyId.value || props.id,
                    step: isOneMinuteInterval,
                });

                evaluateTextData.value = res.data.split('。');
            }
        }, 1000));

        onMounted(async () => {
            if (emulationIds.value?.length) {
                for (let i = 0; i < emulationIds.value.length; i++) {
                    // 创建ws实例
                    let wsInstance = new Ws();
                    let cueEmulationId = emulationIds.value[i];
                    // 保存实例
                    wsMap.set(cueEmulationId, wsInstance);
                    const time = dayjs(startTime).valueOf();
                    // eslint-disable-next-line max-len, vue/max-len
                    const url = `${wsBaseUrl.value}/ws/imEmulationQueueSaturation?emulationId=${cueEmulationId}&startTime=${time}&uuid=${uuid.value}`;
                    let ws = await wsInstance.connect(url, {});
                    ws.onmessage = ({data}) => {
                        if (data === 'ok' || data === '{}') {
                            return;
                        }
                        data = JSON.parse(data);
                        // let i = 0;

                        const index = emulationIds.value.findIndex(item => item === data.taskId);

                        chartList.forEach(item => {
                            // evaluateData.value[item.key].data[index].splice(i, 0, [data.time, data[item.data]]);
                            evaluateData.value[item.key].data[index].push([data.time, data[item.data]]);
                        });
                        // i++;

                        // 设置仿真缓存的时间数据
                        simulationBufferTime.value = data.time;
                    };
                }
            }
        });

        onBeforeUnmount(() => {
            wsMap.forEach(ws => {
                ws.dispose();
            });
        });

        return {
            evaluateData,
            chartList,
            evaluateTextData,
        };

    },

};
</script>

<style scoped lang='less'>
.content-box {
    color: #e5f1ff;
    font-size: 16px;
    margin: 28px;
    border-radius: 8px;
    position: relative;
    height: 600px;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 50px;
        height: 100%;
        transform: scaleX(-1);
        background-image: linear-gradient(270deg, rgba(216, 216, 216, .14) 0%, rgba(7, 175, 135, .1) 100%);
    }

    &::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 50px;
        height: 100%;
        transform: scaleX(-1);
        background-image: linear-gradient(270deg, rgba(7, 175, 135, .1) 0%, rgba(216, 216, 216, .14) 100%);
    }

    .tip {
        // min-height: 52px;
        background-image: linear-gradient(270deg, rgba(19, 71, 62, .5) 1%, rgba(0, 255, 190, .24) 100%);
        margin: 0 50px;
        color: #fff;
        display: flex;
        flex-direction: column;
        // align-items: center;
        justify-content: center;
        font-size: 14px;
        padding: 12px 0 12px 24px;
        gap: 12px;
    }

    .echarts-content-box {
        padding: 14px 50px;
    }

    .echarts-content {
        width: 100%;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;

        .item {
            width: 567px;
            // padding: 16px;
            position: relative;
            display: flex;
            justify-content: space-between;

            .echarts-box {
                width: 100%;
                height: 228px;
                // background: rgba(0,10,40,0.3);
                // border: 1px solid rgba(45, 83, 151, .24);
                margin-top: 8px;
                // background-image: url('/src/assets/images/grid-bg.png');
                background-size: 100% 100%;
                margin-bottom: 11px;
                background-image: linear-gradient(268deg, rgba(57, 108, 179, .2) 0%, rgba(43, 195, 172, .2) 100%);
                border-radius: 8px;
            }

            .small-title {
                font-size: 14px;
                color: #fff;
                position: absolute;
                padding-left: 8px;
                display: flex;
                justify-content: space-between;
                cursor: pointer;
                left: 30px;
                top: 25px;

                &::before {
                    content: '';
                    width: 4px;
                    height: 12px;
                    background: #08d6a5;
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translate(0, -50%);
                }

                span:nth-child(2) {
                    color: #49c3ff;
                }
            }
        }
    }
}

</style>