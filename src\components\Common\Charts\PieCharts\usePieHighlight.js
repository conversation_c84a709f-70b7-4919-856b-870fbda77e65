
import {watch, ref} from 'vue';


const main = (chart, {
    defaultHighLight = 0,
    mouseover,
    mouseout,
    use = true,
}) => {
    const activeIndex = ref(defaultHighLight);

    const listenerHandlerEvent = () => {
        chart.value
            .on('mouseover', e => {
                if (e.dataIndex !== activeIndex.value) {
                    mouseover(e);
                }
            })
            .on('mouseout', e => mouseout(e));
    };

    const resetHighlight = (i = activeIndex.value) => {
        if (!use) {
            return;
        }
        chart.value.dispatchAction({type: 'highlight', seriesIndex: 0, dataIndex: i});
    };

    const activeChange = (n, o) => {
        chart.value.dispatchAction({type: 'downplay', seriesIndex: 0, dataIndex: o});
        chart.value.dispatchAction({type: 'highlight', seriesIndex: 0, dataIndex: n});
    };

    watch(chart, v => {
        if (v && use) {
            listenerHandlerEvent();
        }
    }, {
        immediate: true,
    });

    watch(activeIndex, (n, o) => {
        if (!use) {
            return;
        }
        activeChange(n, o);
    });

    return {
        activeIndex,
        resetHighlight,
    };
};



export default main;