import {ref, shallowRef} from 'vue';

export const engine = shallowRef(null);

export const assetsScene = shallowRef(null);

export const map = shallowRef(null);

export const host = ref(location.host);

export const position = {
    x: 113.26558984459267,
    y: 23.59243808198558,
};

export const mapCenter = ref([113.26558984459267, 23.59243808198558]);

export const roadCenter = [
    112.98681059867869,
    22.701779575880504,
    7.283334149828079,
];

export const cameraHeight = ref();