<template>
    <div>
        <!-- 摄像枪扎点 -->
        <template v-if="mapFilterData.includes('sheshi-shexiangtou')">
            <camera-marker
                v-for="(item, index) in cameraIconList"
                :key="item.cameraId"
                :manager-instace="domManager" :info="{
                    position: [item.lng, item.lat, item.alt],
                    label: item.cameraId,
                    clickCallback: () => clickCallback(item, 'cameraId', index),
                }"
            >
                <detail-list
                    v-model="cameraShow[index]"
                    show-video
                    width="629" :list="cameraList[index]"
                    :title="cameraName[index]"
                    :video-address="cameraVideoId[index]"
                    @close="handleCloseFn"
                />
            </camera-marker>
        </template>
        <!-- 显示屏扎点 -->
        <template v-if="mapFilterData.includes('xianshiping')">
            <intelboard-marker
                v-for="(item, index) in intelboardIconList"
                :key="item.assetsCode"
                :manager-instace="domManager" icon-name="xianshiping"
                bubble-color="rgb(57, 182, 0)"
                :info="{
                    position: [item.lng, item.lat, item.alt],
                    label: item.assetsCode,
                    clickCallback: () => clickCallback(item, 'assetsCode', index),
                }"
            >
                <detail-list
                    v-model="intelboardShow[index]"
                    width="368" :list="intelboardList[index]"
                    :title="intelboardName[index]"
                    @close="handleCloseFn"
                />
            </intelboard-marker>
        </template>
        <!-- 互通立交扎点 -->
        <template v-if="mapFilterData.includes('lijiao')">
            <intelchange-marker
                v-for="(item, index) in interChangeIconList"
                :key="item.sicId"
                :manager-instace="domManager" icon-name="camera-hutonglijiao"
                bubble-color="rgb(7, 237, 222)"
                :info="{
                    position: [item.lng, item.lat, item.alt],
                    label: item.sicId,
                    clickCallback: () => clickCallback(item, 'sicId', index),
                }"
            >
                <detail-list
                    v-model="intelchangeShow[index]"
                    width="328" :list="intelchangeList[index]"
                    :title="intelchangeName[index]"
                    @close="handleCloseFn"
                />
            </intelchange-marker>
        </template>
        <template v-if="mapFilterData.includes('qiaoliang1')">
            <!-- 桥梁扎点 -->
            <bridge-marker
                v-for="(item, index) in bridgeIconList"
                :key="item.structureId"
                :manager-instace="domManager" icon-name="camera-bridge"
                bubble-color="rgb(7, 118, 237)"
                :info="{
                    position: [item.lng, item.lat, item.alt],
                    label: item.structureId,
                    clickCallback: () => clickCallback(item, 'structureId', index),
                }"
            >
                <detail-list
                    v-model="bridgeShow[index]"
                    width="368" :list="bridgeList[index]"
                    :title="bridgeName[index]"
                    @close="handleCloseFn"
                />
            </bridge-marker>
        </template>
        <template v-if="mapFilterData.includes('camera-fuwuqu')">
            <!-- 服务区扎点 -->
            <service-marker
                v-for="(item, index) in serviceIconList"
                :key="item.serviceAreaId"
                :manager-instace="domManager" icon-name="camera-fuwuqu"
                bubble-color="rgb(255, 237, 0)"
                :info="{
                    position: [item.lng, item.lat, item.alt],
                    label: item.serviceAreaId,
                    clickCallback: () => clickCallback(item, 'serviceAreaId', index),
                }"
            >
                <detail-list
                    v-model="serviceShow[index]"
                    width="328" :list="serviceList[index]"
                    :title="serviceName[index]"
                    @close="handleCloseFn"
                />
            </service-marker>
        </template>
        <!-- 收费站扎点 -->
        <template v-if="mapFilterData.includes('toll-station')">
            <toll-station-marker
                v-for="(item, index) in tollStationIconList"
                :key="item.stationId"
                :manager-instace="domManager" icon-name="toll-station"
                bubble-color="rgb(0, 149, 57)"
                :info="{
                    position: [item.lng, item.lat, item.alt],
                    label: item.stationId,
                    clickCallback: () => clickCallback(item, 'stationId', index),
                }"
            >
                <detail-list
                    v-model="stationShow[index]"
                    width="328"
                    :title="stationName[index]"
                    :list="stationList[index]"
                    @close="handleCloseFn"
                />
            </toll-station-marker>
        </template>
    </div>
</template>

<script>
import {BubbleMarker, DetailList} from '@/components/Common/index.js';
import {domManager} from './index.js';
import {
    getServiceAreaInfo, getTollStationInfo, getBridgeInfo, getInterChangeInfo, getInfoboardInfo, getCameraInfo,
} from '@/api/equipment/highspeed.js';
import {viewToMicro, viewToFk} from '@/utils/map/methods/index.js';
import {
    tollDetailConfig,
    intelboardDetailConfig,
    intelchangeDetailConfig,
    bridgeDetailConfig,
    serviceDetailConfig,
    cameraDetailConfig, cameraRoadDirectionDict, cameraDeviceStatusDict,
} from '@/config/highspeed.js';
import {
    mqiGradeDict,
} from '@/config/maintain.js';
import {
    getBridgeInfo as getBridgeInfoApi,
} from '@/api/equipment/facilitydisplay.js';
import {
    cameraIconList, intelboardIconList, interChangeIconList, bridgeIconList, serviceIconList, tollStationIconList,
    cameraList, cameraName, cameraShow,
    stationList, stationName, stationShow,
    intelchangeList, intelchangeName, intelchangeShow,
    intelboardList, intelboardName, intelboardShow,
    bridgeList, bridgeName, bridgeShow,
    serviceList, serviceName, serviceShow, cameraVideoId,
} from '../../utils/map.js';

export default {
    props: {
        mapFilterData: {
            type: Array,
            default: () => ([]),
        },
    },
    components: {
        CameraMarker: BubbleMarker,
        IntelboardMarker: BubbleMarker,
        IntelchangeMarker: BubbleMarker,
        BridgeMarker: BubbleMarker,
        ServiceMarker: BubbleMarker,
        TollStationMarker: BubbleMarker,
        DetailList,
    },
    setup(props) {
        const dictSwitch = (res, item) => {
            let result;
            switch (item.value) {
                case 'roadDirection':
                    result = cameraRoadDirectionDict.get(res.data[item.value]);
                    break;
                case 'deviceStatus':
                    result = cameraDeviceStatusDict.get(res.data[item.value]);
                    break;
                default:
                    result = res.data[item.value];
                    break;
            }
            return result;
        };

        /**
         * 获取列表函数
         *
         * @param arr 数组，列表数据源
         * @param res 对象，包含列表数据对应的值
         * @returns 数组，包含每个列表项对象的数组
         */
        const getListFn = (arr, res) => {
            return arr.map((item, i) => {
                return {
                    label: item.label,
                    value: dictSwitch(res, item) || '暂无信息',
                    ortherClass: {operation: i === arr.length - 1 || false},
                };
            });
        };

        const clickCallback = (item, type, index) => {
            switch (type) {
                case 'cameraId':
                    getCameraInfo(item.cameraId).then(res => {
                        cameraName.value.splice(index, 1, res.data.cameraName);
                        cameraList.value.splice(index, 1, getListFn(cameraDetailConfig, res));
                        cameraShow.value[index] = true;
                        cameraVideoId.value[index] = res.data.videoAddress;
                    });
                    break;
                case 'assetsCode':
                    getInfoboardInfo(item.assetsCode).then(res => {
                        intelboardList.value.splice(index, 1, getListFn(intelboardDetailConfig, res));
                        intelboardName.value.splice(index, 1, res.data.infoBoardName);
                        intelboardShow.value[index] = true;
                    });
                    break;
                case 'sicId':
                    getInterChangeInfo(item.sicId).then(res => {
                        intelchangeList.value.splice(index, 1, getListFn(intelchangeDetailConfig, res));
                        intelchangeName.value.splice(index, 1, res.data.interChangeName);
                        intelchangeShow.value[index] = true;
                    });
                    break;
                case 'structureId':
                    Promise.all([getBridgeInfo({
                        structureId: item.structureId,
                        sectionId: 'G0015440090',
                    }), getBridgeInfoApi(
                        item.structureId
                    )]).then(([res1, res2]) => {
                        const res = {
                            data: {
                                ...res1.data,
                                technicalGrade: mqiGradeDict
                                    .find(item => res2.data.mqiGrade === item.mqiGrade)?.value || '未评定'},
                        };
                        bridgeList.value.splice(index, 1, getListFn(bridgeDetailConfig, res));
                        bridgeName.value.splice(index, 1, res.data.bridgeName);
                        bridgeShow.value[index] = true;
                    });
                    break;
                case 'serviceAreaId':
                    getServiceAreaInfo(item.serviceAreaId).then(res => {
                        serviceList.value.splice(index, 1, getListFn(serviceDetailConfig, res));
                        serviceName.value.splice(index, 1, res.data.serviceAreaName);
                        serviceShow.value[index] = true;
                    });
                    break;
                case 'stationId':
                    getTollStationInfo(item.stationId).then(res => {
                        stationList.value.splice(index, 1, getListFn(tollDetailConfig, res));
                        stationName.value.splice(index, 1, res.data.stationName);
                        stationShow.value[index] = true;
                    });
                    break;
                default:
                    break;
            }
            viewToMicro([+item.lng, +item.lat]);
        };

        const handleCloseFn = () => {
            viewToFk();
        };
        return {
            domManager,
            cameraIconList, serviceIconList,
            interChangeIconList, bridgeIconList,
            intelboardIconList, tollStationIconList,
            cameraList, cameraName, cameraShow,
            stationList, stationName, stationShow,
            intelchangeList, intelchangeName, intelchangeShow,
            intelboardList, intelboardName, intelboardShow,
            bridgeList, bridgeName, bridgeShow,
            serviceList, serviceName, serviceShow, cameraVideoId,
            clickCallback,
            handleCloseFn,
        };
    },
};
</script>

<style scoped>

</style>