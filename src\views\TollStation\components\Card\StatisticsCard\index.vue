<template>
    <div class="statistics-data-card">
        <div class="statistics-data-card__top">
            <div class="statistics-data-card__icon">
                <img :src="info.cover">
                <p>{{ info.value }}</p>
                <p>{{ info.unit }}</p>
                <div class="i-triangle"></div>
                <div class="i-triangle"></div>
                <div class="i-triangle"></div>
                <div class="i-triangle"></div>
            </div>
        </div>
        <div class="statistics-data-card__bottom">{{ info.title }}</div>
    </div>
</template>

<script setup>

defineProps({
    info: {
        type: Object,
        default() {
            return {};
        },
    },
});

</script>

<style lang="less" scoped>
.statistics-data-card {
    position: relative;
    width: 288px;
    height: 314px;
    background-image:
        linear-gradient(
            to bottom,
            rgba(36, 104, 242, .3),
            rgba(36, 104, 242, 0)
        );

    &::before {
        content: '';
        position: absolute;
        width: 50px;
        height: 2px;
        background-color: rgb(0, 237, 255);
        clip-path: polygon(0 0, 100% 0, calc(100% - 1.4px) 100%, 1.4px 100%);
        top: 0;
        left: 50%;
        transform: translateX(-50%);
    }

    &__icon {
        position: relative;
        width: 208px;
        height: 164px;
        text-align: center;

        img {
            display: inline-block;
            width: 126px;
            height: 100px;
            margin-left: 30px;
        }

        p:nth-of-type(1) {
            font-family: RoboData;
            font-size: 42px;
            color: #fff;
            margin-top: 2px;
        }

        p:nth-of-type(2) {
            font-size: 16px;
            margin-top: 10px;
        }

        .i-triangle {
            position: absolute;
            width: 12px;
            height: 12px;
            background-color: rgba(#fff, .1);

            &:nth-of-type(1) {
                top: 0;
                left: 0;
                clip-path: polygon(0 0, 100% 0, 0 100%);
            }

            &:nth-of-type(2) {
                top: 0;
                right: 0;
                clip-path: polygon(0 0, 100% 0, 100% 100%);
            }

            &:nth-of-type(3) {
                bottom: 0;
                left: 0;
                clip-path: polygon(0 0, 100% 100%, 0 100%);
            }

            &:nth-of-type(4) {
                bottom: 0;
                right: 0;
                clip-path: polygon(100% 0, 100% 100%, 0 100%);
            }
        }
    }

    &__data {
        font-size: 16px;

        span:first-child {
            color: #fff;
            font-size: 42px;
            font-family: RoboData;
            margin-right: 2px;
        }
    }

    &__top {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        height: 242px;
        padding: 32px 0 28px 0;
    }

    &__bottom {
        text-align: center;
        height: 72px;
        line-height: 72px;
        font-size: 24px;
        background-color: rgba(18, 74, 166, .2);
        backdrop-filter: blur(10px);
        color: #fff;
        border-top: 1px solid;
        border-image:
            linear-gradient(
                to right,
                rgba(1, 255, 229, .5),
                rgb(36, 104, 242),
            ) 1;
    }
}
</style>