import Legend1 from '@MaintainPlanLayer/assets/img/legend1.png';
import Legend1Active from '@MaintainPlanLayer/assets/img/legend1active.png';
import Legend2 from '@MaintainPlanLayer/assets/img/legend2.png';
import Legend2Active from '@MaintainPlanLayer/assets/img/legend2active.png';
import Legend3 from '@MaintainPlanLayer/assets/img/legend3.png';
import Legend3Active from '@MaintainPlanLayer/assets/img/legend3active.png';
import Legend4 from '@MaintainPlanLayer/assets/img/legend4.png';
import Legend4Active from '@MaintainPlanLayer/assets/img/legend4active.png';
import Legend5 from '@MaintainPlanLayer/assets/img/legend5.png';
import Legend5Active from '@MaintainPlanLayer/assets/img/legend5active.png';
import Legend6 from '@MaintainPlanLayer/assets/img/legend6.png';
import Legend6Active from '@MaintainPlanLayer/assets/img/legend6active.png';

// 养护管理图层——养护周期枚举
export const periodList = [
    {
        value: '',
        name: '全部',
    },
    {
        value: '1',
        name: '月度',
    },
    {
        value: '2',
        name: '季度',
    },
    {
        value: '3',
        name: '年度',
    },
    {
        value: '4',
        name: '中长期',
    },
];

export const planTypeEnum = {
    /** 路面 */
    ROAD_SURFACE: '1',
    /** 路基 */
    ROADBED: '2',
    /** 桥梁 */
    BRIDGE: '3',
    /** 隧道 */
    TUNNEL: '4',
    /** 交安 */
    TRAFFIC_SAFETY: '5',
    /** 警告 */
    WARNING: '6',
};

export const legendList = [
    {
        name: '路面',
        key: planTypeEnum.ROAD_SURFACE,
        icon: Legend6,
        activeIcon: Legend6Active,
    },
    {
        name: '路基',
        key: planTypeEnum.ROADBED,
        icon: Legend5,
        activeIcon: Legend5Active,
    },
    {
        name: '桥梁',
        key: planTypeEnum.BRIDGE,
        icon: Legend4,
        activeIcon: Legend4Active,
    },
    {
        name: '隧道',
        key: planTypeEnum.TUNNEL,
        icon: Legend3,
        activeIcon: Legend3Active,
    },
    {
        name: '交安',
        key: planTypeEnum.TRAFFIC_SAFETY,
        icon: Legend2,
        activeIcon: Legend2Active,
    },
    {
        name: '告警',
        key: planTypeEnum.WARNING,
        icon: Legend1,
        activeIcon: Legend1Active,
    },
];

/** 地图维度类型字典 */
export const mapTypeDictMap = {
    /** 宏观 */
    MACRO: 'MACRO',
    /** 中观 */
    MESO: 'MESO',
    /** 微观2维 */
    MICRO_2D: 'MICRO_2D',
    /** 微观3维 */
    MICRO_3D: 'MICRO_3D',
};

/** 微观三维地图数据显示类型 */
export const micro3dMapTypeDictMap = {
    /** 显示养护计划 */
    PLAN: 'PLAN',
    /** 显示仿真 */
    EMULATION: 'EMULATION',
};

/** 养护计划扎点类型字典 */
export const planTypeDictMap = {
    [planTypeEnum.ROAD_SURFACE]: '路面',
    [planTypeEnum.ROADBED]: '路基养护',
    [planTypeEnum.BRIDGE]: '桥梁',
    [planTypeEnum.TUNNEL]: '隧道',
    [planTypeEnum.TRAFFIC_SAFETY]: '交安',
    [planTypeEnum.WARNING]: '告警',
};

/** 养护计划扎点名称字典 */
export const planNameDictMap = {
    [planTypeEnum.ROAD_SURFACE]: '路面养护',
    [planTypeEnum.ROADBED]: '路基养护',
    [planTypeEnum.BRIDGE]: '桥梁养护',
    [planTypeEnum.TUNNEL]: '隧道养护',
    [planTypeEnum.TRAFFIC_SAFETY]: '交安设施养护',
};

/** 审核状态字典 */
export const auditStatusDictMap = {
    0: '待审核',
    1: '审核通过',
    2: '未通过',
};