<template>
    <Card title="施工事件高发时段统计" card-type="card-long-2">
        <template #content>
            <div class="card-content">
                <FokaiLine
                    y-name="数量"
                    :split-number="3"
                    :data="chartData"
                />
            </div>
        </template>
        <template #titleContent>
            <DatePicker
                v-model="dateTime"
                style="height: fit-content;"
                size="mini"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
            />
        </template>
    </Card>
</template>

<script setup>
import {getEventByDay} from '@/api/construction';
import {Card, FokaiLine} from '@/components/Common';
import dayjs from 'dayjs';
import {computed, ref, watch} from 'vue';
import {DatePicker} from 'element-ui';

// 时间筛选
const dateTime = ref(dayjs().format('YYYY-MM-DD'));

const cardData = ref([]);

const chartData = computed(() => {
    return [
        {
            name: '施工事件数量',
            color: 'rgb(87, 255, 213)',
            colorStops: ['rgba(87, 255, 213, 0.35)', 'rgba(0, 255, 149, 0)'],
            data: cardData.value,
        },
    ];
});

async function fetchData() {
    const {data} = await getEventByDay({
        day: dateTime.value,
    });
    cardData.value = data?.ylist?.map(item => ({
        name: `${+item.time}时`,
        value: +item.eventNum,
    }));
}

watch(
    () => dateTime.value,
    () => {
        fetchData();
    },
    {
        immediate: true,
    }
);

</script>

<style lang="less" scoped>
.card-content {
    height: 315px;
}
</style>