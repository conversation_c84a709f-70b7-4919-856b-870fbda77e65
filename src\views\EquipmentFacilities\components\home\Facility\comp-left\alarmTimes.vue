<template>
    <div class="alarm-times">
        <info-card
            width="556" height="84"
            :info="{
                cnTitle: '告警总数',
                enTitle: 'Total number of alarms',
                icon: 'event',
                unit: '次',
                value: data.value,
            }"
            :style-obj="styleObj"
        />
        <div class="chart">
            <!-- <left-pie-li
                v-if="data?.list?.length" :color="color"
                :data="data.list"
            /> -->
            <div
                v-for="item in data.list" :key="item.name"
                class="chart-item"
            >
                <div class="chart-item__top">
                    <div class="chart-item__top__icon">
                        <icon :size="12" :name="item.icon"/>
                    </div>
                    <span>{{ item.name }}</span>
                </div>
                <div class="chart-item__bottom">
                    <p class="chart-item__bottom__num">
                        <span>{{ item.value }}</span>
                        {{ item.unit }}
                    </p>
                    <p class="chart-item__bottom__ratio">
                        <span>{{ item.ratio * 100 }}</span>
                        %
                    </p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {ref, onMounted} from 'vue';
import {InfoCard} from '@/views/EquipmentFacilities/components/common/index';
import {Icon} from '@/components/Common';
import {facilityCountAlarm} from '@/api/equipment/facilitydisplay';
export default {
    name: 'alarmTimes',
    components: {
        InfoCard,
        Icon,
    },
    setup() {
        const styleObj = {
            cnTitleFontSize: 20,
            enTitleFontSize: 16,
            numFontSize: 42,
        };

        const color = ref(['rgba(255, 85, 85, .8)', 'rgba(238, 168, 15, .8)']);

        const data = ref({});

        const init = (() => {
            facilityCountAlarm().then(res => {
                data.value = {
                    value: res.data.countNum,
                    list: [
                        {
                            name: '桥梁告警',
                            value: res.data.bridgeAlarm.alarmNum,
                            unit: '次',
                            icon: 'qiaoliang',
                            ratio: res.data.bridgeAlarm.alarmProportion / 100,
                            color: 'rgba(255, 85, 85, .8)',
                        },
                        {
                            name: '互通告警', value: res.data.sicAlarm.alarmNum,
                            icon: 'lijiao',
                            unit: '次',
                            ratio: res.data.sicAlarm.alarmProportion / 100,
                            color: 'rgba(238, 168, 15, .8)',
                        },
                    ],
                };
            });
        });

        onMounted(() => init());

        return {
            styleObj,
            data,
            color,
        };
    },
};
</script>

<style lang="less" scoped>
.alarm-times {
    height: 172px;
    :deep(.info-card) {
        background-image: linear-gradient(to right, rgba(236, 62, 62, .3) 0%, rgba(236, 62, 62, 0) 76%);
    }

    .chart {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 80px;
        margin-top: 8px;
        .chart-item {
            width: 48%;
            background-color: rgba(18, 74, 166, 0.24);
            padding: 14px 18px 14px 24px;

            &__top {
                display: flex;
                align-items: center;
                margin-bottom: 3px;

                span {
                    font-size: 18px;
                font-family: 'PingFang';
                color: #fff;
                font-weight: 500;
                }

                &__icon {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 20px;
                    height: 20px;
                    margin-right: 4px;
                    border: 1px solid rgba(#fff, .2);
                }
            }

            &__bottom {
                display: flex;
                justify-content: flex-end;
                align-items: center;

                &__num, &__ratio {
                    font-size: 10px;
                    font-weight: 500;
                    font-family: 'PingFang';
                    color: rgba(#fff, .5);

                    span {
                        font-family: 'RoboData';
                        font-weight: 400;
                        font-size: 24px;
                        color: #fff;
                        margin-right: 4px;
                    }
                }

                &__num {
                    &::after {
                        content: '';
                        display: inline-block;
                        width: 1px;
                        height: 12px;
                        margin: 0 12px;
                        background-color: rgba(#fff, .3);
                    }
                }
            }
        }
    //     width: 556px;
    //     height: 206px;
    //     background-image: linear-gradient(to left, rgba(36, 104, 242, 0.3), rgba(36, 104, 242, 0));
    //     margin-top: 8px;

    //     :deep(ul) {
    //         display: flex;
    //         flex-direction: column;
    //         justify-content: center;

    //         li {
    //             height: 58px;
    //         }
    //     }
    }
}
</style>