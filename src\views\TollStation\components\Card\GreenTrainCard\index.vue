<template>
    <Card
        class="green-train-card" title="绿通车"
        card-type="card-long-2"
    >
        <template #content>
            <el-row :gutter="ratio * 16">
                <el-col
                    v-for="card in getCardInfo"
                    :key="card.field"
                    :span="6"
                >
                    <StatisticsCard :info="card"/>
                </el-col>
            </el-row>
        </template>
        <template #titleContent>
            <div class="btn-group">
                <div
                    v-for="item in timeTypeList"
                    :key="item.value"
                    :class="[
                        'btn-default',
                        {'btn-default__active': item.value === timeType},
                    ]"
                    @click="changeTimeType(item.value)"
                >
                    {{ item.label }}
                </div>
            </div>
        </template>
    </Card>
</template>

<script setup>
import {Card} from '@/components/Common';
import {computed, ref, watch} from 'vue';
import {Row as ElRow, Col as ElCol} from 'element-ui';
import {useUnit, formatCurrency} from '@/utils';
import {getGreenRevenue} from '@/api/tollStation';
import StatisticsCard from '../StatisticsCard/index.vue';
import StatisticsCover1 from '@/assets/images/tollStation/statistics-cover-1.png';
import StatisticsCover2 from '@/assets/images/tollStation/statistics-cover-2.png';
import StatisticsCover3 from '@/assets/images/tollStation/statistics-cover-3.png';
import StatisticsCover4 from '@/assets/images/tollStation/statistics-cover-4.png';

const {ratio} = useUnit();

const cardConfig = [
    {
        title: '绿通车免费车流量',
        cover: StatisticsCover1,
        field: 'greenFreeFlow',
        unit: '辆',
    },
    {
        title: '绿通车收费车流量',
        cover: StatisticsCover2,
        field: 'greenUnFreeFlow',
        unit: '辆',
    },
    {
        title: '绿通车免费金额',
        cover: StatisticsCover3,
        field: 'greenFreeFee',
        unit: '元',
    },
    {
        title: '绿通车收费金额',
        cover: StatisticsCover4,
        field: 'greenUnFreeFee',
        unit: '元',
    },
];
const timeTypeList = [
    {label: '昨日', value: 0},
    {label: '近7日', value: 1},
];

// 0昨天 1近7天
const timeType = ref(0);
const cardData = ref({
    greenFreeFee: '',
    greenFreeFlow: '',
    greenUnFreeFee: '',
    greenUnFreeFlow: '',
});

const getCardInfo = computed(() => {
    return cardConfig.map(item => {
        const value = cardData.value[item.field];
        return {
            ...item,
            value: formatCurrency(value),
        };
    });
});

async function fetchData() {
    const {data} = await getGreenRevenue({
        timeType: timeType.value,
    });
    cardData.value = {
        greenFreeFee: +data.greenFreeFee,
        greenFreeFlow: data.greenFreeFlow,
        greenUnFreeFee: +data.greenUnFreeFee,
        greenUnFreeFlow: data.greenUnFreeFlow,
    };
}

function changeTimeType(val) {
    timeType.value = val;
}

watch(
    () => timeType.value,
    () => {
        fetchData();
    },
    {
        immediate: true,
    }
);

</script>

<style lang="less" scoped>

.bg-gradient {
    background-image:
        linear-gradient(
            to bottom,
            rgba(12, 184, 208, .45),
            rgba(255, 255, 255, 0)
        );
}
</style>