<template>
    <div class="plan-flow-container card">
        <div class="plan-flow-btn">
            <el-button
                type="primary"
                size="small"
                :loading="loading"
                @click="doAnalysis"
            >仿真分析</el-button>
        </div>
        <div class="plan-flow-box">
            <no-data v-if="!getList.length"/>
            <plan-flow-item
                v-for="(item, index) in getList"
                :key="index"
                :data="item"
            />
        </div>
    </div>
</template>

<script>

import {computed} from 'vue';
import {Button} from 'element-ui';
import PlanFlowItem from './PlanFlowItem.vue';
import {planNameDictMap} from '@MaintainPlanLayer/config';
import {NoData} from '@/components/Common';

export default {
    name: 'PlanFlow',
    components: {
        PlanFlowItem,
        [Button.name]: Button,
        NoData,
    },
    props: {
        list: {
            type: Array,
            default: () => ([]),
        },
        loading: Boolean,
    },
    setup(props, {emit}) {
        // status: 1-创建，2-调整，3-审核不通过，4-审核通过 5-计划执行中

        const flowNameMap = {
            1: '养护计划创建',
            2: '养护计划调整',
            3: '养护计划审核',
            4: '养护计划审核',
            5: '养护计划执行',
        };

        const executeStatusTextMap = {
            0: '待执行',
            1: '进行中',
            2: '已执行',
        };

        const auditStatusTextMap = {
            0: '待审核',
            1: '审核通过',
            2: '未通过',
        };

        const getList = computed(() => (
            props.list.map(i => {
                return {
                    ...i,
                    flowName: flowNameMap[i.status],
                    executePlanText: i.executePlan ? '是' : '否',
                    executeStatusText: executeStatusTextMap[i.executeStatus],
                    maintainTypeText: planNameDictMap[i.maintainType],
                    auditStatusText: auditStatusTextMap[i.auditStatus],
                };
            })
        ));

        const doAnalysis = () => {
            emit('analysis');
        };

        return {
            getList,
            doAnalysis,
        };
    },
};
</script>

<style lang="less" scoped>
/* 组件样式 */
@import url('@/assets/css/common-layer.less');

.plan-flow-container {
    position: absolute;
    left: 26px;
    top: 130px;
    bottom: 20px;
    display: flex;
    flex-direction: column;

    .plan-flow-btn {
        text-align: right;
        padding-right: 20px;
    }

    .plan-flow-box {
        flex: 1;
        overflow: auto;
        padding-bottom: 20px;
    }

    .back-btn {
        position: absolute;
        right: -110px;
        top: 0;
        width: 94px;
        height: 30px;
        background: url('@WeatheWarningLayer/asstes/images/Freeze/back-btn-bg.svg') no-repeat;
        background-size: cover;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 12px;
        padding-left: 18px;
        cursor: pointer;

        &::before {
            content: '';
            width: 12px;
            height: 11px;
            background: url('@WeatheWarningLayer/asstes/images/Freeze/back-icon.svg') no-repeat;
            background-size: cover;
            position: absolute;
            top: 50%;
            left: 8px;
            transform: translate(0, -50%);
        }
    }
}
</style>