// 设备品牌 字典
export const brandDist =  new Map([
    ['0', '其他'],
    ['1', '大华'],
    ['2', '宇视'],
    ['3', '海康'],
    ['4', '上海三思'],
    ['5', '百度'],
    ['24', '郑州汉威'],
    ['25', '森斯泰克'],
    ['26', '交嵌'],
]);

// 设备类型 字典
export const typeDict = [
    {
        value: '2',
        label: '枪式摄像机',
    },
    {
        value: '3',
        label: '快球摄像机',
    },
    {
        value: '4',
        label: '卡口摄像机',
    },
    {
        value: '17',
        label: '指收费车道',
    },
    {
        value: '18',
        label: '收费广场摄像机',
    },
    {
        value: '1',
        label: '一体化云台摄像机',
    },
    {
        value: '54',
        label: '一体化抓拍摄像机',
    },
    {
        value: '10',
        label: '悬臂屏服务区',
    },
    {
        value: '107',
        label: '灯杆显示屏',
    },
];

// 系统分类ID 字典
export const systemDict = [
    {
        label: '收费监控',
        value: '2',
    },
    {
        label: '道路监控',
        value: '1',
    },
    {
        label: '可变情报板',
        value: '3',
    },
];

// 故障来源 字典
export const sourceDict = [
    {
        label: '自动上报',
        value: '1',
    },
    {
        label: '人工上报',
        value: '2',
    },
];

// 方向 字典
export const directionDict = new Map([
    ['1', '广州方向'],
    ['2', '开平方向'],
]);

// 故障原因 字典
export const faultReasonDict = new Map([
    ['1', '网络故障'],
    ['2', '电路故障'],
    ['3', '温度过高'],
    ['4', '其他'],
]);

export const levelDict = new Map([
    ['1', '待处置'],
    ['2', '维修中'],
    ['3', '已恢复'],
    ['4', '挂起'],
]);

export const mqiGradeDict = [
    {
        value: '一类',
        mqiGrade: 1,
        color: 'rgba(1, 233, 159, 0.9)',
    },
    {
        value: '二类',
        mqiGrade: 2,
        color: 'rgb(255, 144, 36)',
    },
    {
        value: '三类',
        mqiGrade: 3,
        color: 'rgb(156, 25, 25)',
    },
];

export const mqiGradeRoadDict = [
    {
        value: '优',
        mqiGrade: 1,
        color: 'rgba(1, 233, 159, 0.9)',
    },
    {
        value: '良',
        mqiGrade: 2,
        color: 'rgb(255, 211, 78)',
    },
    {
        value: '中',
        mqiGrade: 3,
        color: 'rgb(255, 144, 36)',
    },
    {
        value: '次',
        mqiGrade: 4,
        color: 'rgb(255, 78, 78)',
    },
    {
        value: '差',
        mqiGrade: 5,
        color: 'rgb(156, 25, 25)',
    },
];