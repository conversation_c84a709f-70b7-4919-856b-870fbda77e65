<template>
    <div class="pr">
        <Card
            title="施工事件统计"
            card-type="card-long-2"
        >
            <template #content>
                <div class="card-content">
                    <ApiTable
                        :columns="columns"
                        :api="getAllProject"
                        :pagination="false"
                        :request-params="{
                            projectType,
                        }"
                        :request-options="{
                            listfield: '',
                        }"
                        :height="318"
                        @row-click="handleRowClick"
                    >
                        <template #actions="{row}">
                            <div class="btn-action-list">
                                <div class="btn-action" @click.stop="handleCreate(row)">创建</div>
                                <div class="btn-action" @click.stop="handleview(row)">查看</div>
                            </div>
                        </template>
                    </ApiTable>
                </div>
            </template>
        </Card>
        <div class="modal-wrapper">
            <!-- 查看摄像列表 -->
            <ModalCard
                class="device-list-modal"
                title="查看摄像详情列表"
                :fullscreen-center="false"
                :visible.sync="visibleList"
                :show-foot="false"
                :width="600"
            >
                <ApiTable
                    :columns="deviceColumns"
                    :api="getDeviceByLngLat"
                    :pagination="false"
                    :height="330"
                    :request-params="getDeviceParams"
                    :request-options="{
                        listfield: '',
                    }"
                >
                    <template #name="{row}">
                        <div class="device-name">
                            <div class="device-name__icon">
                                <Icon name="sheshi-shexiangtou"/>
                            </div>
                            <div>{{ row.name }}</div>
                        </div>
                    </template>
                    <template #status="{row}">
                        <div
                            :class="[
                                'status',
                                `${row.status === 1 ? 'status-success' : 'status-error'}`,
                            ]"
                        >
                            {{ row.status === 1 ? '在线' : '离线' }}
                        </div>
                    </template>
                    <template #actions="{row}">
                        <div class="btn-action m-auto" @click="handleViewDevice(row)">查看</div>
                    </template>
                </ApiTable>
            </ModalCard>
            <!-- 查看摄像详情 -->
            <ModalCard
                class="device-list-modal"
                :title="currentDevice.name"
                :fullscreen-center="false"
                :visible.sync="visibleDetail"
                :show-foot="false"
                :width="400"
            >
                <div class="modal-video-wrapper">
                    <FlvPlayer :camera-info-id="currentDevice.code"/>
                </div>
            </ModalCard>
        </div>
    </div>
</template>

<script setup>
import {Card, ApiTable, ModalCard, Icon} from '@/components/Common';
import {getAllProject, getDeviceByLngLat} from '@/api/construction';
import {useUnit} from '@/utils';
import {computed, ref} from 'vue';
import FlvPlayer from '@/components/Common/video/flvPlayer.vue';
import {projectType} from '../../Visualization/store';
import dayjs from 'dayjs';

const emit = defineEmits(['create', 'rowClick']);

const {ratio} = useUnit();

const columns = [
    {
        prop: 'projectName',
        label: '工程名称',
        width: 145 * ratio.value,
        align: 'left',
    },
    {
        prop: 'eventStartTime',
        label: '事件发生时间',
        width: 130 * ratio.value,
        align: 'center',
        format: ({row}) => dayjs(row.eventStartTime).format('YYYY/MM/DD'),
    },
    {
        prop: 'direction',
        label: '行车方向',
        width: 106 * ratio.value,
        format: ({row}) => (row.direction === 1 ? '广州方向' : '开平方向'),
        align: 'center',
    },
    {
        prop: 'closeLaneNum',
        label: '关闭车道数',
        width: 116 * ratio.value,
        align: 'right',
    },
    {
        prop: 'projectType',
        label: '位置事件类型',
        width: 130 * ratio.value,
        align: 'right',
        format({row}) {
            const dictMap = {
                1: '桥梁施工',
                2: '路面施工',
                3: '互通立交施工',
                4: '设备实施施工',
            };
            return dictMap[row.projectType];
        },
    },
    {
        prop: 'duration',
        label: '预计持续时长',
        width: 130 * ratio.value,
        align: 'left',
        format: ({row}) => `${row.duration / 60}小时`,
    },
    {
        prop: 'eventStartStake',
        label: '位置桩号',
        width: 170 * ratio.value,
        align: 'right',
        format: ({row}) => `S15-${row.eventStartStake}`,
    },
    {
        prop: 'projectStatus',
        label: '工程状态',
        width: 110 * ratio.value,
        align: 'left',
        format({row}) {
            const dictMap = {
                1: '开工',
                2: '完工',
                3: '交工',
                4: '完成投资',
            };
            return dictMap[row.projectStatus];
        },
    },
    {
        prop: 'actions',
        label: '操作',
        width: 130 * ratio.value,
        align: 'left',
        slotName: 'actions',
    },
];

const deviceColumns = [
    {
        prop: 'name',
        label: '摄像机名称',
        width: 265 * ratio.value,
        slotName: 'name',
    },
    {
        prop: 'cameraType',
        label: '摄像机类型',
        width: 125 * ratio.value,
        align: 'center',
        format({row}) {
            const dictMap = {
                0: '枪机',
                1: '半球',
                2: '快球',
                3: '带云台枪机',
            };
            return dictMap[row.cameraType];
        },
    },
    {
        prop: 'status',
        label: '状态',
        width: 100 * ratio.value,
        align: 'center',
        slotName: 'status',
    },
    {
        prop: 'actions',
        label: '操作',
        width: 100 * ratio.value,
        align: 'center',
        slotName: 'actions',
    },
];

const visibleList = ref(false);

const visibleDetail = ref(false);

const currentEvent = ref({});

const currentDevice = ref({
    name: '',
    code: '',
});

const getDeviceParams = computed(() => ({
    lng: currentEvent.value?.lng,
    lat: currentEvent.value?.lat,
}));

function handleview(row) {
    currentEvent.value = row;
    visibleList.value = true;
}

function handleViewDevice(row) {
    currentDevice.value = {
        code: row.cameraIndexCode,
        name: row.name,
    };
    visibleDetail.value = true;
}

function handleCreate(row) {
    emit('create', row);
}

function handleRowClick(row, column, index) {
    emit('rowClick', row, column, index);
}

</script>

<style lang="less" scoped>
.card-content {
    /deep/ .el-table {
        &__header {
            width: 100% !important;
            background-color: rgba(0, 98, 167, .3);

            tr {
                background-color: rgba(0, 98, 167, .3);
                color: rgba(231, 239, 255, .9);
                font-size: 18px;
            }

            th {
                padding: 6px 0;
            }
        }

        &__row {
            cursor: pointer;

            &:hover .btn-action {
                background-color: rgb(0, 255, 229);
                color: #000;
            }
        }
    }
}

.btn-action {
    width: 52px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgb(0, 255, 229);
    border: 1px	solid rgb(0, 255, 229);
    font-size: 18px;
    cursor: pointer;

    &-list {
        display: flex;
        justify-content: space-between;
    }
}

.status {
    &::before {
        display: inline-block;
        content: '';
        width: 8px;
        height: 8px;
        border: 1px solid #fff;
        border-radius: 50%;
        margin-right: 2px;
    }

    &-success::before {
        background-color: rgb(0, 255, 114);
    }

    &-error::before {
        background-color: rgb(255, 60, 0);
    }
}

.device-name {
    display: flex;

    &__icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 24px;
        background-image: url('@/assets/images/base/ranking-bg.svg');
        background-size: 100% 100%;
        margin-right: 8px;
    }
}

.modal-wrapper {
    position: absolute;
    right: 0;
    top: 0;
    transform: translateX(calc(100% + 24px));
    display: flex;
    align-items: flex-start;

    > div:not(:last-child) {
        margin-right: 24px;
    }

    /deep/ .el-table {
        &__header-wrapper {
            display: none;
        }
    }
}

.modal-video-wrapper {
    height: 250px;
    padding: 12px;
    background-color: rgba(0, 98, 167, .35);
}
</style>