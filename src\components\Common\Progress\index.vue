<template>
    <div class="progress">
        <div class="line">
            <div
                class="color"
                :style="{'--width': lineWidth + '%', '--bgc': lineColor, '--afterbgc': lineAfterColor || lineColor}"
            ></div>
            <div class="progress-bg" :style="{'--bgc': backgroundColor,marginLeft: styledMarginLeft + 'px'}"></div>
        </div>
        <div v-if="showNumber" class="number">{{ number || lineWidth }}</div>
    </div>
</template>

<script>
import {useUnit} from '@/utils';
import {computed} from 'vue';

export default {
    props: {
        backgroundColor: {
            type: String,
            default: 'rgba(255, 255, 255, .15)',
        },
        lineColor: {
            type: String,
            default: 'rgba(0, 255, 149, .5)',
        },
        lineAfterColor: {
            type: String,
            default: 'rgb(0, 255, 149)',
        },
        lineWidth: {
            type: [Number, String],
            default: 6,
        },
        showNumber: {
            type: Boolean,
            default: false,
        },
        number: {
            type: Number,
            default: 0,
        },
        marginLeft: {
            type: [Number, String],
            default: 0,
        },
    },
    setup(props) {
        const {ratio} = useUnit();
        const styledMarginLeft =  computed(() => {

            return (props.marginLeft * ratio.value);
        });
        return {
            styledMarginLeft,
            ratio,
        };
    },
};
</script>

<style lang="less" scoped>
.progress {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .line {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .progress-bg {
                flex: 1;
                height: 6px;
                background: var(--bgc);
            }

            .color {
                position: relative;
                width: var(--width);
                height: 6px;
                background: var(--bgc);

                &::after {
                    content: '';
                    position: absolute;
                    right: 0;
                    top: 0;
                    display: block;
                    width: 2px;
                    height: 100%;
                    background-color:  var(--afterbgc);
                }
            }
        }

        .number {
            width: 60px;
            font-size: 20px;
            font-weight: 500;
            font-family: 'PingFang';
            margin-left: 20px;
            color: rgba(255, 255, 255, 1);
        }
    }
</style>