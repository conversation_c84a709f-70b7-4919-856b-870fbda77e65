<template>
    <div class="meso-left">
        <div class="meso-left-item">
            <card title="结构物监测总数及设备状态">
                <template #content>
                    <structureInspection/>
                </template>
            </card>
            <card class="short" title="结构物预警统计">
                <template #titleContent>
                    <div class="warning-total">
                        告警总数 <span>{{ warningStatisticsRef?.warnTotal || 0 }}</span>
                    </div>
                </template>
                <template #content>
                    <warningStatistics ref="warningStatisticsRef"/>
                </template>
            </card>
        </div>
        <div class="meso-left-item">
            <card class="small" title="交通事件列表">
                <template #titleContent>
                    <my-select v-model="eventType" :options="eventTypeOptions"/>
                    <my-select v-model="eventStatus" :options="eventStatusOptions"/>
                </template>
                <template #content>
                    <incidentList :event-type="eventType" :event-status="eventStatus"/>
                </template>
            </card>
            <card class="card-short-2" title="桥梁结构物健康状态">
                <template #content>
                    <healthState/>
                </template>
            </card>
        </div>
        <div class="meso-left-item">
            <card
                v-model="flowRadio" :radio-content="radioList"
                class="card-long-1 short" title="交通流量趋势"
            >
                <template #titleContent>
                    <my-select v-model="bridgeType" :options="bridgeTypeOption"/>
                </template>
                <template #content>
                    <flowTrend
                        :date-time="flowRadio" :bridge-type="bridgeType"
                        :bridge-type-option="bridgeTypeOption"
                    />
                </template>
            </card>
        </div>
    </div>
</template>

<script>
import {Card} from '@/components/Common/index';
import flowTrend from './flowTrend.vue';
import healthState from './healthState.vue';
import incidentList from './incidentList.vue';
import structureInspection from './structureInspection.vue';
import warningStatistics from './warningStatistics.vue';
import {MySelect} from '../../../common/index';
import {ref} from 'vue';

export default {
    name: '结构物中观左侧',
    components: {
        Card, MySelect,
        warningStatistics, structureInspection, incidentList,
        healthState, flowTrend,
    },
    setup() {
        const eventType = ref(null);
        const eventTypeOptions = [
            {value: null, label: '全部'},
            {value: 1, label: '计划性事件'},
            {value: 2, label: '突发性事件'},
            {value: 3, label: '养护施工'},
            {value: 4, label: '重大社会活动'},
            {value: 5, label: '重大事件'},
            {value: 6, label: '车辆故障'},
            {value: 7, label: '交通事故'},
            {value: 8, label: '路面状况'},
            {value: 9, label: '交通气象'},
            {value: 10, label: '服务区事件'},
            {value: 11, label: '交通灾害'},
            {value: 12, label: '中间绿化带养护维修'},
            {value: 13, label: '隧道养护维修'},
            {value: 14, label: '桥梁养护维修'},
            {value: 15, label: '路面养护维修'},
            {value: 16, label: '路面清扫'},
            {value: 17, label: '路况检测'},
            {value: 18, label: '机电设施养护维修'},
            {value: 19, label: '管线养护维修'},
            {value: 20, label: '高空作业'},
            {value: 21, label: '防护设施养护维修'},
            {value: 22, label: '其他'},
            {value: 23, label: '军事演习'},
            {value: 24, label: '领导视察'},
            {value: 25, label: '体育活动'},
            {value: 26, label: '文化活动'},
            {value: 28, label: '车流量大'},
            {value: 29, label: '交通管制'},
            {value: 30, label: '危化品事故'},
            {value: 31, label: '警卫任务'},
            {value: 32, label: '群体事件'},
            {value: 33, label: '电力事故'},
            {value: 34, label: '公共暴力'},
            {value: 35, label: '恶意事件'},
            {value: 36, label: '爆炸'},
            {value: 37, label: '燃气事故'},
            {value: 38, label: '化学污染'},
            {value: 40, label: '抛锚'},
            {value: 41, label: '爆胎'},
            {value: 43, label: '追尾'},
            {value: 44, label: '刮擦'},
            {value: 45, label: '翻车'},
            {value: 46, label: '车辆起火'},
            {value: 47, label: '撞固定物'},
            {value: 48, label: '撞抛洒物'},
            {value: 49, label: '涉遂事故'},
            {value: 50, label: '涉桥事故'},
            {value: 51, label: '其他车车事故'},
            {value: 52, label: '船舶撞桥'},
            {value: 53, label: '其他设施相关'},
            {value: 54, label: '撞动物'},
            {value: 55, label: '撞行人'},
            {value: 56, label: '人车坠落'},
            {value: 57, label: '其他人车事故'},
            {value: 59, label: '抛洒物'},
            {value: 60, label: '倒车'},
            {value: 61, label: '停车'},
            {value: 62, label: '逆行'},
            {value: 63, label: '积水'},
            {value: 64, label: '非机动车'},
            {value: 65, label: '劝离行人'},
            {value: 66, label: '道路结冰'},
            {value: 67, label: '湿滑'},
            {value: 68, label: '货物倾斜'},
            {value: 69, label: '货物散落'},
            {value: 70, label: '液体'},
            {value: 71, label: '机油泄漏'},
            {value: 72, label: '道路障碍'},
            {value: 73, label: '动物'},
            {value: 75, label: '大雾'},
            {value: 76, label: '霾'},
            {value: 77, label: '暴雨'},
            {value: 78, label: '台风'},
            {value: 79, label: '大风'},
            {value: 80, label: '高温'},
            {value: 81, label: '暴雪'},
            {value: 82, label: '雪'},
            {value: 83, label: '冰雹'},
            {value: 84, label: '寒潮'},
            {value: 85, label: '霜'},
            {value: 86, label: '冻'},
            {value: 87, label: '雷电'},
            {value: 88, label: '干旱'},
            {value: 89, label: '沙尘'},
            {value: 91, label: '缺油'},
            {value: 92, label: '无停车位'},
            {value: 93, label: '服务区关闭'},
            {value: 95, label: '路面火灾'},
            {value: 96, label: '路边火灾'},
            {value: 97, label: '道路损坏'},
            {value: 98, label: '水灾'},
            {value: 99, label: '山体滑坡'},
            {value: 100, label: '隧道塌方'},
            {value: 101, label: '隧道火灾'},
            {value: 102, label: '道路设施火灾'},
            {value: 103, label: '桥梁损坏'},
            {value: 104, label: '其他地质灾害'},
            {value: 105, label: '环境污染'},
            {value: 106, label: '海啸'},
            {value: 107, label: '地震'},
            {value: 112, label: '入口冲卡车辆'},
            {value: 113, label: '出口拦截车辆'},
            {value: 114, label: '其他伤亡事件'},
        ];

        const eventStatus = ref(null);
        const eventStatusOptions = [
            {value: null, label: '全部'},
            {value: 0, label: '未处置'},
            {value: 1, label: '已处置'},
        ];

        const warningStatisticsRef = ref(null);

        const flowRadio = ref('1');
        const radioList = [
            {label: '1', name: '时'},
            {label: '2', name: '日'},
            {label: '3', name: '月'},
        ];

        const bridgeType = ref(null);
        const bridgeTypeOption = [
            {value: null, label: '全部'},
            {value: '1', label: '九江大桥与扩建九江大桥'},
            {value: '2', label: '北江大桥'},
            {value: '3', label: '吉利河大桥'},
            {value: '4', label: '潭州大桥'},
        ];

        return {
            eventType, eventTypeOptions,
            eventStatus, eventStatusOptions,
            flowRadio, radioList,
            bridgeType, bridgeTypeOption,
            warningStatisticsRef,
        };
    },
};
</script>

<style lang="less" scoped>
.meso-left {
    width: 100%;
    height: calc(100% - 152px);

    .small :deep(.content) {
        padding: 10px 16px;
    }

    .short :deep(.content) {
        padding: 16px 24px;
    }
    .meso-left-item {
        display: flex;

        > div:not(:last-child) {
            margin-bottom: 23px;
            &:first-child {
                margin-right: 24px;
            }
        }

        .warning-total {
            font-weight: 400;
            font-family: 'RoboData';
            font-size: 18px;
            margin-right: 24px;
            color: rgba(#fff, .7);

            span {
                font-size: 20px;
                color: #fff;
                margin-left: 8px;
            }
        }

        .my-select {
            margin-right: 8px;
        }

        :deep(.el-select) {
            width: 125px;
        }

        :deep(.el-radio-group) {
            margin-right: 16px;
        }
    }
}
</style>