<template lang="">
    <div class="put-color-marker">
        <map-polygon
            v-for="item in polygons"
            :key="item.key"
            :polygon-instace="polygonManager"
            :list="item.polygon"
            :info="item.info"
        />
        <map-line
            v-for="item in borders"
            :key="item.key"
            :line-instace="lineManager"
            :list="item.border"
            :info="item.info"
        />
        <div
            ref="marker"
            class="marker-container"
        >
            <slot></slot>
        </div>
    </div>
</template>
<script>
import {ref, computed, watch, onMounted, onBeforeUnmount} from 'vue';
import {MapPolygon, MapLine} from '@/components/Common';
import {v4 as uuidv4} from 'uuid';
export default {
    name: 'PutColorMarker',
    components: {
        MapPolygon,
        MapLine,
    },
    props: {
        list: {
            type: Array,
            default: () => [],
        },
        info: {
            type: Object,
            default: () => ({}),
        },
        managerInstace: {
            type: Object,
            required: true,
        },
        polygonManager: {
            type: Object,
            required: true,
        },
        lineManager: {
            type: Object,
            required: true,
        },
    },
    setup(props) {
        const marker = ref(null);
        const name = computed(() => props.info.pointName || 'PutColorMarker');
        const color = computed(() => props.info.color || 'rgb(255,255,255)');
        const borderColor = computed(() => props.info.borderColor || 'rgb(255,255,255)');
        const borderWidth = computed(() => props.info.borderWidth ?? 1);
        const opacity = computed(() => props.info.opacity ?? 0.7);
        const isBorder = computed(() => props.info.isBorder ?? true);
        const isOverlap = computed(() => (props.info.isOverlap ?? true) && isBorder.value);
        const hasActive = computed(() => !!props.info.activeIconUrl || false);
        const polygons = computed(() => props.list.map(item => {
            const key = uuidv4();
            return {
                key,
                polygon: item,
                info: {
                    color: color.value,
                    name: name.value + '_polygon_' + key,
                    opacity: opacity.value,
                },
            };
        }));

        const pointMap = computed(() => {
            const map = new Map();
            if (isOverlap.value) {
                return map;
            }
            props.list.forEach(polygon => {
                polygon.forEach(point => {
                    const key = point.join(',');
                    const count = map.get(key) || 0;
                    map.set(key, count + 1);
                });
            });
            return map;
        });

        const borders = computed(() => {
            if (!isBorder.value) return [];
            return props.list.map(item => {
                let key = uuidv4();
                let border = [];
                if (isOverlap.value) {
                    border = [item];
                }
                else {
                    const map = pointMap.value;
                    const list = item.filter(point => map.get(point.join(',')) === 1);
                    let p = list[0];
                    let index = 0;
                    for (let i = 1; i < list.length; i++) {
                        const element = list[i];
                        const [x1, y1] = p;
                        const [x2, y2] = element;
                        const d = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
                        p = element;
                        if (d > 0.5) {
                            const fragment = list.slice(index, i);
                            border.push(fragment);
                            index = i;
                        }
                    }
                    const fragment = list.slice(index, list.length);
                    if (fragment.length) {
                        border.push(fragment);
                    }
                }

                return border.map((item, i) => {
                    key = key + '_' + i;
                    return {
                        key,
                        border: item,
                        info: {
                            color: borderColor.value,
                            name: name.value + '_border_' + key,
                            lineWidth: borderWidth.value,
                        },
                    };
                });
            }).flat();
        });

        const onClick = e => {
            props.info.onClick && props.info.onClick(e);
        };

        const onMouseenter = e => {
            if (hasActive.value) {
                addIcon(true);
            }
            props.info.onMouseenter && props.info.onMouseenter(e);
        };

        const onMouseleave = e => {
            if (hasActive.value) {
                removeIcon(name.value + '-active');
            }
            props.info.onMouseleave && props.info.onMouseleave(e);
        };

        function addIcon(active) {
            if (!props.info.position) return;
            const Name = active ? name.value + '-active' : name.value;
            props.managerInstace.addBubble(
                Name,
                props.info.position,
                {
                    iconUrl: (active ? props.info.activeIconUrl : props.info.iconUrl)
                        || 'maplayer/assets/image/base/icons/normal.png',
                    labelDom: active ? null : marker.value,
                    text: props.info.text,
                    customData: props.info.customData,
                    iconOffset: props.info.iconOffset,
                    textOffset: props.info.textOffset,
                    clickCallback: onClick,
                    onMouseenter: onMouseenter,
                    onMouseleave: onMouseleave,
                    renderOrder: Infinity,
                }
            );
        };

        function removeIcon(name) {
            props.managerInstace.removeBubbleByName(name);
        };

        watch(() => props.info, (_, {pointName}) => {
            removeIcon(pointName);
            removeIcon(pointName + '-active');
            addIcon();
        });

        onMounted(() => {
            addIcon();
        });

        onBeforeUnmount(() => {
            removeIcon(name.value);
            removeIcon(name.value + '-active');
        });
        return {
            marker,
            onClick,
            name,
            color,
            polygons,
            borders,
        };
    },
};
</script>
<style lang="less" scoped>
@import url('@/assets/css/maker.less');
</style>