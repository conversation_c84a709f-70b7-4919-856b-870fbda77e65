<template>
    <div class="toll-facility">
        <statistics-card
            v-for="(item, index) in list" :key="index"
            :info="item"
        />
    </div>
</template>

<script>
import {ref, onMounted} from 'vue';
import {StatisticsCard} from '@/views/EquipmentFacilities/components/common/index';
import {facilityNum} from '@/api/equipment/facilitydisplay';
export default {
    name: '收费设施',
    components: {
        StatisticsCard,
    },
    setup(props) {
        const list = ref([
            {
                value: 0,
                unit: '座',
                title: '收费站',
            },
            {
                value: 0,
                unit: '个',
                title: '收费门架',
            },
        ]);

        const init = () => {
            facilityNum().then(res => {
                list.value[0].value = res.data?.tollNum || 0;
                list.value[1].value = res.data?.tollGantryNum || 0;
            });
        };

        onMounted(() => {
            init();
        });

        return {
            list,
        };
    },
};
</script>

<style lang="less" scoped>
.toll-facility {
    display: flex;
    height: 314px;

    > div:first-child {
        margin-right: 10px;
    }
}
</style>