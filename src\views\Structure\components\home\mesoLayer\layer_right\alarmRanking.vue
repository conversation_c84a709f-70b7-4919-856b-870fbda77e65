<template>
    <div class="structure-list">
        <api-table
            :api="showStructureList"
            :columns="columns"
            :height="314"
            :pagination="false"
            :request-options="{
                listField: '',
            }"
            :request-params="{
                pageAble: true,
            }"
            @row-click="handleCellClick"
        >
            <template #index="{$index}">
                <div>
                    {{ $index + 1 }}
                </div>
            </template>
        </api-table>
    </div>
</template>

<script>
import {ApiTable} from '@/components/Common';
import {countWainingQuarter, countWainingMonth, countWainingYear} from '@/api/structure/index';
import {ref, computed} from 'vue';
import {useUnit} from '@/utils';
import {directionDict} from '@/config/maintain';
import {
    structureId, markerInfo,
    toMicroFn, makerList,
} from '../../../../utils/index';
export default {
    name: '告警次数排名前10',
    props: {
        type: {
            type: String,
            default: '',
        },
    },
    components: {
        ApiTable,
    },
    setup(props) {
        const {ratio} = useUnit();
        const showStructureList = computed(() => {
            switch (props.type) {
                case '1':
                    return () => countWainingYear();
                case '2':
                    return () => countWainingQuarter();
                case '3':
                    return () => countWainingMonth();
                default:
                    return () => countWainingYear();
            }
        });
        const columns = ref([
            {
                label: '排名',
                prop: 'index',
                width: `${80 * ratio.value}px`,
                align: 'left',
                slotName: 'index',
            },
            {
                label: '结构物名称',
                prop: 'structureName',
                width: `${140 * ratio.value}px`,
                align: 'left',
            },
            {
                label: '结构物方向',
                prop: 'sectionDirection',
                width: `${110 * ratio.value}px`,
                align: 'left',
                format: e => {
                    return directionDict.get(e.row.sectionDirection);
                },
            },
            {
                label: '告警次数',
                prop: 'warningCount',
                width: `${80 * ratio.value}px`,
                align: 'center',
                format: e => {
                    return e.row.warningCount + '次';
                },
            },
        ]);

        const handleCellClick = row => {
            structureId.value = row.structureId;
            markerInfo.value = makerList.value.find(item => item.structureId === row.structureId);
            toMicroFn();
        };

        return {
            showStructureList,
            columns,
            handleCellClick,
        };
    },
};
</script>

<style lang="less" scoped>
.structure-list {
    width: 100%;
    height: 314px;
}
</style>