import Vue from 'vue';
import '@/assets/js/element.js';
import router from '@/router/index.js';
import store from '@/store/index.js';
import App from './App.vue';
import '@/assets/theme/index.css';
import '@/assets/css/reset.less';
import 'animate.css';
import 'virtual:origin.css';

Vue.config.productionTip = false;
Vue.config.silent = true;
Vue.prototype.$bus = new Vue({
    store,
    router,
    render: h => h(App),
}).$mount('#app');
