<template>
    <div class="table-container">
        <table>
            <tr class="table_header">
                <th v-for="headerItem of info.header" :key="headerItem">{{ headerItem }}</th>
            </tr>
            <tr
                v-for="(row, index) in rows" :key="index"
                class="table_content"
            >
                <td v-for="(item, index) in row" :key="index">{{ item ? item : '-' }}</td>
            </tr>
        </table>

    </div>
</template>
<script setup>
import {computed} from 'vue';
const props = defineProps({
    info: {
        type: Object,
        default: () => ({
            header: ['上报时间', '行车方向', '设备桩号', '所属路段', '断面流量'],
            fields: ['reportTime', 'direction', 'stake', 'road', 'flow'],
        }),
    },
    list: {
        type: Array,
        default: () => ([
            {
                'stake': 'K55+80',
                'direction': '广州方向',
                'flow': 932,
                'road': '广州-开平',
                'reportTime': '2024-09-08 10:21:10',
            },
            {
                'stake': 'K55+80',
                'direction': '广州方向',
                'flow': 932,
                'reportTime': '2024-09-08 10:21:10',
            },
            {
                'stake': 'K55+80',
                'direction': '广州方向',
                'flow': 932,
                'reportTime': '2024-09-08 10:21:10',
            },
            {
                'stake': 'K55+80',
                'direction': '广州方向',
                'flow': 932,
                'reportTime': '2024-09-08 10:21:10',
            },
            {
                'stake': 'K55+80',
                'direction': '广州方向',
                'flow': 932,
                'reportTime': '2024-09-08 10:21:10',
            },
            {
                'stake': 'K55+80',
                'direction': '广州方向',
                'flow': 932,
                'reportTime': '2024-09-08 10:21:10',
            },
            {
                'stake': 'K55+80',
                'direction': '广州方向',
                'flow': 932,
                'reportTime': '2024-09-08 10:21:10',
            },
            {
                'stake': 'K55+80',
                'direction': '广州方向',
                'flow': 932,
                'reportTime': '2024-09-08 10:21:10',
            },
            {
                'stake': 'K55+80',
                'direction': '广州方向',
                'flow': 932,
                'reportTime': '2024-09-08 10:21:10',
            },
            {
                'stake': 'K55+80',
                'direction': '广州方向',
                'flow': 932,
                'reportTime': '2024-09-08 10:21:10',
            },
        ]),
    },
});

function convertToRows(fields, data) {
    return data.map(item => fields.map(field => item[field]));
}

const rows = computed(() => convertToRows(props.info.fields, props.list));
</script>
<style lang="less" scoped>
table {
                width: 100%;
                font-weight: 500;
                border-collapse: separate;
                border-spacing: 0 6px;
                .table_header {
                    color: rgba(231, 239, 255, 0.9);
                    font-size: 18px;
                    // border-bottom: 2px solid #48494B;
                    background: rgba(0, 98, 167, 0.3);
                }
                .table_content {
                    height: 40px;
                    font-size: 20px;
                    background: rgba(0, 98, 167, 0.3);
                    color: rgb(231, 239, 255);
                    &:hover {
                        background: linear-gradient(119.36deg, rgba(36, 104, 242, 0.5) 3.754%,
                        rgba(1, 255, 229, 0.35) 98.513%);
                        border-radius: 1px;
                    }
                }
                tr, td {
                    th, td {
            padding: 8px;
            text-align: center;
        }
                }
            }

</style>
