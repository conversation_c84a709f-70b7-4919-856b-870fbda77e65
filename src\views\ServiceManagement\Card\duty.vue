<template>
    <div>
        <api-table
            :columns="columns"
            :api="getDuty"
            :pagination="false"
            :request-params="{
                pageAble: false,
            }"
        />
    </div>
</template>

<script setup>
import {ApiTable} from '@/components/Common';
import {useUnit} from '@/utils';
import {getDuty} from '@/api/tollStation';

const {ratio} = useUnit();

const columns = [
    {
        label: '姓名',
        prop: 'dutyPersonName',
        width: `${112 * ratio.value}px`,
    },
    {
        label: '部门',
        prop: 'affiliatedDepartment',
        width: `${112 * ratio.value}px`,
    },
    {
        label: '单位',
        prop: 'affiliatedUnit',
        width: `${166 * ratio.value}px`,
    },
    {
        label: '联系方式',
        prop: 'contactPhone',
        width: `${166 * ratio.value}px`,
    },
];
</script>

<style scoped>

</style>