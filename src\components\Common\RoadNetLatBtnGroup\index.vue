<template>
    <div class="road-net-lat-btn-group">
        <div
            v-for="option in optionList"
            :key="`road-net-lat-btn__${option.value}`"
            :class="['road-net-lat-btn',{'road-net-lat-btn__active': option.active}]"
            @click="handleClick(option.value)"
        >
            <img
                v-if="option.icon"
                class="road-net-lat-btn__icon"
                :src="option.icon"
            >
            <span
                v-for="text in option.label"
                :key="`road-net-lat-btn__${text}`"
            >
                {{ text }}
            </span>
        </div>
    </div>
</template>

<script>
import {computed} from 'vue';
import {isArray} from 'lodash';

export default {
    props: {
        // 当前选中值
        checked: [String, Number],
        // 选项列表
        options: Array,
    },
    setup(props, {emit}) {
        const optionList = computed(() => (props?.options?.map(i => {
            return {
                ...i,
                label: isArray(i.label) ? i.label : [i.label],
                active: props.checked === i.value,
            };
        })));

        function handleClick(e) {
            emit('change', e);
        }

        return {
            optionList,
            handleClick,
        };
    },
};
</script>

<style lang="less" scoped>
.road-net-lat-btn {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    font-size: 12px;
    color: #fff;
    background-image: url('/maplayer/assets/image/base/icons/bg-area-divide.png');
    background-size: 100% 100%;
    cursor: pointer;

    > *:not(:first-child) {
        margin-top: 4px;
    }

    &:not(:last-of-type) {
        margin-bottom: 10px;
    }

    &__icon {
        display: block;
        width: 14px;
        height: 14px;
    }

    &__active,
    &:hover {
        background-image: url('/maplayer/assets/image/base/icons/bg-area-divide_active.png');
    }

    &-group {
        position: fixed;
        top: 105px;
        right: 30px;
    }
}
</style>