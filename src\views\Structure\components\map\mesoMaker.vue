<template>
    <div class="meso-maker">
        <!-- 桥梁扎点 -->
        <div
            v-for="(item) in bridgeList"
            :key="item.structureId"
        >
            <bridge-marker
                :manager-instace="domManager" icon-name="camera-bridge"
                :bubble-color="item.sort ? item.sort <= 3 ? 'red' : 'orange' : 'rgb(7, 118, 237)'"
                show-label
                :info="{
                    position: [item.lngRectify, item.latRectify, item.alt],
                    label: getFullNameFn(item),
                    clickCallback: () => clickCallback(item),
                }"
            />
            <map-line
                v-if="item.startLongitude && item.endLongitude"
                :list="[
                    [item.startLongitude, item.startLatitude],
                    [item.endLongitude, item.endLatitude],
                ]"
                :info="{
                    name: item.structureId + item.name,
                    color: 'rgba(239, 255, 255, .8)',
                }"
            />
        </div>

        <intelchange-marker
            v-for="item in interChangeListData"
            :key="item.sicId"
            :manager-instace="domManager" icon-name="camera-hutonglijiao"
            bubble-color="rgb(7, 237, 222)"
            :need-detail="false"
            show-label
            :info="{
                position: [item.lng, item.lat, item.alt],
                label: item.interChangeName,
            }"
        />
        <intelchange-marker
            v-for="(item, index) in bridgeEventListData"
            :key="index"
            :manager-instace="domManager" icon-name="event"
            bubble-color="rgb(218, 55, 49)"
            :need-detail="false"
            show-label
            :info="{
                position: [item.lng, item.lat, item.alt],
                label: item.name,
            }"
        />
    </div>
</template>

<script>
import {BubbleMarker} from '@/components/Common/index.js';
import {domManager} from './index.js';
import {
    getInterChangeList,
} from '@/api/equipment/highspeed.js';
import {ref, onMounted} from 'vue';
import {structureSensorWaininglist, structureListApi, eventDot} from '@/api/structure';
import {trafficTypeDict} from '@/config/structure';
import MapLine from './line.vue';
import {directionDict} from '@/config/maintain';
import {makerList} from '../../utils/index';

export default {
    props: {
        mapFilterData: {
            type: Array,
            default: () => ([]),
        },
    },
    components: {
        BridgeMarker: BubbleMarker,
        IntelchangeMarker: BubbleMarker,
        MapLine,
    },
    setup(props, {emit}) {
        const bridgeList = ref([]);
        const interChangeListData = ref([]);
        const bridgeEventListData = ref([]);

        const getIcon = () => {
            Promise.all([structureSensorWaininglist(), structureListApi()]).then(([res1, res2]) => {
                makerList.value = res2.data;
                bridgeList.value = res2.data.filter(item => item.structureType === 1).map(item => ({
                    ...item,
                    sort: res1.data.find(e => e.structureId === item.structureId)?.sort || '',
                    sensorWarningId: res1.data.find(e => e.structureId === item.structureId)?.sensorWarningId || '',
                }));
            });
            getInterChangeList().then(res => {
                interChangeListData.value = res.data.map(item => ({
                    ...item,
                    lng: item.centerPoint.split(',')[0],
                    lat: item.centerPoint.split(',')[1],
                    alt: item.centerPoint.split(',')[2] || 0,
                }));
            });
            eventDot().then(res => {
                bridgeEventListData.value = res.data?.map(item => ({
                    ...item,
                    name: trafficTypeDict.get(item.eventType),
                })) || [];
            });
        };
        onMounted(() => {
            getIcon();
        });

        const clickCallback = e => {
            emit('click', e);
        };

        const getFullNameFn = item => {
            let str = '';
            if (item.sort) {
                // eslint-disable-next-line max-len, vue/max-len
                str = `${item.name}\n${item.sectionName}\n${item.sectionDirection ? directionDict.get(item.sectionDirection + '') : ''}`;
            }
            else str = item.name;
            return str;
        };

        return {
            domManager,
            bridgeList,
            interChangeListData, bridgeEventListData,
            clickCallback,
            getFullNameFn,
        };
    },
};
</script>

<style lang="less" scoped>

</style>