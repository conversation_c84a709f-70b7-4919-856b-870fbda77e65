<template>
    <div class="normal-equipment">
        <fokai-line
            v-if="list[0].data.length" y-name="单位：台"
            :data="list"
            :tooltip-formatter="tooltipFormatter"
        />
        <no-data v-else/>
    </div>
</template>

<script>
import {FokaiLine, NoData} from '@/components/Common';
import {getDeviceNormal} from '@/api/equipment/equipmentdisplay';
import {ref} from 'vue';
export default {
    name: '正常运行设备数量图',
    props: {
        deviceType: {
            type: String,
            default: '',
        },
    },
    components: {
        NoData,
        FokaiLine,
    },
    setup(props) {
        const list = ref([
            {
                name: '正常运行设备数量',
                color: 'rgb(88, 255, 220)',
                colorStops: ['rgb(88, 255, 220)', 'rgba(88, 255, 220, 0.1)'],
                data: [],
            },
        ]);

        const init = () => {
            getDeviceNormal(props.deviceType).then(res => {
                if (res.data && res.data.length) {
                    list.value[0].data = res.data.map(item => ({
                        name: item.dateTimeScope,
                        value: item.deviceNum,
                        point: item.deviceRate,
                    }));
                }
                else {
                    list.value[0].data = [];
                }
            });
        };

        const tooltipFormatter = e => {
            return `<div class="tooltip-format">
                <div class="title-rb">${e[0].name}</div>
                <div class="info">
                    <div class="item">
                        <div class="item-name">运行数量</div>
                        <div class="item-info">${e[0].value}</div>
                    </div>
                    <div class="item">
                        <div class="item-name">在线率</div>
                        <div class="item-info">${e[0].data.point * 100}%</div>
                    </div>
                </div>
            </div>`;
        };

        return {
            list,
            init,
            tooltipFormatter,
        };
    },
};
</script>

<style lang="less" scoped>
.normal-equipment {
    width: 1184px;
    height: 190px;
}
</style>