<template>
    <div class="x-progress-wrapper">
        <div class="x-progress__bar">
            <div
                :class="[
                    'x-progress__inner',
                    {'hidden-end-point': percentage === 0},
                ]"
                :style="{width: percentage + '%'}"
            ></div>
        </div>
    </div>
</template>

<script setup>

defineProps({
    percentage: {
        type: Number,
        default: 0,
    },
});

</script>

<style lang="less" scoped>
.x-progress {
    &-wrapper {
        padding-bottom: 8px;
    }

    &__bar {
        position: relative;
        width: 100%;
        height: 10px;
        background-color: #000;
        padding: 1px 3px;

        &::before,
        &::after {
            position: absolute;
            top: 50%;
            margin-top: -6px;
            content: "";
            width: 2px;
            height: 12px;
            background-color: rgb(178, 199, 255);
        }

        &::before {
            left: 0;
        }

        &::after {
            right: 0;
        }
    }

    &__inner {
        position: relative;
        width: 81.4%;
        height: 100%;
        background-color: rgb(0, 255, 114);

        &.hidden-end-point {
            &::after,
            &::before {
                display: none;
            }
        }

        &::after {
            content: '';
            position: absolute;
            bottom: -8px;
            width: 4px;
            height: 6px;
            background-color: #fff;
            clip-path: polygon(50% 0, 100% 100%, 0 100%);
            right: 0;
            transform: translateX(30%);
        }

        &::before {
            position: absolute;
            top: 0;
            right: 0;
            content: '';
            width: 3px;
            height: 100%;
            background-color: rgb(0, 255, 114);
            border-left: 1px solid #000;
        }
    }
}
</style>