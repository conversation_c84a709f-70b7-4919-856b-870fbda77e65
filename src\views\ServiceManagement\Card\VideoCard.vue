<template>
    <card title="监控视频" class="video-card">
        <template #content>
            <el-carousel
                direction="vertical" :autoplay="true"
                :interval="40000" arrow="never"
                indicator-position="none"
                @change="handleCarouselChange"
            >
                <div
                    v-for="(group,index) in groupedVideos"
                    :key="group"
                >
                    <el-carousel-item>
                        <div
                            v-if="currentCarouselIndex === index"
                            class="item"
                        >
                            <main
                                v-for="item in group" :key="item"
                                @click="handclick(item)"
                            >
                                <flv-player
                                    class="video"
                                    :camera-info-id="item"
                                >
                                <!-- <div class="info">
                                    <p>{{ item.name || '-' }}</p>
                                </div> -->
                                </flv-player>

                            </main>
                        </div>
                    </el-carousel-item>
                </div>

            </el-carousel>
            <modal-card
                :visible="showDialog" disabled-confirm
                :title="'监控视频'" :width="800"
                @close="showDialog = false"
            >
                <flv-player
                    class="current-video"
                    :camera-info-id="currentInfo"
                />

            </modal-card>


        </template>
    </card>

</template>
<script setup >
import {Carousel as ElCarousel, CarouselItem as ElCarouselItem} from 'element-ui';
import FlvPlayer from '@/components/Common/video/flvPlayer.vue';
import {Card, NoData} from '@/components/Common/index';
import {ref, computed, onMounted, watch} from 'vue';
import {
    getCameraInfo, getCameraCodeList,
} from '@/api/serviceManager/index.js';
import {serviceId} from '@/views/ServiceManagement/utils/index';
import {ModalCard} from '@/components/Common';
const showDialog = ref(false);
const currentInfo = ref('');
const currentCarouselIndex = ref(0);
const visitedIndexes = ref([0]);
const handclick =  e => {
    showDialog.value = true;
    currentInfo.value = e;
};
const list = ref(['1', '2', '3', '4']);


const getData = async () => {
    const {data} = await getCameraCodeList({serviceId: serviceId.value});
    list.value = data;
};
const handleCarouselChange = index => {
    currentCarouselIndex.value = index;
    visitedIndexes.value = [...new Set([...visitedIndexes.value, index])]; // 添加当前索引并去重
    // console.log('output->visitedIndexes.value', visitedIndexes.value, list.value.length);
};
const groupedVideos = computed(() => {
    const chunks = [];

    for (let i = 0; i < list.value.length; i += 4) {
        chunks.push(list.value.slice(i, i + 4));
    }
    return chunks;
});
onMounted(() => {
    getData();
});
watch(() => serviceId.value, () => {
    getData();
});
</script>
<style scoped lang="less">
/deep/ .el-carousel__container {
    height: 320px;
}

.item {
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.currentInfo {
    position: relative;

    .info {
        position: absolute;
        top: 8px;
        right: 8px;
        background-color: rgba(0, 255, 0, .5);
        border-radius: 4px;
        padding: 4px 8px;

        p {
            color: #fff;
            margin-bottom: 4px;
        }
    }
}

main {
    position: relative;
    border-top: 1px solid rgb(41, 86, 157);
    backdrop-filter: blur(10.87px);

    .video {
        position: relative;
    }

    .info {
        position: absolute;
        top: 8px;
        right: 8px;
        background-color: rgba(0, 0, 0, .5);
        border-radius: 4px;
        padding: 4px 8px;

        p {
            color: #fff;
            margin-bottom: 4px;
        }
    }
}
</style>