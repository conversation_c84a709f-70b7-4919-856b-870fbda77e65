
<script>
import {onMounted, onUnmounted, watch, shallowRef} from 'vue';
import {isOnline} from '@/utils/common';
import {getAlt} from '@/utils/api.js';

export default {
    name: 'BuildMaker',
    props: {
        data: {
            type: Object,
            default: () => null,
        },
        showDistance: {
            type: Boolean,
            default: false,
        },
        // 扎点实例
        managerInstance: {
            type: Object,
            required: true,
        },
    },
    setup(props, {emit}) {
        const maker = shallowRef();

        const createContent = () => {
            const {name, distance, laneId} = props.data;

            const http = isOnline()
                ? `${location.origin}/layer`
                : 'http://**************:8080';

            const content = [
                {
                    width: 87,
                    height: 21,
                    backgroundImage:
                        `${http}/maplayer/assets/image/god/distance.png`,
                    position: [58, 0],
                    textList: [
                        {
                            text: distance?.toFixed(2),
                            fontSize: 14,
                            color: '#FFD800',
                            position: [33, 4],
                            textAlign: 'center',
                        },
                        {
                            text: 'KM',
                            fontSize: 10,
                            color: '#eff',
                            position: [70, 5.5],
                            textAlign: 'center',
                        },
                    ],
                },
                {
                    backgroundImage:
                        `${http}/maplayer/assets/image/god/build.png`,
                    width: 143,
                    height: 82,
                    position: [0, 22],
                    textList: [
                        {
                            text: name,
                            fontSize: 14,
                            color: '#fff',
                            position: [58 + 16, 10],
                            textAlign: 'left',
                        },

                        {
                            text: '占 ' + laneId?.split?.(';')?.join('、') + ' 车道',
                            fontSize: 12,
                            color: '#eff',
                            position: [58 + 16, 29],
                            textAlign: 'left',
                        },
                    ],
                },
            ];
            return content.slice(
                props.showDistance ? 0 : 1
            );
        };

        const drawContent = () => {
            const content = createContent();
            maker.value?.drawContent?.(content);
        };

        const getPosition = async () => {
            const {latitude, longitude} = props.data;
            const alt = await getAlt({
                x: longitude,
                y: latitude,
            }).catch(e => 200);

            return [longitude, latitude, alt ?? 200];
        };

        const createMaker = async () => {
            const {id} = props.data;
            const position = await getPosition();
            const content = createContent();

            maker.value = props.managerInstance.add(`build-${id}`, position, {
                width: 145,
                height: 104,
                clickCallback(e) {
                    emit('handlerClick', props.data, e);
                },
                content,
            });
        };

        const removeMaker = (id = props.data?.id) => {
            props.managerInstance.removeByName(`build-${id}`);
        };

        watch(() => props.data, (n, o) => {
            if (o.id === n.id) return drawContent();
            if (o.id) {
                removeMaker(o.id);
            }
            if (n.id) {
                createMaker();
            }
        }, {
            deep: true,
        });

        onMounted(() => createMaker());
        onUnmounted(() => removeMaker(props.data?.id));
    },
};
</script>
