<template>
    <div class="facility-maintenance">
        <div class="facility-maintenance__infocard">
            <info-card
                v-for="(item, index) in structureList" :key="index"
                :info="item"
                class="infocard__item"
                :class="{'active': item.type === showType}"
                @click.native="handleChangeType(item)"
            />
        </div>
        <div class="facility-maintenance__chart">
            <single-data-bar
                :need-name="false"
                :split-number="3"
                :data="bridgeList"
                :tooltip-formatter="tooltipFormatter"
            />
        </div>
    </div>
</template>

<script>
import {InfoCard} from '@/views/EquipmentFacilities/components/common/index';
import {SingleDataBar} from '@/components/Common/index';
import {facilityTypeNum} from '@/api/equipment/facilitydisplay';
import {ref, onMounted, onUnmounted} from 'vue';

export default {
    name: '设施养护',
    components: {
        InfoCard,
        SingleDataBar,
    },
    setup(props) {
        const structureList = ref([
            {
                value: 0,
                unit: '座',
                cnTitle: '桥梁',
                enTitle: 'BRIDGE',
                icon: 'qiaoliang',
                type: 'facility',
            },
            {
                value: 0,
                unit: '座',
                cnTitle: '边坡',
                enTitle: 'SIDE SLOPE',
                icon: 'bianpo',
                type: 'slope',
            },
            {
                value: 0,
                unit: '座',
                cnTitle: '隧道',
                enTitle: 'TUNNEL',
                icon: 'suidao',
                type: 'tunnel',
            },
            {
                value: 0,
                unit: '座',
                cnTitle: '涵洞',
                enTitle: 'CULVERT',
                icon: 'handong',
                type: 'culvert',
            },
        ]);

        const bridgeList = ref([
            {
                name: '特大桥梁',
                color: 'rgba(87, 255, 213, .6)',
                value: 0,
            },
            {
                name: '大型桥梁',
                color: 'rgba(87, 255, 213, .6)',
                value: 0,
            },
            {
                name: '中型桥梁',
                color: 'rgba(87, 255, 213, .6)',
                value: 0,
            },
            {
                name: '小型桥梁',
                color: 'rgba(87, 255, 213, .6)',
                value: 0,
            },
        ]);

        const structureConfig = new Map([
            ['桥梁', 'bridgeNum'],
            ['边坡', 'slopeNum'],
            ['隧道', 'tunnelNum'],
            ['涵洞', 'culvetNum'],
        ]);

        const apiData = ref({});

        const barConfig = {
            facilityConfig: new Map([
                ['特大桥梁', 'bestBridgeNum'],
                ['大型桥梁', 'bigBridgeNum'],
                ['中型桥梁', 'centerBridgeNum'],
                ['小型桥梁', 'smallBridgeNum'],
            ]),
            tunnelConfig: new Map([
                ['特长隧道', 'bestTunnelNum'],
                ['长型隧道', 'bigTunnelNum'],
                ['中型隧道', 'centerTunnelNum'],
                ['短型隧道', 'smallTunnelNum'],
            ]),
            slopeConfig: new Map([
                ['路堑', 'cuttinglNum'],
                ['路堤', 'terraceNum'],
                ['隧道仰坡', 'frontNum'],
                ['自然斜坡', 'angleNum'],
                ['墩台斜坡', 'pierNum'],
            ]),
            culvertConfig: new Map([
                ['圆管涵洞', 'circularNum'],
                ['拱涵洞', 'archCulvertNum'],
                ['箱涵洞', 'boxCulvertNum'],
                ['板涵洞', 'plateCulvertNum'],
                ['倒吸虹', 'suckBackNum'],
                ['其他', 'otherNum'],
            ]),
        };
        const init = () => {
            facilityTypeNum().then(res => {
                apiData.value = res.data;
                structureList.value = structureList.value.map(item => ({
                    ...item,
                    value: res.data?.[structureConfig.get(item.cnTitle)] || 0,
                }));
                bridgeList.value = bridgeList.value.map(item => ({
                    ...item,
                    value: res.data?.facilityTypeVO?.[barConfig.facilityConfig.get(item.name)] || 0,
                }));
            });
        };

        onMounted(() => {
            init();
        });

        const barData = {
            facilityData: ['特大桥梁', '大型桥梁', '中型桥梁', '小型桥梁'],
            tunnelData: ['特长隧道', '长型隧道', '中型隧道', '短型隧道'],
            slopeData: ['路堑', '路堤', '隧道仰坡', '自然斜坡', '墩台斜坡'],
            culvertData: ['圆管涵洞', '拱涵洞', '箱涵洞', '板涵洞', '倒吸虹', '其他'],
        };

        const showType = ref('facility');
        const handleChangeType = item => {
            showType.value = item.type;
            bridgeList.value = barData[item.type + 'Data'].map(e => ({
                name: e,
                value: apiData.value[item.type + 'TypeVO']?.[barConfig[item.type + 'Config'].get(e)] || 0,
                color: 'rgba(87, 255, 213, .6)',
            }));

        };

        onUnmounted(() => {
            structureConfig.clear();
        });

        const tooltipFormatter = e => {
            return `<div class="tooltip-format">
                <div class="title-pf">${e[0].data.name}</div>
                <div class="info">
                    <div class="item">
                        <div class="item-name">数量</div>
                        <div class="item-info">${e[0].data.value}</div>
                    </div>
                </div>
            </div>`;
        };

        return {
            structureList,
            bridgeList,
            showType,
            tooltipFormatter,
            handleChangeType,
        };
    },
};
</script>

<style lang="less" scoped>
.facility-maintenance {
    width: 556px;
    height: 314px;
    &__infocard {
        display: flex;
        flex-wrap: wrap;

        .infocard__item {
            margin-bottom: 8px;
            cursor: pointer;

            &.active {
                background-image: linear-gradient(to right, rgba(1, 255, 229, .3), rgba(1, 255, 229, 0));
            }

            &:nth-of-type(2n) {
                margin-left: 6px;
            }
        }
    }

    &__chart {
        width: 100%;
        height: 153px;
    }
}
</style>