<template>
    <div class="video-palyer-box">
        <videoPlay
            v-bind="_options"
        />
    </div>
</template>
<script setup lang="ts">
import {computed, reactive} from 'vue';
import 'vue3-video-play/dist/style.css';
// @ts-ignore
import videoPlay from 'vue3-video-play';
const props = defineProps({
    options: {
        type: Object,
        default: () => ({}),
    },
});

const defaultOptions = reactive({
    width: '100%', // 播放器宽度
    height: '100%', // 播放器高度
    title: '', // 视频名称
    src: '', // 视频源
    // type: 'm3u8',
    poster: '',
    muted: true, // 静音
    webFullScreen: false,
    speedRate: ['0.75', '1.0', '1.25', '1.5', '2.0'], // 播放倍速
    autoPlay: true, // 自动播放
    loop: true, // 循环播放
    mirror: false, // 镜像画面
    ligthOff: false, // 关灯模式
    volume: 0.3, // 默认音量大小
    control: false, // 是否显示控制
    controlBtns: [
        // 'audioTrack',
        // 'quality',
        // 'speedRate',
        // 'volume',
        // 'setting',
        // 'pip',
        // 'pageFullScreen',
        // 'fullScreen',
    ], // 显示所有按钮,
});

const _options = computed(() => {
    return Object.assign(defaultOptions, props.options);
});

</script>

<style scoped lang="less">
.video-palyer-box {
    width: 100%;
    height: 100%;
}

</style>

<style>
.d-player-video-main {
    object-fit: cover;
}

/* 隐藏暂停按钮 */
.d-play-btn {
    display: none;
}
</style>