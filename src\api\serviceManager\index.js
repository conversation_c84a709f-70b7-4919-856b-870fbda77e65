// 设备设施高速总览接口
import {request} from '@/utils/network-helper/index';
import {baseConfig} from '@/utils/common';
const {basePrefix} = baseConfig;
import {ref, computed} from 'vue';

const baseStructure = `${basePrefix}/v1`;
import {serviceId} from '@/views/ServiceManagement/utils/index';
// 道路包场枚举
const enums = ref();
let isLoadingEnums = false;
export const getUseEnum = async () => {
    const {data, code} = await request.get('/ihs/monitor/mis/config/info?type=1&subType=50');
    if (+code === 200) {
        return {data, code};
    }
    return {};
};
export async function useEnum(name) {
    const {data} = await getUseEnum();
    enums.value = data;
    return computed(() => (enums.value ? enums.value[name] : []));
}
export function useEnumMap(name) {
    const enums = useEnum(name);
    return computed(() => {
        const map = new Map();
        enums.value.forEach(item => map.set(item.code, item));
        return map;
    });
}
// 服务区总览
export const getSectionOverView = async () => {
    const {data, code} = await request.get(
        baseStructure + '/servicearea/overview',
        {
            serviceId: serviceId.value,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};
// 情报板详情
export const getInfoboardContent = async id => {
    const {data, code} = await request.get(
        baseStructure + '/servicearea/getInfoboardContent',
        {deviceCode: id}
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 饱和度趋势
export const getTrendHour = async (dt = '20240605') => {
    const {data, code} = await request.get(
        baseStructure + '/servicearea/saturation/trendHour',
        {
            dt,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};
// 事件列表 - 分页
export const getEventPage = async params => {
    // {
    //     "plate": "粤A8888", // 车牌号，可不传
    //         "vehicleType": 0, // 车辆类型：3小客车、4面包车、5货车、6大巴车、7两轮车、8危化品车、9轿车、10行人、99其他，可不传
    //             "stake": "K3121+106", // 位置桩号，可不传
    //                 "enterTime": "2024-06-06 17:00:00", // 进入时间，可不传
    //                     "exitTime": "2024-06-06 17:00:00", // 驶出时间，可不传
    //                         "pageNumber": 1,
    //                             "pageSize": 5
    // }
    const {data, code} = await request.post(
        baseStructure + '/servicearea/event/page',
        params
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};
// 车辆列表 - 分页
export const getVehiclePage = async payload => {
    const extendedPayload = {serviceId: serviceId.value, ...payload};

    // {
    //     "plate": "粤A9999", // 车牌号，可不传
    //         "vehicleType": 0, // 车辆类型：3小客车、4面包车、5货车、6大巴车、7两轮车、8危化品车、9轿车、10行人、99其他，可不传
    //             "stake": "K3121+106", // 位置桩号， 可不传
    //                 "enterTime": "2024-06-01 23:28:01", // 驶入时间，可不传
    //                     "exitTime": "2024-06-01 23:28:01", // 驶出时间，可不传
    //                         "serviceId": "SD101", // 服务区id，可不传
    //                             "pageNumber": 1,
    //                                 "pageSize": 5
    // }
    const {data, code} = await request.post(
        baseStructure + '/servicearea/vehicle/page',
        extendedPayload
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};
// 情报板内容发送列表-分页
export const getInfoBoard = async params => {
    // {
    //     "serviceId": "SD101", //  服务区id, 可不传
    //         "infoBoardName": "雅瑶服务区-情报板1", // 情报板名称, 可不传
    //             "infoBoardType": 0, // 情报板类型, 可不传
    //                 "pushTimeStart": "2024-06-01 23:28:01", // 发送开始时间, 可不传
    //                     "pushTimeEnd": "2024-06-01 23:28:01", // 发送结束时间, 可不传
    //                         "pushStatus": 0, // 发送状态, 可不传
    //                             "pageNumber": 1,
    //                                 "pageSize": 5
    // }
    const {data, code} = await request.post(
        baseStructure + '/servicearea/infoBoard/contentRecord',
        params
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};
// 情报板内容发送
export const getInfoBoardPush = async params => {
    // {
    //     "deviceCode": "1",
    //         "deviceType": "58",
    //             "infoBoardName": "1",
    //                 "infoBoardType": 1,
    //                     "place": "雅瑶服务区",
    //                         "serviceId": "SD101",
    //                             "content": "下雨路滑，能见度底"
    // }
    const {data, code} = await request.post(
        baseStructure + '/servicearea/infoBoard/contentPush',
        params
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};
// 事件详情
export const getEventDetail = async (id = '1') => {
    const {data, code} = await request.get(
        baseStructure + '/servicearea/event/detail',
        {
            id,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};
// 车辆详情
export const getVehicleDetail = async (params = {plate: '粤A8888', serviceId: serviceId.value}) => {
    // "plate": "粤A8888"
    // "serviceId": "SD101"
    const {data, code} = await request.get(
        baseStructure + '/servicearea/vehicle/detail',
        params
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};
// 设备详情
export const getDeviceDetail = async (deviceCode = '1') => {
    const {data, code} = await request.get(
        baseStructure + '/servicearea/device/detail',
        {
            deviceCode,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};
// 设备扎点-情报板、卡口、相机
export const getDeviceList = async () => {
    const {data, code} = await request.get(
        baseStructure + '/servicearea/device/list'

    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};
// 设备在线信息总览
export const onlineOverView = async () => {
    const {data, code} = await request.get(
        baseStructure + '/servicearea/device/onlineOverView'

    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};
// 设备运行状态
export const getDeviceState = async payload => {
    const extendedPayload = {serviceId: serviceId.value, ...payload};
    const {data, code} = await request.get(
        baseStructure + '/servicearea/device/statusList',
        extendedPayload
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

export const getCameraList = async payload => {
    const extendedPayload = {serviceId: serviceId.value, ...payload};

    const {data, code} = await request.get(
        baseStructure + '/servicearea/getCameraList',
        extendedPayload
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 1.3 情报板icon(情报板扎点显示)
export const getInfoBoardList = async payload => {
    const extendedPayload = {serviceId: serviceId.value, ...payload};
    const {data, code} = await request.get(
        baseStructure + '/servicearea/getInfoBoardList',
        extendedPayload
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};
// Etc
export const getEtcPage = async payload => {
    const extendedPayload = {serviceId: serviceId.value, ...payload};
    const {data, code} = await request.post(
        baseStructure + '/servicearea/tollGantryPage',
        extendedPayload
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};
export const getRoadList = async payload => {
    const extendedPayload = {serviceId: serviceId.value, ...payload};
    const {data, code} = await request.get(
        baseStructure + '/infra/getRoadList',
        extendedPayload
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};
export const getStakeNumber = async payload => {
    const extendedPayload = {serviceId: serviceId.value, ...payload};
    const {data, code} = await request.get(
        baseStructure + '/stake/getStakeNumber',
        extendedPayload
    );
    if (+code === 200) {
        return {data, code};
    }


    return {};
};
export const getCameraInfo = async payload => {
    const {data, code} = await request.post(
        'http://44.12.89.213:8091/api/cameraInfo/queryByParams',
        payload
    );
    if (+code === 200) {
        return {data, code};
    }

    return {};
};
export const getCameraClose = async payload => {
    const {data, code} = await request.get(
        'http://44.12.89.213:8091/api/video/flv/close',
        payload
    );
    if (+code === 200) {
        return {data, code};
    }

    return {};
};
export const getCameraCodeList = async payload => {
    const {data, code} = await request.get(
        baseStructure + '/servicearea/getCameraCodeList',
        payload
    );
    if (+code === 200) {
        return {data, code};
    }

    return {};
};