<template>
    <div class="axle-analysis">
        <div class="axle-analysis__tab">
            <div
                class="btn-default" :class="{'btn-default__active': activeType === 1}"
                @click="handleType(1)"
            >类型</div>
            <div
                class="btn-default" :class="{'btn-default__active': activeType === 2}"
                @click="handleType(2)"
            >轴型</div>
        </div>
        <div class="axle-analysis__info">
            <div class="axle-analysis__info__left">
                <img src="../../../../images/structure_axle.png" alt="">
                <p>货车日均轴载</p>
                <span>超载{{ apiData.carOverValue || 0 }}%</span>
            </div>
            <div class="axle-analysis__info__right">
                {{ apiData.carAxleLoad || 0 }} <span>kg</span>
            </div>
        </div>
        <div class="axle-analysis__chart">
            <center-pie-dom :list="list"/>
        </div>
    </div>
</template>

<script>
import {ref, onMounted, watch} from 'vue';
import {CenterPieDom} from '@/components/Common';
import {bridgeAxle} from '@/api/structure/index';
import {carTypeDict, carAxleDict} from '../../../../utils/axle.js';
export default {
    name: '轴载数据分析',
    props: {
        bridgeType: {
            type: String,
            default: '',
        },
        dateTime: {
            type: String,
            default: '',
        },
    },
    components: {
        CenterPieDom,
    },
    setup(props) {
        const activeType = ref(1);

        const list = ref([
            {name: '一型货车', value: 0, ratio: 0},
            {name: '二型货车', value: 0, ratio: 0},
            {name: '三型货车', value: 0, ratio: 0},
            {name: '四型货车', value: 0, ratio: 0},
            {name: '五型货车', value: 0, ratio: 0},
            {name: '六型货车', value: 0, ratio: 0},
        ]);
        const apiData = ref({});

        const getData = () => {
            if (activeType.value === 1) {
                list.value = carTypeDict.map(item => {
                    const obj = apiData.value.carTypeLoadCountVOList?.find(e => e.carType === item.key);
                    return {
                        name: item.value,
                        value: obj ? obj.carNum : 0,
                        overLoad: obj ? obj.carAxleLoad : 0,
                        ratio: obj ? obj.carOverValue : 0,
                    };
                });
            }
            else {
                const moreCar = apiData.value.carAxleTypeLoadVOList?.filter(item => {
                    return +item.carAxleType > 6;
                }) || [];
                const moreCarAxleNum = moreCar.reduce((pre, next) => {
                    return pre + (+next.carAxleNum);
                }, 0) || 0;
                const moreOverLoad = moreCar.reduce((pre, next) => {
                    return pre + (+next.carAxleLoad);
                }, 0) || 0;
                const moreCarOverValue = moreCar.reduce((pre, next) => {
                    return pre + (+next.carOverValue);
                }, 0) || 0;

                list.value = [...carAxleDict.slice(0, 6).map(item => {
                    const obj = apiData.value.carAxleTypeLoadVOList?.find(e => e.carAxleType === item.key);
                    return {
                        name: item.value,
                        value: obj ? obj.carAxleNum : 0,
                        overLoad: obj ? obj.carAxleLoad : 0,
                        ratio: obj ? obj.carOverValue : 0,
                    };
                }), {
                    name: '六轴以上',
                    value: moreCarAxleNum,
                    overLoad: moreOverLoad,
                    ratio: moreCarOverValue,
                }];
            }
        };

        const init = () => {
            bridgeAxle({
                bridgeType: props.bridgeType,
                dateTime: props.dateTime,
            }).then(res => {
                apiData.value = res.data;
                getData();
            });
        };

        onMounted(() => init());

        watch(() => [props.bridgeType, props.dateTime], () => {
            init();
        });

        const handleType = type => {
            activeType.value = type;
            getData();
        };

        return {
            activeType, list, apiData,
            handleType,
        };
    },
};
</script>

<style lang="less" scoped>
.axle-analysis {
    width: 100%;
    height: 321px;

    &__tab {
        display: flex;
        justify-content: flex-end;

        .btn-default {
            min-width: 58px;
            padding: 0 8px;
            height: 22px;
            line-height: 20px;
            text-align: center;
            background-color: #142758;
            font-size: 14px;
            border: 1px solid #142758;
            color: rgb(128, 144, 187);
            cursor: pointer;
            clip-path: polygon(0 0, 100% 0, 100% calc(100% - 5px), calc(100% - 5px) 100%, 0 100%);
            margin-left: 4px;

            &:hover {
                color: #fff;
            }

            &__active {
                position: relative;
                color: #fff;
                background-color: #2460b7;
                border-color: #6897e5;

                &::after {
                    position: absolute;
                    right: -1.5px;
                    bottom: 0;
                    content: '';
                    width: 8px;
                    height: 1.5px;
                    background-color: #6897e5;
                    transform: rotate(-45deg);
                }
            }
        }
    }

    &__info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 80px;
        margin: 14px 0 24px;
        padding: 0 22px 0 31px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(8, 74, 178, 0.35);
        background-color: rgba(8, 35, 79, 0.8);
        border-left: 2px solid rgb(1, 255, 229);
        background-image: linear-gradient(to right, rgba(1, 255, 229, .3), rgba(1, 255, 229, 0) 70%);

        &__left {
            display: flex;
            justify-content: space-between;
            align-items: center;
            img {
                width: 86px;
                height: 86px;
                transform: translateY(-8px);
            }

            p {
                font-family: 'PingFang';
                font-size: 24px;
                font-weight: 500;
                color: #fff;
                margin: 0 16px 0 24px;
            }

            span {
                display: block;
                font-family: 'PingFang';
                font-size: 16px;
                font-weight: 400;
                color: #fff;
                padding: 4px 8px;
                background-color: rgba(236, 62, 62, .5);
            }
        }

        &__right {
            color: rgba(#fff, .9);
            font-family: 'RoboData';
            font-weight: 400;
            font-size: 32px;

            span {
                font-size: 16px;
                margin-left: 3px;
                color: rgba(#fff, .5);
            }
        }
    }

    &__chart {
        position: relative;
        width: 100%;
        height: 182px;

        :deep(.full) {
            .t {
                top: 0;
            }
            .l {
                left: 0;
                flex-direction: row;
            }
            .r {
                right: 0;
            }

            .b {
                bottom: 0;
            }

            .item.l {
                height: 50px;
            }
            .item.r {
                height: 41px;
            }

            .item {
                .name {
                    span {
                        font-family: 'PingFang';
                        font-size: 16px;
                        font-weight: 500;
                    }
                }
                .value {
                    font-size: 22px;
                    font-weight: 400;
                }
            }
        }
    }
}
</style>