<template>
    <div class="layout-header">
        <div class="layout-header__left">
            <!-- <div class="layout-header__logo">
                <img src="@/assets/images/logo.svg">
            </div> -->
            <div class="layout-header__sysname">佛开高精度数字底图高分智能综合监控平台</div>
        </div>
        <div class="layout-header__middle">
            <navbar/>
        </div>
        <div class="layout-header__right">
            <!-- <icon-button-group/> -->
            <!-- <div class="layout-header__gapline"></div> -->
            <user/>
        </div>
    </div>
</template>

<script>
import Navbar from './Navbar/index.vue';
// import IconButtonGroup from './IconButtonGroup/index.vue';
import User from './User/index.vue';

export default {
    components: {
        Navbar,
        // IconButtonGroup,
        User,
    },
};
</script>

<style lang="less" scoped>
.layout-header {
    display: flex;
    width: 100%;
    height: 64px;
    background-color: #102e6d;
    color: #fff;

    &__left {
        width: 440px;
        display: flex;
        align-items: center;
        flex-shrink: 0;
    }

    &__middle {
        flex: 1;
    }

    &__right {
        display: flex;
        align-items: center;
        padding: 0 16px;
    }

    &__gapline {
        width: 4px;
        height: 12px;
        background-color: rgb(40, 40, 40);
        margin: 0 10px;
    }

    // &__logo {
    //     display: flex;
    //     justify-content: center;
    //     align-items: center;
    //     width: 170px;
    //     height: 64px;
    //     background-color: rgb(40, 40, 40);

    //     img {
    //         display: block;
    //         width: 128px;
    //     }
    // }

    &__sysname {
        padding: 0 14px;
        font-size: 20px;
        font-weight: 700;
        color: #fff;
    }
}
</style>