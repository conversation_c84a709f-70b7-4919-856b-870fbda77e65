import {addIcon, removeIcon} from '../index';
import {getPointHeight} from '@/utils';
// icon管理器
class IconManager {
    constructor(engine) {
        this.iconMap = new Map();
        this.setHaveMap = new Map();
        this.engine = engine;
    }
    async addIcon(name, point, url, options) {
        if (this.iconMap.has(name)) {
            this.removeIconByName(name);
        }
        if (this.setHaveMap.has(name)) {
            this.setHaveMap.get(name)();
        }
        const next = await this.initHeight(name, point);
        if (!next) return;

        const {width, height, offset = [0, -50], customData, clickCallback} = options || {};
        let {icon, _engine} = addIcon(point, url, {
            width, height, offset, customData, _engine: this.engine,
        });
        this.iconMap.set(name, icon);

        if (clickCallback && typeof clickCallback === 'function') {
            icon.receiveRaycast = true;
            _engine.event.bind(icon, 'click', clickCallback);
        }
    }

    // 初始化高程
    async initHeight(name, point) {
        if (!point[2]) {
            // 解决请求height时await阻塞导致再次添加时无法清除扎点
            let have = true;
            this.setHaveMap.set(name, () => {
                have = false;
            });
            const height = await getPointHeight(point);
            point[2] = height?.data?.[0] || 0;
            if (have) {
                this.setHaveMap.delete(name);
            }
            return have;
        }
        return true;
    }

    removeIconByName(name) {
        const icon = this.iconMap.get(name);
        icon && removeIcon(icon, this.engine);
        this.iconMap.delete(name);
    }

    clear() {
        [...this.iconMap.keys()].forEach(icon => {
            this.removeIconByName(icon);
        });
        this.iconMap.clear();
    }
}

export {
    IconManager,
};