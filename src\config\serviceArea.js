import img1 from '@/assets/images/serviceM/1.jpg';
import img2 from '@/assets/images/serviceM/2.jpg';
import img3 from '@/assets/images/serviceM/3.jpg';
import img4 from '@/assets/images/serviceM/4.jpg';
import img5 from '@/assets/images/serviceM/5.jpg';
import img6 from '@/assets/images/serviceM/6.jpg';


// 事件监测枚举

export const CarImgList = [
    {
        key: '1',
        time: '2024-01-09 23:09:09',
        title: 'SN 123124124124',
        url: img1,
    },
    {
        key: '2',
        time: '2024-01-09 23:09:09',
        title: 'SN 123124124124',
        url: img2,

    },
    {
        key: '3',
        time: '2024-01-09 23:09:09',
        title: 'SN 123124124124',
        url: img3,
    },
    {
        key: '4',
        time: '2024-01-09 23:09:09',
        title: 'SN 123124124124',
        url: img4,
    },
    {
        key: '5',
        time: '2024-01-09 23:09:09',
        title: 'SN 123124124124',
        url: img5,
    },
    {
        key: '6',
        time: '2024-01-09 23:09:09',
        title: 'SN 123124124124',
        url: img6,
    },

];
export const eventMonitor = [
    {
        key: '1',
        time: '刚刚',
        title: '危险化品车辆超时',
        plate: '粤-A9999',
        color: '#E6EC3E',
    },
    {
        key: '2',
        title: '重点关注车辆驶出',
        plate: '粤-A9999',
        time: '1小时前',
        color: '#004FFF',

    },
    {
        key: '3',
        title: '重点关注车辆驶入',
        plate: '粤-A9999',
        color: '#E6EC3E',
        time: '2小时前',
    },

];