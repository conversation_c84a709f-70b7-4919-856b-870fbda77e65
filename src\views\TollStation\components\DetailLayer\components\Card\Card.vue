<template>
    <div class="card">
        <div class="card__left">
            <div class="card__type">
                <div class="card__icon">
                    <icon
                        color="#ffffff"
                        :size="14"
                        :name="info.icon"
                    />
                </div>
                <div class="card__name">{{ info.name }}</div>
            </div>
            <div class="card__count"><span>{{ info.total }}</span>台</div>
        </div>
        <div class="card__right">
            <div class="card__info-list">
                <div class="card__info-item">
                    <span>设备类型：</span>
                    <span>摄像机</span>
                </div>
                <div class="card__info-item">
                    <span>在线率：</span>
                    <span>{{ info.runPercent }}%</span>
                </div>
                <!-- <div class="card__info-item">
                    <span>当前数量：</span>
                    <span>12</span>
                </div> -->
                <div class="card__info-item">
                    <span>故障数：</span>
                    <span>{{ info.faultNum }}</span>
                </div>
                <div class="card__info-item">
                    <span>运行数量：</span>
                    <span>{{ info.runNum }}</span>
                </div>
                <div class="card__info-item">
                    <span>故障率：</span>
                    <span>{{ info.faultPercent }}%</span>
                </div>
                <div class="card__info-item col-2">
                    <span>更新时间：</span>
                    <span>{{ info.time }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {Icon} from '@/components/Common';

export default {
    components: {
        Icon,
    },
    props: {
        info: Object,
    },
};
</script>

<style lang="less" scoped>
.card {
    height: 100px;
    background-color: rgba(255, 255, 255, .1);
    backdrop-filter: blur(12px);
    padding: 12px;
    display: flex;
    justify-content: space-between;
    border-top: 2px solid rgba(255, 255, 255, .2);

    &__type {
        display: flex;
        align-items: center;
    }

    &__icon {
        flex-shrink: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(255, 255, 255, .1);
        border: 1px solid rgba(255, 255, 255, .2);
        border-top-color: rgb(150, 150, 150);
    }

    &__name {
        margin-left: 12px;
        font-style: 16px;
        font-weight: 500;
    }

    &__count {
        margin-top: 16px;
        margin-left: 32px;
        font-size: 14px;
        color: rgba(255, 255, 255, .2);

        span {
            font-size: 36px;
            color: #fff;
            padding-right: 8px;
        }
    }

    &__info {
        &-list {
            width: 240px;
            font-size: 12px;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
        }

        &-item {
            width: 120px;

            &.col-2 {
                width: 240px;
            }

            &:not(&:nth-child(-n+2)) {
                margin-top: 8px;
            }

            span:nth-child(1) {
                color: rgba(255, 255, 255, .5);
            }
        }
    }
}

@media screen and (min-width: 6000px) {
    .card {
        justify-content: space-around;
    }
}
</style>