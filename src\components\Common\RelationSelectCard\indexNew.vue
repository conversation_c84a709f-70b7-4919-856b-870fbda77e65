<template>
    <div class="structure-select-card">
        <div style="height: 30px;">
            <div
                class="structure-select-switch"
            >
                <div class="title">
                    <div class="el-icon-refresh" @click.stop="handleRefreshFn">
                    </div>
                    <div class="name">{{ cityName }}</div>
                </div>
                <div class="el-icon-arrow-down"></div>
            </div>
        </div>
        <!-- 结构物数量卡片，全局组件 -->
        <div class="structure-num-card">
            <!-- 标题 -->
            <div class="structure-card-title">{{ cityName }}</div>

            <!-- 查询 -->
            <div class="structure-card-search">
                <el-input
                    v-model="keyword"
                    placeholder="请输入关键字"
                />
                <el-button icon="el-icon-search" @click.stop="handleSearchFn">查询</el-button>
            </div>

            <!-- 查询结果 -->
            <div v-show="showSelectBack" class="structure-search-back">
                <div class="top">
                    <div>搜索结果</div>
                    <div class="el-icon-close" @click.stop="closeSelectBackFn"></div>
                </div>
                <div class="search-list">
                    <div
                        v-for="item in searchRelationList"
                        :key="item.sectionId"
                        class="search-item"
                        @click.stop="choseSearchBackFn(item)"
                    >
                        {{ item.orgSectionTpye === 0 ? item.orgName : item.sectionName }}
                    </div>
                </div>
            </div>

            <!-- 地区选择 -->
            <div class="structure-card-place">
                <div class="place-left">
                    <div
                        v-for="item in relationOption"
                        :key="item.orgId"
                        class="place-item"
                        :class="{active: item.orgId === mesoActive}"
                        @click.stop="mesoChoseFn(item)"
                    >
                        {{ item.orgName }}（{{ item.sectionNum }}）
                    </div>
                </div>
                <div class="place-right show-scroll">
                    <template v-if="relationRoad.length > 0">
                        <div
                            v-for="item in relationRoad"
                            :key="item.orgId"
                            class="chose-item"
                            :class="{
                                active: typeof microActive === 'string'
                                    ? item.sectionId === microActive
                                    : microActive.includes(item.sectionId),
                            }"
                            @click.stop="microChoseFn(item)"
                        >
                            {{ item.sectionName }}
                        </div>
                    </template>
                    <no-data v-else message="暂无高速数据"/>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {Input, Button} from 'element-ui';
import {
    searchRelation,
    getRelationNameAndNum,
    getRelationById,
} from './api.js';
import NoData from '../NoData/index.vue';

export default {
    name: 'RelationSelectCard',
    props: {
        mesoKey: {
            type: String,
            defaault: '',
        },
        microKey: {
            type: Array,
            default: () => [],
        },
        microCheck: {
            type: Boolean,
            default: false,
        },
    },
    components: {
        [Input.name]: Input,
        [Button.name]: Button,
        NoData,
    },
    watch: {
        mesoKey: {
            handler(newVal) {
                // 如果为null，则把数据都滞空
                if (!newVal) {
                    this.mesoActive = '';
                    this.mesoData = {};
                    this.relationOption.length > 0 && this.setRelationRoadById({orgId: this.relationOption[0].orgId});
                }
                else {
                    this.mesoActive = newVal;
                    this.mesoData = this.relationOption.find(item => item.orgId === newVal);
                    this.setRelationRoadById({orgId: newVal});
                }
            },
            immediate: true,
        },
        microKey: {
            handler(newVal) {
                // 如果为null，则把数组都滞空
                if (!newVal) {
                    this.microActive = [];
                    this.microDataList = [];
                }
                else if (typeof newVal === 'string') {
                    this.microActive = [];
                    this.microActive.push(newVal);
                    this.microDataList = this.relationRoad.filter(item => item.sectionId === newVal);
                }
                else {
                    this.microActive = [...newVal];
                    this.microDataList = this.relationRoad.filter(item => newVal.includes(item.sectionId));
                }
            },
            immediate: true,
        },
    },
    data() {
        return {
            showSelect: false, // 展示选择卡片
            relationOption: [], // 二级机构数据
            relationRoad: [], // 高速公路数据
            keyword: '', // 关键字
            showSelectBack: false, // 展示搜索结果
            searchRelationList: [], // 搜索结果数据
            mesoActive: '', // 二级机构激活项
            mesoData: {}, // 二级机构激活项数据对象
            microActive: [], // 高速激活项数组
            microDataList: [], // 高速路激活项数据数组
        };
    },
    computed: {
        // 被选中的二级机构或高速路名称
        cityName() {
            // 如果为空名称恢复为二级机构；不为空则显示高速路
            if (this.microDataList.length > 0) return this.microDataList.map(e => e.sectionName).join('、');
            else if (this.mesoData?.orgName) return this.mesoData.orgName;
            return '广东省交通集团';
        },
    },
    // 初始化获取第一个二级机构的高速路
    mounted() {
        this.setRelationNameAndNum().then(res => {
            if (!this.mesoKey) {
                this.setRelationRoadById({orgId: res[0].orgId});
            }
        });
    },
    methods: {
        // 显示隐藏卡片组件
        handleSelectFn(flag) {
            this.showSelect = flag;
        },
        // 获取二级单位选项
        async setRelationNameAndNum() {
            const {data} = await getRelationNameAndNum();
            this.relationOption = data;
            return data;
        },
        // 获取高速路数据
        async setRelationRoadById(payload) {
            const {data} = await getRelationById(payload);
            this.relationRoad = data;
        },
        // 点击刷新按钮
        handleRefreshFn() {
            this.mesoActive = '';
            this.mesoData = {};
            this.microActive = [];
            this.microDataList = [];
            this.setRelationRoadById({orgId: this.relationOption[0].orgId});
            this.$emit('handleRefresh');
        },
        // 点击左侧桥路切换右侧高速
        mesoChoseFn(e) {
            this.mesoDataChange(e);
            this.$emit('mesoChoseFn', e, [this.mesoActive], [this.mesoData]);
        },
        // 点击右侧具体的高速、桥获取微观二维结构物数据和告警结构物数据
        microChoseFn(e) {
            // 如果直接点击高速路，则把第一个二级机构数据保存
            if (!this.mesoActive) {
                this.mesoActive = this.relationOption[0].orgId;
                this.mesoData = this.relationOption[0];
            }

            this.microDataChange(e);

            this.$emit(
                'microChoseFn',
                e,
                [this.mesoActive, this.microCheck ? this.microActive : this.microActive.join('')],
                [this.mesoData, this.microCheck ? this.microDataList : this.microDataList[0]]
            );
        },
        // 点击搜索查询按钮
        async handleSearchFn() {
            this.$emit('before-search', this.keyword);
            this.showSelectBack = true;
            this.searchRelationList = [];
            const {data} = await searchRelation({keyWord: this.keyword});
            this.searchRelationList = data;
            this.$emit('after-search', this.searchRelationList);
        },
        // 点击搜索结果的关闭按钮
        closeSelectBackFn() {
            this.showSelectBack = false;
            this.$emit('closeSelectBackFn');
        },
        // 点击搜索的选项
        async choseSearchBackFn(e) {
            if (e.orgSectionTpye === 0) {
                // orgSectionTpye为0，表示搜索到的是二级机构，直接调用二级机构点击事件
                this.mesoDataChange(e);
                this.showSelectBack = false;
            }
            else {
                // orgSectionTpye为1，表示搜索到的是高速路，渲染右侧高速列表；二级机构和高速激活项保存
                if (this.mesoActive !== e.orgId) {
                    await this.setRelationRoadById({orgId: e.orgId});
                    this.mesoActive = e.orgId;
                    this.mesoData = e;
                }
                this.microDataChange(e);
            }
            this.$emit('choseSearchBackFn', e);
        },
        // 组织机构修改函数
        mesoDataChange(e) {
            // 保存二级机构数据
            this.mesoActive = e.orgId;
            this.mesoData = e;
            // 清空高速路数据
            this.microActive = [];
            this.microDataList = [];
        },
        // 高速路修改函数
        microDataChange(e) {
            // 是否允许其多选
            if (this.microCheck) {
                // 点击重复数据
                if (this.microActive.includes(e.sectionId)) {
                    this.microActive = this.microActive.filter(item => item !== e.sectionId);
                    this.microDataList = this.microDataList.filter(item => item.sectionId !== e.sectionId);
                }
                // 点击新数据
                else {
                    this.microActive.push(e.sectionId);
                    this.microDataList.push(e);
                }
            }
            else {
                // 后续优化
                this.microActive = [];
                this.microDataList = [];
                this.microActive.push(e.sectionId);
                this.microDataList.push(e);
            }
        },
    },
};
</script>

<style lang="less" scoped>
.structure-select-card {
    position: relative;

    .el-icon-arrow-down {
        transition: all .3s;
    }

    &:hover {
        .structure-num-card {
            transform: scale(1);
            opacity: 1;
            transition: opacity .3s;
        }

        .el-icon-arrow-down {
            transform: rotate(180deg);
        }
    }

    .structure-select-switch {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 256px;
        height: 100%;
        padding-right: 20px;
        box-sizing: border-box;
        color: #fff;
        border-radius: 3px;
        border: 1px solid rgba(0, 231, 181, .25);
        background-image: linear-gradient(to bottom, rgba(34, 120, 105, .6), rgba(15, 103, 74, .6));
        cursor: pointer;

        .title {
            display: flex;
            align-items: center;
            height: 100%;

            .el-icon-refresh {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 50px;
                height: 100%;
                font-size: 18px;
            }
        }

        .name {
            display: inline-block;
            white-space: nowrap;
            width: 172px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}

.structure-num-card {
    position: absolute;
    bottom: -450px;
    left: 0;
    width: 506px;
    height: 442px;
    background-color: rgb(32, 72, 63);
    padding: 16px 13px 21px 22px;
    box-sizing: border-box;
    z-index: 9999;
    transform: scale(0);
    opacity: 0;
    transition: opacity .3s, transform 0s linear .3s;

    .structure-card-title {
        display: inline-block;
        white-space: nowrap;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        min-width: 237px;
        height: 36px;
        line-height: 36px;
        margin-bottom: 18px;
        padding: 0 20px;
        border: 2px solid rgba(239, 255, 255, .2);
        font-size: 18px;
        letter-spacing: 1px;
        color: #fff;
        font-weight: 600;
        font-family: 'black';
    }

    .structure-card-search {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 437px;
        height: 30px;
        margin-bottom: 20px;

        ::v-deep .el-input {
            position: relative;
            height: 30px;

            &::before {
                content: '';
                position: absolute;
                top: 50%;
                left: -1px;
                transform: translateY(-50%);
                width: 2px;
                height: 9px;
                background-color: rgb(239, 255, 255);
            }

            input {
                height: 30px;
            }
        }

        ::v-deep .el-button {
            height: 30px;
            padding: 7px 8px;
            background:
                linear-gradient(
                    180deg,
                    rgb(26, 240, 147) .07%,
                    rgb(20, 162, 178) 100%
                );
            opacity: .88;
            border-radius: 3px;
            margin-left: 14px;
            color: #fff;
            border: none;
        }
    }

    .structure-search-back {
        position: absolute;
        top: 110px;
        left: 50%;
        transform: translateX(-51%);
        width: 90%;
        height: 320px;
        background: linear-gradient(-66.16deg, rgba(34, 120, 105, 1) 7.662%, rgba(15, 103, 74, 1) 94.257%);
        z-index: 9999;

        .top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 50px;
            padding: 0 20px;
            color: #fff;
            font-size: 17px;
            font-style: italic;
            font-weight: 700;
            border-bottom: 1px solid #fff;

            .el-icon-close {
                cursor: pointer;
            }
        }

        .search-list {
            height: calc(100% - 50px);
            overflow-y: auto;

            .search-item {
                padding-left: 20px;
                height: 50px;
                line-height: 50px;
                color: #fff;
                font-size: 15px;
                cursor: pointer;
                border-bottom: 1px solid rgba(255, 255, 255, .5);
            }
        }

        &::before {
            content: '';
            position: absolute;
            top: -20px;
            left: 50%;
            width: 0;
            height: 0;
            border-top: 10px solid transparent;
            border-right: 10px solid transparent;
            border-bottom: 10px solid rgba(34, 120, 105, 1);
            border-left: 10px solid transparent;
            z-index: 9999;
        }
    }

    .structure-card-place {
        display: flex;
        height: 280px;

        .place-left {
            display: flex;
            flex-direction: column;
            height: 100%;
            width: 125px;
            border-right: 1px solid rgba(239, 255, 255, .2);
            color: rgb(239, 255, 255);
            font-family: 'PingFang SC', 'PingFang-SC';
            font-size: 14px;
            font-weight: 600;
            margin-right: 10px;

            .place-item {
                width: 118px;
                height: 30px;
                line-height: 30px;
                padding-left: 7px;
                border-radius: 3px;
                margin-bottom: 10px;
                cursor: pointer;

                &.active {
                    background:
                        linear-gradient(
                            180deg,
                            rgba(26, 240, 147, .88) .07%,
                            rgba(20, 162, 178, .88) 100%
                        );
                }
            }
        }

        .place-right {
            flex: 1;
            height: 100%;
            overflow-y: scroll;
            padding-right: 10px;

            &::-webkit-scrollbar {
                /* 设置垂直滚动条宽度 */
                width: 2px;
                height: 10px;
            }

            &::-webkit-scrollbar-thumb {
                /* 滚动条里面小方块 */
                border: 4px solid #fff;
            }

            &::-webkit-scrollbar-track {
                /* 滚动条里面轨道 */
                background: rgba(239, 255, 255, .2);
            }

            .chose-item {
                display: inline-block;
                text-align: center;
                width: 100px;
                height: 30px;
                line-height: 30px;
                margin-bottom: 7px;
                margin-right: 10px;
                opacity: .8;
                color: rgb(239, 255, 255);
                font-family: PingFang SC;
                font-size: 14px;
                cursor: pointer;

                &:nth-child(3n) {
                    margin-right: 0;
                }

                &.active {
                    border-radius: 3px;
                    background:
                        linear-gradient(
                            180deg,
                            rgba(26, 240, 147, .88) .07%,
                            rgba(20, 162, 178, .88) 100%
                        );
                }
            }
        }
    }
}
</style>
