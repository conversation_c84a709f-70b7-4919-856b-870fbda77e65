# 事件扎点

### 参数说明  props
|Prop name|Type|Description|
|---|---|---|---|
|`managerInstace`|`object`|自定义事件扎点管理器(EventManager)实例 必填|
|`position`|`Arrray`|经纬度坐标 必填|
|`type`|`string`|扎点事件类型 必填|
|`name`|`string`|扎点名称 表示当前扎点的唯一值|
|`active`|`boolean`|是否选中|
|`bubbleColor`|`string`|bubbleColor 默认根据状态变更 优先以传入值为主|
|`circleBorderColor`|`string`|circleBorderColor|
|`circleColor`|`string`|circleColor 默认：#fff|
|`customData`|`object`|自定义数据|
|`clickCallback`|`function`|点击回调函数|

### icon图片说明
默认根据type生成对应的icon图片，如果需要自定义icon，请在public/maplayer/assets/image/event_warning/icons/目录下添加对应type(中文拼音)的图片,图片命名规则为：type(拼音).png。 选中状态图片命名规则：type(拼音)_on.png

如：车辆故障命名为 cheliangguzhang.png 选中状态为 cheliangguzhang_on.png

并且需求在eventManager中配置nameMap, 配置格式为：中文：中文拼音