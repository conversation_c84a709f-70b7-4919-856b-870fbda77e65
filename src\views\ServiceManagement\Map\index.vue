<template>
    <map-component
        :options="{
            center: mapCenter,
            showSatelliteMap: true,
            showTollStations: true,
            showAssetsScene: true,
        }"
        @mapLoaded="mapLoaded"
    >
        <template v-if="mapInitStatus">
            <slot></slot>
        </template>

    </map-component>
</template>

<script>
import {Map} from '@/components/Common';
import {ref, watch} from 'vue';
import {engine, mapCenter} from '@/store/engine.js';
import {viewToMac, viewToMicro, viewToMicroTwo} from '@/utils';

export default {
    props: {
        pointsList: {
            type: Array,
            default: () => ([]),
        },
        lineName: {
            type: String,
            default: '',
        },
    },
    components: {
        MapComponent: Map,
    },
    setup() {
        const mapInitStatus = ref(false);

        function mapLoaded(map) {
            engine.value = map;
            mapInitStatus.value = true;
        }

        watch(
            () => engine.value,
            val => {
                if (val) {
                    // engine.value.map.setPitch(20);
                    // engine.value.map.setZoom(4.3);
                    // engine.value.map.setHeading(10);
                    //  "centerPoint": "112.9892101,22.71676053"xiqu
                    // "centerPoint": "112.991059,22.71611355" d东区
                    viewToMicro([112.991059, 22.71611355]);
                }
            },
            {
                immediate: true,
            }
        );

        return {
            mapLoaded,
            mapCenter,
            mapInitStatus,
        };
    },
};
</script>