{"name": "apaas-maplayer", "version": "0.0.58", "description": "广东一张图-地图图层SDK", "type": "module", "anthor": "<EMAIL>", "main": "dist/index.js", "types": "dist/index.d.ts", "license": "MIT", "scripts": {"dev": "vite --host 0.0.0.0", "dev:live": "vite --host 0.0.0.0 --mode live", "build": "vite build --mode production", "build:split": "vite build --mode split", "build:test": "vite build --mode test", "build:release": "vite build --mode release", "serve": "vite preview"}, "keywords": ["apaas", "maplayer", "apaas-maplayer"], "peerDependencies": {"@baidu/cloudrenderengine": "1.7.3", "@baidu/mapv-three": "^1.2.19", "element-ui": "2.13.2"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "dependencies": {"@baidu/cloudrenderengine": "1.7.3", "@baidu/mapv-three": "^1.2.19", "@baidu/origin.css": "^1.0.7", "@turf/invariant": "^6.5.0", "@turf/turf": "^6.5.0", "@vitejs/plugin-vue2": "^2.3.1", "@vueuse/core": "^10.7.0", "animate.css": "^4.1.1", "axios": "^1.6.0", "babel-eslint": "^10.1.0", "bmap-three": "^0.158.0", "dayjs": "^1.11.10", "echarts": "^5.4.3", "element-ui": "2.13.2", "gcoord": "^1.0.5", "generate-source-map": "^0.0.5", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "query-string": "^8.1.0", "rollup-plugin-terser": "^7.0.2", "socket.io-client": "^4.7.2", "unplugin-vue-setup-extend-plus": "^1.0.1", "uuid": "^9.0.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.0", "vite-plugin-libcss": "^1.1.1", "vue": "^2.7.14", "vue-flv-player": "^1.0.3", "vue-router": "^3.2.0", "vue-tsc": "^1.8.19", "vuedraggable": "^2.24.3", "vuex": "^3.4.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/eslint-parser": "^7.16.3", "@babel/eslint-plugin": "^7.14.5", "@ecomfe/eslint-config": "^7.3.0", "@ecomfe/stylelint-config": "^1.1.2", "babel-eslint": "^10.1.0", "eslint": "7.5", "eslint-plugin-vue": "7.20.0", "less": "^4.2.0", "less-loader": "^11.1.3", "mitt": "^3.0.1", "mpegts.js": "^1.7.3", "node-forge": "^1.3.1", "postcss": "^8.4.33", "postcss-pxtorem": "^6.0.0", "rollup-plugin-require-context": "^1.0.1", "stylelint": "13.13.1", "stylelint-less": "^1.0.8", "stylelint-order": "^6.0.3", "vite": "^4.4.5", "vite-plugin-dts": "^3.6.0"}}