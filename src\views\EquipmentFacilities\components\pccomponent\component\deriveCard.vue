<template>
    <div v-if="visible" class="derive-shade">
        <div class="derive-card">
            <div class="card-title">
                <p>确认导出内容</p>
                <div class="close" @click.stop="close">
                    <i class="el-icon-close"></i>
                    <span>关闭</span>
                </div>
            </div>

            <div class="card-input">
                <p class="label">文件名称：</p>
                <el-input
                    v-model="input" size="small"
                    placeholder="请输入内容"
                />
            </div>

            <div class="card-list">
                <el-table
                    :data="tableData"
                    :height="220 * ratio"
                    style="width: 100%"
                >
                    <el-table-column
                        prop="date"
                        label="设备名称"
                        :width="245 * ratio"
                        align="left"
                    />
                    <el-table-column
                        prop="name"
                        label="文件大小（M）"
                        :width="110 * ratio"
                        align="right"
                    />
                </el-table>
                <div class="list-info">
                    <p class="info-title">文件大小总计</p>
                    <span class="info-total">2006</span>
                </div>
            </div>

            <div class="card-btns">
                <el-button
                    size="small"
                    plain
                >取消</el-button>
                <el-button
                    size="small" type="primary"
                >
                    导出
                </el-button>
            </div>
        </div>
    </div>
</template>

<script>
import {Input, Button, Table, TableColumn} from 'element-ui';
import {useUnit} from '@/utils/hooks/useUnit';
export default {
    components: {
        [Input.name]: Input,
        [Button.name]: Button,
        [Table.name]: Table,
        [TableColumn.name]: TableColumn,
    },
    model: {
        prop: 'visible',
        event: 'update:visible',
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
    },
    setup(prop, {emit}) {
        const {ratio} = useUnit();
        const tableData = [{
            date: '2016-05-02',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1518 弄',
        }, {
            date: '2016-05-04',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1517 弄',
        }, {
            date: '2016-05-01',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1519 弄',
        }, {
            date: '2016-05-03',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1516 弄',
        }, {
            date: '2016-05-03',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1516 弄',
        }, {
            date: '2016-05-03',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1516 弄',
        }, {
            date: '2016-05-03',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1516 弄',
        }, {
            date: '2016-05-03',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1516 弄',
        }, {
            date: '2016-05-03',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1516 弄',
        }, {
            date: '2016-05-03',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1516 弄',
        }, {
            date: '2016-05-03',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1516 弄',
        }, {
            date: '2016-05-03',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1516 弄',
        }, {
            date: '2016-05-03',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1516 弄',
        }, {
            date: '2016-05-03',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1516 弄',
        }, {
            date: '2016-05-03',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1516 弄',
        }, {
            date: '2016-05-03',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1516 弄',
        }, {
            date: '2016-05-03',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1516 弄',
        }, {
            date: '2016-05-03',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1516 弄',
        }];

        const close = () => {
            emit('update:visible', false);
        };

        return {
            ratio,
            tableData,
            close,
        };
    },
};
</script>

<style lang="less" scoped>
.derive-shade {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.6);
    z-index: 999;

    .derive-card {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 385px;
        height: 427px;
        padding: 16px;
        background: rgba(44, 44, 44, 0.8);
        box-shadow: 0 0 0 4px rgba(44, 44, 44, 0.8);

        .card-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            font-weight: 500;
            p {
                font-size: 14px;
                color: #fff;
                font-family: 'OPlusSans';
            }
            .close {
                display: flex;
                align-items: center;
                cursor: pointer;
                font-size: 12px;
                color: rgba(255, 255, 255, .6);
                font-family: 'OPlusSans';

                i {
                    margin-right: 3px;
                }
            }
        }

        .card-input {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .label {
                width: 33%;
                font-weight: 400;
                font-family: 'OPlusSans';
                color: rgba(255, 255, 255, .6);
                font-size: 14px;

                &::before {
                    content: '*';
                    color: rgba(255, 85, 85, 0.8);
                    margin-right: 2px;
                }
            }

            :deep(.el-input) {
                input {
                    background-color: transparent;

                    &::placeholder {
                        font-weight: 400;
                        font-size: 14px;
                        font-family: 'OPlusSans';
                        color: rgba(255, 255, 255, .4) !important;
                    }
                }
            }
        }

        .card-list {
            flex: 1;
            margin: 24px 0 20px;

            :deep(.el-table) {
                background-color: transparent;

                &::before {
                    background-color: transparent !important;
                }

                tr {
                    background-color: transparent;

                    th {
                        height: 28px;
                        font-size: 12px;
                        font-weight: 500;
                        font-family: 'OPlusSans';
                        color: rgba(255, 255, 255, .5);
                        background-color: rgba(255, 255, 255, .1);

                        .cell {
                            line-height: 28px;
                        }
                    }

                    td {
                        position: relative;
                        height: 36px;
                        font-size: 14px;
                        font-weight: 400;
                        font-family: 'OPlusSans';
                        color: rgba(255, 255, 255, .6);
                        border-bottom: 1px solid rgba(255, 255, 255, .1);

                        .cell {
                            line-height: 36px;
                        }
                    }

                    th:is(:first-child),td:is(:first-child) {
                        text-align: left;
                    }

                    th:is(:last-child),td:is(:last-child) {
                        text-align: right;
                    }
                }
            }

            .list-info {
                display: flex;
                align-items: center;
                justify-content: space-between;
                height: 35px;
                padding: 0 10px;

                .info-title {
                    font-weight: 400;
                    font-family: 'OPlusSans';
                    font-size: 14px;
                    color: rgba(255, 255, 255, 0.8);
                }

                .info-total {
                    font-family: 'OPlusSans';
                    font-weight: 500;
                    font-size: 14px;
                    color: rgba(255, 255, 255, 1);
                }
            }
        }

        .card-btns {
            display: flex;
            justify-content: center;
            :deep(.el-button) {
                width: 35%;
                font-size: 14px;
                font-weight: 400;
                font-family: 'OPlusSans';
            }

            :deep(.el-button--primary) {
                &:hover,
                &:focus {
                    background: #333333;
                    border-color: #333333;
                    color: #fff;
                }
            }

            :deep(.el-button--default) {
                background: transparent;
                color: #fff;
                border: 1px solid rgba(255, 255, 255, .2);
            }
        }
    }
}
</style>