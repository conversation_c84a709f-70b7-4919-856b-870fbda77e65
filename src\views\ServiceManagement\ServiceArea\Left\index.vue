<template>
    <div class="left">
        <div v-if="true" class="leftcom">
            <card title="事件监测" card-type="card-long-1">
                <template #content>
                    <div class="container  fr jsb">
                        <warnning-item
                            v-for="item in eventMonitorList" :key="item"
                            :data="item"
                        />
                    </div>
                    <!-- <no-data v-else message="暂无数据"/> -->
                </template>

                <template #titleContent>
                    <router-link to="/service-area/eventList" class="view">查看更多</router-link>
                </template>
            </card>


            <etc-card/>
        </div>
        <div class="right">
            <DutyInfoCard/>
            <saturation-index :list="trendList"/>

        </div>
    </div>
</template>

<script setup>
import SaturationIndex from '../../Card/saturationIndex.vue';
import EventMon from './eventMonitor.vue';
import CarFlow from './carFlow.vue';
import DailogItem from './dailogItem.vue';
import ShowImg from './showImg.vue';
import {useRoute, useRouter} from '@/utils';
import {RadioGroup as ElRadioGroup, Radio as ElRadio, Dialog as ElDialog} from 'element-ui';
import {useUnit} from '@/utils';
import {onMounted, ref, computed, watch} from 'vue';
import {NoData, Card} from '@/components/Common';
import WarnningItem from '../../Card/waringItem.vue';
import EtcCard from '@/views/ServiceManagement/Card/ETC.vue';
import ServiceAreaOverview from '@/views/ServiceManagement/Card/ServiceAreaoverView.vue';
import DutyInfoCard from '@/views/ServiceManagement/Card/DutyInfoCard.vue';
import duty from '@/views/ServiceManagement/Card/duty.vue';
import {serviceId} from '@/views/ServiceManagement/utils/index';

import {
    getEventPage,
    getTrendHour,
} from '@/api/serviceManager/index.js';
import {confirmResult, eventType, carType, sourceType} from '@/config/serviceMap.js';

const props = defineProps({
    serviceIcon: {
        type: Object,
        default: () => ({}),
    },
    equipIcon: {
        type: Array,
        default: () => ([]),
    },
});
const radio = ref('hour');
const router = useRouter();
const eventList = ref([]);
const eventMonitorList = ref([]);
const trendList = ref([]);
const {px2rem} = useUnit();
const params = ref({
    pageNumber: 1,
    pageSize: 100,
});
const eventData = {
    id: '1231',
    typeName: '异常停车',
    duration: 23,
    icon: 'car',
    during: '23',
    plateColor: 'red',
    info: [
        {label: '事件位置', value: '广州方向 K150+625'},
        {label: '事件来源', value: '广州方向 K150+625'},
        {label: '发生时间', value: '广州方向 K150+625'},
        {label: '车辆类型', value: '危险化品车'},
    ],
};
const showDailog = ref(false);
const handleClick = e => {
    router.push({name: 'eventList', params: {id: e.id, info: e}});
};
const handEventItem = e => {
    showDailog.value = true;

};

const searchFn = () => {
    const params = {
        pageNumber: 1,
        pageSize: 100,
    };
    getEventPage(params).then(res => {
        eventList.value = res.data.result.map(item => {
            return {
                ...item,
                vehicleType: carType[item.vehicleType],
                confirmResult: confirmResult[item.confirmResult],
                eventType: eventType[item.eventType],
            };
        });
        eventMonitorList.value = res.data.result.map(item => {
            return {
                ...item,
                typeName: eventType[item.eventType].title,
                icon: eventType[item.eventType].icon,
                info: [
                    {label: '事件位置', value: item.placeName},
                    {label: '事件来源', value: sourceType[item.detailSource]},
                    {label: '发生时间', value: item.confirmTime},
                    {label: '车辆类型', value: carType[item.vehicleType]},
                ],
            };
        });
    }).catch(err => {
    });
    getTrendHour().then(res => {
        trendList.value = res.data;
        console.log('output->trendList.value', trendList.value);
    });
};

const radioChange = e => {
};
onMounted(() => {

    searchFn();


});
watch(() => serviceId.value, () => {
    searchFn();

});

</script>

<style lang="less" scoped>
.container {
    height: 318px;
    color: #fff;
    flex-wrap: wrap;
    overflow-y: scroll;
    gap: 16px 0;
}

.right {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.left {
    :deep(.card) {
        margin-bottom: 16px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    display: flex;

    .leftcom {
        margin-right: 24px;
    }
}

.view {
    color: #01ffe5;
    cursor: pointer;
    border: 1px solid #01ffe5;
    padding: 4px 16px;
    margin-right: 20px;
}

:deep(.dailog-box) {
    width: 615px;
}

.dailog-content {
    display: flex;
    justify-content: space-between;
}
</style>