<template>
    <div class="construction-list">
        <no-data v-if="!list?.length"/>
        <card
            v-for="item in list"
            :key="item"
            :class="['construction-item', {'construction-item__active': item.id === activeId}]"
            :show-icon="false"
            :title="item.projectName"
            @click="handleClickCard(item)"
        >
            <template #titleContent>
                <div class="btn-emulation" @click.stop="handleClickEmulation(item)">
                    <span>创建仿真方案</span>
                    <i class="el-icon-arrow-right"></i>
                </div>
            </template>
            <template #content>
                <div class="list">
                    <div class="item">
                        <span class="label">事件发生时间：</span>
                        <span class="value">{{ item.eventStartTime }}</span>
                    </div>
                    <div class="item">
                        <span class="label">预计持续时长：</span>
                        <span class="value">{{ item.duration }}分钟</span>
                    </div>
                    <div class="item">
                        <span class="label">位置桩：</span>
                        <span class="value">{{ item.highSpeedName }} - {{ item.eventStartStake }}</span>
                    </div>
                    <div class="item">
                        <span class="label">行车方向：</span>
                        <span class="value">{{ item.direction }}</span>
                    </div>
                    <div class="item">
                        <span class="label">关闭车道数：</span>
                        <span class="value">{{ item.closeLaneNum }}</span>
                    </div>
                    <div class="item">
                        <span class="label">事件位置类型：</span>
                        <span class="value">{{ eventPostionMap[item.closeLaneNum] }}</span>
                    </div>
                    <div class="item">
                        <span class="label">工程状态：</span>
                        <span class="value">
                            <div
                                :class="[
                                    'construction-tag',
                                    projectStatusMap[item.projectStatus].className]"
                            >
                                {{ projectStatusMap[item.projectStatus].text }}
                            </div>
                        </span>
                    </div>
                </div>
            </template>
        </card>
    </div>
</template>

<script>
import {Card, NoData} from '@/components/Common';
import {eventPostionMap, projectStatusMap} from '@/config/construction';

export default {
    components: {
        Card,
        NoData,
    },
    props: {
        list: Array,
        activeId: Number,
    },
    setup(props, {emit}) {
        function handleClickEmulation(e) {
            emit('createEmulation', e);
        }

        function handleClickCard(e) {
            emit('clickCard', e);
        }

        return {
            handleClickEmulation,
            handleClickCard,
            eventPostionMap,
            projectStatusMap,
        };
    },
};
</script>

<style lang="less" scoped>
.btn-emulation {
    line-height: 24px;
    font-size: 14px;
    color: #28c282;
    cursor: pointer;
}

.construction {
    &-list &-item:not(:first-child) {
        margin-top: 16px;
    }

    &-item {
        cursor: pointer;
        border-radius: 0;

        /deep/ .header {
            border-left: 2px solid rgba(255, 255, 255, .2);
        }

        &__active {
            border-color: #fff;

            /deep/ .header {
                border-color: #fff;
            }
        }
    }

    &-tag {
        display: inline-block;
        height: 22px;
        padding: 0 6px;
        line-height: 22px;
        text-align: center;
        font-size: 14px;
        border-radius: 2px;

        &-gray {
            color: rgba(#fff, .6);
            background-color: rgba(#606060, .3);
        }

        &-orange {
            color: #eea80f;
            background-color: rgba(#eea80f, .3);
        }

        &-green {
            color: #28c282;
            background-color: rgba(#28c282, .3);
        }

        &-blue {
            color: #009eff;
            background-color: rgba(#009eff, .3);
        }
    }
}
</style>