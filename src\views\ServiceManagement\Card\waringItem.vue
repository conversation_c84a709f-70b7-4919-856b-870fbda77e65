<template>
    <!-- <div class="active"> -->
    <div class="p-24 fs-14 fc w-580 warning-item">
        <div class="row h-28">
            <div class="left fs-16">
                <icon
                    :name="data.icon || 'car'" :size="'26'"
                    class="w-28 mr-4 icon"
                />
                <div class="title w-144 tc dib lh-28 mr-16">
                    {{ data?.typeName }}
                </div>
                <span class="num">{{ data?.during || '0' }} min</span>
            </div>
            <div class="right w-106 h-28 lh-28 tc num" :class="`type-${data.plateColor || '1'}`">
                {{ data.plate || '-' }}
            </div>
        </div>
        <div class="row gap-8 mt-8">
            <div
                v-for="(item, index) in data?.info"
                :key="item.label"
                class="info-item fr jsb h-28 tc lh-28 px-8"
                :class="{'full-width': isLastSingleItem(index)}"
            >
                <span class="key o-60">{{ item.label }}：</span>
                <span class="value">{{ item.value }}</span>
            </div>
        </div>
        <div :class="{'active': data?.active}"></div>
    </div>
    <!-- </div> -->
</template>
<script setup lang="ts">
import {computed} from 'vue';
import {Icon} from '@/components/Common';

const props = defineProps({
    data: {
        type: Object,
        required: true,
    },
});
/**
 * 判断是否为最后一个单独的项
 *
 * @param index 项目的索引值
 * @returns 若是最后一个单独的项目则返回true，否则返回false
 */
const isLastSingleItem = index => {
    return index === props.data?.info.length && props.data?.info?.length - 1 && props.data?.info?.length % 2 !== 0;
};
</script>
<style lang="less" scoped>

.warning-item {
    position: relative;
    background: rgba(18, 74, 166, .3);
    height: 148px;

    &::after {
        content: '';
        z-index: 1;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 1px;
        background: linear-gradient(-25.62deg, rgb(36, 104, 242) 3.571%, rgba(1, 255, 229, .5) 133.442%);
    }

    .row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;

        .icon {
            border: 1px solid rgba(255, 255, 255, .4);
            padding: 4px;
        }

        .title {
            background: linear-gradient(-25.62deg, rgb(36, 104, 242) -17.545%, rgba(1, 255, 229, .5) 133.113%);
            padding: 0 16px;
        }

        .right {
            border-radius: 4px;
            font-size: 20px;

            &.type-1 {
                color: #fff;
                background: rgb(0, 62, 189);
                border: 1px solid rgb(255, 255, 255);
            }

            &.type-2 {
                border: 1px solid rgb(0, 0, 0);
                color: #fff;
                background: rgb(255, 203, 0);
            }
        }

        .info-item {
            flex: 1 1 calc(50% - 10px);
            background: rgba(0, 98, 167, .3);
        }

        .full-width {
            flex: 1;
        }

        .ic {
            font-family: 'material-icons';
        }

        .icf {
            font-family: 'iconfont';
        }

        .num {
            font-family: 'RoboData';
        }
    }
}

.active {
    z-index: 2;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right, rgba(236, 62, 62) 0, rgba(236, 62, 62, 0) 100%);

    &::before {
        content: '';
        position: absolute;
        top: 148px;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(to right, rgba(236, 62, 62) 0, rgba(236, 62, 62, 0) 100%);
    }

    &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 6px;
        height: 148px;
        background: rgba(236, 62, 62);
    }
}

</style>