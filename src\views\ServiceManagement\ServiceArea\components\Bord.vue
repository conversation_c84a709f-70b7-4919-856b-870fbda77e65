<template>
    <modal-card
        :visible="visible"
        :width="820"
        title="发布到情报板"
        @close="handleClose"
        @confirm="handleConfirm"
    >
        <div class="release-board scroll">
            <el-input
                v-model="textarea"
                type="textarea"
                clearable
                :rows="2"
                placeholder="请输入内容"
            />
        </div>
    </modal-card>
</template>

<script>
import {ModalCard} from '@/components/Common';
import {useUnit} from '@/utils';
import {
    Input as ElInput,
} from 'element-ui';
import {computed, watch, ref} from 'vue';

export default {
    components: {
        ModalCard,
        ElInput,
    },
    props: {
        visible: Boolean,
        textInfo: String,
    },
    setup(props, {emit}) {
        const {ratio} = useUnit();
        const labelWidth = computed(() => `${ratio.value * 100}px`);
        const textarea = ref(props.textInfo);

        watch(
            () => props.textInfo,
            newVal => {
                textarea.value = newVal;
            }
        );
        // watch(textarea, newVal => {
        //     emit('update:textInfo', newVal);
        // });
        async function handleConfirm() {
            emit('update:visible', false);
        }

        function handleClose() {
            emit('update:visible', false);
            textarea.value = props.textInfo;

        }

        return {
            labelWidth,
            handleConfirm,
            handleClose,
            textarea,
        };
    },
};
</script>

<style lang="less" scoped>
.release-board {
    .board-collapse-wrapper {
        height: 620px;
        overflow-y: auto;
        padding-right: 4px;
    }
}
</style>