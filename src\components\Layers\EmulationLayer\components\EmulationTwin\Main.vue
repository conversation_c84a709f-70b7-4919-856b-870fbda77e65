<template>
    <div ref="mainRef" class="emulation-twin-main">
        <div class="map-box">
            <!-- 地图 -->
            <map-component
                :options="{
                    center: [113.29290479974627, 23.57957115989944],
                }"
                :style="{
                    height: mapHeight + 'px',
                }"
                @mapLoaded="mapLoaded"
            />
            <!-- 桩号标杆尺 -->
            <stake-axis :event-start-stake="eventStartStake" :engine="engine_twin"/>
            <!-- 时间轴 -->
            <time-axis
                :emulationnowtime="emulationnowtime"
                :ws-disabale="wsDisabale"
                @changeSliderTime="changeSliderTime"
            />
            <!-- 绘制面、弹窗详情 -->
            <drew-item
                v-for="(info, index) in strategyCanvas"
                :key="index"
                :data="info"
                :engine="engine_twin"
                :emulation-id="emulationId"
            />
        </div>
        <div class="center">
            <div class="top">
                <div class="title">{{ strategyName }}</div>
                <div class="buttons">
                    <el-button
                        type="primary"
                        size="small"
                        @click="pauseAndRecover"
                    >暂停/恢复
                    </el-button>
                    <el-button
                        type="primary"
                        size="small"
                        @click="stop"
                    >结束仿真
                    </el-button>
                </div>
            </div>
            <div class="detail">
                <template v-if="strategyList.length">
                    <div class="detail-inner">
                        <strategy-card
                            v-for="item in strategyList"
                            :key="item.type"
                            :info="item"
                        />
                    </div>
                </template>
                <div v-else class="no-data">暂无策略</div>
            </div>
        </div>
        <!-- 施工区域渲染 -->
        <construction-area
            v-if="isConstruction"
            :line-manager="lineManager"
            :polygon-manager="polygonManager"
            :list="getConstructionList"
        />
    </div>
</template>

<script>
import {onMounted, ref, computed, watch, shallowRef} from 'vue';
import {geojsonUtils} from '@baidu/mapv-three';
import {Ws} from '@/utils/ws';
import {TwinManager, EventManager, LineManager, PolygonManager} from '@/utils/map/manager';
import {findEventType} from '@EmulationLayer/utils/index';
import {colorBaseArr1, colorBaseArr2} from './config';
import {emulationEnd, getPointsElevation} from '@EmulationLayer/api';
import {viewTo} from '@/utils';
import {emulationInfo, uuid, strategyId, emulationIds, simulationTime,
        simulationBufferTime} from '@EmulationLayer/store/index';
import {wsHost} from '@EmulationLayer/store/common';
import {Button, Message} from 'element-ui';
import {emulationConfig} from '@/utils/common';

import dayjs from 'dayjs';
import _ from 'lodash';
import Map from './Map.vue';
import StakeAxis from './StakeAxis.vue';
import TimeAxis from './TimeAxis.vue';
import StrategyCard from './StrategyCard.vue';
import DrewItem from './DrewItem.vue';
import ConstructionArea from './ConstructionArea.vue';

import {engine as _engine} from '@/store/engine';
import {isArray} from 'lodash-es';

export default {
    components: {
        MapComponent: Map,
        StakeAxis,
        TimeAxis,
        StrategyCard,
        DrewItem,
        [Button.name]: Button,
        ConstructionArea,
    },
    props: {
        id: {
            type: String,
            default: () => null,
        },
        emulationId: {
            type: String,
            default: () => null,
        },
        ifSelf: {
            type: Boolean,
            default: () => false,
        },
        index: {
            type: Number,
            default: () => 0,
        },
        isFinish: {
            type: Boolean,
            default: () => false,
        },
        emulationType: {
            type: Number,
            default: 1,
        },
    },
    setup(props, {emit}) {
        const {basePrefix, baseWsHost} = emulationConfig;
        const mapHeight = ref(1080);
        const mainRef = ref(null);
        const engine_twin = shallowRef(null); // 地图引擎
        const emulationnowtime = ref(''); // 当前仿真时间
        const policyEmulationWs = ref(); // 仿真ws
        const twin = ref(); // 仿真twin
        const viewPosition = ref(); // 视角参数
        const handleType = ref(); // 仿真操作类型 normal: 正常仿真, pause: 暂停
        const isinited = ref(false); // 是否已经初始化
        const iconName = ref(null); // 扎点name
        const eventManager = ref(null);
        const wsDataCount = ref(0);
        const lineManager = ref(null);
        const polygonManager = ref(null);

        // 是否为施工仿真
        const isConstruction = computed(() => props.emulationType === 3);
        // 施工区域坐标点集合
        const getConstructionList = computed(() => {
            if (!isConstruction.value) return [];
            const [firstEvent = {}] = emulationInfo.value?.eventList;
            let constructionGeoList = firstEvent?.constructionGeoList;
            if (!isArray(constructionGeoList) || !isArray(constructionGeoList[0])) return [];
            // 'lng,lat,alt' => [lng, lat, alt]
            const formatList = constructionGeoList[0].map(item => item.split(','));
            const [first] = formatList;
            first && formatList.push(first);
            return formatList;
        });

        const emulationName = 'fangzhen1';

        let twinManager;

        const wsBaseUrl = computed(() => {
            return `${wsHost.value || baseWsHost}${basePrefix}`;
        });

        // 事件桩号位置
        const eventStartStake = computed(() => emulationInfo.value.eventList?.[0]?.eventStartStake);
        // 策略列表
        const strategyList = computed(() => emulationInfo.value.strategyList?.[props.index] || []);

        // 策略名称
        const strategyName = computed(() => {
            return strategyList.value?.length ? `策略${props.index + 1}` : '无策略';
        });
        // 策略canvas
        const strategyCanvas = computed(() => emulationInfo.value.strategyCanvas?.[props.emulationId]);

        // 仿真类型
        const emulation_type = computed(() => props.emulationType);

        // 地图加载完成后执行的回调函数
        const mapLoaded = async map => {
            engine_twin.value = map;
            twinManager = new TwinManager(map);
            twinManager.addTwin(emulationName);
            twin.value = twinManager.getTwinByName(emulationName);

            eventManager.value = new EventManager(map);
            lineManager.value = new LineManager(map);
            polygonManager.value = new PolygonManager(map);

            engine_twin.value.rendering.addPrepareRenderListener(e => {
                if (e.rendering.renderState.viewChanged) {
                    const {x, y, z} = e.camera.position;
                    const p = geojsonUtils.unprojectPointArr([x, y, z]);
                    viewPosition.value = p;
                }
            });
            // 视角移动到事件位置
            const eventInfo = emulationInfo.value?.eventList?.[0];
            let position = eventInfo?.eventPosition?.split(',')?.map(i => Number(i));
            if (position && engine_twin.value) {
                viewTo({
                    zoom: 15,
                    center: position,
                    key: 'engine_tiwin',
                }, 1000, map);
            }
            const response = await getPointsElevation(position);
            const h = response.data?.[0] || 0;
            position = [position[0], position[1], h];
            // 事件扎点
            const cur = findEventType(eventInfo.eventType, emulation_type.value);
            const eventName = cur?.name || '';
            iconName.value = `event-${props.emulationId}-${props.index}`;

            eventManager.value.addEventPoint(
                iconName.value,
                position,
                {
                    labelText: eventName,
                }
            );
        };

        // 监听仿真ws数据推送数据帧数 小于帧时禁止时间进度条拖动
        const wsDisabale = computed(() => {
            return wsDataCount.value < 4;
        });

        // 创建策略下发的仿真ws
        const initPolicyEmulation = async startTime => {
            policyEmulationWs.value = new Ws();
            const _id = props.emulationId;
            const _uuid = uuid.value;
            const params = `emulationId=${_id}&startTime=${startTime}&uuid=${_uuid}`;
            const url = `${wsBaseUrl.value}/ws/imEmulationScheme?${params}`;
            let ws = await policyEmulationWs.value.connect(url, {});
            ws.onmessage = ({data}) => {
                if (data === 'ok' || data === '{}') {
                    return;
                }
                data = JSON.parse(data);

                wsDataCount.value += 1;
                let result = [];
                data.data.forEach(item => {
                    let resultItem = {
                        timestamp: data.timestamp,
                    };
                    if (item.type === 1) {
                        item.color = colorBaseArr1[item.id.charAt(item.id.length - 1)];
                    }
                    else if (item.type === 2) {
                        item.color = colorBaseArr2[item.id.charAt(item.id.length - 1)];
                    }
                    result.push(resultItem);
                });

                if (result.length > 0) {
                    emulationnowtime.value = result[0].timestamp;

                    const nowTime = dayjs(result[0].timestamp).format('YYYY-MM-DD HH:mm:ss');
                    !simulationTime.value && (simulationTime.value = nowTime);

                    // 1分钟更新一次
                    const expectTime = dayjs(simulationTime.value).add(1, 'minute').format('YYYY-MM-DD HH:mm:ss');
                    if (dayjs(nowTime).format('YYYY-MM-DD HH:mm') === dayjs(expectTime).format('YYYY-MM-DD HH:mm')) {
                        simulationTime.value = nowTime;
                    }
                }

                if (wsDataCount.value > 3) {
                    twin.value?.push(data.data);
                }
            };
            if (policyEmulationWs.value.ws) {
                // 初始创建的策略仿真ws直接开始仿真
                policyEmulationWs.value.ws.send('normal');
                isinited.value = true;
            }
        };

        // 修改ws进度
        const changeSliderTime = params => {
            // 修改进度前清除仿真
            twinManager.removeTwinByName(emulationName);
            twinManager.addTwin(emulationName);
            twin.value = twinManager.getTwinByName(emulationName);
            // policyEmulationWs.value.ws?.send(params);
            wsDataCount.value = 0;
            emit('changeSliderTime', params);
        };

        const sendSliderTime = time => {
            policyEmulationWs.value.ws?.send(time);
        };

        const closeWss = () => {
            policyEmulationWs.value.dispose();
            policyEmulationWs.value = null;
        };

        const initWss = async () => {
            const startTime = emulationInfo.value.emulationStartTime;
            const time = dayjs(startTime).valueOf();
            initPolicyEmulation(time);
            handleType.value = 'normal';
        };

        // 释放当前仿真资源
        const disposeData = () => {
            strategyId.value = null;
            emulationIds.value = null;
            emulationInfo.value = null;
            simulationTime.value = null;
            simulationBufferTime.value = null;
            eventManager.value.removeEventPointByName(`${iconName.value}`);
            engine_twin.value.map?.dispose();
            engine_twin.value = null;

            // 给全局地图设置视角
            _engine?.value?.map?.setZoom(15);
        };

        // // 暂停和恢复
        const pauseAndRecover = () => {
            if (!handleType.value) {
                Message.warning('请先开启仿真');
                return;
            }
            handleType.value = handleType.value === 'normal' ? 'pause' : 'normal';
            policyEmulationWs.value.ws.send(handleType.value);
        };

        // 结束仿真
        const stop = async () => {
            const params = {
                emulationId: props.emulationId,
                schemeId: strategyId.value || props.id,
                uuid: uuid.value,
            };
            await emulationEnd(params);
            handleType.value = null;
            closeWss();
            disposeData();
        };

        watch(() => props.isFinish, () => {
            if (!props.isFinish) {
                return;
            }
            stop();
        });

        watch(() => props.emulationId, () => {
            if (!props.emulationId) {
                return;
            }
            initWss();
        });

        watch(() => viewPosition.value, _.debounce(
            val => {
                if (!val || !policyEmulationWs.value?.ws || !isinited.value) {
                    return;
                }
                let params = JSON.stringify({
                    vehicleScope: {
                        longitude: val[0],
                        latitude: val[1],
                        scope: 1000,
                    },
                });
                policyEmulationWs.value.ws.send(params);
            }, 500
        ));

        onMounted(() => {
            const height = props.ifSelf
                ? window.innerHeight - 40
                : (mainRef.value.offsetWidth || 1920) * 9 / 16 - 40;
            mapHeight.value = height < 1080 ? height : 1080;
            if (!props.emulationId) {
                return;
            }
            initWss();
        });

        return {
            engine_twin,
            mapHeight,
            mainRef,
            emulationnowtime,
            eventStartStake,
            strategyList,
            strategyName,
            wsDisabale,
            strategyCanvas,
            mapLoaded,
            stop,
            changeSliderTime,
            sendSliderTime,
            lineManager,
            polygonManager,
            isConstruction,
            getConstructionList,
            pauseAndRecover,
        };
    },
};
</script>

<style lang="less" scoped>
.emulation-twin-main {
    position: relative;
    flex: 1;

    .map-box {
        width: 100%;
        position: relative;
    }

    .center {
        min-height: 190px;
        background-color: rgba(32, 72, 63, .6);
        border-bottom: 1.5px solid;
        border-image: linear-gradient(180deg, #379084 0%, #5cf4c3 99%) 1.5 1.5 1.5 1.5;
        position: relative;

        &::before {
            content: '';
            position: absolute;
            top: 45px;
            right: 5px;
            width: 4px;
            height: 82px;
            background-color: #08d6a5;
        }

        .top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 40px;
            background-color: rgba(7, 175, 135, .21);
            position: relative;
            padding: 0 15px;
            box-sizing: border-box;

            &::after {
                position: absolute;
                content: '';
                bottom: 0;
                left: 0;
                width: 100%;
                height: 1px;
                background-color: rgba(8, 214, 165, .3);
            }

            .title {
                font-size: 16px;
                color: #e4f1ff;
                line-height: 24px;
            }
        }

        .detail {
            min-height: 210px;
            position: relative;
            overflow-x: auto;
            overflow-y: hidden;

            .detail-inner {
                display: flex;
                width: max-content;
                position: absolute;
            }

            .no-data {
                width: 100%;
                height: 100%;
                display: flex;
                font-size: 16px;
                line-height: 210px;
                color: #e4f1ff;
                justify-content: center;
                align-items: center;
            }
        }
    }

    .el-button--primary {
        background-color: #07af87;
    }
}
</style>