<template>
    <div class="card-list">
        <div
            v-for="item in vidiconList"
            :key="item.deviceType"
            class="card-item"
        >
            <card :info="item"/>
        </div>
    </div>
</template>

<script>
import {onMounted, ref} from 'vue';
import Card from './Card.vue';
import {getTollCameraCount} from '@/api/tollStation';
import {cardTypeMap} from './config.js';

export default {
    components: {
        Card,
    },
    props: {
        stationId: String,
        stationName: String,
    },
    setup(props) {
        const vidiconList = ref([]);

        async function initVidiconList() {
            if (!props.stationId || !props.stationName) return;
            const {data} = await getTollCameraCount(props);
            vidiconList.value = data.map(item => {
                const card = cardTypeMap[item.deviceType];
                return {
                    ...item,
                    ...card,
                };
            });
        }

        onMounted(() => {
            initVidiconList();
        });

        return {
            vidiconList,
        };
    },
};

</script>

<style lang="less" scoped>
.card {
    &-list {
        width: 100%;
        display: flex;
        justify-content: space-between;
    }

    &-item {
        width: calc((100% - 48px) / 4);
    }
}
</style>