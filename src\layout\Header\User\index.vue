<template>
    <el-dropdown trigger="click">
        <div class="user">
            <img class="user__avatar" src="@/assets/images/default_avatar.svg">
            <div class="user__nickname">百度路方测试</div>
        </div>
        <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>个人中心</el-dropdown-item>
            <el-dropdown-item>退出</el-dropdown-item>
        </el-dropdown-menu>
    </el-dropdown>
</template>
<script>
import {
    Dropdown,
    DropdownItem,
    DropdownMenu,
} from 'element-ui';
export default {
    components: {
        [Dropdown.name]: Dropdown,
        [DropdownItem.name]: DropdownItem,
        [DropdownMenu.name]: DropdownMenu,
    },
};
</script>

<style lang="less" scoped>
.user {
    display: flex;
    align-items: center;
    width: 132px;
    background-color: rgb(40, 40, 40);
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;

    &__avatar {
        flex-shrink: 0;
        width: 24px;
        height: 24px;
    }

    &__nickname {
        flex: 1;
        margin-left: 6px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
    }
}
</style>