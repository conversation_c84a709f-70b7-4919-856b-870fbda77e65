<template>
    <map-component
        :show-satellite-map="!isEmulating"
    >
        <PageWrapper>
            <Visualization
                v-if="!isEmulating" :show-road-line="true"
            />
            <EmulationTwin v-else @stop="handleStopEmulation"/>
        </PageWrapper>
    </map-component>
</template>

<script setup>
import MapComponent from './components/Map/index.vue';
import {onMounted, ref} from 'vue';
import {getProjectList, getDeviceListById} from '@/api/construction';
import {viewToMicro} from '@/utils';
import {PageWrapper} from '@/components/Common';
import Visualization from './components/Visualization/index.vue';
import EmulationTwin from './components/Emulation/components/EmulationTwin/index.vue';
import {
    isEmulating,
} from './components/Emulation';

// 项目类型 1桥梁 2路面 3互通立交 4设备实施
const projectType = ref(1);
// 施工列表
const constructionList = ref([]);
// 设备列表
const deviceList = ref();
const activeId = ref(null);
// 是否显示高速路线
const showRoadLine = ref(true);

async function fetchConstructionList() {
    const {data} = await getProjectList(projectType.value);
    constructionList.value = data.map(item => ({
        ...item,
        position: [
            ...item.eventPosition.split(','),
            item.eventAltitude,
        ],
    }));
}

function changeType(e) {
    projectType.value = e;
    deviceList.value = [];
    fetchConstructionList();
}

async function fetchDeviceListById(id) {
    const {data} = await getDeviceListById(id);
    deviceList.value = data.map(item => ({
        deviceStake: item.stake,
        sectionName: item.sectionName,
        position: [item.lng, item.lat, item.alt],
    }));
}

function handleClickConstruction({id, position}) {
    if (id === activeId.value) return;
    viewToMicro(position);
    activeId.value = id;
    fetchDeviceListById(id);
    showRoadLine.value = false;
}

function handleStopEmulation() {
    isEmulating.value = false;
}

onMounted(() => {
    fetchConstructionList();
});
</script>

<style lang="less" scoped>
.page-wrapper {
    width: 100%;
    height: 100%;
    display: flex;

    .map {
        flex: 1;
        height: 100%;

        .emulation-twin {
            position: absolute;
        }
    }
}
</style>