<template>
    <div class="list-layer">
        <map-line/>
        <toll-station-marker
            :list="tollStationList"
            @clickMarker="handleClickMarker"
        />
    </div>
</template>

<script>
import TollStationMarker from './components/TollStationMarker/index.vue';
import MapLine from './components/MapLine/index.vue';
import {onMounted, ref} from 'vue';
import {getTollList} from '@/api/tollStation';

export default {
    components: {
        MapLine,
        TollStationMarker,
    },
    props: {
        defaultCenter: Array,
    },
    setup(props, {emit}) {

        const tollStationList = ref([]);

        async function initTollStation() {
            const {data = []} = await getTollList();
            tollStationList.value = data.map(i => ({
                ...i,
                position: [i.lng, i.lat, i.alt],
            }));
        }

        onMounted(() => {
            initTollStation();
        });

        function handleClickMarker(e) {
            emit('clickMarker', e);
        }

        return {
            tollStationList,
            handleClickMarker,
        };
    },
};
</script>