<template>
    <div class="table">
        <div v-if="length" class="chose-num">
            <p>已选择<span>{{ length }}</span>项</p>
            <p class="link" @click.stop="exportExcel">
                <i class="el-icon-download"></i>
                批量导出
            </p>
        </div>
        <el-table
            :data="data"
            class="maintain-table"
            style="width: 100%"
            :height="767 * ratio"
            :row-style="rowStyle"
            @selection-change="handleSelectionChange"
        >
            <el-table-column
                type="selection"
                width="55"
                align="center"
            />
            <el-table-column
                v-for="col in column"
                :key="col.name"
                :prop="col.prop"
                :label="col.label"
                v-bind="col"
            />
        </el-table>
        <el-pagination
            :current-page="pages.pageNumber"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="pages.pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
    </div>
</template>

<script>
import {Table, TableColumn, Loading, Pagination} from 'element-ui';
import {ref, nextTick, watch} from 'vue';
import {useUnit} from '@/utils/hooks/useUnit';
export default {
    props: {
        data: {
            type: Array,
            default: () => ([]),
        },
        column: {
            type: Array,
            default: () => ([]),
        },
        loading: {
            type: Boolean,
            default: false,
        },
        pages: {
            type: Object,
            default: () => ({}),
        },
        total: {
            type: Number,
            default: 0,
        },
    },
    components: {
        [Table.name]: Table,
        [TableColumn.name]: TableColumn,
        [Pagination.name]: Pagination,
    },
    setup(props, {emit}) {
        const length = ref(0);
        const deviceIdList = ref([]);
        const handleSelectionChange = e => {
            length.value = e.length;
            deviceIdList.value = e.map(item => item.deviceId);
        };
        const {ratio} = useUnit();

        const tableLoading = ref(null);

        // 加载动画
        watch(() => props.loading, val => {
            if (val === false) tableLoading.value?.close();
            else {
                nextTick(() => {
                    tableLoading.value = Loading.service({
                        target: '.maintain-table',
                        background: 'rgba(255, 255, 255, .3)',
                    });
                });
            }
        }, {immediate: true});

        // 更改选中行背景色
        const rowStyle = ({row}) => {
            if (deviceIdList.value.includes(row.deviceId)) {
                return {'background-color': 'rgba(40, 194, 130, .1)', cursor: 'pointer'};
            }
            return {cursor: 'pointer'};
        };

        // 分页器发生改变时
        const handleSizeChange = e => {
            emit('size-change', e);
        };
        const handleCurrentChange = e => {
            emit('current-change', e);
        };

        // 点击导出按钮
        const exportExcel = () => {
            emit('export-excel', deviceIdList.value);
        };

        return {
            length,
            ratio,
            handleSelectionChange,
            handleSizeChange,
            handleCurrentChange,
            exportExcel,
            rowStyle,
        };
    },
};
</script>

<style lang="less" scoped>
    .table {
        .chose-num {
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            height: 48px;
            padding: 0 16.5px;
            font-family: 'OPluSans';
            font-size: 14px;
            font-weight: 400;
            color: rgba(255, 255, 255, .8);
            border: 1px solid rgba(0, 255, 114, 0.7);
            background-color: rgba(0, 255, 114, 0.08);
            margin-bottom: 16px;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 4px;
                height: 100%;
                background: rgba(40, 194, 130, .4);
            }

            span {
                margin: 0 8px;
                font-weight: 500;
                color: rgba(255, 255, 255, 1);
            }

            .link {
                color: rgb(0, 255, 149);
                cursor: pointer;
            }
        }

        :deep(.el-pagination) {
            text-align: right;
            margin-top: 10px;
            button, .el-pager li, input {
                background-color: rgba(0, 97, 255, .1);
                font-family: 'OPlusSans';
            }
            .el-pagination__jump {
                margin-left: 0;
            }
            input {
                border: none;
            }
            button {
                color: #fff;
            }
            .el-pager li {
                color: rgba(255, 255, 255, .6);

                &.active {
                    background-color: rgb(36, 104, 242);
                    color: #fff;
                }
            }
        }

        :deep(.el-loading-mask) {
            circle {
                stroke: #fff !important;
            }
        }

        :deep(.el-table) {
            background: transparent !important;
            color: rgba(255, 255, 255, .6);
            overflow: visible;

            .el-table__row:hover {
                cursor: pointer;
            }

            .el-table__row {
                td {
                    border-top: 0;
                }
            }

            .el-table__body-wrapper {
                    scrollbar-gutter: stable both-edges;
                    flex: 1;
                    overflow-y: auto !important;
                    overflow-x: hidden !important;
                }

            td {
                border-bottom: 1px solid rgba(196, 196, 196, .4);
            }

            .cell {
                height: 32px;
                display: flex;
                align-items: center;

                .btn {
                    color: #ffd6d3;
                    font-size: 12px;
                    cursor: pointer;
                    transition: filter .3s;

                    &:hover {
                        filter: contrast(1.2);
                    }

                    &:active {
                        filter: contrast(1.5);
                    }
                }
            }

            &::before,
            .el-table__fixed-right::before {
                content: '';
                display: none;
            }

            thead {
                height: 48px;
                font-size: 16px;
                color: rgba(255, 255, 255, .6);
                font-weight: 400;
                font-family: 'OPlusSans';
                background: rgba(18, 74, 166, 0.3);

                .cell {
                    height: 100%;
                    line-height: 48px;
                }
            }

            tr {
                background: transparent;
            }

            .el-table__cell {
                background: transparent !important;
                height: 32px !important;
                padding: 0 !important;
            }

            .el-table__body-wrapper {
                overflow: visible;
            }

            .el-table__row {
                position: relative;
                height: 50px;
                border: 1px solid red !important;

                .cell {
                    height: 100%;
                    line-height: 50px;
                }
            }

            .el-table__body {
                border-collapse: separate;

                tr {
                    border: 1px solid rgba(255, 255, 255, .15);
                }

                .el-table_1_column_1 .cell {
                    height: 100%;
                    display: flex;
                    align-items: center;
                }
            }

            .el-table__empty-block {
                height: calc(100% - 8px) !important;

                .el-table__empty-text {
                    color: #e7efffb4;
                }
            }
        }
    }
</style>