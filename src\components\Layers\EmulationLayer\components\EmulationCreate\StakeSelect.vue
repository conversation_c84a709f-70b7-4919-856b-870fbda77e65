<template>
    <div class="select-area">
        <el-select
            v-model="km"
            placeholder="千米桩"
            :class="[
                'h-40 mr-8',
                `${isFull ? 'w-50%' : 'w-100'}`,
            ]"
            filterable
            popper-class="emulation-el-popper"
            :disabled="disabled"
            @change="kmChange"
            @focus="focus"
        >
            <el-option
                v-for="item in KMList"
                :key="item.id"
                :label="item.stakeKM"
                :value="item.stakeKM"
            />
        </el-select>
        <el-select
            v-model="m"
            placeholder="米桩"
            popper-class="emulation-el-popper"
            :class="[
                'h-40',
                `${isFull ? 'w-50%' : 'w-80'}`,
            ]"
            :disabled="disabled"
            @change="mChange"
            @focus="focus"
        >
            <el-option
                v-for="item in mList"
                :key="item"
                :label="item"
                :value="item"
            />
        </el-select>
    </div>
</template>

<script>
import {Select, Option} from 'element-ui';
export default {
    name: 'StakeSelect',
    components: {
        [Select.name]: Select,
        [Option.name]: Option,
    },
    props: {
        list: {
            type: Array,
            default: () => [],
        },
        value: {
            type: String,
            default: undefined,
        },
        isFull: {
            type: Boolean,
            default: false,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    },
    model: {
        prop: 'value',
        event: 'changeValue',
    },
    data() {
        return {
            mList: [],
            KMList: [],
            km: '',
            m: '',
        };
    },
    components: {
        [Select.name]: Select,
        [Option.name]: Option,
    },
    computed: {
        stakeNumber() {
            if (!this.km) return undefined;
            if (!this.m) return undefined;
            return `${this.km}+${this.m}`;
        },
    },
    watch: {
        value: {
            handler(v) {
                if (v) {
                    const [km, m] = v.split('+');
                    this.km = km;
                    this.setMList(km);
                    this.m = m;
                }
            },
            immediate: true,
            deep: true,
        },
        list: {
            handler() {
                this.formateStakeNumberList();
                this.setMList();
            },
            immediate: true,
        },
    },
    methods: {
        formateStakeNumberList() {
            let list = [], preKm = '', MList = [];
            for (let el of this.list) {
                const [km, m] = el.stakeNumber.split('+');
                if (km !== preKm) {
                    list.push(Object.assign({...el}, {
                        stakeKM: preKm = km,
                        m: MList = [],
                    }));
                }
                MList.push(m);
            }
            this.KMList = list;
        },
        setMList(km = this.km) {
            const res = this.KMList.find(e => e.stakeKM === km);
            this.mList = new Set(res?.m) || [];
        },
        kmChange(v) {
            if (!v) return;
            this.setMList(v);
            this.m = this.mList[0];
            this.$emit('changeValue', this.stakeNumber);
            this.$emit('change', this.stakeNumber);
        },
        mChange(v) {
            this.$emit('changeValue', this.stakeNumber);
            this.$emit('change', this.stakeNumber);
        },
        focus() {
            this.$emit('focus');
        },
    },
};
</script>

<style lang="less" scoped>
    .select-area {
        padding: 0  0 !important;
        padding-top: 0 !important;
        display: flex;
        margin-bottom: 0 !important;
        margin-left: 0 !important;

        >span {
            display: block;
            color: #c4cfde;
            width: 98px !important;
        }

        .item {
            flex: 1;
            display: flex;
            align-items: center;
            flex-wrap: wrap;

            .select-item {
                display: flex;
                align-items: center;
                cursor: pointer;

                span:nth-child(1) {
                    display: inline-block;
                    width: 14px;
                    height: 14px;
                    border: 1px solid #98b8de;
                    border-radius: 50%;
                    margin-right: 8px;
                    margin-left: 16px;
                }

                span:nth-child(2) {
                    color: #c4cfde;
                }

                &.active-item {
                    span:nth-child(1) {
                        border-color: #49c3ff;
                        position: relative;

                        &::before {
                            content: '';
                            width: 8px;
                            height: 8px;
                            background: #49c3ff;
                            border-radius: 50%;
                            position: absolute;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%, -50%);
                        }
                    }

                    span:nth-child(2) {
                        color: #c4cfde;
                    }
                }
            }
        }
    }
</style>