<template>
    <div class="meso-maker">
        <!-- 桥梁扎点 -->
        <bridge-marker
            :manager-instace="domManager" icon-name="camera-bridge"
            bubble-color="rgb(7, 118, 237)"
            :need-detail="false"
            :info="{
                position: [markerInfo.lngRectify, markerInfo.latRectify, markerInfo.alt],
            }"
        >
            <div class="label">
                <p>{{ markerInfo.name }}</p>
                <p>{{ markerInfo.sectionName }}</p>
                <p v-if="markerInfo.sectionDirection">{{ directionDict.get(markerInfo.sectionDirection + '') }}</p>
                <p v-if="markerInfo.stake">{{ markerInfo.stake }}</p>
            </div>
        </bridge-marker>

        <!-- 摄像枪扎点 -->
        <camera-marker
            v-for="item in carmeraList"
            :key="item.cameraId"
            show-label
            :need-detail="false"
            :manager-instace="domManager" :info="{
                position: [item.lng, item.lat, item.alt],
                label: item.cameraName,
            }"
        />
        <!-- 显示屏扎点 -->
        <intelboard-marker
            v-for="item in boardList"
            :key="item.assetsCode"
            :manager-instace="domManager" icon-name="xianshiping"
            bubble-color="rgb(57, 182, 0)"
            show-label
            :need-detail="false"
            :info="{
                position: [item.lng, item.lat, item.alt],
                label: item.infoBoardName,
            }"
        />
        <intelboard-marker
            v-for="item in gantryList"
            :key="item.gantryId"
            :manager-instace="domManager" icon-name="shebei"
            bubble-color="rgb(0, 149, 57)"
            show-label
            :need-detail="false"
            :info="{
                position: [item.lng, item.lat, item.alt || 0],
                label: item.gantryName,
            }"
        />
    </div>
</template>

<script>
import {BubbleMarker} from '@/components/Common/index.js';
import {domManager} from './index.js';
import {
    getInfoboardList,
    getCameraList,
} from '@/api/equipment/highspeed.js';
import {ref, onMounted} from 'vue';
import {bridgeGantry} from '@/api/structure';
import {markerInfo} from '../../utils/index';
import {directionDict} from '@/config/maintain';

export default {
    props: {
        mapFilterData: {
            type: Array,
            default: () => ([]),
        },
    },
    components: {
        BridgeMarker: BubbleMarker,
        CameraMarker: BubbleMarker,
        IntelboardMarker: BubbleMarker,
    },
    setup(props, {emit}) {
        const carmeraList = ref([]);
        const boardList = ref([]);
        const gantryList = ref([]);
        const getIcon = () => {
            bridgeGantry().then(res => {
                gantryList.value = res.data;
            });
            getInfoboardList().then(res => {
                boardList.value = res.data;
            });
            getCameraList().then(res => {
                carmeraList.value = res.data;
            });
        };
        onMounted(() => {
            getIcon();
        });

        const getFullNameFn = item => {
            // eslint-disable-next-line max-len, vue/max-len
            let str = `${item.name}\n\n${item.sectionName}\n\n${item.sectionDirection ? directionDict.get(item.sectionDirection + '') : ''}`;
            return str;
        };

        return {
            domManager,
            markerInfo,
            carmeraList, boardList, gantryList,
            directionDict,
            getFullNameFn,
        };
    },
};
</script>

<style lang="less" scoped>
.label {
    width: max-content;
    background: rgba(#000, .8);
    padding: 10px 8px;
    font-size: 16px;
    border-radius: 4px;

    p {
        margin-bottom: 6px;

        &:last-child {
            margin-bottom: 0;
        }
    }
}
</style>