<template>
    <div class="equipment-distribution">
        <single-data-bar
            v-if="list.length"
            y-name="单位：台" :legend-data="legendData"
            :data="list"
            :tooltip-formatter="tooltipFormatter"
        />
        <no-data v-else/>
    </div>
</template>

<script>
import {SingleDataBar, NoData} from '@/components/Common';
import {getDeviceSpace} from '@/api/equipment/equipmentdisplay';
import {ref} from 'vue';
export default {
    name: '设备数量空间分布图',
    props: {
        deviceType: {
            type: String,
            default: '',
        },
    },
    components: {
        SingleDataBar,
        NoData,
    },
    setup(props) {
        const legendData = [
            {
                name: '收费分布数量',
                color: 'rgba(87, 255, 213, .8)',
            },
        ];
        const list = ref([]);

        const init = () => {
            getDeviceSpace(props.deviceType).then(res => {
                if (res.data && res.data.length) {
                    list.value = res.data.map(item => ({
                        color: 'rgba(87, 255, 213, .6)',
                        name: item.stake,
                        value: item.deviceNum,
                    }));
                }
                else {
                    list.value = [];
                }
            });
        };

        const tooltipFormatter = e => {
            return `<div class="tooltip-format">
                <div class="title-rb">${e[0].name}</div>
                <div class="info">
                    <div class="item">
                        <div class="item-name">设备数量</div>
                        <div class="item-info">${e[0].value}</div>
                    </div>
                </div>
            </div>`;
        };

        return {
            legendData,
            list,
            init,
            tooltipFormatter,
        };
    },
};
</script>

<style lang="less" scoped>
.equipment-distribution {
    width: 1184px;
    height: 190px;
}
</style>