<template>
    <div class="">
        <BubbleMarker
            v-for="item in list"
            :key="item.label"
            show-label
            :manager-instace="domManager"
            icon-name="car"
            bubble-type="diamond"
            bubble-color="#FF4B4B"
            :info="item"
            :need-hover="false"
        />
    </div>
</template>

<script setup>
import {getKeyCarList} from '@/api/construction';
import {BubbleMarker} from '@/components/Common';
import {watch, ref} from 'vue';
import {domManager} from '@/views/Construction/utils';
import {projectType} from '../../store';

const list = ref([]);

async function fetchData() {
    const {data} = await getKeyCarList({
        projectType: projectType.value,
    });
    list.value = data?.map(item => ({
        position: [item.lng, item.lat, item.alt],
        label: item.carNo,
    }));
}

watch(
    () => projectType.value,
    () => {
        fetchData();
    },
    {
        immediate: true,
    }
);
</script>