<template>
    <Card
        title="车辆流水" card-type="card-long-2"
        class="grid-item"
    >
        <template #content>
            <api-table
                :columns="columns" :api="getVehiclePage"
                :request-params="{
                    serviceId,
                }" :request-options="{
                    pageField: 'pageNumber',
                    sizeField: 'pageSize',
                    listField: 'result',
                    totalField: 'totalCount',
                }"
            />
        </template>
        <template #titleContent>
            <router-link to="/service-area/carFlow" class="view">查看更多</router-link>
        </template>
    </Card>
</template>
<script setup>
import {ApiTable, Card} from '@/components/Common';
import {useRouter, useUnit} from '@/utils';

import {
    getVehiclePage,
} from '@/api/serviceManager/index.js';
import {vehicleType} from '@/config/serviceMap.js';
import {serviceId} from '@/views/ServiceManagement/utils/index';

const {ratio} = useUnit();

const columns = [
    {
        label: '车牌号',
        prop: 'plate',
        width: `${112 * ratio.value}px`,

    },
    {
        label: '车型',
        prop: 'vehicleType',
        width: `${112 * ratio.value}px`,
        enumData: vehicleType,
    },
    {
        label: '进入时间',
        prop: 'enterTime',
        width: `${166 * ratio.value}px`,
    },

];
const router = useRouter();


</script>

<style scoped>
.view {
    color: #01ffe5;
    cursor: pointer;
    border: 1px solid #01ffe5;
    padding: 4px 16px;
    margin-right: 20px;
}
</style>