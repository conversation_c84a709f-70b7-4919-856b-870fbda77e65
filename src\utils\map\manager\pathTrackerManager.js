import {addPathTracker, addLine, addModel, removeModel, removeLine} from '../index';
// 是野蛮懂管理器
class PathTrackerManager {
    constructor(engine) {
        this.pathTrackerMap = new Map();
        this.engine = engine;
    }
    addPathTracker(name, options) {
        if (this.pathTrackerMap.has(name)) {
            this.removePathTrackerByName(name);
        }
        const {
            position,
            positions,
            color = '#d0a63c',
            opacity = 1, lineWidth = 15,
            modelType,
            viewMode = 'unlock',
        } = options || {};

        let {line} = addLine(positions, {
            lineWidth, color, opacity,
        });

        addModel(modelType, position, 150, model => {
            addPathTracker({
                positions: positions,
                position,
                model,
                viewMode,
            });

            this.pathTrackerMap.set(name, {
                'line': line,
                'model': model,
            });
        });
    }
    removePathTrackerByName(name) {
        const pathTracker = this.pathTrackerMap.get(name);
        pathTracker && removeModel(pathTracker.model, this.engine);
        pathTracker && removeLine(pathTracker.line, this.engine);
        this.pathTrackerMap.delete(name);
    }

    clear() {
        [...this.pathTrackerMap.keys()].forEach(pathTracker => {
            this.removePathTrackerByName(pathTracker);
        });
        this.pathTrackerMap.clear();
    }
}

export {
    PathTrackerManager,
};