<template>
    <div class="detail-wrapper">
        <LeftPanel :collapse.sync="leftCollapse"/>
        <RightPanel :collapse.sync="rightCollapse"/>
        <div
            :class="['btn-back', {collapse: leftCollapse}]"
            @click="handleBack"
        >
            <i class="el-icon-arrow-left"></i>
            <span>返回主页</span>
        </div>
        <MapIconFilter
            v-model="filterKeys"
            multiple
            :class="['map-control', {collapse: rightCollapse}]"
            :list="typeList"
        />
        <VidiconMarker/>
        <GantryMarker v-if="filterKeys.includes('MENJIA')"/>
        <TollStationWarn v-if="filterKeys.includes('YONGDU')"/>
        <BoardMarker v-if="filterKeys.includes('QINGBAOBAN')"/>
        <BubbleMarker
            :manager-instace="domManager"
            bubble-type="rect"
            bubble-color="#009539"
            icon-name="toll-station"
            show-label
            :need-detail="false"
            :need-hover="false"
            :info="getTollStationInfo"
        />
    </div>
</template>

<script setup>
import {computed, ref} from 'vue';
import LeftPanel from './LeftPanel/index.vue';
import RightPanel from './RightPanel/index.vue';
import VidiconMarker from './VidiconMarker/index.vue';
import GantryMarker from './GantryMarker/index.vue';
import TollStationWarn from './TollStationWarn/index.vue';
import BoardMarker from './BoardMarker/index.vue';
import {BubbleMarker, MapIconFilter} from '@/components/Common';
import {domManager} from '@/views/TollStation/utils';
import {tollStation} from '../../store';
import {useCollapsePanel} from '@/hooks/useCollapsePanel';

const emit = defineEmits(['back']);

const {collapse: leftCollapse} = useCollapsePanel();
const {collapse: rightCollapse} = useCollapsePanel();

const filterKeys = ref(['MENJIA', 'YONGDU', 'QINGBAOBAN']);

const typeList = [
    {
        icon: 'menjia',
        value: 'MENJIA',
        label: '门架',
    },
    {
        icon: 'yongdu',
        value: 'YONGDU',
        label: '拥堵',
    },
    {
        icon: 'xianshiping',
        value: 'QINGBAOBAN',
        label: '情报板',
    },
];

const getTollStationInfo = computed(() => ({
    label: tollStation.value.stationName,
    position: tollStation.value.position,
}));

const handleBack = () => {
    emit('back');
};

</script>

<style lang="less" scoped>
.btn-back {
    position: absolute;
    z-index: 100;
    bottom: 85px;
    left: 1290px;
    width: 124px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    color: #fff;
    font-size: 18px;
    cursor: pointer;
    transition: .32s linear;
    background-image:
        linear-gradient(
            to right,
            rgb(36, 104, 242),
            rgba(1, 255, 229, .7)
        );
    clip-path:
        polygon(
            0 0,
            100% 0,
            100% calc(100% - 10px),
            calc(100% - 10px) 100%,
            0 100%
        );

    span {
        padding-left: 6px;
    }

    &.collapse {
        left: 40px;
    }
}

.map-control {
    position: absolute;
    bottom: 85px;
    right: 1290px;
    transition: .32s linear;
    z-index: 30;

    &.collapse {
        right: 40px;
    }
}
</style>