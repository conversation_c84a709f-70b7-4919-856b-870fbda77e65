# BuildMaker 事件扎点组件
## 传参
|参数名|类型|是否必传|含义|默认值|
|-|-|-|-|-|
|data|Event|true|事件数据|null|
|managerInstance|Object|true|扎点示例|null|
|showDistance|Boolean|false|是否展示距离|false|


```ts
interface Maker {
    typeName: string,
    latitude: string|number,
    longitude: string|number,
    stakeNumber: string,
    length: number,
    laneId: string,
    [string]: any
}

const laneId = '1;2;3';
```

## 事件
|事件名|含义|参数|
|-|-|-|
|handlerClick|扎点点击回调|(info)|


