<template>
    <div
        class="back"
        @click="callback"
    >
        <i class="el-icon-back"></i>
        {{ title }}
    </div>
</template>

<script>
export default {
    props: {
        title: {
            type: String,
            default: '返回上一级',
        },
        callback: {
            type: Function,
            default: null,
        },
    },
};
</script>

<style lang="less" scoped>
.back {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 30px;
    padding: 0 10px;
    border-radius: 3px;
    border: 1px solid rgba(0, 231, 181, .25);
    background-image: linear-gradient(to bottom, rgba(34, 120, 105, .6), rgba(15, 103, 74, .6));
    color: #fff;
    font-size: 12px;
    letter-spacing: 1px;
    font-weight: 400;
    font-family: 'PingFang SC', 'PingFang-SC';
    cursor: pointer;
}
</style>