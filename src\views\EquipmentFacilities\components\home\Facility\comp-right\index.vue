<template>
    <div class="comp-right">
        <div class="comp-right-item">
            <card class="card-short-2" title="路面遗撒物统计">
                <template #content>
                    <relictStatistics/>
                </template>
            </card>
            <card class="card-short-2" title="路面技术评定排名">
                <template #content>
                    <roadTechnicalRanking/>
                </template>
            </card>
        </div>
        <div class="comp-right-item">
            <card class="card-short-2" title="路段清洁物统计">
                <template #content>
                    <cleaningStatistics/>
                </template>
            </card>
            <card class="card-short-2" title="桥梁技术评定排名">
                <template #content>
                    <bridgeTechnicalRanking/>
                </template>
            </card>
        </div>
    </div>
</template>

<script>
import {Card} from '@/components/Common/index';
import relictStatistics from './relictStatistics.vue';
import roadTechnicalRanking from './roadTechnicalRanking.vue';
import cleaningStatistics from './cleaningStatistics.vue';
import bridgeTechnicalRanking from './bridgeTechnicalRanking.vue';
export default {
    name: '设施管理右',
    components: {
        Card,
        relictStatistics,
        roadTechnicalRanking,
        cleaningStatistics,
        bridgeTechnicalRanking,
    },
};
</script>

<style lang="less" scoped>
.comp-right {
    width: 100%;
    height: calc(100% - 152px);
    .comp-right-item {
        display: flex;

        &:first-child {
            margin-bottom: 23px;
        }

        > div {
            &:first-child {
                margin-right: 24px;
            }
        }
    }
}
</style>