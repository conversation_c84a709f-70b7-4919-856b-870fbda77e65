<template>
    <div
        :class="['left-panel', {collapse}]"
    >
        <collapse-panel
            show-header
            :title="stationName"
            :collapse.sync="collapse"
        >
            <template #header-right>
                <div
                    class="btn-back"
                    @click="handleBack"
                >
                    <i class="el-icon-arrow-left"></i>
                    返回上一级
                </div>
            </template>
            <div class="card-wrapper">
                <data-card :info="tollStationOperation"/>
                <info-card :info="tollStationInfo"/>
            </div>
        </collapse-panel>
    </div>
</template>

<script>
import {onMounted, ref} from 'vue';
import {CollapsePanel} from '@/components/Common';
import DataCard from './Card/DataCard.vue';
import InfoCard from './Card/InfoCard.vue';
import {getTollInfo, getTollOperation} from '@/api/tollStation';

export default {
    components: {
        CollapsePanel,
        DataCard,
        InfoCard,
    },
    props: {
        stationId: String,
        stationName: String,
    },
    setup(props, {emit}) {
        const collapse = ref(false);
        const tollStationInfo = ref({});
        const tollStationOperation = ref({});

        function handleBack() {
            emit('back');
        }


        async function initTollStationInfo() {
            if (!props.stationId || !props.stationName) return;
            const [
                {data: tollInfo},
                {data: tollOperation},
            ] = await Promise.all([
                getTollInfo(props),
                getTollOperation(props),
            ]);
            tollStationInfo.value = tollInfo;
            tollStationOperation.value = tollOperation;
        }

        onMounted(() => {
            initTollStationInfo();
        });

        return {
            collapse,
            handleBack,
            tollStationInfo,
            tollStationOperation,
        };
    },
};
</script>

<style lang="less" scoped>
.left-panel {
    flex-shrink: 0;
    margin-right: 16px;

    &.collapse {
        margin-right: 0;
    }
}

.btn-back {
    cursor: pointer;
    color: #28c282;
    font-size: 14px;
}

.card-wrapper {
    & > *:not(:first-child) {
        margin-top: 16px;
    }
}
</style>