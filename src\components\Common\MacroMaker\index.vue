
<!-- 宏观视角图层 -->
<template>
    <div
        v-if="renderDom"
        ref="marker"
        @click="onHandle"
    >
        <div class="marker-dom">
            <p>{{ info.labelName }}</p>
        </div>
        <slot></slot>
    </div>
</template>
<script>
import {ref, onMounted, onBeforeUnmount, watch, computed} from 'vue';
import {getTextWidth} from '@/utils';

export default {
    props: {
        // 基本信息 包括位置、名称 自定义数据 点击回调等
        info: {
            type: Object,
            default: () => null,
        },
        // 宏观管理实例
        macroInstace: {
            type: Object,
            default: () => null,
        },
    },
    setup(props) {
        const marker = ref(null);
        const iconName = ref(() => props.info.pointName || 'MacroMaker');
        const hasActive = computed(() => !!props.info.activeIconUrl || false);
        const renderDom = computed(() => props.info.renderDom ?? true);
        const getDomWidth = computed(() => `${getTextWidth(props.info.labelName, 16) + 20}px`);

        const onClick = e => {
            props.info.clickCallback && props.info.clickCallback(e);
        };

        const onMouseenter = e => {
            if (hasActive.value) {
                addIcon(true);
            }
            props.info.onMouseenter && props.info.onMouseenter(e);
        };

        const onMouseleave = e => {
            if (hasActive.value) {
                removeIcon(iconName.value + '-active');
            }
            props.info.onMouseleave && props.info.onMouseleave(e);
        };

        function addIcon(active) {
            if (!props.info.position) return;
            const Name = active ? iconName.value + '-active' : iconName.value;
            props.macroInstace.addBubble(
                Name,
                props.info.position,
                {
                    iconUrl: (active ? props.info.activeIconUrl : props.info.iconUrl)
                        || 'maplayer/assets/image/base/icons/normal.png',
                    labelDom: marker.value,
                    labelText: props.info.labelName,
                    text: props.info.number,
                    customData: props.info.customData,
                    clickCallback: onClick,
                    onMouseenter,
                    onMouseleave,
                }
            );
        };

        function removeIcon(name) {
            props.macroInstace.removeBubbleByName(name);
        };

        const onHandle = () => {
            props.info.clickCallback(props.info);
        };

        watch(() => props.info, (_, {pointName}) => {
            removeIcon(pointName);
            removeIcon(pointName + '-active');
            addIcon();
        }, {deep: true});

        onMounted(() => {
            addIcon();
        });

        onBeforeUnmount(() => {
            removeIcon(iconName.value);
            removeIcon(iconName.value + '-active');
        });
        return {
            marker,
            onHandle,
            renderDom,
            getDomWidth,
        };
    },
};
</script>

<style lang="less" scoped>
.marker-dom {
    position: absolute;
    top: -10px;
    display: flex;
    width: v-bind(getDomWidth);
    justify-content: center;
    align-items: center;
    height: 30px;
    color: #fff;
    background-image: url('/maplayer/assets/image/base/macrolebal_background.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
    font-size: 14px;
    transform: translateX(-50%);
    z-index: 200;
}
</style>