<template>
    <div class="emulation-wrapper">
        <template v-if="!isEmulating">
            <emulation-list
                v-if="!createConfig.show"
                class="emulation-right"
                :emulation-type="emulationType"
                :emulation-platform="emulationPlatform"
                @create="handleCreate"
                @view="handleView"
                @edit="handleEdit"
                @run="handleRun"
            />
            <emulation-create
                v-else
                class="emulation-right"
                :emulation-type="emulationType"
                :emulation-platform="emulationPlatform"
                :is-edit="createConfig.isEdit"
                :default-info="createConfig.info"
                @save="handleSave"
                @run="handleRun"
                @cancel="handleCancel"
            />
        </template>
        <!-- <emulation-twin
            v-else
            :scheme-id="emulationData.schemeId"
            :uuid="emulationData.uuid"
            @stop="handleStop"
            @predict="$emit('predict', $event)"
        /> -->
    </div>
</template>

<script>
import {onMounted, ref} from 'vue';
import EmulationList from './components/EmulationList/index.vue';
import EmulationCreate from './components/EmulationCreate/index.vue';
import {getUuid} from '@/api/emulation';
import {
    isEmulating,
    createConfig,
    schemaInfo,
} from '.';
import {uniq} from 'lodash-es';

export default {
    components: {
        EmulationList,
        EmulationCreate,
    },
    props: {
        emulationType: {
            type: Number,
            default: 3,
        },
        emulationPlatform: {
            type: String,
            default: 'FOKAI_EMULATION',
        },
    },
    setup(props, {emit}) {
        const emulationData = ref({
            uuid: '',
            schemeId: '',
        });

        function handleCreate() {
            createConfig.value = {
                show: true,
                isEdit: false,
                info: null,
            };
        }

        function handleView({id}) {
            emulationData.value.schemeId = id;
            schemaInfo.value = {
                ...emulationData.value,
            };
            isEmulating.value = true;
            emit('entryEmulation');
        }

        function handleCancel() {
            createConfig.value.show = false;
        }

        function handleStop() {
            isEmulating.value = false;
        }

        // 解析仿真方案信息
        function formatSchemeInfo(schemeInfo) {
            if (!schemeInfo) return {};
            const [event = {}] = schemeInfo.eventList;
            const info = {
                baseInfo: {
                    id: schemeInfo.id,
                    name: schemeInfo.name,
                    areaChoose: schemeInfo.areaChoose,
                    weatherScene: schemeInfo.weatherScene,
                    highSpeedName: schemeInfo.highSpeedName,
                    emulationStartStake: schemeInfo.emulationStartStake,
                    emulationEndStake: schemeInfo.emulationEndStake,
                    emulationStartTime: schemeInfo.emulationStartTime,
                    emulationEndTime: schemeInfo.emulationEndTime,
                    flowDistance: schemeInfo.flowDistance,
                    eventType: event.eventType,
                    eventLocationType: event.eventLocationType,
                    eventStartTime: event.eventStartTime,
                    duration: event.duration,
                    highSpeedName1: schemeInfo.highSpeedName,
                    eventStartStake: event.eventStartStake,
                    eventPosition: event.eventPosition,
                    direction: event.direction,
                    closeLane: event.closeLane?.split(',').map(i => +i),
                    limitSpeed: event.limitSpeed,
                    upstreamTransitionLength: event.upstreamTransitionLength,
                    constructionLength: event.constructionLength,
                    downstreamTransitionLength: event.downstreamTransitionLength,
                    // 施工相关
                    constructionCornerGeoList: event.constructionCornerGeoList,
                    constructionGeoList: event.constructionGeoList,
                },
                flowInfo: {
                    flowInputType: schemeInfo.flowInputType,
                    flowList: schemeInfo.flowList,
                    customFlowStartTime: schemeInfo.emulationStartTime,
                    customFlowEndTime: schemeInfo.emulationEndTime,
                },
                strategyInfo: {
                    strategyInputType: schemeInfo.strategyInputType,
                },
                modelInfo: schemeInfo.model,
            };
            if (info.strategyInfo.strategyInputType === 2) {
                const newStrategyInfo = {};
                schemeInfo?.strategyList[0]?.forEach(item => {
                    if (item.type === 1) {
                        newStrategyInfo.emergencyConfig = {
                            controlStake: item.controlStake,
                            position: item.position,
                            controlLength: item.controlLength,
                            limitSpeed: item.limitSpeed,
                            controlDuration: item.controlDuration,
                            type: 1,
                        };
                    }
                    else if (item.type === 2) {
                        const lane = item.lane?.split(',').map(Number);
                        const laneControlType = item.laneControlType?.split(',').map(Number);
                        const limitSpeed = item.limitSpeed?.split(',').map(Number);
                        newStrategyInfo.laneControlConfig = {
                            controlStake: item.controlStake,
                            position: item.position,
                            controlLength: item.controlLength,
                            configList: lane.map((l, index) => ({
                                lane: l,
                                laneControlType: laneControlType[index],
                                limitSpeed: limitSpeed[index],
                            })),
                        };
                    }
                    else if (item.type === 3) {
                        const entranceExitType = item.entranceExitType?.split(',').map(Number);
                        const otherKeyMap = {
                            1: '',
                            2: 'entryConfig',
                            3: 'exportConfig',
                        };
                        newStrategyInfo.entryAndExitConfig = {
                            rampName: item.rampName,
                            configList: entranceExitType.map(type => {
                                const otherKey = otherKeyMap[type];
                                const otherConfig = item[otherKey] || {};
                                return {
                                    ...otherConfig,
                                    entranceExitType: type,
                                };
                            }),
                        };
                    }
                });
                info.strategyInfo = {
                    ...info.strategyInfo,
                    ...newStrategyInfo,
                    strategyType: uniq(schemeInfo?.strategyList[0]?.map(item => item.type)),
                };
            }
            return info;
        }

        function handleEdit(e) {
            createConfig.value = {
                show: true,
                isEdit: true,
                info: formatSchemeInfo(e),
            };
        }

        function handleSave() {
            createConfig.value.show = false;
        }

        function handleRun({id}) {
            emulationData.value.schemeId = id;
            schemaInfo.value = {
                ...emulationData.value,
            };
            isEmulating.value = true;
            emit('entryEmulation');
        }

        async function initUUID() {
            const {data} = await getUuid();
            emulationData.value.uuid = data;
        }

        onMounted(() => {
            initUUID();
        });

        return {
            isEmulating,
            createConfig,
            handleCreate,
            handleView,
            handleCancel,
            handleStop,
            handleEdit,
            handleSave,
            emulationData,
            handleRun,
        };
    },
};
</script>