<template>
    <div class="time-select">
        <div class="time-select-header">
            <span class="time-select-header__title">
                {{ title }}
            </span>
        </div>
        <div class="time-select-content">
            <ul class="time-select-content__hour">
                <li class="time-select__hour-header">时</li>
                <li
                    v-for="item in hours"
                    ref="hoursRef"
                    :key="item.hour"
                    class="time-select__hour-item"
                    :class="{'time-select__hour-item--selected': item.hour === hour}"
                    @click="selectHour(item)"
                >
                    <span>
                        {{ item.hour }}
                    </span>
                </li>
            </ul>
            <ul class="time-select-content__minute">
                <li class="time-select__minute-header">分</li>
                <li
                    v-for="item in minutes"
                    ref="minutesRef"
                    :key="item.minute"
                    class="time-select__minute-item"
                    :class="{
                        'time-select__minute-item--disabled': item.disabled,
                        'time-select__minute-item--selected': item.minute === minute,
                    }"
                    @click="selectMinute(item)"
                >
                    <span>
                        {{ item.minute }}
                    </span>
                </li>
            </ul>
        </div>
        <hr class="dividing-line" size="3">
        <div class="time-select-footer">
            <el-button
                class="time-select-footer__cancel"
                icon="el-icon-circle-close"
                size="medium"
                @click="handleCancel"
            >
                取消
            </el-button>
            <el-button
                class="time-select-footer__confirm"
                icon="el-icon-circle-check"
                size="medium"
                @click="handleConfirm"
            >
                确定
            </el-button>
        </div>
    </div>
</template>
<script>
import {ref, shallowRef, onMounted, onBeforeMount, watch} from 'vue';
import {Button} from 'element-ui';
import dayjs from 'dayjs';
import {
    dateFormat,
    hourList,
    minuteList,
    padStart,
    getFormatPrefix,
} from './config';
export default {
    name: 'TimeSelect',
    components: {
        [Button.name]: Button,
    },
    props: {
        title: {
            type: String,
            default: '选择时间',
        },
        value: {
            type: String,
            default: '',
        },
        format: {
            type: String,
            default: 'YYYY-MM-DD HH:mm:ss',
        },
        futureTime: {
            type: Boolean,
            default: false,
        },
        firstSelection: {
            type: Boolean,
            default: false,
        },
    },
    model: {
        prop: 'value',
        event: 'update:value',
    },
    setup(props, {emit}) {
        const hours = ref([]);
        const minutes = shallowRef([]);
        const hoursRef = shallowRef([]);

        const day = ref('');
        const hour = ref('');
        const minute = ref('');

        const minutesRef = ref([]);

        const selectHour = item => {
            hour.value = item.hour;
            day.value = item.date;
        };

        const selectMinute = item => {
            minute.value = item.minute;
        };

        const handleConfirm = () => {
            const value = getValue();
            update(value);
            emit('confirm', value);
        };

        const handleCancel = () => {
            emit('cancel');
            reset(props.value);
        };

        function getValue() {
            return dayjs(`${day.value}${hour.value}:${minute.value}`).format(props.format);
        }

        function update(value) {
            emit('update:value', value);
        }

        function initHours() {
            updateMinutes();
            const now = dayjs();
            const today = now.format(dateFormat);
            if (!props.futureTime) {
                hours.value = hourList.map(h => {
                    return {
                        date: today,
                        hour: h,
                    };
                });
                return;
            }
            // 未来24小时
            const nextday = now.add(1, 'day').format(dateFormat);
            const hourIndex = now.hour();
            const todayHours = hourList.slice(hourIndex);
            const nextdayHours = hourList.slice(0, hourIndex);
            hours.value = [
                ...todayHours.map(h => {
                    return {
                        date: today,
                        hour: h,
                    };
                }),
                ...nextdayHours.map(h => {
                    return {
                        date: nextday,
                        hour: h,
                    };
                }),
            ];
        };

        function updateMinutes() {
            const now = dayjs();
            const minuteIndex = now.minute();
            minutes.value = minuteList.map(m => {
                const selected = dayjs(`${day.value}${hour.value}`).hour();
                const now = dayjs().hour();
                return {
                    minute: m,
                    disabled: props.futureTime && selected === now && m < minuteIndex,
                };
            });
        };

        function reset(val) {
            if (!val) {
                val = dayjs().format(props.format);
            }
            const formatPrefix = getFormatPrefix(props.format);
            if (formatPrefix) {
                val = dayjs().format(formatPrefix) + val;
            }
            const date = dayjs(val);
            day.value = date.format(dateFormat);
            hour.value = padStart(date.hour());
            minute.value = padStart(date.minute());
        };

        function toActiveMinute() {
            const to = el => {
                const {top: parentTop} = el.parentElement.getBoundingClientRect();
                const {top, height} = el.getBoundingClientRect();
                el.parentElement.scrollTo({
                    top: top - parentTop - height,
                    behavior: 'smooth',
                });
            };

            let hourIndex = hours.value.map(item => item.hour).indexOf(hour.value);
            let minuteIndex = minutes.value.map(item => item.minute).indexOf(minute.value);
            if (minuteIndex === -1) {
                minuteIndex = minutes.value.map(item => item.disabled).indexOf(false);
            }

            const minuteEl = minutesRef.value[minuteIndex];
            const hourEl = hoursRef.value[hourIndex];

            to(hourEl);
            to(minuteEl);
        };

        watch(() => hour.value, () => {
            if (props.futureTime) {
                updateMinutes();
            }
        });

        watch(() => props.value, reset);

        onBeforeMount(() => {
            reset(props.value);
            initHours();
        });

        onMounted(() => {
            toActiveMinute();
            if (props.firstSelection && !props.value) {
                const value = getValue();
                update(value);
            }
        });

        return {
            hour,
            minute,
            hours,
            minutes,
            handleConfirm,
            handleCancel,
            selectHour,
            selectMinute,
            hoursRef,
            minutesRef,
        };
    },
};
</script>
<style lang="less" scoped>
.time-select {
    width: 178px;
    height: 408px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    background: rgba(32, 72, 63, .78);

    .time-select-header {
        height: 40px;
        background: linear-gradient(270deg, rgba(26, 154, 116, .8), rgba(25, 189, 142, .8) 100%);
        text-align: center;

        .time-select-header__title {
            color: rgb(255, 255, 255);
            font-family: PingFang SC;
            font-size: 16px;
            font-weight: 400;
            line-height: 36.4px;
            letter-spacing: .5px;
        }
    }

    .time-select-content {
        display: flex;
        padding: 10px 0;
        width: inherit;
        height: 295px;
        overflow: hidden;

        .time-select-content__hour,
        .time-select-content__minute {
            width: 50%;
            overflow-y: auto;

            .time-select__hour-item,
            .time-select__minute-item {
                padding: 4px 29px;
                font-size: 12px;
                color: #fff;
                text-align: center;
                cursor: pointer;

                span {
                    width: 31px;
                    height: 31px;
                    display: block;
                    padding: 10px 0;
                }
            }

            .time-select__minute-item--disabled {
                color: rgb(165, 173, 186);
                cursor: not-allowed;
            }

            .time-select__hour-item--selected,
            .time-select__minute-item--selected {
                span {
                    border-radius: 3px;
                    box-shadow: inset 0 1px 11px 0 rgba(0, 255, 216, .5);
                    background:
                        linear-gradient(
                            180deg,
                            rgb(0, 255, 186) -29.533%,
                            rgba(0, 255, 219, 0) 24.267%,
                            rgba(0, 255, 205, .43) 151.258%,
                            rgb(0, 255, 186) 216.911%
                        );
                }
            }

            .time-select__hour-header,
            .time-select__minute-header {
                padding: 14px 29px;
                font-size: 12px;
                text-align: center;
                color: rgb(165, 173, 186);
            }
        }
    }

    .time-select-footer {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 65px;
        border: 2px solid;
        border-image: linear-gradient(transparent 90%, rgba(92, 244, 195, .5)) 1;
        clip-path: inset(0 round 0 0 0 3px);

        .time-select-footer__cancel,
        .time-select-footer__confirm {
            width: 63px;
            height: 30px;
            padding: 5px;
            color: #fff;
            font-size: 12px;
            border-radius: 3px;
            border: none;
        }

        .time-select-footer__cancel {
            border: 1.5px solid;
            border-image: linear-gradient(rgba(55, 144, 132, .7), rgba(92, 244, 195, .9)) 1;
            clip-path: inset(0 round 1.5px);
            background: linear-gradient(-66.16deg, rgba(34, 120, 105, .58) 9.656%, rgba(15, 103, 74, .62) 92.129%);
        }

        .time-select-footer__confirm {
            background: linear-gradient(180deg, rgb(26, 240, 147) .07%, rgb(20, 162, 178) 100%);
        }
    }

    .dividing-line {
        width: 100%;
        border: none;
        margin: 0;
        background: linear-gradient(.25turn, transparent, rgb(0, 255, 186), transparent);
    }

    &::after {
        content: '';
        position: absolute;
        top: calc(100% - 1px);
        right: 0;
        width: 59px;
        height: 8px;
        background: url('./assets/images/rect.svg');
    }
}
</style>./config/config.js