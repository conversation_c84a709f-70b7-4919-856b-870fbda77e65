<template>
    <card class="main-maintain-plan-list" title="待执行计划列表">
        <template #content>
            <div class="maintain-list-box">
                <div class="maintain-list-header">
                    <div class="list-header-item">养护计划时间</div>
                    <div class="list-header-item">养护计划结束时间</div>
                    <div class="list-header-item">养护施工路段</div>
                    <div class="list-header-item">审核状态</div>
                </div>
                <div class="maintain-list-content">
                    <no-data v-if="!list.length"/>
                    <div
                        v-for="item in list"
                        :key="item"
                        class="list-row"
                        @click="rowClick(item)"
                    >
                        <div class="list-row-item">{{ item.startTime }}</div>
                        <div class="list-row-item">{{ item.endTime }}</div>
                        <div class="list-row-item">{{ item.roadName }}</div>
                        <div class="list-row-item">{{ item.status }}</div>
                    </div>
                </div>
            </div>
        </template>
    </card>
</template>

<script>
import {Card, NoData} from '@/components/Common';
export default {
    name: 'MainMaintainPlanList',
    props: {
        list: {
            type: Array,
            default: () => ([]),
        },
    },
    components: {
        Card,
        NoData,
    },
    setup(_, {emit}) {
        const rowClick = item => {
            emit('rowClick', item);
        };
        return {
            rowClick,
        };
    },
};
</script>

<style lang="less" scoped>
/* 组件样式 */
.maintain-list-box {
    display: flex;
    flex: 1;
    flex-direction: column;
    color: #fff;
    font-size: 12px;
    line-height: 14px;

    .maintain-list-header {
        display: flex;
        background: linear-gradient(deg, rgba(6, 191, 160, .29) -22.911%, rgba(102, 255, 205, 0) 100%);
        padding: 8px 0;

        .list-header-item {
            flex: 1;
            padding: 0 8px;

            &:nth-child(3) {
                flex: 1.5;
            }
        }
    }

    .maintain-list-content {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: auto;
        max-height: 240px;

        .list-row {
            display: flex;
            padding: 6px 0;
            cursor: pointer;

            .list-row-item {
                flex: 1;
                display: flex;

                &:nth-child(3) {
                    flex: 1.5;
                }
            }

            &:hover {
                background: rgba(255, 255, 255, .1);
            }
        }
    }
}

.main-maintain-plan-list.card {
    flex: 1;
    display: flex;
    flex-direction: column;

    /deep/ .content {
        padding: 4px 16px;
        flex: 1;
        display: flex;
        flex-direction: column;
    }
}
</style>