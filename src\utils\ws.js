/**
 * @file websocket相关方法
 */

import qs from 'query-string';

class Ws {
    // 构造函数
    constructor() {
        // 三维视图
        this.lockReconnect = false;
        this.connectNumber = 0;
        this.ws = null;
        this.max = null;
        this.beforeReconnect = null;
        this.disposeStatus = false;
    }

    reconnect(url, params) {
        if (this.lockReconnect || this.disposeStatus) {
            return;
        }
        this.lockReconnect = true;
        this.connectNumber++;
        setTimeout(async () => {
            console.log('重连中, 请稍候', this.ws);  // eslint-disable-line
            this.lockReconnect = false;
            const fn = this.ws.onmessage;
            await this.connect(url, params, fn);
        }, 2000);
    }

    connect(url, params, msgFn) {
        return new Promise((resolve, reject) => {
            const token = true;
            if (token) {
                const tokenObj = {
                    ...params,
                    //  token: JSON.parse(token).token
                };
                const paramsUrl = qs.stringify(tokenObj);
                const fullUrl = `${url}${paramsUrl ? `?${paramsUrl}` : ''}`;
                const ws = new WebSocket(decodeURIComponent(fullUrl));
                this.ws = ws;
                ws.onopen = () => {
                    console.log('WebSocket=>onopen');  // eslint-disable-line
                    this.connectNumber = 0;
                    resolve(ws);
                };
                ws.onerror = () => {
                    if (!this.max || this.connectNumber <= this.max) {
                        if (this.beforeReconnect) {
                            this.beforeReconnect();
                        }
                        this.reconnect(url, params);
                    }
                    reject(ws);
                };
                ws.onclose = () => {
                    if (!this.max || this.connectNumber <= this.max) {
                        if (this.beforeReconnect) {
                            this.beforeReconnect();
                        }
                        this.reconnect(url, params);
                    }
                    reject(ws);
                };
                if (msgFn) {
                    ws.onmessage = msgFn;
                }
            }
            else {
                console.log('token不存在，请刷新页面'); // eslint-disable-line
            }
        });
    }

    dispose() {
        this.disposeStatus = true;
        this.ws.onclose = () => { };
        this.ws.onerror = () => { };
        this.ws.close();
    }
}

const heartCheck = {
    timeout: 5000, // 3分钟发一次心跳
    timeoutObj: null,
    serverTimeoutObj: null,
    reset() {
        clearTimeout(this.timeoutObj);
        clearTimeout(this.serverTimeoutObj);
        return this;
    },
    start(curWs) {
        let self = this;
        this.timeoutObj = setTimeout(() => {
            // 这里发送一个心跳，后端收到后，返回一个心跳消息，
            // onmessage拿到返回的心跳就说明连接正常
            curWs.send('ping');
            self.serverTimeoutObj = setTimeout(() => {
                // 如果超过一定时间还没重置，说明后端主动断开了
                curWs.close(); // 如果onclose会执行reconnect，我们执行ws.close()就行了.如果直接执行reconnect 会触发onclose导致重连两次
            }, self.timeout);
        }, this.timeout);
    },
};

export {heartCheck, Ws};
