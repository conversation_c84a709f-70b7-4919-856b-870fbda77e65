import {ref} from 'vue';
import {
    getCameraList, getInfoboardList, getInterChangeList, getBridgeList, getServiceAreaList, getTollStationList,
} from '@/api/equipment/highspeed.js';


/**
 * 摄像枪模块
 */
export const cameraName = ref([]);
export const cameraList = ref([]);
export const cameraShow = ref([]);
export const cameraIconList = ref([]);
export const cameraVideoId = ref([]);

/**
 * 显示屏模块
 */
export const intelboardName = ref([]);
export const intelboardList = ref([]);
export const intelboardShow = ref([]);
export const intelboardIconList = ref([]);

/**
 * 互通立交模块
 */
export const intelchangeName = ref([]);
export const intelchangeList = ref([]);
export const intelchangeShow = ref([]);
export const interChangeIconList = ref([]);

/**
 * 桥梁模块
 */
export const bridgeName = ref([]);
export const bridgeList = ref([]);
export const bridgeShow = ref([]);
export const bridgeIconList = ref([]);

/**
 * 服务区模块
 */
export const serviceName = ref([]);
export const serviceList = ref([]);
export const serviceShow = ref([]);
export const serviceIconList = ref([]);

/**
 * 收费站模块
 */
export const stationName = ref([]);
export const stationList = ref([]);
export const stationShow = ref([]);
export const tollStationIconList = ref([]);

export const initMaker = () => {
    getCameraList().then(res => {
        cameraIconList.value = res.data;
        cameraName.value = Array(cameraIconList.value.length).fill('');
        cameraList.value = Array(cameraIconList.value.length).fill([]);
        cameraVideoId.value = Array(cameraIconList.value.length).fill('');
        cameraShow.value = Array(cameraIconList.value.length).fill(false);
    });
    getInfoboardList().then(res => {
        intelboardIconList.value = res.data;
        intelboardName.value = Array(intelboardIconList.value.length).fill('');
        intelboardList.value = Array(intelboardIconList.value.length).fill([]);
        intelboardShow.value = Array(intelboardIconList.value.length).fill(false);
    });
    getInterChangeList().then(res => {
        interChangeIconList.value = res.data.map(item => {
            return {
                ...item,
                lng: item.centerPoint.split(',')[0],
                lat: item.centerPoint.split(',')[1],
                alt: item.centerPoint.split(',')[2] || 0,
            };
        });
        intelchangeName.value = Array(interChangeIconList.value.length).fill('');
        intelchangeList.value = Array(interChangeIconList.value.length).fill([]);
        intelchangeShow.value = Array(interChangeIconList.value.length).fill(false);
    });
    getBridgeList().then(res => {
        bridgeIconList.value = res.data;
        bridgeName.value = Array(bridgeIconList.value.length).fill('');
        bridgeList.value = Array(bridgeIconList.value.length).fill([]);
        bridgeShow.value = Array(bridgeIconList.value.length).fill(false);
    });
    getTollStationList().then(res => {
        tollStationIconList.value = res.data.map(item => {
            return {
                ...item,
                lng: item.stationPoint.split(',')[0],
                lat: item.stationPoint.split(',')[1],
                alt: item.stationPoint.split(',')[2] || 0,
            };
        });
        stationName.value = Array(tollStationIconList.value.length).fill('');
        stationList.value = Array(tollStationIconList.value.length).fill([]);
        stationShow.value = Array(tollStationIconList.value.length).fill(false);
    });
    getServiceAreaList().then(res => {
        serviceIconList.value = res.data.map(item => {
            return {
                ...item,
                lng: item.centerPoint.split(',')[0],
                lat: item.centerPoint.split(',')[1],
                alt: item.centerPoint.split(',')[2] || 0,
            };
        });
        serviceName.value = Array(serviceIconList.value.length).fill('');
        serviceList.value = Array(serviceIconList.value.length).fill([]);
        serviceShow.value = Array(serviceIconList.value.length).fill(false);
    });
};