<template>
    <div v-if="value" class="alarm-info">
        <header class="alarm-info__header">
            <div class="alarm-info__header__bg"></div>
            <div class="alarm-info__header__content">
                <p>桥梁检测告警</p>
                <div class="btn">
                    <div
                        v-if="routeName !== 'Micro'" class="to_detail"
                        @click.stop="getMicro3dFn"
                    >微观视角</div>
                    <span class="el-icon-close" @click.stop="closeCardFn"></span>
                </div>
            </div>
        </header>

        <main class="alarm-info__content">
            <div class="item">
                <div class="label">预警位置：</div>
                <div class="value">
                    <template v-if="alarmInfo.structureWainMessageVO.structureType">
                        {{ alarmInfo.structureWainMessageVO.sectionName }}
                        {{ alarmInfo.structureWainMessageVO.structureName }}
                        {{ alarmInfo.structureWainMessageVO
                            ? sectionDirectionDict
                                .find(e => alarmInfo.structureWainMessageVO.sectionDirection
                                    === e.key.toString())?.name
                            : '暂无方向信息'
                        }}
                        {{ alarmInfo.structureWainMessageVO.centerStake }}
                    </template>
                    <template v-else>
                        暂无信息
                    </template>
                </div>
            </div>
            <div class="item">
                <div class="label-item">
                    <div class="label">预警时间：</div>
                    <div class="value">
                        {{ alarmInfo?.structureWainMessageVO.warningTime || '暂无信息' }}
                    </div>
                </div>
                <div class="label-item">
                    <div class="label">预警指标：</div>
                    <div class="value">
                        {{ alarmInfo?.structureWainMessageVO.measureName || '暂无信息' }}
                    </div>
                </div>
                <div class="label-item">
                    <div class="label">预警等级：</div>
                    <div class="value">
                        {{ alarmInfo?.structureWainMessageVO.warningLevel || '暂无信息' }}
                    </div>
                </div>
            </div>
            <div class="item more">
                <div>
                    <div class="label">传感器名称：</div>
                    <div class="place">{{ alarmInfo?.wainingLocationVO?.sensorName || '暂无信息' }}</div>
                </div>
                <div>
                    <div class="label">预警值：</div>
                    <div class="place">
                        {{ alarmInfo?.wainingLocationVO?.prewarningValue || '暂无信息' }}
                        {{ alarmInfo?.wainingLocationVO?.unit || '' }}
                    </div>
                </div>
            </div>
            <div class="other">
                <div class="other-title">附近视频 {{ alarmInfo.structureWainMessageVO?.centerStake || '-' }}</div>
                <div class="other">
                    <div class="other-video" @click="handleVideoFn('up')">
                        <div class="icon">
                            <icon :size="14" name="sheshi-shexiangtou"/>
                        </div>
                        <span>
                            {{ alarmInfo.videoInfo?.upName || '-' }} 上行
                            <span v-if="direction === 'up'">（播放中）</span>
                        </span>
                    </div>
                    <div class="other-video" @click="handleVideoFn('down')">
                        <div class="icon">
                            <icon :size="14" name="sheshi-shexiangtou"/>
                        </div>
                        <span>
                            {{ alarmInfo.videoInfo?.downName || '-' }} 下行
                            <span v-if="direction === 'down'">（播放中）</span>
                        </span>
                    </div>
                </div>
            </div>
        </main>

        <footer class="alarm-info__footer">
            <flvPlayer
                v-if="alarmInfo.videoInfo[direction + 'Camera']"
                :camera-info-id="alarmInfo.videoInfo[direction + 'Camera']"
            />
            <no-data v-else/>
            <div class="left" @click.stop="changePageFn('left')"></div>
            <div class="right" @click.stop="changePageFn('right')"></div>
        </footer>
    </div>
</template>

<script>
import {ref} from 'vue';
import {
    sectionDirectionDict,
    strcutureDataList,
} from '@/config/structure.js';
import {routeName, alarmInfo, structureId} from '../../../utils/index';
import {Message} from 'element-ui';
import {Icon, flvPlayer} from '@/components/Common';
import {selectWarningMessageToday} from '@/api/structure';

export default {
    props: {
        value: {
            type: Boolean,
            default: false,
        },
    },
    components: {
        Icon, flvPlayer,
    },
    model: {
        prop: 'value',
        event: 'update:value',
    },
    setup(props, {emit}) {
        const direction = ref('up');
        const handleVideoFn = type => {
            direction.value = type;
        };

        // 点击关闭按钮
        const closeCardFn = () => {
            emit('update:value', false);
        };

        const pageNumber = ref(1);
        const init = async () => {
            const res = await selectWarningMessageToday({
                structureId: structureId.value,
                pageNumber: pageNumber.value, // 当前页
                pageSize: 1, // 当前页
            });
            alarmInfo.value = {
                ...alarmInfo.value,
                total: res.data.total,
                structureWainMessageVO: res.data.records[0].structureWainMessageVO,
                wainingLocationVO: res.data.records[0].wainingLocationVO,
            };
        };

        // 切换页码
        const changePageFn = type => {
            if (type === 'left') {
                if (pageNumber.value <= 1) {
                    return Message.warning('当前已经是第一页');
                }
                pageNumber.value -= 1;
            }
            else {
                if (pageNumber.value >= alarmInfo.value.total) {
                    return Message.warning('当前已经是最后一页');
                }
                pageNumber.value += 1;
            }
            init();
        };

        // 点击微观视角
        const getMicro3dFn = () => {
            pageNumber.value = 1;
            emit('update:value', false);
            emit('micro');
        };
        return {
            alarmInfo, routeName,
            strcutureDataList,
            sectionDirectionDict,
            direction,
            closeCardFn,
            changePageFn,
            handleVideoFn,
            getMicro3dFn,
        };
    },
};
</script>

<style lang="less" scoped>
.alarm-info {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 900px;
    min-height: 806px;
    background-color: rgba(8, 35, 79, 0.8);
    border: 1px solid rgba(8, 74, 178, 0.35);
    backdrop-filter: blur(10px);
    z-index: 9999;

    &__footer {
        position: relative;
        width: 868px !important;
        height: 532px;
        background-color: rgba(18, 74, 166, 0.24);
        backdrop-filter: blur(10px);
        border-top: 2px solid rgb(41, 86, 157);
        padding: 16px;
        margin: 2px auto 0;

        .left, .right {
            position: absolute;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
        }

        .left {
            left: 32px;
            background: url('../../../images/left.png') no-repeat center center / 100% 100%;
        }
        .right {
            right: 32px;
            background: url('../../../images/right.png') no-repeat center center / 100% 100%;
        }
    }

    &__content {
        padding: 15px 20px;

        .item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            // min-height: 25px;
            padding: 6px 16px;
            margin-bottom: 16px;
            background-color: rgba(36, 104, 242, .1);
            font-size: 16px;
            font-weight: 400;
            font-family: 'PingFang';

            .label-item {
                display: flex;
                align-items: center;
            }

            &:last-child {
                margin-bottom: 20px;
            }

            .label {
                color: #fff;
                opacity: .7;

                &.long {
                    width: 140px;
                }
            }

            .value {
                color: rgb(238, 168, 15);
            }

            &.more {
                >div {
                    display: flex;

                    .place {
                        color: #fff;
                    }
                }
            }
        }

        .other {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .other-title {
            width: 250px;
        }

        .other-title,
        span {
                font-weight: 400;
                font-size: 16px;
                color: #fff;
                font-family: 'PingFang';
                letter-spacing: 1.17px;
            }

            .other-video {
                display: flex;
                align-items: center;
                cursor: pointer;

                &:first-child {
                    margin-right: 24px;
                }

                > .icon {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 31px;
                    height: 24px;
                    background: url('@/assets/images/base/iconBg.png') no-repeat center center / 100% 100%;
                    margin-right: 8px;
                }
            }
        }
    }

    &__header {
        width: 100%;
        height: 58px;
        background-color: rgba(36, 104, 242, 0.3);
        &__bg {
            width: 100%;
            height: 10px;
            background-color: rgba(77, 135, 255, 0.3);
        }

        &__content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 48px;
            padding: 0 16px;

            p {
                font-size: 20px;
                font-weight: 700;
                font-family: 'PingFang';
                color: rgba(#fff, .9);
            }

            .btn {
                display: flex;
                align-items: center;
                .to_detail {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 96px;
                    height: 24px;
                    background-color: rgb(1, 255, 229);
                    color: rgb(0, 0, 0);
                    font-size: 16px;
                    font-weight: 400;
                    font-family: 'PingFang';
                    border: 1px dashed rgba(1, 255, 229, 0.6);
                    cursor: pointer;
                }

                span {
                    font-size: 18px;
                    color: rgb(192, 213, 245);
                    margin-left: 25px;
                    cursor: pointer;
                }
            }
        }
    }
}
</style>