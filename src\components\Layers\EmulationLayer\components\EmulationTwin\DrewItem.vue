<template>
    <div ref="domRef" class="drew-label">
        <div class="title">
            <p>{{ title }}</p>
            <p @click="close">
                <i class="el-icon-close"></i>
            </p>
        </div>
        <!-- 开放应急车道 -->
        <template v-if="type === 1">
            <div class="item">
                <div>开放起始位置：</div>
                <div>{{ info.controlStake }}</div>
            </div>
            <div class="item">
                <div>管控距离：</div>
                <div>{{ info.controlLength }}m</div>
            </div>
            <div class="item">
                <div>应急车道限速值：</div>
                <div>{{ info.limitSpeed }}km/h</div>
            </div>
            <div class="item">
                <div>持续时间：</div>
                <div>{{ info.controlDuration }}分钟</div>
            </div>
        </template>
        <!-- 行车道管控 -->
        <template v-if="type === 2">
            <div class="item">
                <div>开始起始位置：</div>
                <div>{{ info.controlStake }}</div>
            </div>
            <div class="item">
                <div>管控距离：</div>
                <div>{{ info.controlLength }}m</div>
            </div>
            <div class="item">
                <div>行驶车道管控：</div>
                <div>
                    {{ laneList }}
                </div>
            </div>
            <div
                v-for="(info, index) in laneControlType"
                :key="index"
                class="item"
            >
                <div>{{ info[0] }}：</div>
                <div>
                    {{ info[1] }}
                </div>
            </div>
        </template>
        <!-- 出入口管控 -->
        <template v-if="type === 3">
            <div class="item">
                <div>出入口：</div>
                <div>{{ info.rampName }}</div>
            </div>
            <div class="item">
                <div>收费站上行：</div>
                <div>
                    <p v-for="(i, index) in entranceExitList" :key="index">{{ i }}</p>
                </div>
            </div>
        </template>
    </div>
</template>

<script>
import {watch, ref, onBeforeUnmount, computed, onMounted} from 'vue';
import {PolygonManager, DOMManager} from '@/utils/map/manager';
import {strategy_list} from '@EmulationLayer/config';

export default {
    props: {
        engine: {
            type: Object,
            default: () => null,
        },
        emulationId: {
            type: String,
            default: () => null,
        },
        data: {
            type: Object,
            default: () => null,
        },
    },
    setup(props) {
        console.log(props, 'props=======');
        const domRef = ref(null);
        const polygonManager = ref(null);
        const domManager = ref(null);
        const polygonNames = ref([]);
        const labelName = ref(null);

        const info = computed(() => props.data.strategy || {});
        const type = computed(() => info.value?.type);
        const title = computed(() => strategy_list.find(i => i.key === type.value)?.name);

        // 行驶车道管控
        const laneList = computed(() => {
            return info.value?.lane?.split(',')?.map(lane => `${lane}车道`)?.toString();
        });

        // 管控详情
        const laneControlType = computed(() => {
            return info.value?.lane?.split(',')?.map(lane => {
                const laneControlType = info.value?.laneControlType?.split(',')?.[Number(lane) - 1];
                const limitSpeed = info.value?.limitSpeed?.split(',')?.[Number(lane) - 1];
                return [`${lane}车道`, `${+laneControlType === 1 ? `限速 ${limitSpeed}km/h` : '关闭'}`];
            });
        });

        // 收费站上行
        const entranceExitList = computed(() => {
            if (info.type !== 3 || !info.entranceExitType || info.entranceExitType === '') {
                return [];
            }

            const types = info.entranceExitType.split(',');

            return types.map(i => {
                if (Number(i) === 1) {
                    return '入口关闭';
                }

                if (Number(i) === 2) {
                    const entryConfig = info.entryConfig || {};
                    return `入口限流 持续时间${entryConfig.controlDuration || '-'}
                        分钟，入口限流量${entryConfig.flowLimit || '-'}
                        辆/小时，车辆限制
                        ${vehicle_type_list.find(i => i.code === +entryConfig.vehicleType)?.name || '-'}`;
                }

                if (Number(i) === 3) {
                    const exportConfig = info.exportConfig || {};
                    return `出口限流 持续时间${exportConfig.controlDuration || '-'}
                        分钟，绕行比例${exportConfig.bypassRate || '-'}%`;
                }
            });
        });

        // 绘制多边形
        const drewPolygon = () => {
            if (!props.data?.canvasMap) {
                return;
            }
            Object.keys(props.data.canvasMap)?.forEach(key => {
                let color = +key === 99 ? '#00ffba' : '#fec931';
                const name = `Polygon-${props.emulationId}-${key}`;
                const points = props.data.canvasMap[key]?.map(item => {
                    return item.split(',').map(i => Number(i));
                });
                if (!points || !points.length) {
                    return;
                }
                polygonNames.value.push(name);
                polygonManager.value.addPolygon(name, points, {
                    color,
                });
            });
        };

        // 绘制弹窗
        const drewLabel = () => {
            const position = props.data.centerPoint.split(',')?.map(i => Number(i));
            if (!position || position?.length < 2) {
                return;
            }
            const name = `Label-${props.emulationId}`;
            labelName.value = name;
            domManager.value.addDOM(name, position, domRef.value);
        };

        const disposeData = () => {
            polygonNames.value?.forEach(name => {
                polygonManager.value.removePolygonByName(name);
            });
            polygonNames.value = [];

            labelName.value && domManager.value.removeDOMByName(labelName.value);
            labelName.value = null;
        };

        const close = () => {
            labelName.value && domManager.value.removeDOMByName(labelName.value);
            labelName.value = null;
        };

        let isInit = true;
        const init = () => {
            if (!isInit) {
                return;
            }
            polygonManager.value = new PolygonManager(props.engine);
            domManager.value = new DOMManager(props.engine);
            drewPolygon();
            drewLabel();
            isInit = false;
        };

        watch(() => [props.engine, props.emulationId, props.data], () => {
            (props.engine && props.emulationId && props.data) && init();
        });

        onMounted(() => {
            (props.engine && props.emulationId && props.data) && init();
        });

        onBeforeUnmount(() => {
            disposeData();
        });

        return {
            info,
            type,
            title,
            laneList,
            laneControlType,
            entranceExitList,
            domRef,
            close,
        };
    },
};
</script>

<style lang="less" scoped>
.drew-label {
    width: max-content;
    min-width: 200px;
    height: max-content;
    background-color: rgba(32, 72, 63, .6);
    font-size: 12px;
    position: absolute;
    top: -9999999px;
    border-bottom: 2px solid rgb(125 238 204 / 60%);
    padding-bottom: 10px;
    user-select: none;

    .title {
        height: 40px;
        padding: 0 20px;
        background: rgba(7, 175, 135, .2);
        border-bottom: 1px solid rgb(8, 214, 165, .2);
        font-size: 14px;
        color: #e5f1ff;
        line-height: 22px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        p {
            &:last-child {
                &:hover {
                    cursor: pointer;
                }
            }
        }
    }

    .item {
        display: flex;
        padding: 0 20px;
        justify-content: space-between;
        pointer-events: none;

        & > div {
            font-size: 12px;
            text-align: justify;
            line-height: 20px;
            padding-top: 6px;
            width: max-content;
            color: #efffff;

            &:last-child {
                text-align: right;
            }
        }
    }
}
</style>