<template>
    <Card title="值班信息" card-type="card-short-2">
        <template #content>
            <api-table
                :columns="columns"
                :api="getDuty"
                :request-params="{
                    pageAble: true,
                }"
            />
        </template>
    </Card>
</template>

<script setup>
import {ApiTable, Card} from '@/components/Common';
import {useUnit} from '@/utils';
import {getDuty} from '@/api/tollStation';

const {ratio} = useUnit();

const columns = [
    {
        label: '姓名',
        prop: 'dutyPersonName',
        width: `${112 * ratio.value}px`,
        align: 'center',
    },
    {
        label: '部门',
        prop: 'affiliatedDepartment',
        width: `${112 * ratio.value}px`,
        align: 'center',
    },
    {
        label: '单位',
        prop: 'affiliatedUnit',
        width: `${166 * ratio.value}px`,
        align: 'center',
    },
    {
        label: '联系方式',
        prop: 'contactPhone',
        width: `${166 * ratio.value}px`,
        align: 'center',
    },
];

</script>