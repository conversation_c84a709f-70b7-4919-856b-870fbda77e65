<template>
    <div ref="echartsRef" class="chart"></div>
</template>

<script setup>
import 'echarts/lib/component/dataZoom';
import {dataKey, areaColor, barOptions} from './config';
import {useUnit} from '@/utils';
import {nextTick, ref, watch} from 'vue';
import {useEcharts} from '@/hooks/useEcharts';
import {sortBy} from 'lodash-es';

const props = defineProps({
    title: {
        type: String,
        default: '',
    },
    echartsData: {
        type: Array,
        default: () => {},
    },
    name: {
        type: String,
    },
    dataProps: {
        type: String,
    },
    type: {
        type: Number,
    },
    color: {
        type: String,
    },
    isHasPolicy: {
        type: Boolean,
    },

});

const echartsRef = ref(null);
const options = ref();

const {setOptions} = useEcharts(echartsRef);
const {ratio} = useUnit();

watch(
    () => props.echartsData,
    () => {
        nextTick(() => {
            updateCharts();
        });
    },
    {
        immediate: true,
        deep: true,
    }
);

function updateCharts() {
    const {echartsData} = props;
    // cv
    let xData = [];
    let yData1 = [];
    let yData2 = [];
    let yData3 = [];
    let data1 = [];
    let data2 = [];
    if (!echartsData || !echartsData.data || echartsData.data?.length === 0 || !props.dataProps) {
        return;
    }
    // console.log(val[0][props.dataProps]);
    let data = echartsData.data?.[0][props.dataProps];
    // 路段拥堵排名、平均排队长度路段排序展示
    if (props.type === 8 || props.type === 9) {
        data = sortBy(data, function (item) {
            // k252+200 ---> 252200
            return +item.sectionName.replace(/(K|k|\+)/g, '');
        });
    }
    xData = data?.map(item => item.time);
    yData1 = data?.map(item => item[dataKey[props.type]]);
    if (echartsData.flag && echartsData.data?.length > 1) {
        data1 = echartsData.data?.[1]?.[props.dataProps];
        data2 = echartsData.data?.[2]?.[props.dataProps];
        yData2 = data1?.map(item => item[dataKey[props.type]]);
        yData3 = data2?.map(item => item[dataKey[props.type]]);
    }
    else {
        yData2 = [];
    }

    let legendData = yData2?.length > 0 ? [
        {
            name: '无策略',
        },
        {
            name: '策略1',
        },
    ] : [
        {
            name: '无策略',
        },
    ];
    // if (props.type === 3 || props.type === 4) {
    legendData = yData3?.length > 0 ? [
        ...legendData,
        {
            name: '策略2',
        },
    ] : legendData;
    // data2 = val.data.find(item => item.dataType === 100);
    if (data2?.length) {
        yData3 = data2?.map(item => item[dataKey[props.type]]);
    }
    // }
    if (props.type === 8 || props.type === 9) {
        options.value = barOptions({
            xData: data.map(item => item.sectionName),
            yData: data.map(item => item[dataKey[props.type]].toFixed(2)),
            yData1: data1?.map(item => item[dataKey[props.type]].toFixed(2)),
            yData2: data2?.map(item => item[dataKey[props.type]].toFixed(2)),
        });
    }
    else {
        options.value = {
            tooltip: {
                trigger: 'axis',
                textStyle: {
                    fontSize: 12 * ratio.value,
                },
                padding: 4 * ratio.value,
                formatter(params) {
                    const {data, axisValue, seriesName} = params[0];
                    return `
                        <div style="font-family: 'OPlusSans'; color: #fff;">
                            <div style="font-size: ${12 * ratio.value}px; font-weight: 700;">
                                ${axisValue}
                            </div>
                            <div style="font-size: ${10 * ratio.value}px;">
                                <span style="color: rgba(255,255,255, .6);">${seriesName}</span>
                                <span style="font-weight: 700; margin-left: ${4 * ratio.value}px">
                                    ${data}
                                </span>
                            </div>
                        </div>
                    `;
                },
                position(point) {
                    return point.map(item => item + 16 * ratio.value);
                },
            },
            legend: {
                show: true,
                // selectedMode: false,
                icon: 'diamond',
                right: '5%',
                top: 10 * ratio.value,
                itemWidth: 8 * ratio.value,
                itemHeight: 8 * ratio.value,
                orient: 'horizontal',
                itemGap: 20 * ratio.value,
                textStyle: {
                    color: '#F9FBFF',
                    fontSize: 12 * ratio.value,
                },
                data: legendData,
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: 50 * ratio.value,
                containLabel: true,
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                axisLine: {
                    show: true,
                    lineStyle: {
                        type: 'solid',
                        color: 'rgba(78,141,237,0.7)',
                        width: '1',
                    },
                },
                splitLine: {
                    show: false,
                },
                axisLabel: {
                    showMaxLabel: true, // x轴最后的显示
                    textStyle: {
                        color: '#98B8DE',
                        fontSize: 12 * ratio.value,
                        fontFamily: 'DINAlternate-Bold',
                    },
                },
                axisTick: {
                    show: true,
                    inside: true,
                    length: 3 * ratio.value,
                    lineStyle: {
                        width: 1,
                        type: 'solid',
                    },

                },
                data: xData,
            },
            yAxis: {
                type: 'value',
                axisLine: {
                    show: false,
                },
                nameTextStyle: {
                    color: '#F9FBFF',
                    fontSize: 12 * ratio.value,
                    fontFamily: 'FZLTZHJW--GB1-0',
                },
                axisTick: {
                    show: false,
                },
                splitNumber: 4 * ratio.value,
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: 'rgba(78,141,237,0.2)',
                        type: 'dash',
                        width: 1 * ratio.value,
                    },
                },
                axisLabel: {
                    textStyle: {
                        color: '#98B8DE',
                        fontSize: 12 * ratio.value,
                        fontFamily: 'DINAlternate-Bold',
                    },
                },
            },
            series: [
                {
                    name: '无策略',
                    type: 'line',
                    smooth: true,
                    symbol: 'none',
                    data: yData1,
                    color: props.color,
                    lineStyle: {
                        width: 2 * ratio.value,
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: areaColor[props.type],
                            global: false, // 缺省为 false
                        },
                        opacity: 0.2,
                    },
                },
                {
                    name: '策略1',
                    type: 'line',
                    smooth: true,
                    symbol: 'none',
                    lineStyle: {
                        width: 2 * ratio.value,
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [
                                {
                                    offset: 0,
                                    color: 'rgba(26,228,255,0.41)', // 0% 处的颜色
                                },
                                {
                                    offset: 1,
                                    color: 'rgba(26,228,255,100) ', // 100% 处的颜色
                                },
                            ],
                            global: false, // 缺省为 false
                        },
                        opacity: 0.2,
                    },
                    data: yData2,
                },
                {
                    name: '策略2',
                    type: 'line',
                    smooth: true,
                    symbol: 'none',
                    lineStyle: {
                        width: 2 * ratio.value,
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [
                                {
                                    offset: 0,
                                    color: 'rgba(138,170,225,0.41)', // 0% 处的颜色
                                },
                                {
                                    offset: 1,
                                    color: 'rgba(138,170,225,100) ', // 100% 处的颜色
                                },
                            ],
                            global: false, // 缺省为 false
                        },
                        opacity: 0.2,
                    },
                    data: yData3,
                    color: '#8aaae1',
                },
            ],
        };
    }

    setOptions(options.value);
}

</script>

<style lang="less" scoped>
.chart {
    width: 100%;
    height: 100%;
}

</style>