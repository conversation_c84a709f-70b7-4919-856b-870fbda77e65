<template>
    <div class="stake-axis">
        <el-slider
            v-model="sliderStation"
            :step="1"
            :show-tooltip="true"
            :debounce="300"
            :format-tooltip="formatTooltip"
            :marks="marks"
            :min="0"
            :max="max"
            @change="changeSliderStation"
        />
    </div>
</template>

<script>
import {ref, watch, onMounted} from 'vue';
import {Slider} from 'element-ui';
import {viewTo} from '@/utils';
import {emulationInfo, stakenumberlist, init_number_list} from '@EmulationLayer/store/index';

export default {
    props: {
        eventStartStake: {
            type: String,
            default: () => null,
        },
        engine: {
            type: Object,
            default: () => null,
        },
    },
    components: {
        [Slider.name]: Slider,
    },
    setup(props) {
        const sliderStation = ref(0); // 当前选中的桩号位置
        const marks = ref({0: 'K6+0'}); // 标记
        const max = ref(0);

        const formatTooltip = val => {
            return stakenumberlist.value[val] ? stakenumberlist.value[val].stakeNumber : '';
        };

        const changeSliderStation = val => {
            const info = stakenumberlist.value?.[val];
            let position = [info.longitude, info.latitude];
            viewTo({
                zoom: 15,
                center: position,
                key: 'engine_tiwin',
            }, 1000, props.engine);
        };

        const getStakenumberlist = async () => {
            const highSpeedName = emulationInfo.value?.highSpeedName;
            await init_number_list(highSpeedName);
        };

        // 处理桩号列表并定位到当前开始桩号的位置
        const getstakenumberlistData = () => {
            let data = stakenumberlist.value;
            marks.value[0] = data[0]?.stakeNumber;
            marks.value[data.length - 1] = data[data.length - 1]?.stakeNumber;
            max.value = data.length - 1;
            data.forEach((item, index) => {
                if (item.stakeNumber === props.eventStartStake) {
                    sliderStation.value = index;
                    marks.value[index] = item.stakeNumber;
                }
            });
        };

        watch(() => stakenumberlist.value, () => {
            stakenumberlist.value && getstakenumberlistData();
        });

        onMounted(() => {
            getStakenumberlist();
        });

        return {
            sliderStation,
            max,
            marks,
            stakenumberlist,
            formatTooltip,
            changeSliderStation,
        };

    },
};
</script>

<style lang="less" scoped>
.stake-axis {
    width: calc(100% - 64px);
    height: 20px;
    position: absolute;
    bottom: 70px;
    z-index: 2;
    right: 32px;

    /deep/ .el-slider__runway {
        background-color: rgba(8, 214, 165, .6);
        border-radius: 0;
    }

    /deep/ .el-slider__bar {
        background-color: rgba(8, 214, 165, .6);
    }

    /deep/ .el-slider__button {
        width: 10px;
        height: 10px;
        border: solid 1px #fff;
        background-color: #07af87;
        border-radius: 0;
    }

    /deep/ .el-slider__stop {
        background-color: transparent;
    }

    /deep/ .el-slider__marks-text {
        font-size: 12px;
        color: rgba(239, 255, 255, .9);
        margin-top: 8px;
        transform: scale(.7);
        margin-left: -10px;
    }
}
</style>