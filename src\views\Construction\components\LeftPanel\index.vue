<template>
    <div
        :class="['left-panel', {collapse}]"
    >
        <collapse-panel
            title="施工项目列表"
            :show-header="showHeader"
            :collapse.sync="collapse"
        >
            <div class="card-wrapper">
                <slot></slot>
            </div>
        </collapse-panel>
    </div>
</template>

<script>
import {ref} from 'vue';
import {CollapsePanel} from '@/components/Common';

export default {
    components: {
        CollapsePanel,
    },
    props: {
        showHeader: Boolean,
    },
    setup(props, {emit}) {
        const collapse = ref(false);

        return {
            collapse,
        };
    },
};
</script>

<style lang="less" scoped>
.left-panel {
    flex-shrink: 0;
    margin-right: 16px;

    &.collapse {
        margin-right: 0;
    }
}
</style>