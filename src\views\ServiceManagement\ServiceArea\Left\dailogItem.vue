<template>
    <div>
        <item
            v-for="item in infoList" :key="item.label"
            :info="item"
            class="item"
        />
    </div>
</template>
<script>
import {onMounted, ref, computed} from 'vue';
import {Item} from '@/components/Common/index';
export default {
    components: {
        Item,
    },
    props: {
        info: {
            type: Object,
            default: () => {},
        },
    },
    setup(props) {
        const infoList = computed(() => [

            {
                label: '告警时间:',
                value: 1,
            },
            {
                label: '告警原因:',
                value: 1,
            },
            {
                label: '车牌号:',
                value: 1,
            },
            {
                label: '驶入时间:',
                value: 1,
            },
            {
                label: '是否停入专:',
                value: 1,
            },
            {
                label: '车型:',
                value: 'asdsadsadsa啊实打实的撒旦撒旦撒阿萨德啊实打',
            },
            {
                label: '车辆信息:',
                value: 1,
            },
            {
                label: '类型信息:',
                value: '非剧毒危险品运输车运输类型：易燃液体（经营性3类',
            },
            {
                label: '联系人:',
                value: 1,
            },
            {
                label: '手机号:',
                value: 1,
            },
            {
                label: '电话号:',
                value: 1,
            },
            {
                label: '所属公司:',
                value: 1,
            },
            {
                label: '发证机关:',
                value: 1,
            },
        ]);
        return {
            infoList,
        };
    },
};
</script>
<style lang='less' scoped>
.item{
 flex: 1;
}
.border_line {
    height: 1px;
    margin-bottom: 17px;
    background: linear-gradient(to right, rgba(100, 168, 255, 0), rgb(255, 255, 255), rgba(100, 168, 255, 0))
}
</style>