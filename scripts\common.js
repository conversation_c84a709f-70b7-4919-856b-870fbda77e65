import path from 'path';

const aliasMap = {
    '@EmulationLayer': './src/components/Layers/EmulationLayer',
    '@StructureLayer': './src/components/Layers/StructureLayer',
    '@MaintainPlanLayer': './src/components/Layers/MaintainPlanLayer',
    '@MaintainProjectLayer': './src/components/Layers/MaintainProjectLayer',
    '@ThreeUrgentLayer': './src/components/Layers/ThreeUrgentLayer',
    '@AccidentAreaLayer': './src/components/Layers/AccidentAreaLayer',
    '@LinkageRegionLayer': './src/components/Layers/LinkageRegionLayer',
    '@ManualInspectionLayer': './src/components/Layers/ManualInspectionLayer',
    '@FacilityLayer': './src/components/Layers/FacilityLayer',
};

// 分包路径入口配置
const inputConfig = {};

Object.keys(aliasMap).forEach(key => {
    const name = key.replace('@', '');
    const _path = path.resolve('./', `src/components/Layers/${name}/index.js`);
    inputConfig[name] = _path;
});

const splitChunkNames = Object.keys(inputConfig);

const LayersName = Object.keys(aliasMap).map(key => key.replace('@', ''));

const externalPackages = [
    'vue',
    'element-ui',
    '@baidu/mapv-three',
    '@baidu/cloudrenderengine',
    'socket.io-client',
    'bmap-three',
    'three',
];

export {
    aliasMap,
    inputConfig,
    splitChunkNames,
    LayersName,
    externalPackages,
};