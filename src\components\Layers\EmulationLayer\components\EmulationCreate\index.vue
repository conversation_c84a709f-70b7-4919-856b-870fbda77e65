<template>
    <div class="box-b">
        <div class="emulation-create">
            <div class="header">{{ editId ? '更新' : '创建' }}仿真方案</div>
            <div class="content">
                <!-- tab区域 开始-->
                <div class="tabs">
                    <div
                        v-for="item in moduleList" :key="item.key"
                        :class="{
                            'tab-pane-active': curIndex === item.key,
                        }"
                        @click="onTab(item)"
                    >{{ item.name }}</div>
                </div>
                <!-- tab区域 结束-->
                <!-- 主体区域开始 -->
                <div class="detail-content">
                    <!-- <base-component v-if="curIndex === 1"/> -->
                    <!-- 基本信息模块 -->
                    <div v-show="curIndex === 1">
                        <div class="small-title">
                            {{ isConstruction ? '施工基本信息' : '仿真基本信息' }}
                        </div>
                        <div class="select-area">
                            <span>区域选择：</span>
                            <div class="item">
                                <div
                                    :class="{
                                        'select-item': true,
                                        'active-item': flowInfo.areaChoose === 1,
                                    }"
                                    @click="onAreaChoose(1)"
                                >
                                    <span></span>
                                    <span>路段选择</span>
                                </div>
                                <div
                                    :class="{
                                        'select-item': true,
                                        'active-item': flowInfo.areaChoose === 2,
                                    }"
                                    @click="onAreaChoose(2)"
                                >
                                    <span></span>
                                    <span>自定义框选</span>
                                </div>
                            </div>
                        </div>
                        <div v-if="flowInfo.areaChoose === 1" class="select-area">
                            <el-select
                                v-model="flowInfo.highSpeedName"
                                placeholder="高速名称"
                                class="w-50% h-40 mr-8"
                                popper-class="emulation-el-popper"
                                filterable
                                @change="onSelectChange"
                            >
                                <el-option
                                    v-for="item in highSpeedOptions"
                                    :key="item.highSpeedName"
                                    :label="item.highSpeedNameCn"
                                    :value="item.highSpeedName"
                                />
                            </el-select>
                            <stake-select
                                v-model="flowInfo.emulationStartStake"
                                :list="sumoStakeOptions"
                                @change="startChange"
                            />
                        </div>
                        <div class="form-item">
                            <span>仿真开始时间：</span>
                            <el-date-picker
                                v-model="flowInfo.emulationStartTime"
                                type="datetime"
                                class="f-1 h-40 ml-8"
                                popper-class="emulation-el-popper"
                                placeholder="请选择仿真开始时间"
                                value-format="yyyy-MM-dd HH:mm:ss"
                            />
                        </div>
                        <div class="form-item">
                            <span>仿真结束时间：</span>
                            <el-date-picker
                                v-model="flowInfo.emulationEndTime"
                                class="f-1 h-40 ml-8"
                                type="datetime"
                                popper-class="emulation-el-popper"
                                placeholder="请选择仿真结束时间"
                                value-format="yyyy-MM-dd HH:mm:ss"
                            />
                        </div>
                        <div class="form-item">
                            <span>出车点距离：</span>
                            <el-input
                                v-model="flowInfo.flowDistance"
                                class="f-1 h-40 ml-8"
                                placeholder="距离500-5000之间"
                                @blur="verifyDistance"
                            >
                                <span slot="suffix" class="suffix-text">米</span>
                            </el-input>
                        </div>
                        <div class="small-title mt-28 mb-16">
                            {{ isConstruction ? '施工区域方案信息' : '交通事件方案信息' }}
                        </div>
                        <div class="edit-item">
                            <span>{{ isConstruction ? '施工类型' : '事件类型：' }}</span>
                            <el-select
                                v-model="flowInfo.eventType"
                                placeholder="请选择类型"
                                class="f-1 h-40"
                                popper-class="emulation-el-popper"
                                clearable
                            >
                                <el-option
                                    v-for="item in event_type_list"
                                    :key="item.code"
                                    :label="item.name"
                                    :value="item.code"
                                />
                            </el-select>
                        </div>
                        <template v-if="flowInfo.eventType !== -1">
                            <div class="edit-item">
                                <span>{{ isConstruction ? '施工位置类型' : '事件位置类型：' }}</span>
                                <el-select
                                    v-model="flowInfo.eventLocationType"
                                    placeholder="请选择位置类型"
                                    class="f-1 h-40"
                                    clearable
                                    popper-class="emulation-el-popper"
                                >
                                    <el-option
                                        v-for="item in evnetLocationTypeList"
                                        :key="item.code"
                                        :label="item.name"
                                        :value="item.code"
                                    />
                                </el-select>
                            </div>
                            <div class="edit-item">
                                <span>{{ isConstruction ? '施工开始时间' : '事件开始时间' }}</span>
                                <el-date-picker
                                    v-model="flowInfo.eventStartTime"
                                    type="datetime"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    popper-class="emulation-el-popper"
                                    class="f-1 h-40"
                                    :placeholder="isConstruction ? '请选择施工开始时间' : '请选择事件开始时间'"
                                />
                            </div>
                            <div class="edit-item">
                                <span>预计持续时长：</span>
                                <el-input
                                    v-model="flowInfo.duration"
                                    class="f-1 h-40"
                                    placeholder="请输入"
                                >
                                    <span slot="suffix" class="suffix-text">分钟</span>
                                </el-input>
                            </div>
                            <div class="edit-item">
                                <span>高速名称</span>
                                <el-select
                                    v-model="flowInfo.highSpeedName1"
                                    placeholder="请选择高速名称"
                                    class="f-1 h-40"
                                    filterable
                                    popper-class="emulation-el-popper"
                                    :disabled="disabled"
                                    @change="eventRoadNameChange"
                                >
                                    <el-option
                                        v-for="item in highSpeedOptions"
                                        :key="item.highSpeedName"
                                        :label="item.highSpeedNameCn"
                                        :value="item.highSpeedName"
                                    />
                                </el-select>
                            </div>
                            <div class="edit-item">
                                <span>{{ isConstruction ? '施工位置' : '事件位置' }}</span>
                                <stake-select
                                    v-model="flowInfo.eventStartStake"
                                    :disabled="disabled"
                                    :list="stakeListOptions"
                                    :is-full="true"
                                />
                            </div>
                            <div class="edit-item">
                                <span style="visibility: hidden;">事件位置</span>
                                <el-input
                                    ref="eventInput"
                                    v-model="flowInfo.eventPosition"
                                    class="f-1 h-40"
                                    placeholder="请在地图上选择"
                                    :disabled="disabled"
                                    @focus="() => handlePositionFocus('eventPosition')"
                                />
                                <img
                                    v-if="!positionVisible"
                                    src="@EmulationLayer/assets/images/position.svg"
                                    alt=""
                                    @click="onPoint"
                                >
                                <span
                                    v-else class="point-btn"
                                    @click="pointed"
                                >确定</span>
                            </div>
                            <div class="edit-item">
                                <span> {{ isConstruction ? '影响方向' : '事件方向' }}：</span>
                                <el-select
                                    v-model="flowInfo.direction"
                                    placeholder="请选择方向"
                                    class="f-1 h-40"
                                    disabled
                                    popper-class="emulation-el-popper"
                                    clearable
                                >
                                    <el-option
                                        v-for="item in road_direction"
                                        :key="item.code"
                                        :label="item.name"
                                        :value="item.code"
                                    />
                                </el-select>
                            </div>
                            <div class="edit-item">
                                <span>封闭行车道：</span>
                                <div class="edit-con">
                                    <div
                                        v-for="item in laneOptions"
                                        :key="`${item}${uuid}`"
                                        :class="{disabled: disabled}"
                                        @click="onItem(item)"
                                    >
                                        <span
                                            :class="{
                                                'selected': !!(closedLane.find(i => i === item)),
                                            }"
                                        ></span>
                                        <span>{{ `${item === '99' ? '应急' : item}车道` }}</span>
                                    </div>
                                </div>
                            </div>
                            <temmplate v-if="isConstruction">
                                <div class="edit-item">
                                    <span>施工限速：</span>
                                    <el-input
                                        v-model.number="flowInfo.limitSpeed" v-filters
                                        class="f-1 h-40"
                                        placeholder="请输入"
                                    >
                                        <span slot="suffix" class="suffix-text">km/h</span>
                                    </el-input>
                                </div>
                                <div class="edit-item">
                                    <span>上游过渡区长度：</span>
                                    <el-input
                                        v-model="flowInfo.upstreamTransitionLength"
                                        v-filters
                                        style="flex: 1; height: 40px;"
                                        placeholder="请输入"
                                    >
                                        <span slot="suffix" class="suffix-text">米</span>
                                    </el-input>
                                </div>
                                <div class="edit-item">
                                    <span>作业区长度：</span>
                                    <el-input
                                        v-model="flowInfo.constructionLength"
                                        v-filters
                                        class="f-1 h-40"
                                        placeholder="请输入"
                                    >
                                        <span slot="suffix" class="suffix-text">米</span>
                                    </el-input>
                                </div>
                                <div class="edit-item">
                                    <span>下游过渡区长度：</span>
                                    <el-input
                                        v-model="flowInfo.downstreamTransitionLength"
                                        v-filters
                                        class="f-1 h-40"
                                        placeholder="请输入"
                                    >
                                        <span slot="suffix" class="suffix-text">米</span>
                                    </el-input>
                                </div>
                            </temmplate>


                        </template>
                    </div>
                    <!-- 流量信息模块 -->
                    <flow-component
                        v-if="curIndex === 2"
                        :flow-info="flowData"
                        :date-range="dateRange"
                        :input-mode="inputMode"
                        @onChangeMode="(mode) => inputMode = mode"
                        @change="(info) => flowData = info"
                        @onDateChange="onDateChange"
                    />
                    <!-- 策略输入模块 -->
                    <strategy-component
                        v-if="curIndex === 3"
                        :strategy-mode-type="strategyMode"
                        :strategy-list="strategyList"
                        :stakenumberlist="stakeListOptions"
                        :toll-list="tollList"
                        :flow-info="flowInfo"
                        :lane-config="laneOptions"
                        @stakeChange="stakeChange"
                        @onStrategyModeChange="val => strategyMode = val"
                    />
                    <!-- 模型配置模块 -->
                    <model-component
                        v-show="curIndex === 4"
                        :data="modelInfo"
                    />
                </div>
                <!-- 主体区域结束 -->
            </div>
            <!-- 底部区域开始 -->
            <div class="footer">
                <div v-if="footerConfig.saveName" class="edit-item">
                    <span>方案名称：</span>
                    <el-input
                        v-model="flowInfo.name" class="f-1 h-40"
                        placeholder="请输入"
                    />
                </div>
                <div class="btn">
                    <span v-if="footerConfig.runName" @click="onRun">仿真运行</span>
                    <span v-if="footerConfig.saveName" @click="saveCase">保存方案</span>
                    <span v-if="footerConfig.cancelName" @click="cancelCase">取消方案</span>
                </div>
            </div>
            <!-- 底部区域结束 -->
        </div>
        <!-- 施工区域渲染 -->
        <template
            v-if="isConstruction"
        >
            <construction-area
                v-for="item in getConstructionList"
                :key="item"
                :line-manager="lineManager"
                :polygon-manager="polygonManager"
                :list="item"
            />
        </template>
    </div>
</template>

<script>
import {computed, ref, watch, onMounted, onUnmounted, unref} from 'vue';
import {Select, Option, DatePicker, Input} from 'element-ui';
import {moduleList, evnet_location_type, default_strategy_config} from './config';
import {messageTip, viewTo} from '@/utils';
import {fetchFlow, getEmulationRange, fetchGate,
        updateScheme, createScheme, getStakeInfo,
        getConstructionGeo, getPointsElevation} from '@EmulationLayer/api';
import {cloneDeep, isEqual} from 'lodash';
import {road_direction, lane_config, burst_event_type_list, build_type_list} from '@EmulationLayer/config';
import {v4 as uuidv4} from 'uuid';
import {iconManager, eventManager, lineManager, polygonManager} from '@EmulationLayer/utils/Map';
import {findEventType} from '@EmulationLayer/utils/index';
import {engine} from '@/store/engine';
import {strategyId, stakenumberlist, highSpeedInfo,
        sumoStakeList, roadInfo, init_number_list, init_sumo_list,
        isCustomArea, addArea, updateAreaInfo} from '@EmulationLayer/store/index';
import {detailInfo, createConfig, clickposition} from '@EmulationLayer/store/emulationCreate';

import Flow from './Flow.vue';
import Model from './Model.vue';
import Strategy from './Strategy.vue';
import dayjs from 'dayjs';
// import store from '@/store/index';
import StakeSelect from './StakeSelect.vue';
import ConstructionArea from '../EmulationTwin/ConstructionArea.vue';
import {isNil} from 'lodash-es';

const uuid = uuidv4();

const position_icon_name = 'position-icon';

export default {
    name: 'EmulationList',
    components: {
        FlowComponent: Flow,
        ModelComponent: Model,
        StrategyComponent: Strategy,
        StakeSelect,
        [Select.name]: Select,
        [Option.name]: Option,
        [DatePicker.name]: DatePicker,
        [Input.name]: Input,
        ConstructionArea,
    },
    props: {
        // 仿真参数信息
        info: {
            type: Object,
            default: () => null,
        },
        // 平台来源 默认仿真平台
        platformSource: {
            type: [String, Number],
            default: 'emulation',
        },
        // 仿真类型 1-实时事件 3-施工区域
        emulationType: {
            type: Number,
            default: 1,
        },
        // 底部按钮配置
        footerConfig: {
            type: Object,
            default: () => {
                return {
                    runName: '仿真运行',
                    saveName: '保存方案',
                    cancelName: '取消方案',
                };
            },
        },
        // 事件类型列表 默认使用实时事件类型类表 可传施工区域等类型的列表数据
        eventTypeList: {
            type: Array,
            default: () => null,
        },
        // 事件位置类型列表
        evnetLocationTypeList: {
            type: Array,
            default: () => evnet_location_type,
        },
        // 高速名称下拉数据
        highSpeedList: {
            type: Array,
            default: () => highSpeedInfo.value,
        },
        // 默认高速名称
        highSpeedName: {
            type: String,
            default: 'G1523',
        },
        // 桩号下拉列表数据
        stakeList: {
            type: Array,
            default: () => stakenumberlist,
        },
        // 顶部基本信息桩号列表
        sumoStakeList: {
            type: Array,
            default: () => sumoStakeList,
        },
        // 创建仿真方案基础配置信息
        createConfig: {
            type: Object,
            default: () => createConfig.value,
        },
        // 车道数据配置
        laneList: {
            type: Array,
            default: () => lane_config,
        },
    },
    setup(props, {emit}) {

        const eventMakerNameMap = new Map();

        const curIndex = ref(1);
        const componentId = ref('Base');

        const emulationStartStakeList = ref([]);
        const emulationEndStakeList = ref([]);

        const closedLane = ref([]);
        const lanePosition = ref([]);
        const tollList = ref([]);


        const curLane = ref(0);
        const disabled = ref(false);
        const canDrewIcon = ref(true);
        const positionVisible = ref(false);
        const eventInput = ref(null);

        // 流量信息
        const flowData = ref([]);
        const inputMode = ref(1);
        const dateRange = ref([
            dayjs().subtract(2, 'hours').format('YYYY-MM-DD HH:mm:ss'),
            dayjs().subtract(1, 'hours').format('YYYY-MM-DD HH:mm:ss'),
        ]);
        const flowInfo = ref({
            name: '',
            areaChoose: 1,
            weatherScene: 1,
            highSpeedName: props.highSpeedName,
            highSpeedName1: props.highSpeedName,
            emulationStartTime: dayjs().subtract(2, 'hours').format('YYYY-MM-DD HH:mm:ss'),
            emulationEndTime: dayjs().subtract(1, 'hours').format('YYYY-MM-DD HH:mm:ss'),
            eventType: props.emulationType === 1 ? 0 : 21,
            eventLocationType: 1,
            eventStartTime: dayjs().subtract(2, 'hours').format('YYYY-MM-DD HH:mm:ss'),
            eventStartStake: 'K1170+0',
            eventPosition: '116.450996,23.482901',
            direction: 1,
            duration: 60,
            flowDistance: 1000,
            emulationStartStake: '',
            limitSpeed: 60,
            upstreamTransitionLength: 100,
            constructionLength: 100,
            downstreamTransitionLength: 100,
            closeLane: '',
        });

        let updatedFlow = false;

        // 策略输入
        const strategyMode = ref(3);
        const strategyList = ref([
            cloneDeep(default_strategy_config),
        ]);

        // 模型配置
        const modelInfo = ref({
            smallVehicleAccel: 2.6,
            smallVehicleDecel: 4.5,
            bigCarRate: 10,
            smallVehicleEmergencyDecel: 9,
            smallVehicleMaxSpeed: 120,
            bigVehicleAccel: 1.3,
            bigVehicleDecel: 4,
            bigVehicleEmergencyDecel: 7,
            bigVehicleMaxSpeed: 100,
        });

        // 施工区域坐标信息
        const constructionGeoInfo = ref();
        // 施工区域坐标点集合
        const getConstructionList = computed(() => {
            if (!unref(constructionGeoInfo)) return [];
            const constructionGeoList = unref(constructionGeoInfo).constructionGeoList || [];
            return constructionGeoList.map(item => {
                const formatList = item.map(item => item.split(','));
                const [first] = formatList;
                first && formatList.push(first);
                return formatList;
            });
        });

        const editId = computed(() => {
            return props.createConfig.id;
        });

        const sumoStakeOptions = computed(() => unref(props.sumoStakeList));

        const platform_source = computed(() => props.platformSource);

        const flowParams = computed(() => {
            const {
                emulationStartTime,
                emulationEndTime,
                emulationStartStake,
                emulationEndStake,
                eventPosition,
                eventStartStake,
                flowDistance,
                highSpeedName,
                eventType,
            } = flowInfo.value;

            // eslint-disable-next-line no-use-before-define
            const cur = sumoStakeOptions.value?.find(item => item.stakeNumber === emulationStartStake);
            const params = {
                // eslint-disable-next-line no-use-before-define
                id: editId.value || undefined,
                emulationStartStake,
                emulationEndStake,
                eventPosition: eventType === -1 ? `${cur.longitude},${cur.latitude}` : eventPosition,
                eventStartStake: eventType === -1 ? emulationStartStake : eventStartStake,
                emulationType: 1,
                flowDistance,
                highSpeedName,
            };

            Object.assign(params, inputMode.value === 1 ? {
                emulationStartTime,
                emulationEndTime,
                flowInputType: 1,
            } : {
                customFlowStartTime: dateRange.value.length ? dateRange.value[0] : '',
                customFlowEndTime: dateRange.value.length ? dateRange.value[1] : '',
                flowInputType: 2,
            });

            return params;
        });

        const isConstruction = computed(() => props.emulationType === 3);

        const detail = computed(() => props.info || detailInfo.value);

        const highSpeedOptions = computed(() => props.highSpeedList || []);

        const stakeListOptions = computed(() => unref(props.stakeList || []));

        const laneOptions = computed(() => props.laneList || []);

        const event_type_list = computed(() => (
            props.eventTypeList || isConstruction.value
                ? build_type_list : burst_event_type_list));

        // tab切换
        const onTab = item => {
            curIndex.value = item.key;
            componentId.value = item.component;
        };

        const isNoStrategyMode = () => strategyMode.value === 3;

        const getParams =  () => {
            flowInfo.value.closeLanePosition = lanePosition.value.join(';');

            const strategyInputType = strategyMode.value;
            flowInfo.value.flowInputType = inputMode.value;
            if (dateRange.value && dateRange.value.length > 0) {
                flowInfo.value.customFlowStartTime = dateRange.value[0];
                flowInfo.value.customFlowEndTime = dateRange.value[1];
            };
            const cur = sumoStakeOptions.value
                ?.find(e => e.stakeNumber === flowInfo.value.emulationStartStake);

            const info = flowInfo.value;
            const params = {
                ...flowInfo.value,
                eventList: [
                    {
                        eventType: info.eventType,
                        eventLocationType: info.eventLocationType,
                        duration: info.duration,
                        eventStartStake: info.eventStartStake,
                        eventPosition: info.eventPosition || `${cur.longitude},${cur.latitude}`,
                        direction: info.direction,
                        closeLane: info.closeLane,
                        closeLanePosition: info.closeLanePosition,
                        influencesLength: info.influencesLength,
                        limitSpeed: info.limitSpeed,
                        eventStartTime: info.eventStartTime,
                        highSpeedName: info.highSpeedName1,
                    },
                ],
                emulationType: props.emulationType,
                strategyInputType,
                flowList: flowData.value,
                model: modelInfo.value,
                strategyList: strategyList.value,
            };
            if (editId.value) {
                params.id = editId.value;
            }

            // 施工仿真
            if (isConstruction.value) {
                params.constructionCornerGeoList = constructionGeoInfo.value?.constructionCornerGeoList;
                params.constructionGeoList = constructionGeoInfo.value?.constructionGeoList;
                params.eventList[0] = {
                    ...params.eventList[0],
                    constructionCornerGeoList: constructionGeoInfo.value?.constructionCornerGeoList,
                    constructionGeoList: constructionGeoInfo.value?.constructionGeoList,
                    constructionLength: info.constructionLength,
                    downstreamTransitionLength: info.downstreamTransitionLength,
                    upstreamTransitionLength: info.upstreamTransitionLength,
                };
            }

            // 如果是无策略模式 提交时清除策略信息
            if (isNoStrategyMode()) {
                strategyList.value = [];
            }
            return params;
        };

        // 清除全部icon label
        const clearLayer = name => {
            [...eventMakerNameMap.keys()].forEach(name => {
                eventManager.removeEventPointByName(name);
            });
            eventMakerNameMap.clear();
        };

        const addTrafficEvent = async row => {
            if (!row) return;
            if (!canDrewIcon.value) return;
            console.log('props.emulationType, row.eventType-', props.emulationType, row.eventType);
            const cur = findEventType(row.eventType, props.emulationType);
            const eventName = cur ? cur.name : '';

            clearLayer(eventName);

            let eventPositionArr = row.eventPosition.split(',');
            let position = [Number(eventPositionArr[0]), Number(eventPositionArr[1])];
            viewTo({
                zoom: 15,
                center: position,
            }, 1000);
            const response = await getPointsElevation(position);
            const h = response.data?.[0] || 0;
            position = [position[0], position[1], h];
            eventManager.addEventPoint(
                eventName,
                position,
                {
                    labelText: eventName,
                }
            );
            eventMakerNameMap.set(eventName, {
                position,
            });
        };

        // 仿真运行
        const onRun = async () => {
            const params = getParams();
            const fetchScheme = editId.value ? updateScheme : createScheme;
            const res = await fetchScheme({
                ...params,
                mapLayer: platform_source.value,
            });
            if (res.code === 200) {
                // store.dispatch('set_emparam', editId.value ? params : res.data);
                // emulationInfo.value = editId.value ? params : res.data;
                const id =  editId.value ? params.id : res.data.id;

                strategyId.value = id;
                emit('onRun', params, {
                    strategyId: id,
                });
                if (res.data && res.data.eventList) {
                    addTrafficEvent(res.data.eventList[0]);
                }
            }
        };

        // 保存方案
        const saveCase = async () => {
            if (!updatedFlow) {
                // eslint-disable-next-line no-use-before-define
                await getFlow();
            }
            const params = getParams();
            console.log('params==', params);
            if (!params.name) return messageTip('请填写方案名称');
            console.log('editId.value ==', editId.value);
            const fetchScheme = editId.value ? updateScheme : createScheme;
            const res = await fetchScheme({
                ...params,
                mapLayer: platform_source.value,
            });
            if (res.code === 200) {
                messageTip('保存成功');
                // 后续优化
                // this.$parent.$children[0].getData(this.curEmulation);
                emit('close');
                emit('onSave', params);
                clearLayer();
            }
        };

        // 取消方案
        const cancelCase = () => {
            emit('close');
            clearLayer();
        };

        // 流量信息 Function
        const verifyDistance = () => {
            const flowDistance = flowInfo.value.flowDistance;
            if (isNaN(flowDistance)) {
                flowInfo.value.flowDistance = 1000;
                return messageTip('请设置正确的出车点距离') && false;
            }
            else if (flowDistance < 500 || flowDistance > 5000) {
                flowInfo.value.flowDistance = Math.min(Math.max(flowDistance, 500), 5000);
                return messageTip('出车点距离不能小于500米或大于5000米') && false;
            }
            return true;
        };
        const getFlow = async () => {
            if (!verifyDistance()) return;
            const {data} = await fetchFlow(flowParams.value);
            if (!data) return;
            const {autoRampList, manualRampList} = data;
            updatedFlow = true;
            flowData.value = inputMode.value === 1 ? autoRampList : manualRampList;
        };

        const onDateChange = val => {
            if (!val) return messageTip('请选择查询时间区间');

            let timeDiff = dayjs(val[1]).format('X') - dayjs(val[0]).format('X');
            if (timeDiff > 3 * 60 * 60) {
                dateRange.value = [];
                return messageTip('历史时间区间需要满足在三个小时以内');
            }

            dateRange.value = val;
            getFlow();
        };

        const emulationRange = async (highSpeedName, stakeNumber) => {
            const {data} = await getEmulationRange({
                highSpeedName,
                stakeNumber,
            });
            if (data) {
                flowInfo.value.emulationStartStake = data.emulationStartStake;
                flowInfo.value.emulationEndStake = data.emulationEndStake;
                flowInfo.value.highSpeedName = data.highSpeedName;
            }
        };

        const initGate = async () => {
            const {data} = await fetchGate({
                highSpeedName: flowInfo.value.highSpeedName,
                emulationStartStake: flowInfo.value.emulationStartStake,
                emulationEndStake: flowInfo.value.emulationEndStake,
            });
            tollList.value = data;
        };

        // 区域下拉选择
        const onSelectChange = async val => {
            // const cur = highSpeedInfo.value.find(item => item.highSpeedName === val);
            // emulationStartStakeList.value = cur ? cur.upstreamRampList : [];
            // emulationEndStakeList.value = cur ? cur.downstreamRampList : [];
            // init_number_list(flowInfo.value.highSpeedName);
            emit('sumoStakeChange', flowInfo.value.highSpeedName);
            flowInfo.value.highSpeedName1 = val;
            if (val) {
                emit('stakenumberChange', val);
                await init_number_list(val);
                flowInfo.value.eventStartStake
                    = stakeListOptions.value[stakeListOptions.value.length / 2 | 0]?.stakeNumber;
            }
            emulationRange(val, flowInfo.value.eventStartStake);
            await init_sumo_list({highSpeedName: flowInfo.value.highSpeedName});
            flowInfo.value.emulationStartStake = sumoStakeOptions.value[sumoStakeOptions.value.length / 2 | 0]
                ?.stakeNumber;
            const {longitude, latitude} = sumoStakeOptions.value[sumoStakeOptions.value.length / 2 | 0];
            viewTo({
                zoom: 15,
                center: [longitude, latitude, 45],
            }, 1000);
            emit('onSelectChange', {
                type: 'highSpeedInfo',
                value: val,
            });
        };

        // 桩号选择事件
        const startChange = val => {
            const cur = sumoStakeOptions.value.find(item => item.stakeNumber === val);
            if (cur) {
                let position = [cur.longitude, cur.latitude];
                viewTo({
                    zoom: 15,
                    center: position,
                }, 1000);
            }
        };

        const onAreaChoose = type => {
            flowInfo.value.areaChoose = type;
        };

        const eventRoadNameChange = async v => {
            flowInfo.value.highSpeedName = v;
            onSelectChange(v);
            if (v) {
                emit('stakenumberChange', v);
                await init_number_list(v);
                flowInfo.value.eventStartStake
                    = stakeListOptions.value[stakeListOptions.value.length / 2 | 0]?.stakeNumber;
            }
        };

        const stakeChange = (val, type) => {
            const cur = stakeListOptions.value.find(item => item.stakeNumber === val);
            if (type === 'event' && cur) {
                flowInfo.value.eventPosition = `${cur.longitude},${cur.latitude}`;
                emulationRange(flowInfo.value.highSpeedName, val);
            }
        };

        const handlePositionFocus = (type, index) => {
            let position = [];
            if (type === 'eventPosition' && flowInfo.value.eventPosition) {
                position = flowInfo.value.eventPosition.split(',');
                viewTo({
                    zoom: 15,
                    center: [Number(position[0]), Number(position[1]), 1],
                }, 1000);
                addTrafficEvent({
                    eventType: flowInfo.value.eventType,
                    eventPosition: flowInfo.value.eventPosition,
                    id: new Date().getTime(),
                });
            }
            if (type === 'lanePosition') {
                position = lanePosition.value[index].split(',');
                curLane.value = index;
                if (position.length > 1) {
                    viewTo({
                        zoom: 15,
                        center: [Number(position[0]), Number(position[1]), 1],
                    }, 1000);
                }
            }
        };

        const onPoint = () => {
            if (disabled.value) return;
            eventInput.value?.focus();
            document.querySelector('body').style.cursor = 'crosshair';
            positionVisible.value = !positionVisible.value;
        };

        const pointed = () => {
            if (disabled.value) return;
            eventInput.value?.blur();
            document.querySelector('body').style.cursor = 'pointer';
            positionVisible.value = !positionVisible.value;
            clearLayer();
            addTrafficEvent({
                eventType: flowInfo.value.eventType,
                eventPosition: flowInfo.value.eventPosition,
            });
        };

        const onItem = item => {
            if (disabled.value) return;
            if (closedLane.value.includes(item)) {
                const index = closedLane.value.findIndex(i => i === item);
                closedLane.value.splice(index, 1);
                lanePosition.value.splice(index, 1);
            }
            else {
                closedLane.value.push(item);
                lanePosition.value.push('');
            }
            flowInfo.value.closeLane = closedLane.value.join(',');
        };

        const addAndUpdateDomPoint = async e => {
            const highSpeedName = localStorage.getItem('highSpeedName') || props.highSpeedName;
            const {point} = e;
            const [lng, lat] = point;
            iconManager.removeIconByName(position_icon_name);
            iconManager.addIcon(position_icon_name, e.point, 'maplayer/assets/image/position.svg', {
                width: 22,
                height: 40,
                offset: [0, -40],
            });

            const {code, data: info} = await getStakeInfo({
                longitude: lng,
                latitude: lat,
                highSpeedName,
            });

            if (info && code === 200) {
                // this.showDomPoint();
                roadInfo.value = {...info, ...info.querySumoEdgeDto};
                // if (this.clickModule === 'showStakeNumber') {
                //     this.dompointDataSource.setData([
                //         {
                //             type: 'Feature',
                //             geometry: {
                //                 type: 'Point',
                //                 coordinates: point,
                //             },
                //             properties: {
                //                 stake: info?.stakeNumber,
                //                 edgeId: info?.edgeId,
                //                 laneId: info?.laneId,
                //                 carId: info?.id,
                //                 direction: road_direction?
                //                 find(e => +e.code === info?.querySumoEdgeDto?.direction)?.name,
                //             },
                //         },
                //     ]);
                // }
                // else {
                //     this.hideDomPoint();
                // }
            }


        };

        const getConstruction = async params => {
            const {data} = await getStakeInfo(params);
            if (data && data.querySumoEdgeDto) {
                closedLane.value = data.querySumoEdgeDto.closeLane ? data.querySumoEdgeDto.closeLane.split(',') : [];
                flowInfo.value.closeLane = closedLane.value.join(',');
                flowInfo.value.direction = data.querySumoEdgeDto?.direction || 1;
            }
        };

        const fetchConstructionList = async params => {
            const {data} = await getConstructionGeo(params);
            constructionGeoInfo.value = data;
        };

        // 更新扎点位置
        watch(
            () => [flowInfo.value.eventType, flowInfo.value.eventPosition],
            val => {
                const [eventType, eventPosition] = val;
                if (!isNil(eventType) && !isNil(eventPosition)) {
                    addTrafficEvent({
                        eventType,
                        eventPosition,
                    });
                }
            },
            {
                immediate: true,
            }
        );

        watch(
            () => flowInfo.value.eventStartStake,
            val => {
                const cur = stakeListOptions.value.find(item => item.stakeNumber === val);
                if (cur) {
                    flowInfo.value.eventPosition = `${cur.longitude},${cur.latitude}`;
                    emulationRange(flowInfo.value.highSpeedName, val);
                }
            },
            {
                immediate: true,
            }
        );


        watch(
            () => [
                flowInfo.value.highSpeedName1,
                flowInfo.value.eventStartStake,
                flowInfo.value.eventPosition,
                flowInfo.value.closeLane,
                flowInfo.value.upstreamTransitionLength,
                flowInfo.value.constructionLength,
                flowInfo.value.downstreamTransitionLength,
            ],
            val => {
                // 施工仿真
                if (isConstruction.value) {
                    // 有效参个数
                    const effectiveSize = val.filter(i => i !== undefined && i !== '').length;
                    if (val.length !== effectiveSize) return;
                    const [
                        highSpeedName,
                        startStake,
                        location,
                        closeLane,
                        upstreamTransitionLength,
                        constructionLength,
                        downstreamTransitionLength,
                    ] = val;
                    fetchConstructionList({
                        highSpeedName,
                        startStake,
                        location,
                        closeLane,
                        upstreamTransitionLength,
                        constructionLength,
                        downstreamTransitionLength,
                    });
                }
            },
            {
                deep: true,
            }
        );

        watch(() => curIndex.value, val => {
            if (val === 2 && !updatedFlow) {
                getFlow();
            }
            if (val === 4) {
                emit('stakenumberChange', flowInfo.value.highSpeedName1 || flowInfo.value.highSpeedName);
                init_number_list(flowInfo.value.highSpeedName1 || flowInfo.value.highSpeedName);
            }
        });

        watch(() => flowParams.value, val => {
            if (val) {
                updatedFlow = false;
            }
        }, {
            deep: true,
            immediate: true,
        });

        watch(() => detail.value, val => {
            if (!val) {
                const {eventType, highSpeedName, highSpeedName1, eventStartStake} = flowInfo.value;
                // 恢复默认值
                if (eventType !== -1) {
                    emulationRange(highSpeedName, eventStartStake);
                }
                // emulationRange(flowInfo.value.highSpeedName, flowInfo.value.eventStartStake);
                init_number_list(highSpeedName1);
                init_sumo_list({highSpeedName});
                emit('stakenumberChange', highSpeedName1);
                emit('sumoStakeChange', highSpeedName);
                return;
            }

            const cloneVal = cloneDeep(val);
            const {areaChoose, weatherScene, flowDistance, flowInputType, flowList} = cloneVal;
            const {closeLane: _closeLane, closeLanePosition, direction, duration, eventStartStake,
                   eventStartTime, eventType, eventPosition, eventLocationType, highSpeedName,
                   limitSpeed, upstreamTransitionLength, constructionLength, downstreamTransitionLength,
            } = cloneVal.eventList?.[0] || cloneVal;
            const newFlowInfo = {
                weatherScene: weatherScene ?? 1,
                areaChoose: areaChoose ?? 1,
                closeLane: _closeLane,
                closeLanePosition,
                direction,
                duration,
                eventStartStake,
                highSpeedName: val.highSpeedName?.toUpperCase(),
                highSpeedName1: highSpeedName?.toUpperCase() || val.highSpeedName?.toUpperCase()
                    || highSpeedOptions.value[0].highSpeedName,
                eventStartTime,
                eventType,
                eventPosition,
                eventLocationType,
                flowDistance: flowDistance ?? 1000,
            };
            // 施工仿真
            if (isConstruction.value) {
                newFlowInfo.limitSpeed = limitSpeed;
                newFlowInfo.upstreamTransitionLength = upstreamTransitionLength;
                newFlowInfo.constructionLength = constructionLength;
                newFlowInfo.downstreamTransitionLength = downstreamTransitionLength;
            }
            Object.assign(flowInfo.value, cloneVal, newFlowInfo);
            inputMode.value = flowInputType || inputMode.value;
            console.log('--eventPosition', eventPosition);
            if (eventPosition) {
                addTrafficEvent({
                    eventType: eventType,
                    eventPosition: eventPosition,
                    id: new Date().getTime(),
                });
            }

            if (flowInfo.value.flowInputType === 1) {
                dateRange.value = [flowInfo.value.emulationStartTime, flowInfo.value.emulationEndTime];
            }
            else {
                dateRange.value = [flowInfo.value.customFlowStartTime, flowInfo.value.customFlowEndTime];
            }
            // if (props.createConfig.type === 'create' && cloneVal.eventType !== -1) {
            //     emulationRange(flowInfo.value.highSpeedName, flowInfo.value.eventStartStake).then(() => initGate());
            // }
            // else if (flowInfo.value.emulationStartStake) {
            //     emulationRange(flowInfo.value.highSpeedName, flowInfo.value.eventStartStake).then(() => initGate());
            // }

            init_number_list(flowInfo.value.highSpeedName1);
            init_sumo_list({highSpeedName: flowInfo.value.highSpeedName});
            emit('stakenumberChange', flowInfo.value.highSpeedName1);
            emit('sumoStakeChange', flowInfo.value.highSpeedName);

            closedLane.value = flowInfo.value.closeLane ? flowInfo.value.closeLane.split(',') : [];
            lanePosition.value = flowInfo.value.closeLanePosition ? flowInfo.value.closeLanePosition.split(';') : [];
            flowData.value = flowList ?? [];
            if (flowData.value?.length) {
                setTimeout(() => {
                    updatedFlow = true;
                }, 0);
            }

            // 存在历史策略则更新，不存在使用默认值
            // if (cloneVal.strategyList.length) {
            strategyList.value = cloneVal?.strategyList || strategyList.value;
            // }

            console.log('cloneVal.strategyInputType--', cloneVal);

            strategyMode.value = cloneVal?.strategyInputType || 3;

            modelInfo.value = cloneVal?.model || modelInfo.value;
        }, {
            deep: true,
            immediate: true,
        });

        watch(() => engine.value, val => {
                  if (val) {
                      val.event.bind('click', async e => {
                          if (e.object?.is3DTiles) {
                              const {point} = e;
                              const [lng, lat] = point;
                              clickposition.value = `${lng}, ${lat}`;
                              setTimeout(() => {
                                  addAndUpdateDomPoint(e);
                              }, 0);
                              emit('onRoadClick', e);
                          }

                      });
                  }
              },
              {
                  immediate: true,
              }
        );

        // 监听地图点击获取经纬度
        watch(() => clickposition.value, val => {
            if (val && positionVisible.value) {
                flowInfo.value.eventPosition = val;
            }
        });

        watch(() => inputMode.value, val => {
            if (val) {
                flowInfo.value.flowInputType = val;
                getFlow();
            }
        });

        watch(() => flowInfo.value.eventPosition, val => {
            if (val) {
                getConstruction(
                    {
                        highSpeedName: flowInfo.value.highSpeedName,
                        // startStake: flowInfo.value.eventStartStake,
                        // endStake: flowInfo.value.eventStartStake,
                        // location: flowInfo.value.eventPosition,
                        // closeLane: closedLane.value.join(','),
                        longitude: val.split(',')[0],
                        latitude: val.split(',')[1],
                    }
                );
            }
        });

        watch(() => flowInfo.value.areaChoose, val => {
            if (val) {
                if (val === 2) {
                    isCustomArea.value = true;
                    addArea.value = 3;
                    engine.value.map.setPitch(15);
                    engine.value.map.setZoom(15);
                }
            }
        });

        watch(() => flowInfo.value.highSpeedName, val => {
            if (val) {
                localStorage.setItem('highSpeedName', val);
            }
        });

        watch(() => roadInfo.value, val => {
            if (!val || !positionVisible.value) return;
            flowInfo.value.direction = val.direction;
            closedLane.value = val.closeLane?.split(',');
            flowInfo.value.eventStartStake = val.stakeNumber;
            flowInfo.value.closeLane = closedLane.value.join(',');
            flowInfo.value.highSpeedName1 = val.roadName || flowInfo.value.highSpeedName1;
        }, {
            deep: true,
        });

        watch(() => updateAreaInfo.value, val => {
            if (val && val.data !== null) {
                const [firstItem] = val?.data;
                flowInfo.value.highSpeedName = firstItem.highSpeedName;
                flowInfo.value.emulationStartPtake = firstItem.emulationStartStake;
                flowInfo.value.emulationEndStake = firstItem.emulationEndStake;
            }
            this.flowInfo.areaChoose = val.type;
        });

        // 获取收费站列表
        watch(
            () => [
                flowInfo.value.highSpeedName,
                flowInfo.value.emulationStartStake,
                flowInfo.value.emulationEndStake,
            ],
            (newVal, oldVal) => {
                const hasEmpty = newVal.some(item => !item);
                if (isEqual(newVal, oldVal) || hasEmpty) return;
                initGate();
                if (newVal[0] !== oldVal[0]) {
                    strategyList.value = [];
                    strategyMode.value = 3;
                }
            },
            {
                immediate: true,
            }
        );

        onMounted(() => {
            // init_number_list();
            // init_sumo_list();
            if (highSpeedOptions.value?.length) {
                const [firstItem] = highSpeedOptions.value;
                emulationStartStakeList.value = firstItem.upstreamRampList;
                emulationEndStakeList.value = firstItem.downstreamRampList;
            }
        });

        onUnmounted(() => {
            clearLayer();
        });

        return {
            editId,
            curIndex,
            moduleList,
            componentId,
            flowData,
            modelInfo,
            dateRange,
            inputMode,
            flowInfo,
            closedLane,
            laneOptions,
            road_direction,
            highSpeedInfo,
            sumoStakeOptions,
            event_type_list,
            stakeListOptions,
            disabled,
            positionVisible,
            eventInput,
            uuid,
            isConstruction,
            strategyMode,
            strategyList,
            tollList,
            eventManager,
            highSpeedOptions,
            onDateChange,
            onTab,
            onRun,
            saveCase,
            cancelCase,
            onSelectChange,
            onAreaChoose,
            eventRoadNameChange,
            stakeChange,
            handlePositionFocus,
            onPoint,
            pointed,
            onItem,
            startChange,
            getConstructionList,
            lineManager,
            polygonManager,
        };
    },

};
</script>

<style scoped lang="less">
@import url('@EmulationLayer/assets/css/common.less');

.box-b {
    height: 100%;
    // position: absolute;
    // width: 100%;
    // height: 100%;
    // pointer-events: none;

    // > * {
    //     pointer-events: auto;
    // }
}

.emulation-create {
    width: 472px;
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    height: 100%;
    .background-card();

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 24px;
        background: rgba(8, 176, 137, .2);
        cursor: pointer;
        font-size: 16px;
        color: #fff;
    }

    .content {
        flex: 1;
        overflow-y: auto;
        padding: 24px;

        .tabs {
            display: flex;
            height: 32px;
            align-items: center;
            justify-content: space-between;
            border: 1px solid rgb(1, 121, 108);
            box-sizing: border-box;

            > div {
                flex: 1;
                height: 100%;
                cursor: pointer;
                font-size: 14px;
                color: #93b4b7;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: background .2s, color .2s;
                background: rgba(1, 67, 69, .5);

                &.tab-pane-active {
                    background: rgb(1, 174, 137);
                    color: #fff;
                }
            }
        }

        .detail-content {
            margin-top: 24px;
            position: relative;

            >div {
                position: absolute;
            }

            .small-title {
                font-size: 14px;
                color: #fff;
                position: relative;
                padding-left: 8px;
                display: flex;
                justify-content: space-between;
                cursor: pointer;

                &::before {
                    content: '';
                    width: 4px;
                    height: 12px;
                    background: #08d6a5;
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translate(0, -50%);
                }

                span:nth-child(2) {
                    color: #49c3ff;
                }
            }

            .select-area {
                padding: 16px 0;
                display: flex;
                margin-left: 10px;

                >span {
                    display: block;
                    color: #fff;
                    // width: 98px !important;
                }

                .item {
                    flex: 1;
                    display: flex;
                    align-items: center;
                    flex-wrap: wrap;
                    color: #c4cfde;

                    .select-item {
                        display: flex;
                        align-items: center;
                        cursor: pointer;

                        span:nth-child(1) {
                            display: inline-block;
                            width: 14px;
                            height: 14px;
                            border: 1px solid #08d6a5;
                            border-radius: 50%;
                            margin-right: 8px;
                            margin-left: 16px;
                        }

                        span:nth-child(2) {
                            color: #c4cfde;
                        }

                        &.active-item {
                            span:nth-child(1) {
                                border-color: #08d6a5;
                                position: relative;

                                &::before {
                                    content: '';
                                    width: 8px;
                                    height: 8px;
                                    background: #08d6a5;
                                    border-radius: 50%;
                                    position: absolute;
                                    left: 50%;
                                    top: 50%;
                                    transform: translate(-50%, -50%);
                                }
                            }

                            span:nth-child(2) {
                                color: #fff;
                            }
                        }
                    }
                }
            }

            .form-item {
                display: flex;
                margin-bottom: 16px;
                align-items: center;
                margin-left: 10px;

                span {
                    width: 98px;
                    font-size: 14px;
                    color: #fff;
                    letter-spacing: 0;
                    flex-shrink: 0;
                }

                /deep/.el-input__suffix {
                    line-height: 40px;
                    color: #6f88a7;
                }
            }

            .edit-item {
                margin-bottom: 16px;
                display: flex;
                align-items: center;
                margin-left: 10px;

                >img {
                    width: 18px;
                    height: 28px;
                    margin-left: 12px;
                    cursor: pointer;
                }

                .point-btn {
                    background: #49c3ff;
                    color: #e5f1ff;
                    display: inline-block;
                    width: 52px;
                    height: 28px;
                    font-size: 14px;
                    border-radius: 4px;
                    margin-left: 8px;
                    text-align: center;
                    line-height: 28px;
                }

                >span {
                    display: inline-block;
                    width: 115px;
                    font-size: 14px;
                    color: #fff;
                    letter-spacing: 0;
                    flex-shrink: 0;
                }

                /deep/.el-input__suffix {
                    line-height: 40px;
                    color: #6f88a7;
                }

                .edit-con {
                    flex: 1;
                    display: flex;
                    align-items: center;
                    flex-wrap: wrap;

                    .disabled {
                        cursor: not-allowed;
                    }

                    >div {
                        margin: 0 12px;
                        display: flex;
                        align-items: center;
                        margin-top: 16px;

                        span:nth-child(1) {
                            display: inline-block;
                            width: 14px;
                            height: 14px;
                            border: 1px solid #98b8de;

                            &.selected {
                                background: #49c3ff;
                                position: relative;

                                &::before {
                                    content: '✓';
                                    color: rgba(10, 28, 53, 1);
                                    position: absolute;
                                    left: 50%;
                                    top: 50%;
                                    transform: translate(-50%, -50%);
                                }
                            }
                        }

                        span:nth-child(2) {
                            font-family: 'FZLTZHJW--GB1-0', sans-serif;
                            font-size: 14px;
                            color: #c4cfde;
                            margin-left: 8px;
                        }

                        .config {
                            margin-left: 5px !important;
                            color: #49c3ff !important;
                        }
                    }
                }

                .item {
                    flex: 1;
                    height: 40px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .reduce,
                    .add {
                        display: flex;
                        width: 40px;
                        height: 40px;
                        background: rgba(26, 132, 255, .8);
                        border: 1px solid rgba(52, 146, 255, 1);
                        justify-content: center;
                        align-items: center;
                        font-size: 20px;
                        color: #e5f1ff;
                    }

                    .reduce {
                        border-radius: 2px 0 0 2px;
                    }

                    span:nth-child(2) {
                        flex: 1;
                        background: rgba(9, 38, 89, .5);
                        border-top: 1px solid rgba(45, 83, 151, .5);
                        border-bottom: 1px solid rgba(45, 83, 151, .5);
                    }

                    .add {
                        border-radius: 0 2px 2px 0;
                    }

                    /deep/.el-input-number__decrease,
                    /deep/.el-input-number__increase {
                        background: rgba(26, 132, 255, .8);
                        border: 1px solid rgba(52, 146, 255, 1);
                        font-size: 16px;
                        color: #e5f1ff;
                    }

                    .el-icon-delete {
                        font-size: 16px;
                        color: #e5f1ff;
                        cursor: pointer;
                    }

                    /deep/.el-input {
                        height: 40px;
                    }
                }

                .add-btn {
                    width: 100%;
                    height: 40px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    border: 1px solid rgba(178, 214, 255, 1);
                    border-radius: 2px;
                    color: #b2d6ff;
                    margin-bottom: 16px;
                }

                .action-btn {
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    span {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        width: 107px;
                        height: 36px;

                        &:nth-child(1) {
                            border: 1px solid rgba(178, 214, 255, 1);
                            border-radius: 2px;
                            color: #b2d6ff;
                        }

                        &:nth-child(2) {
                            background: rgba(26, 132, 255, .8);
                            border: 1px solid rgba(52, 146, 255, 1);
                            border-radius: 2px;
                            color: #e5f1ff;
                            margin-left: 16px;
                        }
                    }
                }
            }
        }
    }

    .footer {
        // height: 124px;
        align-items: center;
        cursor: pointer;
        padding: 16px 36px 16px 36px;
        background-image: linear-gradient(270deg, rgba(0, 255, 188, .4) 1%, rgba(0, 255, 190, .24) 100%);
        box-shadow: inset 0 1px 3px 0 rgba(8, 214, 165, .22);
        border-top: 1px solid #07af87;

        .edit-item {
            display: flex;
            align-items: center;
            color: #fff;
            font-size: 14px;
        }

        .btn {
            display: flex;
            margin-top: 20px;

            span {
                display: flex;
                height: 36px;
                justify-content: center;
                border-radius: 2px;
                align-items: center;
                font-size: 14px;
                flex: 1;

                &:nth-child(1) {
                    background-image: linear-gradient(180deg, #077a9c 0%, #009672 100%);
                    color: #efffff;
                }

                &:nth-child(2) {
                    border: 1px solid rgba(22, 210, 166, 1);
                    border-radius: 2px;
                    color: #efffff;
                    margin: 0 12px;
                }

                &:nth-child(3) {
                    border: 1px solid rgba(22, 210, 166, 1);
                    border-radius: 2px;
                    color: #efffff;
                }
            }
        }
    }
}

</style>