<template>
    <map-component
        ref="mapRef"
        :options="{
            center: [113.55, 23.5555],
            showSatelliteMap: true,
            showAssetsScene: true,
        }"
        class="map-box"
        :style="{
            height: height + 'px',
        }"
        @mapLoaded="mapLoaded"
    >
        <layer-top
            title="测试集成仿真图层"
            style="position: absolute; top: 0;"
        />
        <test-emulation-layer
            v-if="mapInitStatus"
        />
    </map-component>
</template>

<script>
import {Map} from '@/index';
import {ref, onMounted} from 'vue';
import {initTestEmulationLayerConfig} from '@/components/Layers/TestEmulationLayer/utils';
import TestEmulationLayer from '@/components/Layers/TestEmulationLayer/index.vue';
import LayerTop from '@/components/Common/LayerTop/index.vue';

const apiHost = import.meta.env.MODE === 'test' ? 'http://************:8751' : '';

export default {
    components: {
        MapComponent: Map,
        TestEmulationLayer,
        LayerTop,
    },
    setup() {
        const mapRef = ref(null);
        const engine = ref(null);
        const height = ref(1080);
        const mapInitStatus = ref(false);
        // 地图加载完成后执行的回调函数
        const mapLoaded = () => {
            initTestEmulationLayerConfig({
                engine: mapRef.value.map,
                apiHost,
            });
            mapInitStatus.value = true;
        };

        onMounted(() => {
            height.value = window.innerHeight;
            document.title = '测试集成仿真图层';
        });

        return {
            mapRef,
            engine,
            height,
            mapInitStatus,
            mapLoaded,
            apiHost,
        };
    },
};
</script>

<style scoped>
.map-box {
    position: relative;
    width: 100vw !important;
}
</style>
