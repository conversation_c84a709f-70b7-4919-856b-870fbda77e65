

import {ref, watch} from 'vue';
import {io} from 'socket.io-client';

import {v4 as uuidV4} from 'uuid';

const main = ({
    id = uuidV4(),
    url,
}, cb = () => {}
) => {
    const willSearchInfo = ref(null);
    const searchIng = ref(false);
    let ws = null;

    const globalSearchVehicle = () => {
        const {plate} = willSearchInfo.value;
        if (plate) {
            searchIng.value = true;
            // 发送车牌，尝试获取该车辆所在位置的最新的经纬度;
            ws.emit('message', {plate});
        }
    };

    const onmessageCarInfo = info => {
        const {plate} = info;
        if (willSearchInfo.value.plate && willSearchInfo.value.plate === plate) {
            cb?.(info);
        }
    };

    const checkKeyVehicleType = data => {
        // doing for data;
        const keys = Object.keys(data);

        keys.forEach(key => {
            const value = data[key];

            switch (key) {
                // 重点车辆相关
                // case 'focusList':
                //     this.checkFocusList(wrapArray(value));
                //     break;
                // 车辆追踪相关
                case 'carTrack':
                    onmessageCarInfo(value);
                    break;
                // 球机追踪相关
                // case 'ballSectionList':
                //     this.updateState({
                //         ballSectionList: value,
                //     });
                //     break;
                // // 球机提示相关
                // case 'ballSectionMsg':
                //     this.$message({
                //         type: 'info',
                //         message: value.msg,
                //         duration: 5000,
                //     });
                //     break;
                default:
                    break;
            }
        });
    };

    const createSearchWs = () => {
        if (ws) ws.close();
        ws = io(url.value, {
            transports: ['websocket'],
            secure: true,
            query: {
                twinId: id + '3',
                userType: 1,
                tid: 'imBigScreenShowSocketIO',
            },
        });

        // 从服务器接收消息
        ws.on('message', data => {
            if (data === 'ok') return;
            try {
                const d = JSON.parse(data);
                // 根据数据key来区分数据类型
                checkKeyVehicleType(d);
            }
            catch (err) {
                console.log(err); // eslint-disable-line
            }
        });
    };

    watch(url, v => {
        if (v) createSearchWs();
    },
    {immediate: true}
    );


    const search = info => {
        willSearchInfo.value = info;
        globalSearchVehicle();
    };

    const cancel = () => {
        willSearchInfo.value = null;
        searchIng.value = false;
        console.log('search cancel');
    };

    return {
        searchIng,
        search,
        cancel,
    };
};

export default main;