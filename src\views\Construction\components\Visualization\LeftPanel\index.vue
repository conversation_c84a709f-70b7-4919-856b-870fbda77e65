<template>
    <CollapsePanel
        :collapse="collapse"
        @collapse="$emit('update:collapse', !collapse)"
    >
        <div class="panel-content">
            <div class="panel-content__left">
                <EventStatisticsCard/>
                <EventListCard
                    class="mt-24"
                    @rowClick="$emit('rowClick', $event)"
                    @create="$emit('createScheme', $event)"
                />
            </div>
            <div class="panel-content__right">
                <DutyInfoCard/>
            </div>
        </div>
    </CollapsePanel>
</template>

<script setup>
import {CollapsePanel} from '@/components/Common';
import EventStatisticsCard from '../../Card/EventStatisticsCard/index.vue';
import EventListCard from '../../Card/EventListCard/index.vue';
import DutyInfoCard from '@/views/TollStation/components/Card/DutyInfoCard/index.vue';

defineProps({
    collapse: Boolean,
});
</script>

<style lang="less" scoped>
.panel-content {
    display: flex;
    align-items: flex-start;
    pointer-events: none;

    > * {
        pointer-events: auto;
    }

    &__left {
        width: 1232px;
    }

    &__right {
        margin-left: 24px;
    }
}
</style>