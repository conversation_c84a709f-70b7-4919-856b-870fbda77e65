<template>
    <div class="sensor-online">
        <monitor-card
            width="556" height="100"
            :need-progress="false"
            :info="{
                cnTitle: '传感器在线率',
                enTitle: 'Total number of Sensor online',
                icon: 'svg',
                unit: '%',
                value: data.value || 0,
            }"
            :style-obj="styleObj"
        />
        <div class="chart">
            <left-pie-li
                v-if="data?.list?.length" :color="color"
                :data="data.list"
            />
        </div>
    </div>
</template>

<script>
import {ref, onMounted} from 'vue';
import {MonitorCard} from '@/views/Structure/components/common/index';
import {LeftPieLi} from '@/components/Common';
import {countSensorStatusBystructureId} from '@/api/structure/index';
import {structureId} from '@/views/Structure/utils/index';
import dayjs from 'dayjs';
export default {
    name: '传感器在线率统计',
    components: {
        MonitorCard,
        LeftPieLi,
    },
    setup() {
        const styleObj = {
            cnTitleFontSize: 20,
            enTitleFontSize: 16,
            numFontSize: 42,
        };

        const color = ref(['rgb(0, 176, 255)', 'rgb(208, 208, 208)']);

        const data = ref({});

        const init = (() => {
            countSensorStatusBystructureId({
                structureId: structureId.value,
                dateTime: dayjs(new Date()).format('YYYY-MM-DD'),
            }).then(res => {
                data.value = {
                    value: res.data.onlineRate,
                    list: [
                        {
                            name: '传感器在线数',
                            value: res.data.onlineNum || 0,
                            unit: '个',
                            // eslint-disable-next-line max-len, vue/max-len
                            ratio: res.data.onlineNum ? res.data.offlineNum / (res.data.onlineNum + res.data.offlineNum) : 0,
                            color: color.value[0],
                        },
                        {
                            name: '传感器离线数',
                            value: res.data.offlineNum || 0,
                            unit: '个',
                            // eslint-disable-next-line max-len, vue/max-len
                            ratio: res.data.offlineNum ? res.data.offlineNum / (res.data.onlineNum + res.data.offlineNum) : 0,
                            color: color.value[1],
                        },
                    ],
                };
            });
        });

        onMounted(() => init());

        return {
            styleObj,
            data,
            color,
        };
    },
};
</script>

<style lang="less" scoped>
.sensor-online {
    height: 318px;
    :deep(.info-card) {
        background-image: linear-gradient(to right, rgba(1, 255, 229, .3) 0%, rgba(1, 255, 229, 0) 76%);
        border-left: 4px solid rgb(1, 255, 229);
        border-image: none;

        .info-card__left__info {
            width: 300px;
        }
    }

    .chart {
        width: 556px;
        height: 206px;
        margin-top: 8px;

        :deep(ul) {
            display: flex;
            flex-direction: column;
            justify-content: center;

            li {
                height: 58px;
            }
        }
    }
}
</style>