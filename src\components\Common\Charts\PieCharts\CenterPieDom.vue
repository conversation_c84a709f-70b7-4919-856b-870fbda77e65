<template>
    <div class="full" :class="{'normal': !normal}">
        <CenterPie
            :data="list"
            :active-index="index"
            :need-center="false"
            @mouseover="mouseover"
            @mouseout="mouseout"
        />
        <!-- <div class="center">
            {{ total }}
            <span class="unit fs-16 lh-24">(%)</span>
        </div> -->
        <div
            v-for="(item, i) in list"
            :key="item.name"
            class="item fc p-4"
            :class="className[i] + ' ' + (i === index ? 'active' : '')"
            @mouseover="mouseover(i)"
            @mouseout="mouseout"
        >
            <template v-if="normal">
                <div class="name">
                    <span
                        class="pa dib w-6 h-6"
                        :style="{backgroundColor: item.color}"
                    ></span>
                    {{ item.name }}
                </div>
                <div class="value tc fs-32 lh-34">
                    {{ percent(item.ratio) }}
                    <span class="unit fs-12 lh-20">%</span>
                </div>
            </template>
            <template v-else>
                <div class="faility-top name">
                    {{ item.name }}
                    <div>
                        {{ item.value }}个
                        <span
                            class="pa dib w-6 h-6"
                            :style="{backgroundColor: item.color}"
                        ></span>
                    </div>
                </div>
                <div class="faility-bottom value tc fs-32 lh-34">
                    {{ percent(item.ratio) }}
                    <span class="unit fs-12 lh-20">%</span>
                </div>
            </template>
        </div>
    </div>
</template>
<script setup>
import {ref, computed, onMounted, onBeforeUnmount} from 'vue';
import {useIntervalFn} from '@vueuse/core';
import CenterPie from './CenterPie.vue';

const props = defineProps({
    list: {
        type: Array,
        default: () => [],
    },
    normal: {
        type: Boolean,
        default: true,
    },
});
const index = ref(0);
const className = computed(() => {
    switch (props.list.length) {
        case 1:
        case 2:
        case 3:
        case 4:
            return ['l t', 'l b', 'r b', 'r t'];
        case 5:
        case 6:
            return ['l t', 'l m', 'l b', 'r b', 'r m', 'r t'];
        case 7:
            return ['l t', 'l m', 'l b', 'r b', 'r m1', 'r m2', 'r t'];
        case 8:
            return ['l t', 'l m1', 'l m2', 'l b', 'r b', 'r m1', 'r m2', 'r t'];
        default:
            return ['l t', 'l b', 'r b', 'r t'];
    }
});
const percent = num => (num * 100).toFixed(2);
// const total = computed(() => {
//     return percent(props.list[index.value]?.ratio || 0);
// });
function activeIndexChange() {
    const len = props.list.length;
    index.value = (index.value + 1) % len;
};

const {pause, resume} = useIntervalFn(
    () => {
        activeIndexChange();
    },
    5 * 1000,
    {
        immediate: false,
        immediateCallback: false,
    }
);

const mouseover = i => {
    pause();
    index.value = i;
};

const mouseout = () => {
    resume();
};

onMounted(() => {
    resume();
});

onBeforeUnmount(() => {
    pause();
});
</script>
<style lang="less" scoped>
.full {
    height: 100%;
}
.center {
    width: 100%;
    height: 50px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: absolute;
    width: 168px;
    height: 72px;
    background: rgba(18, 74, 166, 0.24);
    backdrop-filter: blur(10.87px);
    font-family: 'PingFang';
    font-size: 16px;
    font-weight: 500;
    color: rgba(255, 255, 255, .8);

    &.active {
        background: rgba(18, 74, 166, 0.8);
        color: rgba(255, 255, 255, 1);
    }

    &::before {
        position: absolute;
        content: '';
        display: block;
        width: 2px;
        height: 100%;
        background-color: rgb(36, 104, 242);
    }

    &.l {
        left: 24px;
        padding: 0 10px 0 22px;
        &::before {
            left: 0;
        }

        & span {
            right: 11px;
        }

        & .name {
            background: linear-gradient(to left, rgba(0, 0, 0, .3), rgba(0, 0, 0, .1) 90%, rgba(0, 0, 0, 0));
        }
    }

    &.r {
        flex-direction: row-reverse;
        right: 24px;
        padding: 0 17px 0 16px;
        &::before {
            right: 0;
        }

        & span {
            left: 11px;
        }

        & .name {
            background: linear-gradient(to right, rgba(0, 0, 0, .3), rgba(0, 0, 0, .1) 90%, transparent);
        }
    }

    &.t {
        top: 24px;
    }

    &.b {
        bottom: 24px;
    }

    &.l.t {
        clip-path: polygon(0 0, 100% 0%, 100% 50px, 148px 100%, 0 100%);
    }

    &.r.t {
        clip-path: polygon(0 0, 100% 0%, 100% 100%, 20px 100%, 0 50px);
    }

    &.l.b {
        clip-path: polygon(0 0, 148px 0, 100% 22px, 100% 100%, 0 100%);
    }

    &.r.b {
        clip-path: polygon(20px 0, 100% 0, 100% 100%, 0 100%, 0 22px);
    }

    &.m,
    &.m1,
    &.m2 {
        top: 50%;
    }

    &.m {
        transform: translateY(-50%);
    }

    &.m1 {
        transform: translateY(-105%);
    }

    &.m2 {
        transform: translateY(8%);
    }

    // &.l.m {
    //     clip-path: polygon(0 0, 100% 100%, 100% 62px, 100% 100%, 0 100%);
    // }

    .value, .unit {
        font-family: 'RoboData';
    }
}

.normal {
    .item {
        height: 72px;
        padding: 4px 0;
    }
    .faility-top {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 90%;
        height: 28px;

        span.pa {
            top: 50%;
            transform: translateY(-50%);
        }
    }

    .l .faility-top {
        padding-right: 20px;

        span.pa {
            right: 0;
        }

    }

    .r .faility-top {
        padding-left: 20px;

        span.pa {
            left: 0;
        }
    }

    .r {
        flex-direction: column;
    }

    .b {
        flex-direction: column-reverse;
    }
}
</style>