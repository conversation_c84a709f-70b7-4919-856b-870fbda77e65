

import {twinManager} from '@TrafficLayer/utils/map/index.js';
import {onUnmounted, ref, watch} from 'vue';
import {v4 as uuidV4} from 'uuid';
import {io} from 'socket.io-client';
import {
    showPlate,
} from '@TrafficLayer/store/common';
import {useIntervalFn} from '@vueuse/core';

export default ({
    url,
    id = uuidV4(),
}) => {
    let twin = null;
    let ws = null;
    let twinSuccess = ref(false);
    let wsSuccess = ref(false);

    const viewTracks = ref([]);
    const canFollowPlateSet = new Set();
    const followCarInfo = ref(null);
    const followInfo = ref(null);
    let backParams = null;

    const onCurrentCarList = e => {
        const {content} = e;
        canFollowPlateSet.clear();
        content?.List.forEach(car => {
            const {plate, uuid} = car;
            if (plate) canFollowPlateSet.add(plate);
        });
    };
    const canFollowPlate = plate => canFollowPlateSet.has(plate);

    const onmessage = tracks => {
        followCarInfo.value = null;
        viewTracks.value = tracks.filter(e => {
            if (!e.plate) return false;
            if (followInfo.value && e.plate === followInfo.value.plate) {
                followCarInfo.value = e;
            }
            return true;
        });
    };

    const onConnected = () => {
        twinSuccess.value = true;
    };

    const followTraffic = (info = followInfo.value) => {
        // 地图视角追踪车辆
        twin.viewFollow(true, info);
        // 告诉服务端追踪哪辆车
        // ws.sendFollow(info);
    };

    const unFollowTraffic = (info = followInfo.value) => {
        twin?.viewFollow(false, info);
    };

    const {resume, pause} = useIntervalFn(() => {
        followTraffic();
    }, 200, {
        immediateCallback: false,
        immediate: false,
    });

    const follow = () => {
        pause();
    };

    const unfollow = () => {
        if (followInfo.value) {
            resume();
        }
    };

    // 创建地图内部孪生类
    const createTwin = () => {
        twin = twinManager.add('text1', {
            url: url.value,
            id: id,
            tid: 'imTwinReplaySocketIO',
            showPlate: showPlate.value,
            isSubsection: true,
            onDisconnected: () => {},
            onConnected,
            onCurrentCarList: onCurrentCarList,
            follow,
            unfollow,
        });
        setTimeout(() => {
            twinSuccess.value = true;
        }, 500);
        twin.viewFollow = (follow = true, info) => {
            twin.followTraffic({
                isFollow: follow,
                followRotations: {Roll: false, Pitch: false, Yaw: true},
                carId: info?.id,
                plateNumber: info?.plate,
                color: {r: .1, g: .4, b: 1},
            });
        };
    };
    // 创建孪生ws，为了拿到数据
    const createWs = () => {
        if (ws) ws.close();
        ws = io(url.value, {
            transports: ['websocket'],
            secure: true,
            query: {
                twinId: id + '2',
                userType: 1,
                tid: 'imTwinReplaySocketIO',
            },
        });

        ws.sendFollow = info => {
            ws.emit('message', info);
        };

        ws.on('connect', () => {
            wsSuccess.value = true;
        });

        // 从服务器接收消息
        ws.on('message', data => {
            try {
                const d = JSON.parse(data || '{}');
                d.tracks && onmessage(d.tracks);
            }
            catch (error) {}
        });
    };


    const initTwin = async () => {
        createWs();
        createTwin();
    };

    const stop = () => {
        if (!twin) return;
        twin.message = {'cmd': 'stop'};
        ws?.emit('message', twin.message);
    };

    watch(showPlate, () => {
        twin.labelVisible = showPlate.value;
    });


    watch(twinSuccess, v => {
        if (v && followInfo.value) {
            twin.message = {...backParams};
            resume();
        }
        else {
            pause();
        }
    });

    watch(wsSuccess, v => {
        if (v && followInfo.value) {
            ws.send({...backParams});
        }
    });

    const destroyTwin = () => {
        twinManager.removeByName('text1');
        stop();
        ws.close();
        followCarInfo.value = null;
        followInfo.value = null;
    };

    const track = info => {
        followInfo.value = {...info};
        // 开始追踪
        resume();
    };

    const unTrack = () => {
        pause();
        unFollowTraffic();
        followInfo.value = null;
        followCarInfo.value = null;
    };

    const backPlay = params => {
        followInfo.value = params;
        backParams = {
            'replay': {
                ...params,
                plate: [params.plate || params.plateNumber],
                plateNumber: [params.plate || params.plateNumber],
                bIncOther: 1,
            },
        };
        if (twinSuccess.value) {
            twin.message = {...backParams};
            resume();
        }
        if (wsSuccess.value) {
            ws.send({...backParams});
        }
    };

    const changePlateState = v => {
        showPlate.value = v;
    };


    watch(url, v => {
        if (v) initTwin();
    });

    onUnmounted(() => {
        destroyTwin();
    });


    return {
        viewTracks,
        followCarInfo,
        backPlay,
        canFollowPlate,
        track,
        unTrack,
        changePlateState,
        initTwin,
        destroyTwin,
    };
};