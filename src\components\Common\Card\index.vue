<template>
    <div
        :class="['card', cardType]"
        v-bind="$attrs"
        v-on="$listeners"
    >
        <!-- 头部 -->
        <header class="header">
            <div class="title">
                <span :class="{'noMargin': !showIcon}">{{ title }}</span>
                <slot name="extraTitle"></slot>

            </div>

            <div class="titleContent">
                <el-radio-group
                    v-if="radioContent.length" v-model="radio"
                    @input="input"
                >
                    <el-radio
                        v-for="item in radioContent" :key="item.label"
                        :label="item.label"
                        :style="{'--bgc': radioBgc}"
                    >
                        {{ item.name }}
                    </el-radio>
                </el-radio-group>
                <slot name="titleContent"></slot>
            </div>
            <div
                v-if="allowClose" title="关闭"
                @click="close"
            >
                <icon :class="allowAnimation ? 'icon-close' : 'icon-close-no-t '" name="zengjia"/>
            </div>
        </header>
        <!-- 内容区 -->
        <div class="content" :class="{'show-scroll': showScroll, 'marginBottom': marginBottom}">
            <slot name="content"></slot>
        </div>
    </div>
</template>

<script>
import {computed} from 'vue';
import Icon from '../Icon/index.vue';
import {RadioGroup, Radio} from 'element-ui';
export default {
    name: 'Card',
    components: {
        Icon,
        [RadioGroup.name]: RadioGroup,
        [Radio.name]: Radio,
    },
    props: {
        title: {
            type: String,
            default: '',
        },
        allowClose: {
            type: Boolean,
            default: false,
        },
        // 是否显示关闭效果动画
        allowAnimation: {
            type: Boolean,
            default: true,
        },
        // 展示进度条
        showScroll: {
            type: Boolean,
            default: false,
        },
        // 展示进度条
        marginBottom: {
            type: Boolean,
            default: false,
        },
        icon: {
            type: String,
            default: 'el-icon-s-data',
        },
        // 是否展示图标
        showIcon: {
            type: Boolean,
            default: true,
        },
        // 单选框内容
        radioContent: {
            type: Array,
            default: () => ([]),
        },
        radio: {
            type: String,
            default: '',
        },
        // 单选框背景色
        radioBgc: {
            type: String,
            default: 'black',
        },
        cardType: {
            type: String,
            /**
             * card-long-1  长卡片，标题区域较短
             * card-long-2  长卡片，标题区域较长
             * card-short-1 短卡片，标题区域较短
             * card-short-2 短卡片，标题区域较长
             */
            default: 'card-short-1',
        },
    },
    model: {
        prop: 'radio',
        event: 'update:radio',
    },
    setup(props, {emit}) {
        const display = computed(() => (props.title ? 'block' : 'none'));
        const close = () => {
            emit('close');
        };

        const input = e => {
            emit('update:radio', e);
            emit('radioChange', e);
        };

        return {
            close,
            display,
            input,
        };
    },
};
</script>

<style lang="less" scoped>
@import url('@/assets/css/common-layer.less');

.card {
    .header {
        .title {
            &::before {
                display: v-bind(display);
            }
        }
    }
}
</style>
