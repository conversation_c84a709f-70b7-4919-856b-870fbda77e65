<template>
    <div class="tool-button-wrapper">
        <Tooltip
            placement="left"
            :content="showScheme ? '隐藏仿真' : '显示仿真'"
        >
            <div
                :class="[
                    'tool-button-item',
                    {'tool-button-item__active': showScheme},
                ]"
                @click="showScheme = !showScheme"
            >
                <span>仿真</span>
                <span>方案</span>
            </div>
        </Tooltip>
        <map-icon-filter
            :value="projectType"
            :list="typeList"
            @change="handleChangeType"
            @recoverView="handleRecoverView"
        />
    </div>
</template>

<script setup>
import {MapIconFilter} from '@/components/Common';
import {Tooltip} from 'element-ui';
import {projectType, activeProjectInfo} from '../../store';
import {viewToFk} from '@/utils';
import {showScheme} from '../../../Emulation';

const typeList = [
    {
        icon: 'qiaoliang',
        value: 1,
        label: '桥梁',
    },
    {
        icon: 'lumian',
        value: 2,
        label: '路面',
    },
    {
        icon: 'lijiao',
        value: 3,
        label: '互通立交',
    },
    {
        icon: 'shebei',
        value: 4,
        label: '设备设施',
    },
];

function handleChangeType(e) {
    projectType.value = e;
    activeProjectInfo.value = undefined;
    viewToFk();
}

function handleRecoverView() {
    activeProjectInfo.value = undefined;
}
</script>

<style lang="less" scoped>
.tool-button {
    &-item {
        position: relative;
        width: 42px;
        height: 42px;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        background-color: rgba(3, 31, 68, .5);
        color: rgba(#fff, .5);
        cursor: pointer;
        user-select: none;
        backdrop-filter: blur(12px);
        border: 1px	solid rgba(72, 100, 146, .5);

        &::before {
            content: '';
            position: absolute;
            width: 100%;
            height: 1.4px;
            background-color: transparent;
            left: 0;
            top: 0;
            transition: background-color .25s;
        }

        &:not(:last-child) {
            margin-bottom: 16px;
        }

        &__active,
        &:hover {
            color: #fff;
            border-color: rgba(#fff, .3);
        }

        &__active {
            background:
                linear-gradient(
                    to right,
                    rgb(36, 104, 242),
                    rgba(1, 255, 229, .7)
                );
        }
    }
}
</style>