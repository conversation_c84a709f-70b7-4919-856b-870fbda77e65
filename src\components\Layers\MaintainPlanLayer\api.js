import {request} from '@/utils/network-helper/index.js';
import {baseConfig} from '@/utils/common';
import {
    apiHost,
} from '@MaintainPlanLayer/store/common';

const {basePrefix} = baseConfig;
const baseUrl = () => {
    return apiHost.value || baseConfig.baseService;
};
const baseMaintainPlan = () => `${baseUrl()}${basePrefix}/maintain/repairPlan`;

// 获取宏观养护计划数据
export async function getMacroPlan(planType) {
    const {data, code} = await request.post(
        `${baseMaintainPlan()}/getBaseRank`,
        {
            planType,
        },
        {
            extraInfo: {
                noGlobalLoading: true,
            },
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
}

// 获取中观养护计划数据
export async function getMesoPlan(payload) {
    const {data, code} = await request.post(
        `${baseMaintainPlan()}/getSectionRank`,
        payload,
        {
            extraInfo: {
                noGlobalLoading: true,
            },
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
}

// 获取微观二维高速养护计划数据
export async function getMicro2dPlan(payload) {
    const {data, code} = await request.post(
        `${baseMaintainPlan()}/getSectionPlans`,
        payload,
        {
            extraInfo: {
                noGlobalLoading: true,
            },
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
}

// 获取养护计划详情数据
export async function getPlanDetail(planId) {
    const {data, code} = await request.post(
        `${baseMaintainPlan()}/getPlanInfo/${planId}`,
        {},
        {
            extraInfo: {
                noGlobalLoading: true,
            },
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
}

// 查询待执行计划列表
export async function getUnExecutList(payload) {
    const {data, code} = await request.post(
        `${baseMaintainPlan()}/getUnExecutList`,
        payload,
        {
            extraInfo: {
                noGlobalLoading: true,
            },
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
}

// 查询养护计划柱形图数据
export async function getHistogram(payload) {
    const {data, code} = await request.post(
        `${baseMaintainPlan()}/getHistogram`,
        payload,
        {
            extraInfo: {
                noGlobalLoading: true,
            },
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
}