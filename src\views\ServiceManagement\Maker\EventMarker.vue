<template>
    <!-- <div>
        <EventMarker
            :manager-instace="manager"
            :info="makerInfo"
        >
            <div @click.stop>
                <TextDetail
                    v-if="event && isOpen" :detail-info="detailInfo"
                    @close="close"
                />
            </div>
        </EventMarker>
    </div> -->
    <div>
        <bubble-marker
            v-for="item in dataList " :key="item.id"
            :manager-instace="domManager"
            icon-name="path"
            bubble-color="#ed5953" :info="item"
        >
            <DetailList
                v-model="show[item.recordId]"
                class="detail-card"
                :list="item.detailInfo"
                width="328"
                :title="item.title"
                @close="infoClose"
            />
        </bubble-marker>
    </div>
</template>


<script setup >
import {MapLine, BubbleMarker, DetailList} from '@/components/Common/index.js';
import {lineManager, domManager} from '@/views/ServiceManagement/Map/index.js';
import {computed, ref, onMounted} from 'vue';
import Vue from 'vue';
import {Ws} from '@/utils/ws';
import TextDetail from '@/components/Common/MarkerDetail/TextDetail.vue';
import {viewToMicro, viewToFk} from '@/utils/map/methods/index.js';

import {useEnum, useEnumMap} from '@/api/serviceManager/index.js';
import _ from 'lodash';
const dataList = ref();
const show = ref([]);
function clickCallback(item, index) {
    // const info  = JSON.parse(item.cameraInfoList);
    viewToMicro([item.longitude, item.latitude]);
    show.value[item.recordId] = !show.value[item.recordId];
}
const wsData = ref();
const deviceList = ref([]);
const infoClose = e => {
    viewToFk();
};
const initWs = async e => {
    wsData.value = new Ws();
    const url = `${import.meta.env.VITE_WS_URL}/event`;

    let ws = await wsData.value.connect(url, {});
    ws.onmessage = ({data}) => {
        if (data === 'ok' || data === '{}') {
            return;
        }
        const {eventList} = JSON.parse(data);
        eventList.filter(n => n.status === 1
            && (n.stakeNumber === 'K3120+345' || n.stakeNumber === 'K3121+900'));
        deviceList.value = _.uniqBy([...deviceList.value, ...eventList], 'id');
        deviceList.value.forEach(item => {
            Vue.set(show.value, item.recordId, false);
        });
        console.log('output->dataList.value', dataList.value);
        dataList.value = deviceList.value.slice(0, 100).map((item, index) => ({
            ...item,
            position: [item.longitude, item.latitude, item.latitude ?? 0],
            name: item.typeName,
            customData: item,
            label: item.typeName,
            title: item.typeName,
            clickCallback: () => clickCallback(item, index),
            eventId: item.id.toString(),
            detailInfo: [
                {label: '位置桩号', value: item.stakeNumber},
                {label: '行车方向', value: item.travelDirectionName},
                {label: '发生时间', value: item.startTime},
                {label: '事件等级', value: item.eventLevel},
                {label: '事件来源', value: item.sourceName},
                {label: '当下状态', value: item.statusName},
            ],
        }));
    };
    if (wsData.value.ws) {
        wsData.value.ws.send('refresh');
    }
};
onMounted(() => {
    initWs();
});

</script>
<style lang="less" scoped>
</style>