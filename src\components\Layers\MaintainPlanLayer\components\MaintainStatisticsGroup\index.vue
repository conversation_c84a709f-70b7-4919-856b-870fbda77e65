<template>
    <div class="main-maintain-statistics">
        <!-- 组件内容 -->
        <maintain-plan-statistics :info="maintainChartData"/>
        <maintain-plan-list :list="pendingPlanList" @rowClick="handleRowClick"/>
    </div>
</template>

<script>
import MaintainPlanList from '@MaintainPlanLayer/components/MaintainPlanList/index.vue';
import MaintainPlanStatistics from '@MaintainPlanLayer/components/MaintainPlanStatistics/index.vue';
import {
    pendingPlanList,
    initMicro3dMap,
    orgId,
    sectionId,
    maintainChartData,
} from '@MaintainPlanLayer/store';

export default {
    name: 'MainMaintainStatistics',
    components: {
        MaintainPlanList,
        MaintainPlanStatistics,
    },
    setup() {

        function handleRowClick(rowData) {
            orgId.value = rowData.orgId;
            initMicro3dMap(rowData.planId);
            setTimeout(() => {
                sectionId.value = [rowData.sectionId];
            }, 1000);
        }

        return {
            pendingPlanList,
            handleRowClick,
            maintainChartData,
        };
    },
};
</script>

<style lang="less" scoped>
/* 组件样式 */
.main-maintain-statistics {
    position: absolute;
    display: flex;
    flex-direction: column;
    left: 26px;
    top: 130px;
    bottom: 10px;
    overflow: auto;
}
</style>