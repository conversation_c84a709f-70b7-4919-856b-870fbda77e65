<template>
    <Card
        class="traffic-statistics-card"
        title="车流量统计"
        card-type="card-short-2"
    >
        <template #content>
            <div class="traffic-statistics-content">
                <el-row :gutter="6 * ratio">
                    <el-col
                        v-for="card in getCardInfo"
                        :key="card.title"
                        :span="card.span"
                        :class="{'mb-6': card.type === 1}"
                    >
                        <div v-if="card.type === 1" class="statistics-data-card">
                            <div class="statistics-data-card__inner">
                                <div class="statistics-data-card__left">
                                    <div>{{ card.title }}</div>
                                    <div>{{ card.enTitle }}</div>
                                </div>
                                <div class="statistics-data-card__right">
                                    <span>{{ card.value }}</span>
                                    <span>{{ card.unit }}</span>
                                </div>
                            </div>
                            <img class="traffic-cover" src="@/assets/images/tollStation/traffic-cover.svg">
                        </div>
                        <div v-else class="statistics-data-card2">
                            <div class="statistics-data-card2__top">
                                <div class="statistics-data-card2__icon">
                                    <div class="i-triangle"></div>
                                    <div class="i-triangle"></div>
                                    <div class="i-triangle"></div>
                                    <div class="i-triangle"></div>
                                    <span>{{ card.value }}</span>
                                </div>
                                <div class="statistics-data-card2__unit">
                                    {{ card.unit }}
                                </div>
                            </div>
                            <div class="statistics-data-card2__bottom">{{ card.title }}</div>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </template>
    </Card>
</template>

<script setup>
import {Card} from '@/components/Common';
import {Row as ElRow, Col as ElCol} from 'element-ui';
import {useUnit, formatCurrency} from '@/utils';
import {getMotherFlow} from '@/api/tollStation';
import {computed, onMounted, ref} from 'vue';

const {ratio} = useUnit();

const cardConfig = [
    {
        title: '月均通行车流量',
        enTitle: 'Traffic volume',
        field: 'motherFlow',
        unit: '万辆',
        type: 1,
        span: 24,
    },
    {
        title: '昨日总车流量',
        field: 'yesterFlow',
        unit: '万辆',
        type: 2,
        span: 12,
    },
    {
        title: '近七日总车流量',
        field: 'weekFlow',
        unit: '万辆',
        type: 2,
        span: 12,
    },
];

const cardData = ref({
    motherFlow: 0,
    weekFlow: 0,
    yesterFlow: 0,
});

const getCardInfo = computed(() => cardConfig.map(card => {
    return {
        ...card,
        value: cardData.value[card.field],
    };
}));

async function fetchData() {
    const {data} = await getMotherFlow();
    cardData.value = {
        motherFlow: formatCurrency(data.motherFlow),
        weekFlow: formatCurrency(data.weekFlow),
        yesterFlow: formatCurrency(data.yesterFlow),
    };
}

onMounted(() => {
    fetchData();
});

</script>

<style lang="less" scoped>
.traffic-statistics-content {
    height: 314px;
}

.traffic-statistics-card {
    &__bottom {
        margin-top: 8px;
        display: flex;
        justify-content: space-between;
    }

    .statistics-data-card {
        position: relative;

        &__inner {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 20px 20px 136px;
            height: 100px;
            background-image:
                linear-gradient(
                    to right,
                    #3377a4,
                    rgba(18, 74, 166, .8)
                );
            clip-path: polygon(0 0, 100% 0, 100% calc(100% - 8px), calc(100% - 8px) 100%, 0 100%);
        }

        &__left {
            flex-shrink: 0;
            font-size: 14px;
            color: rgba(#fff, .3);

            div:nth-child(1) {
                margin-bottom: 4px;
                font-size: 24px;
                color: #fff;
            }
        }

        &__right {
            font-size: 16px;
            color: rgba(#fff, .6);

            span:nth-child(1) {
                font-family: 'RoboData';
                font-size: 42px;
                color: #fff;
                margin-right: 4px;
            }
        }

        .traffic-cover {
            position: absolute;
            width: 138.5px;
            height: 99px;
            bottom: 8px;
            left: 24px;
        }
    }

    .statistics-data-card2 {
        position: relative;
        width: 274px;
        background-image:
            linear-gradient(
                to bottom,
                rgba(36, 104, 242, .3),
                rgba(36, 104, 242, 0)
            );

        &::before {
            content: '';
            position: absolute;
            width: 20px;
            height: 2px;
            background-color: rgb(0, 237, 255);
            clip-path: polygon(0 0, 100% 0, calc(100% - 1.4px) 100%, 1.4px 100%);
            top: 0;
            left: 50%;
            transform: translateX(-50%);
        }

        &__icon {
            position: relative;
            width: 200px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            justify-content: center;

            span {
                color: #fff;
                font-size: 42px;
                font-family: RoboData;
            }

            .i-triangle {
                position: absolute;
                width: 12px;
                height: 12px;
                background-color: rgba(#fff, .1);

                &:nth-of-type(1) {
                    top: 0;
                    left: 0;
                    clip-path: polygon(0 0, 100% 0, 0 100%);
                }

                &:nth-of-type(2) {
                    top: 0;
                    right: 0;
                    clip-path: polygon(0 0, 100% 0, 100% 100%);
                }

                &:nth-of-type(3) {
                    bottom: 0;
                    left: 0;
                    clip-path: polygon(0 0, 100% 100%, 0 100%);
                }

                &:nth-of-type(4) {
                    bottom: 0;
                    right: 0;
                    clip-path: polygon(100% 0, 100% 100%, 0 100%);
                }
            }
        }

        &__unit {
            font-size: 16px;
            margin-top: 8px;
        }

        &__top {
            display: flex;
            flex-direction: column;
            align-items: center;
            height: 148px;
            padding: 23px 0;
        }

        &__bottom {
            text-align: center;
            height: 60px;
            line-height: 60px;
            font-size: 24px;
            background-color: rgba(18, 74, 166, .2);
            backdrop-filter: blur(10px);
            color: #fff;
            border-top: 1px solid;
            border-image:
                linear-gradient(
                    to right,
                    rgba(1, 255, 229, .5),
                    rgb(36, 104, 242),
                ) 1;
        }
    }
}

</style>