<template>
    <el-dropdown trigger="click">
        <div class="user">
            <img class="user__avatar" src="@/assets/images/default_avatar.svg">
            <div class="user__nickname">{{ user?.realName }}</div>
        </div>
        <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>个人中心</el-dropdown-item>
            <el-dropdown-item>退出</el-dropdown-item>
        </el-dropdown-menu>
    </el-dropdown>
</template>
<script>
import {
    Dropdown,
    DropdownItem,
    DropdownMenu,
} from 'element-ui';
import {user, useUserInfo} from '@/store/user.js';
import {onMounted} from 'vue';

export default {
    components: {
        [Dropdown.name]: Dropdown,
        [DropdownItem.name]: DropdownItem,
        [DropdownMenu.name]: DropdownMenu,
    },
    setup() {
        onMounted(() => {
            useUserInfo();
        });

        return {
            user,
        };
    },
};
</script>

<style lang="less" scoped>
.user {
    display: flex;
    align-items: center;
    width: 132px;
    background-color: rgba(#fff, .1);
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;

    &__avatar {
        flex-shrink: 0;
        width: 24px;
        height: 24px;
    }

    &__nickname {
        flex: 1;
        margin-left: 6px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        color: rgba(#fff, .6);
    }
}
</style>