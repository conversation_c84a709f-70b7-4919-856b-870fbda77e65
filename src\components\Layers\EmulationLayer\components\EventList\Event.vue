<template>
    <div class="real-event">
        <div class="content">
            <el-button
                v-show="moduleType === 2 && !isEdit"
                type="primary"
                plain
                class="w-100% mb-10"
                @click="handleAdd"
            >
                <i class="el-icon-plus"></i>
                <span>新增事件</span>
            </el-button>
            <template v-if="!isEdit">
                <div
                    v-if="eventList.length > 0 || loading"
                    v-loading="loading"
                    class="event-list"
                >
                    <card-view
                        v-for="item in eventList"
                        :key="item.id"
                        :info="item"
                        :module-type="moduleType"
                        @onEdit="onEdit"
                        @getData="getData"
                        @addEvent="addTrafficEvent"
                    />
                </div>
                <div v-else class="no-data">
                    <no-data/>
                </div>
            </template>
            <template v-else>
                <card-edit
                    :detail="detail"
                    :is-edit="!!curEditId"
                    :emulation-type="emulationType"
                    @save="onSave"
                    @cancel="onCancel"
                    @addEvent="addTrafficEvent"
                />
            </template>
        </div>
        <!-- 施工区域渲染 -->
        <construction-area
            v-if="isConstruction"
            :line-manager="lineManager"
            :polygon-manager="polygonManager"
            :list="constructionList"
        />
    </div>
</template>

<script>
import {ref, onMounted, watch, computed} from 'vue';
import {Button} from 'element-ui';
import {fetchList, getPointsElevation, getConstructionGeo} from '@EmulationLayer/api';
import {evnet_location_type} from './config';
import {road_direction} from '@EmulationLayer/config';
import {eventManager, lineManager, polygonManager} from '@EmulationLayer/utils/Map';
import {findEventType} from '@EmulationLayer/utils/index';
import {viewTo} from '@/utils';
import NoData from '@EmulationLayer/components/NoData.vue';
import CardView from './Card/view.vue';
import CardEdit from './Card/edit.vue';
import ConstructionArea from '../EmulationTwin/ConstructionArea.vue';
import {createConfig} from '../../store/emulationCreate';
import {defaultCenter} from '../../store';

export default {
    components: {NoData, CardView, CardEdit, [Button.name]: Button, ConstructionArea},
    name: 'RealEvet',
    props: {
        moduleType: {
            type: Number,
            default: 1,
        },
        eventType: {
            type: String,
            default: '',
        },
        dateRange: {
            type: String,
            default: '',
        },
        emulationType: {
            type: [String, Number],
            default: 1,
        },
    },
    filters: {
        formatEventType(val) {
            const cur = findEventType(val);
            return cur ? cur.name : '';
        },
        formatEventLocation(val) {
            const cur = evnet_location_type.find(item => item.code === val);
            return cur ? cur.name : '';
        },
        formatDirection(val) {
            const cur = road_direction.find(item => item.code === val);
            return cur ? cur.name : '';
        },
        formatePosition(item) {
            return item.highSpeedNameCn ? item.highSpeedNameCn + '-' : '' + item.eventStartStake;
        },
    },
    methods: {
        // 俯视角对齐坐标
        planeAnimationTo(longitude, latitude) {
            viewTo({
                zoom: 20,
                center: [longitude, latitude],
            }, 1000);
        },
    },
    setup(props, {emit}) {
        const isEdit = ref(false);
        const loading = ref(false);
        const actionType = ref(null);
        const eventList = ref([]);
        const curEditId = ref(null);
        const detail = ref({});
        const iconName = ref();
        const isConstruction = computed(() => props.emulationType === 3);
        // 施工区域边界值
        const constructionList = ref([]);

        // 获取施工区域边界值坐标点集合
        const fetchConstructionList = async params => {
            const {data} = await getConstructionGeo(params);
            const [constructionGeoList = []] = data.constructionGeoList;
            const formatList = constructionGeoList.map(item => item.split(','));
            const [first] = formatList;
            first && formatList.push(first);
            constructionList.value = formatList;
        };

        // 获取事件列表
        const getData =  async () => {
            loading.value = true;
            const {moduleType, eventType, dateRange, emulationType} = props;
            try {
                eventList.value = [];
                const res = await fetchList({
                    eventType,
                    emulationType,
                    eventCategory: moduleType,
                    startDate: dateRange ? dateRange[0] : '',
                    endDate: dateRange ? dateRange[1] : '',
                });
                eventList.value = res.data;
            }
            finally {
                loading.value = false;
            }
        };

        // 新增事件
        const handleAdd = () => {
            const {moduleType} = props;
            isEdit.value = true;
            actionType.value = 'add';
            curEditId.value = '';
            detail.value.eventCategory = moduleType;
            // bus.$emit('clearLane', []);
        };

        // 编辑事件
        const onEdit = val => {
            curEditId.value = val.id;
            isEdit.value = true;
            actionType.value = 'edit';
            detail.value = val;
        };

        const onSave = () => {
            isEdit.value = false;
            getData();
        };

        const onCancel = () => {
            isEdit.value = false;
        };

        // 地图事件扎点 需要移除上一个的事件点
        const addTrafficEvent = async info => {
            const {emulationType} = props;
            const cur = findEventType(info.eventType, emulationType);
            const eventName = cur ? cur.name : '';
            iconName.value = eventName;
            eventManager.removeEventPointByName(eventName);

            if (!info) {
                return;
            }
            let eventPositionArr = info.eventPosition.split(',');
            let position = [Number(eventPositionArr[0]), Number(eventPositionArr[1])];

            const response = await getPointsElevation(position);
            const h = response.data?.[0] || 0;
            position = [position[0], position[1], h];
            viewTo({
                zoom: 15,
                center: position,
            }, 1000);

            eventManager.addEventPoint(
                eventName,
                position,
                {
                    labelText: eventName,
                }
            );

            // 施工区域类型则获取模仿施工区域边界值数据
            if (isConstruction.value) {
                fetchConstructionList({
                    highSpeedName: info.highSpeedName,
                    startStake: info.eventStartStake,
                    location: info.eventPosition,
                    closeLane: info.closeLane,
                    upstreamTransitionLength: info.upstreamTransitionLength,
                    constructionLength: info.constructionLength,
                    downstreamTransitionLength: info.downstreamTransitionLength,
                    // 'highSpeedName': 'G0423',
                    // 'startStake': 'K250+500',
                    // 'location': '113.264234,23.592922',
                    // 'closeLane': '1,2,3',
                    // 'upstreamTransitionLength': 100,
                    // 'constructionLength': 100,
                    // 'downstreamTransitionLength': 100,
                });
            }

            return {
                eventName,
                position,
            };
        };

        const removeTrafficEvent = () => {
            iconName.value && eventManager.removeEventPointByName(iconName.value);
        };

        watch(
            () => [
                props.eventType,
                props.dateRange,
                props.moduleType,
                props.emulationType,
            ],
            () => {
                getData();
            });

        watch(
            () => props.emulationType,
            () => {
                removeTrafficEvent();
                isEdit.value = false;
            }
        );

        const stopWatchEventList = watch(
            () => eventList.value,
            async newVal => {
                const first = newVal?.[0];
                if (!first) return;
                const {position} = await addTrafficEvent(first);
                defaultCenter.value = position;
                stopWatchEventList();
            }
        );

        watch(
            () => createConfig.value.show,
            val => {
                if (val) {
                    removeTrafficEvent();
                    return;
                }
                const first = eventList.value?.[0];
                if (!first) return;
                addTrafficEvent(first);
            }
        );


        onMounted(getData);

        return {
            isEdit,
            loading,
            actionType,
            eventList,
            curEditId,
            detail,
            getData,
            handleAdd,
            onEdit,
            onSave,
            onCancel,
            addTrafficEvent,
            isConstruction,
            lineManager,
            polygonManager,
            constructionList,
            fetchConstructionList,
        };
    },
};
</script>

<style scoped lang="less">
.real-event {
    display: flex;
    flex-direction: column;
    height: 100%;

    .content {
        font-family: 'FZLTZHJW--GB1-0', sans-serif;
        margin-top: 8px;
        flex: 1;
        overflow-y: auto;

        .event-list {
            min-height: 336px;
            position: relative;
            display: flex;
            gap: 8px;
            flex-direction: column;
        }

        .no-data {
            background: rgba(32, 72, 63, .6);
            height: 336px;
        }
    }

    .el-button--primary.is-plain {
        border-color: #07af87;
        color: #07af87;

        &:hover {
            background: #07af87;
            color: #fff;
        }
    }
}

</style>