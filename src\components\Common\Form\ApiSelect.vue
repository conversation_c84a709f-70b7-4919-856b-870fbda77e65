<template>
    <el-select
        v-bind="getProps"
        :value="value"
        :loading="loading"
        @visible-change="visibleChange"
        @change="handleChange"
    >
        <el-option
            v-for="option in getOptions"
            :key="option.value"
            :value="option.value"
            :label="option.label"
            :disabled="option.disabled"
        />
    </el-select>
</template>

<script>
import {Select, Option} from 'element-ui';
import {computed, ref, unref, watch} from 'vue';
import {isFunction, get, isEqual, isEmpty} from 'lodash';

export default {
    components: {
        [Select.name]: Select,
        [Option.name]: Option,
    },
    props: {
        // 原el-select props
        props: Object,
        value: [String, Number, Boolean, Array],
        // api
        api: Function,
        // api 函数参数， 仅对 api prop 不为空时生效
        params: Object,
        // 仅对 api prop 不为空时生效
        resultField: {
            type: String,
            default: 'data',
        },
        // 仅对 api prop 不为空时生效
        labelField: {
            type: String,
            default: 'label',
        },
        // 仅对 api prop 不为空时生效
        valueField: {
            type: String,
            default: 'value',
        },
        // 是否立即发起请求, 仅对 api prop 不为空时生效
        immediate: {
            type: Boolean,
            default: true,
        },
        // 是否每次点击下拉框重新获取数据, 仅对 api prop 不为空时生效
        droupDownFetch: Boolean,
        // 格式化处理options函数, 仅对 api prop 不为空时生效
        formatOptions: {
            type: Function,
        },
        // 选项值，仅对 api prop 为空时生效
        options: {
            type: Array,
        },
        // 是否默认选中第一项
        defaultSelectedFirst: Boolean,
    },
    setup(props, {emit, attrs}) {
        const isFirstFetch = ref(true);
        const apiOptions = ref([]);
        const loading = ref(false);
        const getProps = computed(() => ({
            ...props.props,
            ...attrs,
        }));
        const getOptions = computed(() => {
            if (isFunction(props.api)) return handleOptions(apiOptions.value);
            return props.options;
        });

        function handleOptions(options) {
            const opts =  options.map(option => {
                return {
                    label: props.labelField ? get(option, props.labelField) : option,
                    value: props.valueField ? get(option, props.valueField) : option,
                };
            });
            const formatOptions = props.formatOptions;
            return isFunction(formatOptions) ? formatOptions(opts) : opts;
        }

        watch(
            () => getOptions.value,
            (newVal, oldVal) => {
                if (!props.defaultSelectedFirst) return;
                if (isEqual(newVal, oldVal) || isEmpty(newVal)) return;
                if (!props.value) {
                    const [firstOption] = newVal;
                    emit('change', firstOption);
                    emit('input', firstOption.value);
                }
            },
            {
                immediate: true,
                deep: true,
            }
        );

        watch(
            () => props.immediate,
            val => {
                if (val) {
                    fetchOptions();
                }
            },
            {
                immediate: true,
            }
        );

        watch(
            () => props.params,
            (newVal, oldVal) => {
                if (isEqual(newVal, oldVal)) return;
                fetchOptions();
            },
            {
                immediate: props.immediate,
                deep: true,
            }
        );

        async function fetchOptions() {
            loading.value = true;
            try {
                const {
                    api,
                    resultField,
                    params = {},
                } = props;
                if (!api || !isFunction(api)) return;
                const apiResult = await api(params);
                apiOptions.value = resultField ? get(apiResult, resultField, []) : apiResult;
                isFirstFetch.value = false;
                emit('options-change', apiOptions.value);
            }
            catch (error) {
                emit('fetch-error', error);
            }
            finally {
                loading.value = false;
            }
        }

        function visibleChange(visible) {
            // 隐藏不获取
            if (!visible) return;
            // 非首次加载且非每次下拉获取
            if (!isFirstFetch.value && !props.droupDownFetch) return;
            fetchOptions();
        }

        function handleChange(e) {
            const selectItem = unref(getOptions).find(i => i.value === e);
            emit('change', selectItem || {value: e});
            emit('input', e);
        }

        return {
            getProps,
            loading,
            getOptions,
            visibleChange,
            handleChange,
        };
    },
};
</script>