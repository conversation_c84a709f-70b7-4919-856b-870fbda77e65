<template>
    <Card
        class="revenue-analysis-card"
        title="收费站运营统计分析"
        card-type="card-long-2"
    >
        <template #content>
            <el-row :gutter="ratio * 16">
                <!-- 渐变卡片 -->
                <el-col
                    v-for="gradientCard in getCardInfo"
                    :key="gradientCard.title"
                    :span="8"
                >
                    <div class="statistics-data-card">
                        <div class="statistics-data-card__top">
                            <div class="statistics-data-card__icon">
                                <div class="i-triangle"></div>
                                <div class="i-triangle"></div>
                                <div class="i-triangle"></div>
                                <div class="i-triangle"></div>
                                <span>{{ gradientCard.value }}</span>
                            </div>
                            <div class="statistics-data-card__unit">
                                {{ gradientCard.unit }}
                            </div>
                        </div>
                        <div class="statistics-data-card__bottom">{{ gradientCard.title }}</div>
                    </div>
                </el-col>
            </el-row>
        </template>
    </Card>
</template>

<script setup>
import {Card} from '@/components/Common';
import {computed, onMounted, ref} from 'vue';
import {Row as ElRow, Col as ElCol} from 'element-ui';
import {formatCurrency, useUnit} from '@/utils';
import {getTollRevenue} from '@/api/tollStation';
import {tollStation} from '@/views/TollStation/store';

const {ratio} = useUnit();

const cardData = ref({
    f1: 43.2,
    f2: 9421,
    ff1: 1000,
});

const cardConfig = [
    {
        field: 'totalFee',
        title: '收费金额',
        unit: '万元',
    },
    {
        field: 'flow',
        title: '车流量',
        unit: '辆',
    },
    {
        field: 'greenFlow',
        title: '绿通数据',
        enTitle: 'Small car',
        unit: '辆',
    },
];

const getCardInfo = computed(() => (
    cardConfig.map(item => ({
        ...item,
        value: cardData.value[item.field],
    }))
));

async function fetchData() {
    const {data} = await getTollRevenue({
        stationId: tollStation.value.stationId,
        stationName: tollStation.value.stationName,
    });
    cardData.value = {
        totalFee: formatCurrency(data.totalFee),
        flow: formatCurrency(data.flow),
        greenFlow: formatCurrency(data.greenFlow || 0),
    };
}

onMounted(() => {
    fetchData();
});




</script>

<style lang="less" scoped>
.revenue-analysis-card {
    .gradient-card {
        height: 76px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-left: 18px;
        padding-right: 24px;
        background-image:
            linear-gradient(
                to right,
                rgba(12, 184, 208, .5),
                rgba(18, 74, 166, .3)
            );

        &__left {
            position: relative;
            height: 32px;
            line-height: 32px;
            padding: 0 8px;
            text-align: center;
            margin-left: 4.2px;
            background-image:
                linear-gradient(
                    -45deg,
                    rgb(36, 104, 242),
                    rgba(1, 255, 229, .5),
                );

            &::before {
                content: '';
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                left: -4.2px;
                width: 2.2px;
                height: 32px;
                background-color: rgba(1, 255, 229, .5);
            }

            &::after {
                content: '';
                position: absolute;
                top: 50%;
                right: -276px;
                width: 270px;
                height: 1px;
                background-image:
                    linear-gradient(
                        to right,
                        #fff,
                        rgba(#fff, 0)
                    );
                transform: translateY(-50%);
            }
        }

        &__right {
            font-size: 14px;

            span:first-child {
                font-family: RoboData;
                font-size: 42px;
                margin-right: 2px;
                color: #fff;
            }
        }
    }

    .single-card {
        height: 80px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 24px;
        background-color: rgba(18, 74, 166, .8);
        backdrop-filter: blur(10px);

        &__left {
            div:nth-child(1) {
                font-size: 18px;
                color: #fff;
            }

            div:nth-child(2) {
                margin-top: 2px;
                font-size: 14px;
                text-transform: uppercase;
                color: rgba(#fff, .3);
            }
        }

        &__right {
            font-size: 14px;

            span:first-child {
                font-family: RoboData;
                font-size: 32px;
                margin-right: 2px;
                color: #fff;
            }
        }

        &__num {
            font-size: 14px;
            color: rgba(#fff, .3);

            span:first-child {
                font-family: RoboData;
                font-size: 32px;
                margin-right: 2px;
                color: #fff;
            }
        }

        &__status {
            display: flex;
            align-items: center;
            padding-left: 16px;
            font-size: 20px;

            &::before {
                content: '';
                width: 8px;
                height: 8px;
                border: 1px solid #fff;
                border-radius: 50%;
                margin-right: 8px;
                margin-top: 4px;
            }

            &-success {
                color: rgb(0, 255, 114);

                &::before {
                    background-color: rgb(0, 255, 114);
                }
            }
        }
    }
}

.statistics-data-card {
    position: relative;
    width: 384px;
    background-image:
        linear-gradient(
            to bottom,
            rgba(36, 104, 242, .3),
            rgba(36, 104, 242, 0)
        );

    &::before {
        content: '';
        position: absolute;
        width: 50px;
        height: 2px;
        background-color: rgb(0, 237, 255);
        clip-path: polygon(0 0, 100% 0, calc(100% - 1.4px) 100%, 1.4px 100%);
        top: 0;
        left: 50%;
        transform: translateX(-50%);
    }

    &__icon {
        position: relative;
        width: 200px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        justify-content: center;

        span {
            color: #fff;
            font-size: 42px;
            font-family: RoboData;
        }

        .i-triangle {
            position: absolute;
            width: 12px;
            height: 12px;
            background-color: rgba(#fff, .1);

            &:nth-of-type(1) {
                top: 0;
                left: 0;
                clip-path: polygon(0 0, 100% 0, 0 100%);
            }

            &:nth-of-type(2) {
                top: 0;
                right: 0;
                clip-path: polygon(0 0, 100% 0, 100% 100%);
            }

            &:nth-of-type(3) {
                bottom: 0;
                left: 0;
                clip-path: polygon(0 0, 100% 100%, 0 100%);
            }

            &:nth-of-type(4) {
                bottom: 0;
                right: 0;
                clip-path: polygon(100% 0, 100% 100%, 0 100%);
            }
        }
    }

    &__unit {
        font-size: 16px;
        margin-top: 8px;
    }

    &__top {
        display: flex;
        flex-direction: column;
        align-items: center;
        height: 148px;
        padding: 23px 0;
    }

    &__bottom {
        text-align: center;
        height: 60px;
        line-height: 60px;
        font-size: 24px;
        background-color: rgba(18, 74, 166, .2);
        backdrop-filter: blur(10px);
        color: #fff;
        border-top: 1px solid;
        border-image:
            linear-gradient(
                to right,
                rgba(1, 255, 229, .5),
                rgb(36, 104, 242),
            ) 1;
    }
}
</style>