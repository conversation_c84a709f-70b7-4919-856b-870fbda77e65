<template>
    <div class="drop-down-selector">
        <div class="select-box">
            <div v-if="placeholder" class="select-content">
                {{
                    multiple
                        ? labels.join(', ') || placeholder
                        : activeItem?.label || placeholder
                }}
            </div>
            <div v-else class="select-content">
                <span>{{ title }}：</span>
                <span>{{
                    multiple
                        ? labels.join(', ') || activeItem.label
                        : activeItem.label
                }}</span>
            </div>
            <div class="el-icon-arrow-down"></div>
        </div>
        <!-- 级联 -->
        <template v-if="cascade">
            <div class="dropdown-container">
                <!-- 可展开盒子 -->
                <div class="choose-box">
                    <div
                        class="dropdown-box show-scroll"
                        :style="{height: height + 'px'}"
                    >
                        <template v-if="optionData.length">
                            <div
                                v-for="item in optionData"
                                :key="item.value"
                                class="dropdown-item"
                                :class="[
                                    firstLevelActive === item.value ? 'active' : '',
                                ]"
                                @click="onItemExpand(item)"
                            >
                                {{ item.label }}
                                <i
                                    class="arrow-right-icon"
                                    :class="[
                                        firstLevelActive === item.value
                                            ? 'rotate'
                                            : '',
                                    ]"
                                ></i>
                            </div>
                        </template>
                        <no-data v-else/>
                    </div>
                    <i class="bottom-icon"></i>
                </div>
                <!-- 拓展框 -->
                <div
                    v-if="firstLevelActive !== -1 && optionData.length"
                    class="final-box"
                    :style="{height: height + 'px'}"
                >
                    <div
                        v-for="item in finalOptions"
                        :key="item.value"
                        class="final-item"
                        :class="[activeItem === item ? 'active' : '']"
                        @click="onItemClick(item)"
                    >
                        {{ item.label }}
                    </div>
                </div>
            </div>
        </template>
        <template v-else>
            <div class="dropdown-container">
                <div class="choose-box">
                    <div
                        class="dropdown-box show-scroll"
                        :style="{height: height + 'px'}"
                    >
                        <template v-if="optionData.length">
                            <div
                                v-for="item in optionData"
                                :key="item.value"
                                class="dropdown-item"
                                :class="[
                                    multiple
                                        ? vals.includes(item.value)
                                            ? 'active'
                                            : ''
                                        : activeItem === item
                                            ? 'active'
                                            : '',
                                ]"
                                @click="onItemClick(item)"
                            >
                                {{ item.label }}
                            </div>
                        </template>
                        <no-data v-else/>
                    </div>
                    <i class="bottom-icon"></i>
                </div>
            </div>
        </template>
    </div>
</template>
<script>
import {ref, watch, onBeforeMount, computed} from 'vue';
import {NoData} from '@/components/Common/index';
export default {
    components: {
        NoData,
    },
    props: {
        title: {
            type: String,
            default: '统计指标',
        },
        // optionData
        list: {
            type: Array,
            default: () => [],
        },
        // 双向绑定的值
        value: {
            type: Number,
            default: -1,
        },
        // 自定义下拉框高度
        height: {
            type: Number,
            default: 157,
        },
        placeholder: {
            type: String,
        },
        // 支持多选
        multiple: {
            type: Boolean,
            default: false,
        },
        // 切换成级联选择器
        cascade: {
            type: Boolean,
            default: false,
        },
    },
    setup(props, {emit}) {
        // 下拉框数据
        const optionData = computed(() => props.list);
        // 当前活跃的选项(单选)
        const activeItem = ref(null);
        // 当前活跃的选项(多选)
        const activeItems = ref([]);
        // 多选value的集合
        const vals = ref([]);
        // 多选label集合
        const labels = ref([]);
        const addVal = val => {
            vals.value.push(val.value);
            labels.value.push(val.label);
            activeItems.value.push(val);
        };
        const removeVal = val => {
            vals.value = vals.value.filter(item => item !== val.value);
            labels.value = labels.value.filter(item => item !== val.label);
            activeItems.value = activeItems.value.filter(item => item !== val);
        };
        const isEmpty = computed(() => {
            return props.list.length === 0;
        });

        // 首次进入
        let isStatusChange = ref(false);
        watch(
            () => [activeItem?.value, vals.value],
            ([single, multi]) => {
                if (props.multiple) {
                    emit('update:value', vals.value);
                }
                else if (!props.placeholder || (isStatusChange && single)) {
                    emit('update:value', activeItem.value.value);
                }
            },
            {deep: true}
        );

        // 选中item
        const onItemClick = item => {
            isStatusChange.value = true;
            if (props.multiple) {
                if (vals.value.includes(item.value)) {
                    removeVal(item);
                }
                else {
                    addVal(item);
                }
                return emit('change', activeItem.value, activeItems.value);
            }
            activeItem.value = item;
            emit('change', activeItem.value);
        };

        // 点击展开item
        const firstLevelActive = ref(-1);
        const finalOptions = ref([]);
        const onItemExpand = item => {
            firstLevelActive.value = item.value;
            finalOptions.value = item.children;
        };
        // 初始化
        const init = () => {
            // 当前活跃的选项(单选)
            activeItem.value = null;
            // 当前活跃的选项(多选)
            activeItems.value = [];
            // 多选value的集合
            vals.value = [];
            // 多选label集合
            labels.value = [];
            // 拓展框
            firstLevelActive.value = -1;
            finalOptions.value = [];
            if (!props.placeholder) {
                activeItem.value = props.list[0];
                props.value = props.list[0].value;
                if (props.multiple) {
                    addVal(props.list[0]);
                }
            }
        };
        onBeforeMount(() => {
            init();
        });

        watch(() => optionData.value, val => {
            init();
        });
        return {
            optionData,
            activeItem,
            isStatusChange,
            activeItems,
            labels,
            vals,
            firstLevelActive,
            finalOptions,
            isEmpty,
            onItemClick,
            onItemExpand,
            init,
        };
    },
};
</script>
<style lang="less" scoped>
.drop-down-selector {
    position: relative;
    width: 256px;
    box-sizing: border-box;
    color: rgb(255, 255, 255);

    .el-icon-arrow-down,
    .arrow-right-icon {
        transition: all .3s;
    }

    .select-box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 30px;
        padding: 0 12px;
        border: .5px solid rgb(92, 244, 195);
        border-radius: 3px;
        font-family: PingFang SC;
        font-size: 12px;
        font-weight: 400;
        background:
            linear-gradient(
                -66.16deg,
                rgba(34, 120, 105, .58) 7.662%,
                rgba(15, 103, 74, .62) 94.257%
            );
        cursor: pointer;

        .select-content {
            width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    }

    &:hover {
        .dropdown-container,
        .dropdown-box,
        .bottom-icon {
            transform: scale(1);
            opacity: 1;
            transition: opacity .3s;
        }

        .el-icon-arrow-down {
            transform: rotate(180deg);
        }
    }

    .dropdown-container {
        display: flex;
        z-index: 9999;
        position: absolute;
        left: 0;
        top: 41px;
        transform: scale(0);
        opacity: 0;
        transition: opacity .3s, transform 0s linear .3s;

        .choose-box {
            position: relative;
            margin-right: 10px;

            .dropdown-box {
                .dropdown-item {
                    position: relative;
                    height: 39px;
                    padding-left: 14px;
                    line-height: 39px;
                    cursor: pointer;

                    .arrow-right-icon {
                        position: absolute;
                        right: 10px;
                        top: calc(50% - 5px);
                        width: 9px;
                        height: 10px;
                        background-image: url('@/assets/images/arrow-right-icon.png');
                        background-size: 100% 100%;
                    }

                    &:hover {
                        background-color: rgba(15, 103, 74, .72);
                    }
                }

                .rotate {
                    transform: rotate(180deg);
                }
            }
        }

        .final-box {
            .final-item {
                height: 39px;
                padding-left: 14px;
                line-height: 39px;
                cursor: pointer;

                &:hover {
                    background-color: rgba(15, 103, 74, .72);
                }
            }
        }
    }

    .active {
        color: rgb(0, 244, 178);
        background-color: rgba(15, 103, 74, .72);
    }

    .final-box,
    .dropdown-box {
        box-sizing: border-box;
        width: 256px;
        // height: 156px;
        font-weight: 500;
        background: rgba(32, 72, 63, .78);
        border-bottom: 1px solid rgb(29, 218, 189);
        overflow-y: scroll;

        &::-webkit-scrollbar {
            /* 设置垂直滚动条宽度 */
            display: block;
            width: 2px;
            height: 10px;
        }

        &::-webkit-scrollbar-thumb {
            /* 滚动条里面小方块 */
            border: 4px solid rgb(29, 218, 189);
        }

        &::-webkit-scrollbar-track {
            /* 滚动条里面轨道 */
            background: rgba(239, 255, 255, .2);
        }
    }

    .bottom-icon {
        z-index: 9999;
        position: absolute;
        right: 0;
        bottom: -8px;
        width: 59px;
        height: 8px;
        content: '';
        background-image: url('@/assets/images/bottom_icon.png');
        background-size: 100% 100%;
        transform: scale(0);
        opacity: 0;
        transition: opacity .3s, transform 0s linear .3s;
    }
}
</style>
