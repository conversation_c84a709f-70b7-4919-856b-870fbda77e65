<template>
    <div class="fc c-50 fs-12 pr full">
        <div v-if="data.length" class="fr jsb h-24 pt-6">
            <span>{{ yName }}</span>
            <div
                v-if="data.length > 1"
                class="fr gap-15"
            >
                <div
                    v-for="item in data" :key="item.name"
                    class="fr ac gap-5"
                >
                    <span
                        class="dib w-10 h-10 b-50%"
                        :style="{
                            backgroundColor: item.color,
                        }"
                    ></span>
                    <span>{{ item.name }}</span>
                </div>
            </div>
        </div>
        <div class="f-1">
            <div
                v-show="data.length"
                ref="chartRef" style="position: absolute;"
                class="l-0 t-30 r-0 b-0"
            ></div>
        </div>
        <Empty v-if="!data.length"/>
    </div>
</template>
<script setup>
import {ref, computed, watch} from 'vue';
import useChartCommon from './useChartCommon';
import {tooltipFormatter} from './util';
import {Empty} from 'ant-design-vue';
import {echarts} from './common';
import {pxToRem} from '@/utils/util';
const {ratio} = pxToRem();

const props = defineProps({
    yName: {
        type: String,
    },
    data: {
        type: Array,
        default: () => [],
    },
    xAxis: {
        type: Array,
        default: () => [],
    },
    grid: {
        type: Array,
        default: () => [6, 0, 0, 0], // [top, right, bottom, left]
    },
    tooltipFormatter: {
        type: Function,
        default: tooltipFormatter,
    },
});

const step = ref(5);
const fontColor = 'rgba(255, 255, 255, .5)';
const markArea = ref([]);
const chartRef = ref(null);
const position = computed(() => props.grid.map(num => num * ratio.value));

const getOptions = () => {
    const max = Math.ceil(Math.max(...props.data.map(items => items.data.map(item => {
        const {value} = item;
        return value ?? item;
    })).flat(), 1));
    const s = Number((max / 5).toFixed(1));
    step.value = s > 1 ? Math.ceil(s) : s;
    const xAxis = props.data[0] ? props.data[0].data.map(item => item.name) : [];
    return {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                lineStyle: {
                    color: 'rgb(255, 255, 255)',
                    width: 2,
                    type: 'solid',
                },
            },
            className: 'statistics-chart-tooltip',
            position: function (point, params, dom, rect, size) {
                const {contentSize, viewSize} = size;
                let x;
                if (point[0] + contentSize[0] < viewSize[0]) {
                    x = Math.min(point[0], viewSize[0] - contentSize[0]);
                }
                else {
                    x = point[0] - contentSize[0];
                }
                // 固定在顶部
                return [x, 3];
            },
            formatter: props.tooltipFormatter,
        },
        grid: {
            top: position.value[0],
            right: position.value[1],
            bottom: position.value[2],
            left: position.value[3],
            containLabel: true,
        },
        xAxis: {
            type: 'category',
            show: true,
            axisTick: {
                show: false,
            },
            axisLabel: {
                interval: 0,
                formatter: (_, index) => {
                    return props.xAxis[index] ?? xAxis[index];
                },
                fontSize: 12 * ratio.value,
                color: fontColor,
            },
            axisPointer: {
                type: 'shadow',
                shadowStyle: {
                    color: new echarts.graphic.LinearGradient(
                        0,
                        0,
                        0,
                        1,
                        [
                            {
                                offset: 0,
                                color: 'rgba(255,255,255)',
                            },
                            {
                                offset: 1,
                                color: 'rgba(255,255,255,0)',
                            },
                        ],
                        false
                    ),
                    opacity: 0.3,
                },
            },
        },
        yAxis: {
            type: 'value',
            min: 0,
            max: max,
            interval: step.value,
            splitNumber: 5,
            splitLine: {
                show: true,
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.1)',
                },
            },
            axisLabel: {
                show: true,
                fontSize: 12 * ratio.value,
                color: fontColor,
            },
        },
        series: [
            ...props.data.map(item => {
                const {name, color, data} = item;
                return {
                    name,
                    type: 'bar',
                    data,
                    barWidth: 24,
                    itemStyle: {
                        color,
                    },
                    markLine: {
                        silent: true,
                        symbol: 'none',
                        animation: false,
                        data: [{
                            yAxis: 0,
                            lineStyle: {
                                color: 'rgba(255, 255, 255, .76)',
                                type: 'solid',
                                width: 1,
                            },
                            label: {
                                show: false,
                            },
                        }],
                    },
                };
            }),
        ],
    };
};

const {forceRender} = useChartCommon(chartRef, {
    getOptions,
});

watch(markArea, () => forceRender());

watch(() => [
    props.data,
    props.xAxis,
    props.yName,
    props.tooltipFormatter,
    ratio.value,
    position.value,
], () => {
    forceRender();
});
</script>
<style lang="less" scoped>
.c-50 {
    color: rgba(255, 255, 255, .5);
}

:deep(.ant-empty) {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}
</style>
<style lang="less">
@import './util.less';
.statistics-chart-tooltip {
    #chart-tooltip();
}
</style>