<template>

    <div class="block">
        <el-carousel
            :autoplay="false" trigger="click"
        >
            <el-carousel-item
                v-for="item in 4" :key="item"
                class="carouser"
                label="2222"
            >
                <!-- 轮播组件 -->
                <div class="carouser-item">
                    <div
                        v-for="item in CarImgList" :key="item"
                        class="img-item" :name="item.title"
                        :lable="item.title"
                    >
                        <img :src="item.url" alt="">

                        <div class="hover">
                            <div class="info">
                                <p>{{ item.title }}</p>
                                <div>{{ item.time }}</div>
                            </div>
                            <div class="buttom" @click="handleClick(item.url)">
                                <img src="@/assets/images/serviceM/amp.png" alt="">
                            </div>
                        </div>
                    </div>
                </div>

            </el-carousel-item>
            <el-dialog
                :visible="showDailog" :destroy-on-close="true"
                :close-on-click-modal="false"
                :close-on-press-escape="false" custom-class="dailog-box"
                @close="showDailog = false"
            >
                <div class="dailog-content">
                    <img
                        class="imgitem" :src="url"
                        alt=""
                    >
                </div>

            </el-dialog>
        </el-carousel>
    </div>
</template>
<script>
import {Carousel, CarouselItem, Dialog} from 'element-ui';
import {CarImgList} from '@/config/serviceArea';
import img3 from '@/assets/images/serviceM/3.jpg';

import {ref} from 'vue';
export default {
    props: {

    },
    components: {
        [Dialog.name]: Dialog,
        ElCarousel: Carousel,
        ElCarouselItem: CarouselItem,
    },

    setup(props, {emit}) {
        const showDailog = ref(false);
        const url = ref('');
        const data = [
            {},
        ];
        const handleClick = e => {
            url.value = e;
            showDailog.value = true;
            // console.log('output->url', e);
        };
        return {
            showDailog,
            handleClick,
            url,
            img3,
            CarImgList,
        };
    },
};
</script>
<style scoped lang="less">
:deep(.el-carousel__indicators) {
    display: none;
}

.el-carousel__item {
    width: 100%;
}

:deep(.dailog-box) {
    width: 610px;

    .el-dialog__header {
        height: 50px;
    }

    .dailog-content {
        display: flex;
        justify-content: center;
        align-items: center;

        .imgitem {
            width: 100%;
            height: 100%;
        }
    }
}

.el-carousel__item {

}

.el-carousel__arrow--right,
.el-carousel__arrow--left {
    margin-top: 30px;
}

.el-carousel__arrow--right:hover,
.el-carousel__arrow--left:hover {
}

.carouser-item {
    display: flex;
    justify-content: space-around;
    align-items: center;

    .hover {
        display: none;
    }

    .img-item {
        margin-top: 60px;
        position: relative;
        width: 268px;
        height: 150px;

        &:hover {
            .hover {
                display: block;
            }
        }

        img {
            height: 100%;
            width: 100%;
        }

        .info {
            position: absolute;
            top: 8px;
            left: 8px;
            border-radius: 4px;
            padding: 4px 8px;

            p {
                color: #fff;
                margin-bottom: 4px;
            }
        }

        .buttom {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 24px;
            cursor: pointer;
            height: 24px;

            img {
                width: 100%;
                height: 100%;
            }
        }
    }
}
</style>