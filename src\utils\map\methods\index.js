import {FlyManager} from '@baidu/mapv-three';
import {engine} from '@/store/engine';

const isLegalPoint = point => {
    if (!point || !Array.isArray(point) || point?.length < 2) {
        return false;
    }
    const [lng, lat] = point;
    return lng > -180 && lng < 180 && lat > -90 && lat < 90;
};

// 视角移动
export const viewTo = (viewParams, duration = 2000, _engine = engine.value) => {
    const {zoom = 20, center, pitch = 70, heading = 60, key = 'engine'} = viewParams;
    const is_engine = key === 'engine';
    if (!isLegalPoint(center) || !_engine) {
        return;
    }

    if (!_engine.FLYMANAGER) {
        _engine.FLYMANAGER = _engine.add(new FlyManager());
    }

    const _flyManager =  _engine.FLYMANAGER;
    _flyManager.flyTo({
        // zoom: 12,
        range: Math.pow(4, 20 - zoom) + 10, // 3dMap使用这个参数， 意思为目标点和相机的距离
        center,
        pitch,
        heading,
    }, {
        duration,
    });
};

// 移动到宏观视角
export const viewToMac = (center, _engin = engine.value) => {
    viewTo({
        zoom: 10,
        pitch: 30,
        heading: 10,
        center: center || [113, 22.954321],
    }, 1000, _engin);
};

// 移动到中观视角
export const viewToMid = (center, _engin = engine.value) => {
    viewTo({
        zoom: 11.5,
        pitch: 30,
        heading: 10,
        center,
    }, 1000, _engin);
};

// 移动到微观视角 - 二维
export const viewToMicroTwo = (center, _engin = engine.value) => {
    if (!center) {
        return;
    }
    viewTo({
        zoom: 12.5,
        center,
    }, 1000, _engin);
};

// 移动到微观视角 - 三维
export const viewToMicro = (center, _engin = engine.value) => {
    if (!center) {
        return;
    }
    viewTo({
        zoom: 15.5,
        center,
    }, 1000, _engin);
};

// 移动到扎点
export const viewToPoint = (center, _engin = engine.value) => {
    if (!center) {
        return;
    }
    viewTo({
        zoom: 17,
        center,
    }, 1000, _engin);
};
// 设置地图居中并设置zoom
export const setMapCenterAndZoom = (center, zoom = 10.15, _engine = engine.value) => {
    if (center) {
        viewTo({
            zoom,
            center,
        }, 700, _engine);
    }

};
// 地图放大到市级
export const viewToCity = (center, _engin = engine.value) => {
    viewTo({
        zoom: 10.5,
        pitch: 30,
        heading: 10,
        center: center || [113, 22.954321],
    }, 1000, _engin);
};
// 地图放大到区县
export const viewToDistrict = (center, _engin = engine.value) => {
    viewTo({
        zoom: 11,
        pitch: 30,
        heading: 10,
        center: center || [113, 22.954321],
    }, 1000, _engin);
};

// 佛开高速默认横行视角
export const viewToFk = (_engin = engine.value) => {
    viewTo({
        zoom: 11.88,
        pitch: 2.493,
        heading: 62.407,
        center: [112.95621798323867, 22.75292616966224, -100],
    }, 1000, _engin);
};