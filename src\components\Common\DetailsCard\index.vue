<template>
    <card
        class="details-card"
        :title="title"
        v-bind="$attrs"
        v-on="$listeners"
    >
        <template #content>
            <no-data v-if="isNoData" message="暂无数据"/>
            <div v-else class="details-content">
                <item
                    v-for="item in list"
                    :key="item.label"
                    :split="item.split"
                    :info="item"
                />
            </div>
        </template>
    </card>
</template>
<script>
import {computed} from 'vue';
import {NoData, Card, Item} from '@/components/Common';
export default {
    name: 'DetailsCard',
    components: {
        NoData,
        Card,
        Item,
    },
    props: {
        list: {
            type: Array,
            default: () => [],
        },
        title: {
            type: String,
            default: '',
        },
    },
    setup(props) {
        const isNoData = computed(() => props.list.length === 0);
        return {
            isNoData,
        };
    },
};
</script>