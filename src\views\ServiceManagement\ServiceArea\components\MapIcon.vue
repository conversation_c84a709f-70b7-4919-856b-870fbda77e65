<template>
    <div>
        <template v-if="mapFilterData.includes('camera')">
            <BubbleMarker
                v-for="(item, index) in cameraList" :key="item"
                :manager-instace="domManager"
                bubble-color="rgb(7, 118, 237)" icon-name="sheshi-shexiangtou"
                :info="{
                    ...item,
                    clickCallback: () => clickCallback(item, 'camera', index),
                }"
            >
                <detail-list
                    v-model="showCamera[item.id]" class="detail-card"
                    :list="item.detailInfo" width="368"
                    :title="item.title" show-video
                    @close="infoClose"
                >
                    <template #title>
                        <div class="modal-title">
                            <div class="detail-list-header-title">{{ item.title ?? '摄像头' }} </div>
                            <div v-if="item.status" class="modal-state">
                                <span></span>{{ item.status ? '在线' : '离线' }}
                            </div>

                        </div>
                    </template>
                </detail-list>
            </BubbleMarker>
        </template>
        <template v-if="mapFilterData.includes('fwq')">
            <BubbleMarker
                v-for="(item, index) in fwqList" :key="item"
                :manager-instace="domManager"
                icon-name="camera-fuwuqu" bubble-color="rgb(255, 237, 0)"
                :info="{
                    ...item,

                    clickCallback: () => clickCallback(item, 'serviceAreaId', index),
                }"
            >
                <detail-list
                    v-model="serviceShow[index]" width="298"
                    :list="serviceList[index]"
                    :title="serviceName[index]"
                />
            </BubbleMarker>
        </template>

        <template v-if="mapFilterData.includes('qbb')">
            <BubbleMarker
                v-for="(item, index) in qbbList" :key="item"
                :manager-instace="domManager"
                icon-name="xianshiping" bubble-color="rgb(57, 182, 0)"
                :info=" {
                    ...item,
                    clickCallback: () =>
                        clickCallback(item, 'qbb', index),
                }"
            >

                <modal-card
                    class="modal" :visible.sync="showqbb[item.id]"
                    :fullscreen-center="false" :show-foot="false"
                    icon-name="camera-fuwuqu" @close="handClose"
                >
                    <template #title>
                        <div class="modal-title">
                            <div class="text">{{ item.title ?? '入口情报板' }} </div>
                            <div v-if="item.status" class="modal-state">
                                <span></span>{{ item.status ? '在线' : '离线' }}

                            </div>

                        </div>

                    </template>
                    <div class="service-content">
                        <div class="service-text">{{ qbbText ?? '前方大雾，请缓慢行驶' }}</div>
                        <div class="text-list">
                            <div class="text-item">
                                <span>设备名称：</span>
                                <span>{{ item.infoBoardName ?? 'SN 12314145331' }}</span>
                            </div>
                            <div class="text-item">
                                <span>设备编号：</span>
                                <span>{{ item.deviceCode ?? 'SN 12314145331' }}</span>
                            </div>
                            <div class="text-item">
                                <span>设备种类：</span>
                                <span>{{ item.type ?? '悬臂式大屏' }}</span>
                            </div>
                            <div class="text-item">
                                <span>设备位置：</span>
                                <span>{{ item.stakeNumber ?? '松岗服务区西北角' }}</span>
                            </div>

                        </div>

                    </div>
                    <!-- <div class="modal-footer">
                        <div
                            class="btn btn-default" @click="handleConfirm(item.stationName
                                ?? '前方大雾，请缓慢行驶')"
                        >编辑情报板</div>
                    </div> -->
                </modal-card>


            </BubbleMarker>
        </template>
        <road-maker/>
        <device-marker/>
        <board-modal :visible.sync="boardModal" :text-info="textInfo"/>
    </div>
</template>

<script setup>
import {MapLine, BubbleMarker, DetailList} from '@/components/Common/index.js';
import {lineManager, domManager} from '../../Map/index.js';
import ModalCard from '@/components/Common/ModalCard/index.vue';
import BoardModal from './Bord.vue';
import {getInfoboardContent} from '@/api/serviceManager/index.js';

import RoadMaker from '@/views/ServiceManagement/Maker/CongestionMaker.vue';
import DeviceMarker from '@/views/ServiceManagement/Maker/EventMarker.vue';
import {viewToMicro, viewToFk} from '@/utils/map/methods/index.js';
import {
    serviceDetailConfig, cameraRoadDirectionDict,
    cameraDeviceStatusDict,
} from '@/config/highspeed.js';
import {
    getServiceAreaInfo,

} from '@/api/equipment/highspeed.js';
import {ref, watch, set} from 'vue';

const props = defineProps({

    mapFilterData: {
        type: Array,
        default: () => ([]),
    },
    cameraList: {
        type: Array,
        default: () => ([]),
    },
    qbbList: {
        type: Array,
        default: () => ([]),
    },
    fwqList: {
        type: Array,
        default: () => ([]),
    },

});
const boardModal = ref(false);
const showfwq = ref([]);
const textInfo = ref('');
const showqbb = ref({});
const showCamera = ref([]);


const handClose = () => {
    viewToFk();
};

const emit = defineEmits(['confirm']);

function handleConfirm(type) {
    emit('confirm', type);
    console.log('output->aaaa', type);
    textInfo.value = type;
    boardModal.value = true;

}
const dictSwitch = (res, item) => {
    let result;
    switch (item.value) {
        case 'roadDirection':
            result = cameraRoadDirectionDict.get(res.data[item.value]);
            break;
        case 'deviceStatus':
            result = cameraDeviceStatusDict.get(res.data[item.value]);
            break;
        default:
            result = res.data[item.value];
            break;
    }
    return result;
};
const getListFn = (arr, res) => {
    return arr.map((item, i) => {
        return {
            label: item.label,
            value: dictSwitch(res, item),
            ortherClass: {operation: i === arr.length - 1 || false},
        };
    });
};
/**
 * 服务区模块
 */
const serviceName = ref([]);
const serviceList = ref([]);
const serviceShow = ref([]);
watch(
    () => props.fwqList,
    newVal => {
        serviceName.value = Array(newVal.length).fill('');
        serviceList.value = Array(newVal.length).fill([]);
        serviceShow.value = Array(newVal.length).fill(false);
    },
    {deep: true, immediate: true}
);
watch(
    () => props.cameraList,
    newVal => {
        newVal.forEach((item, index) => {
            set(showCamera.value, item.id, false);
        });

    }, {deep: true, immediate: true}
);

watch(() => props.qbbList, newVal => {

    newVal.forEach((item, index) => {
        // v2j'ting不到新数据的响应式
        set(showqbb.value, item.id, false);

    });

}, {deep: true, immediate: true});
const qbbText = ref('前方大雾，请缓慢行驶');

const clickCallback = (item, type, index) => {
    switch (type) {
        case 'serviceAreaId':
            getServiceAreaInfo(item.serviceAreaId).then(res => {
                serviceList.value.splice(index, 1, getListFn(serviceDetailConfig, res));
                serviceName.value.splice(index, 1, res.data.serviceAreaName);
                serviceShow.value.splice(index, 1, true);
            });
            break;
        case 'camera':
            showCamera.value[item.id] = !showCamera.value[item.id];
            break;
        case 'qbb':
            showqbb.value[item.id] = true;
            getInfoboardContent(item.id).then(res => {
                qbbText.value = res.data.content || '';
            });
            break;

        default:
            break;
    }
    viewToMicro(item.position);
    console.log('output->item', item);

};
</script>

<style scoped lang="less">
.text-list {
    margin-top: 16px;
}

.service-content {
    padding-top: 16px;

    .service-text {
        width: 100%;
        height: 120px;
        background-color: #000;
        color: #5de47e;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 24px;
    }
}

.detail-list-header-title {
    font-size: 20px;
}

.modal-title {
    display: flex;

    .modal-state {
        margin-left: 10px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        border-radius: 2px;
        width: 38px;
        background: rgba(255, 255, 255, .1);
        height: 14px;
        font-size: 10px;

        span {
            background: rgb(77, 232, 0);
            width: 6px;
            height: 6px;
            border-radius: 50%;
        }
    }
}

.modal-footer {
    display: flex;
    padding-top: 21px;
    justify-content: center;

    .btn {
        width: 100%;
        height: 32px;
        line-height: 32px;
        text-align: center;
        cursor: pointer;
        user-select: none;

        &:active {
            opacity: .85;
        }

        &:not(:first-child) {
            margin-left: 12px;
        }

        &-plain {
            border: 1px solid rgba(#fff, .2);
            background-color: rgba(#2c2c2c, .9);
        }

        &-default {
            background-color: #27b279;
            color: #000;
        }
    }
}

.text-item {
    color: #fff;

    span:first-child {
        color: rgba(#fff, .6);
    }

    &:not(:first-child) {
        margin-top: 12px;
    }
}
</style>