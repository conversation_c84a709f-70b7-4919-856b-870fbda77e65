<template>
    <div class="micro-right">
        <div class="micro-right-item">
            <card title="传感器在线率统计">
                <template #content>
                    <sensorOnline/>
                </template>
            </card>
            <card title="视频图像">
                <template #titleContent>
                    <div class="btn-group">
                        <div
                            v-for="item in monitorList"
                            :key="item.value"
                            :class="[
                                'btn-default',
                                {'btn-default__active': item.value === monitorType},
                            ]"
                            @click="changeMonitorType(item.value)"
                        >
                            {{ item.label }}
                        </div>
                    </div>
                </template>
                <template #content>
                    <videoImage :type="monitorType"/>
                </template>
            </card>
        </div>
        <div class="micro-right-item">
            <card class="card-short-2" title="实时与历史数据对比">
                <template #titleContent>
                    <my-select
                        v-model="measureType" :options="measureList"
                        placeholder="选择测点"
                        @change="changeMeasureType"
                    />
                    <my-select
                        v-model="sensorName" :options="locationList"
                        placeholder="选择测量指标"
                        @change="changeSensorName"
                    >
                        <template #svg>
                            <icon name="cedian"/>
                        </template>
                    </my-select>
                </template>
                <template #content>
                    <dataComparison :measure-type="measureType" :sensor-name="sensorName"/>
                </template>
            </card>
            <card
                v-model="timeType" :radio-content="timeTypeList"
                class="card-short-2" title="结构物告警趋势统计"
            >
                <template #content>
                    <alarmTypes :type="timeType"/>
                </template>
            </card>
        </div>
    </div>
</template>

<script>
import {Card, Icon} from '@/components/Common/index';
import {MySelect} from '../../../common/index';
import sensorOnline from './sensorOnline.vue';
import videoImage from './videoImage.vue';
import dataComparison from './dataComparison.vue';
import alarmTypes from './alarmTypes.vue';
import {ref, onMounted} from 'vue';
import {getStationMeasureValueList, getStationLocationList} from '@/api/structure/index';
import {structureId} from '@/views/Structure/utils/index';
export default {
    name: '结构物微观右侧',
    components: {
        Card, MySelect, Icon,
        sensorOnline,
        videoImage,
        dataComparison,
        alarmTypes,
    },
    setup() {
        const monitorType = ref('1');
        const monitorList = ref([
            {
                label: '上行',
                value: '1',
            },
            {
                label: '下行',
                value: '2',
            },
        ]);

        const changeMonitorType = type => {
            monitorType.value = type;
        };

        const timeType = ref('1');
        const timeTypeList = ref([
            {name: '日', label: '1'},
            {name: '月', label: '2'},
            {name: '季度', label: '3'},
        ]);

        // 实时与历史数据对比
        const measureType = ref('');
        const sensorName = ref('');
        const locationList = ref([]);
        const getLocationList = () => {
            getStationLocationList({
                structureId: structureId.value,
                measureType: measureType.value,
            }).then(res => {
                locationList.value = res.data.map(item => ({
                    label: item,
                    value: item,
                    name: item,
                }));
                sensorName.value = locationList.value[0]?.label;
            });
        };
        const measureList = ref([]);
        const getMeasureList = () => {
            return new Promise(resolve => {
                getStationMeasureValueList({
                    structureId: structureId.value,
                    sensorName: sensorName.value,
                }).then(res => {
                    measureList.value = res.data.map(item => ({
                        label: item,
                        value: item,
                        name: item,
                    }));
                    measureType.value = measureList.value[0]?.label;
                    resolve();
                });
            });
        };
        const init = async () => {
            await getMeasureList();
            getLocationList();
        };
        onMounted(() => init());

        const changeMeasureType = e => {
            getLocationList();
        };
        const changeSensorName = e => {
            getMeasureList();
        };

        return {
            monitorType, monitorList,
            timeType, timeTypeList,
            measureType, sensorName, locationList, measureList,
            changeSensorName, changeMeasureType,
            changeMonitorType,
        };
    },
};
</script>

<style lang="less" scoped>
.micro-right {
    width: 1232px;
    height: calc(100% - 152px);

    &-item {
        display: flex;
        .card:nth-child(1) {
            margin-right: 24px;
        }
        &:not(:last-child) {
            margin-bottom: 24px;
        }

        :deep(.my-select) {
            margin-left: 12px;

            .el-select {
                width: 130px;
            }
        }
    }
}
</style>