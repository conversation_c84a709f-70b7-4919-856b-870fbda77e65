<template>
    <div class="strategy-page">
        <div class="select-area" style="padding-top: 0;">
            <span>策略输入方式：</span>
            <div class="item">
                <div
                    :class="{
                        'select-item': true,
                        'active-item': strategyMode === 2,

                    }"
                    @click="onStrategyModeChange(2)"
                >
                    <span></span>
                    <span>手动输入</span>
                </div>
                <div
                    :class="{
                        'select-item': true,
                        'active-item': strategyMode === 3,

                    }"
                    @click="onStrategyModeChange(3)"
                >
                    <span></span>
                    <span>无策略输入</span>
                </div>
            </div>
        </div>
        <template v-if="strategyMode === 2">
            <div class="strategy-content">
                <div class="tabs-list">
                    <div
                        v-for="(item, index) in strategyList"
                        :key="index"
                        :class="{
                            'tabs-item': true,
                            'tabs-active-item': curIndex === index + 1,

                        }"
                        @click="curIndex = index + 1"
                    >策略{{ index + 1 }}
                    </div>
                    <div
                        v-if="strategyList.length < 2" class="tabs-item tabs-item-add"
                        @click="addStrategy"
                    >+添加策略</div>
                </div>
                <div v-if="strategyList.length" class="content">
                    <div class="edit-item">
                        <span>策略选择：</span>
                        <div class="edit-con">
                            <div
                                v-for="(item, index) in strategy_list"
                                :key="item.key"
                                @click="chooseStrategy(item, index)"
                            >
                                <span
                                    :class="{
                                        'selected': curStrategyInfo?.some(cur => item.key === cur.type),
                                    }"
                                ></span>
                                <span>{{ item.name }}</span>
                            </div>
                        </div>
                    </div>
                    <div
                        v-for="curStrategy in curStrategyInfo"
                        :key="curStrategy.type"
                    >
                        <!-- 开放应急车道开始 -->
                        <template v-if="curStrategy.type === 1">
                            <div class="small-title">
                                开放应急车道
                            </div>
                            <div class="edit-item mt-16">
                                <span>开放起始位置：</span>
                                <stake-select
                                    v-model="curStrategy.controlStake"
                                    :list="stakenumberlist"
                                    :is-full="true"
                                    @change="val => stakeChange(val, 'emergency')"
                                />
                            </div>
                            <div class="edit-item">
                                <span>管控距离：</span>
                                <el-input
                                    v-model="curStrategy.controlLength"
                                    class="f-1 h-40"
                                    placeholder="请输入"
                                >
                                    <span slot="suffix" class="suffix-text">m</span>
                                </el-input>

                            </div>
                            <div class="edit-item">
                                <span>应急车道限速值：</span>
                                <el-input
                                    v-model="curStrategy.limitSpeed"
                                    class="f-1 h-40"
                                    placeholder="请输入"
                                >
                                    <span slot="suffix" class="suffix-text">km/h</span>
                                </el-input>
                            </div>
                            <div class="edit-item">
                                <span>持续时间：</span>
                                <el-input
                                    v-model="curStrategy.controlDuration"
                                    class="f-1 h-40"
                                    placeholder="请输入"
                                >
                                    <span slot="suffix" class="suffix-text">分钟</span>
                                </el-input>
                            </div>
                        </template>
                        <!-- 开放应急车道结束 -->

                        <!-- 行车道管控开始 -->
                        <template v-if="curStrategy.type === 2">
                            <div class="small-title">
                                行车道管控
                            </div>
                            <div class="edit-item mt-16">
                                <span>开始起始位置：</span>
                                <stake-select
                                    v-model="curStrategy.controlStake"
                                    :list="stakenumberlist"
                                    :is-full="true"
                                />
                                <!-- <el-select
                                        v-model="controlLaneInfo.controlStake"
                                        placeholder="请选择"
                                        class="f-1 h-40"
                                        clearable
                                        filterable
                                    >
                                        <el-option
                                            v-for="item in stakenumberlist"
                                            :key="item.id"
                                            :label="item.roadNameCn + '-' + item.stakeNumber"
                                            :value="item.stakeNumber"
                                        />
                                    </el-select> -->
                            </div>
                            <div class="edit-item">
                                <span>管控距离</span>
                                <el-input
                                    v-model="curStrategy.controlLength"
                                    class="f-1 h-40"
                                    placeholder="请输入"
                                >
                                    <span slot="suffix" class="suffix-text">m</span>
                                </el-input>

                            </div>
                            <!-- <div class="edit-item">
                                <span>行驶方向：</span>
                                <el-select
                                    v-model="controlLaneInfo.direction"
                                    placeholder="请选择"
                                    class="f-1 h-40"
                                    clearable
                                >
                                    <el-option
                                        v-for="item in road_direction"
                                        :key="item.code"
                                        :label="item.name"
                                        :value="item.code"
                                    />
                                </el-select>
                            </div> -->
                            <div class="edit-item">
                                <span>行驶车道管控：</span>
                                <div class="edit-con">
                                    <div
                                        v-for="(item, index) in lane_config" :key="item"
                                        @click="onControlLane(item, index)"
                                    >
                                        <span
                                            :class="{
                                                'selected': curStrategy.lane.includes(item),
                                            }"
                                        ></span>
                                        <span>{{ `${item === '99' ? '应急' : item}车道` }}</span>
                                    </div>
                                </div>
                            </div>
                            <div
                                v-for="(item,index) in curStrategy.lane.split(',')" :key="item"
                                class="pb-20"
                            >
                                <div class="select-area">
                                    <span v-if="item !== '99'">
                                        {{ `车道${item}：` }}
                                    </span>
                                    <span v-else>
                                        应急车道：
                                    </span>
                                    <div class="item">
                                        <div
                                            :class="{
                                                'select-item': true,
                                                'active-item': curStrategy.laneControlType.split(',')[index] === '1',

                                            }"
                                            @click="onChangeControlType(1, index)"
                                        >
                                            <span></span>
                                            <span>限速</span>
                                        </div>
                                        <div
                                            :class="{
                                                'select-item': true,
                                                'active-item': curStrategy.laneControlType.split(',')[index] === '2',

                                            }"
                                            @click="onChangeControlType(2, index)"
                                        >
                                            <span></span>
                                            <span>关闭</span>
                                        </div>
                                    </div>
                                </div>
                                <div
                                    v-show="curStrategy.laneControlType.split(',')[index] === '1'"
                                >
                                    <div class="edit-item">
                                        <span style="visibility: hidden;">{{ `车道${item}：` }}</span>
                                        <el-input
                                            v-model="curStrategy.limitSpeed.split(',')[index]"
                                            class="h-40"
                                            :maxlength="3"
                                            placeholder="请输入车道限速值"
                                            @input="val => onLimitSpeedChange(val, index)"
                                        >
                                            <span slot="suffix" class="suffix-text">km/h</span>
                                        </el-input>
                                    </div>
                                </div>
                                <!-- <div class="edit-item">
                                    <span style="visibility: hidden;">{{ `车道${item}：` }}</span>
                                    <el-input
                                        v-model="controledLane[index].position" class="f-1 h-40"
                                        placeholder="请在地图上选择"
                                        @focus="() => handlePositionFocus('controlLane', index)"
                                    />
                                </div> -->
                            </div>
                        </template>
                        <!-- 行车道管控结束 -->
                        <!-- 出入口管控开始 -->
                        <template
                            v-if="curStrategy.type === 3"
                        >
                            <div class="pb-20">
                                <div class="small-title">
                                    出入口管控
                                </div>
                                <div class="edit-item">
                                    <span>出入口选择：</span>
                                    <el-select
                                        v-model="curStrategy.rampName"
                                        placeholder="请选择"
                                        popper-class="emulation-el-popper"
                                        class="f-1 h-40"
                                        clearable
                                    >
                                        <el-option
                                            v-for="item in tollList"
                                            :key="item.id"
                                            :label="item.tollName"
                                            :value="item.tollName"
                                        />
                                    </el-select>
                                </div>
                                <div class="edit-item">
                                    <span>{{ curStrategy.rampName || '收费站' }}
                                        {{ flowInfo.direction }}：</span>
                                    <div class="edit-con" style="flex-direction: column; align-items: self-start;">
                                        <div
                                            v-for="item in gate_list" :key="item.key"
                                            style="display: flex;"
                                        >
                                            <div @click="onGate(item)">
                                                <span
                                                    :class="{
                                                        'selected': curStrategy.entranceExitType.includes(item.key),
                                                    }"
                                                ></span>
                                                <span>{{ item.name }}</span>
                                            </div>
                                            <span
                                                v-if="item.config" class="config"
                                                @click="setConfig(item)"
                                            >配置</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                        <!-- 出入口管控结束 -->
                    </div>
                </div>
            </div>

        </template>
        <!-- 入口限流配置 -->
        <el-dialog
            :visible.sync="entryVisible"
            title="入口限流配置"
            width="320px"
            class="config-modal"
            :modal-append-to-body="false"
        >
            <div class="content-box">
                <div class="edit-item">
                    <span>持续时间：</span>
                    <el-input
                        v-model="entryConfig.controlDuration"
                        class="f-1 h-40"
                        placeholder="请输入"
                    >
                        <span slot="suffix" class="suffix-text">分钟</span>
                    </el-input>
                </div>
                <div class="edit-item">
                    <span>入口限流量：</span>
                    <el-input
                        v-model.number="entryConfig.flowLimit"
                        class="f-1 h-40"
                        placeholder="请输入"
                    >
                        <span slot="suffix" class="suffix-text">辆/小时</span>
                    </el-input>
                </div>
                <div class="edit-item">
                    <span>车辆限制</span>
                    <el-select
                        v-model.number="entryConfig.vehicleType"
                        placeholder="请选择"
                        class="f-1 h-40"
                        popper-class="emulation-el-popper"
                        clearable
                    >
                        <el-option
                            v-for="item in vehicle_type_list"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"
                        />
                    </el-select>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <span @click="entryVisible = false">取 消</span>
                <span @click="onConfirm(2)">保存</span>
            </span>
        </el-dialog>
        <!-- 出口限流配置 -->
        <el-dialog
            :visible.sync="exportVisible"
            title="出口分流配置"
            width="320px"
            class="config-modal"
            :modal-append-to-body="false"
        >
            <div class="content-box">
                <el-form
                    ref="exportForm"
                >
                    <el-form-item label="持续时间：" prop="controlDuration">
                        <div class="edit-item">
                            <el-input
                                v-model="exportConfig.controlDuration"
                                class="f-1 h-40"
                                placeholder="请输入"
                            >
                                <span slot="suffix" class="suffix-text">分钟</span>
                            </el-input>
                        </div>
                    </el-form-item>
                    <el-form-item label="绕行比例：" prop="bypassRate">
                        <div class="edit-item">
                            <el-input
                                v-model="exportConfig.bypassRate"
                                class="f-1 h-40"
                                placeholder="请输入"
                            >
                                <span slot="suffix" class="suffix-text">%</span>
                            </el-input>
                        </div>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <span @click="exportVisible = false">取 消</span>
                <span @click="onConfirm(3)">保存</span>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import {computed, ref} from 'vue';
import {Select, Option, Input, Dialog, Form, FormItem} from 'element-ui';
import StakeSelect from './StakeSelect.vue';
import {vehicle_type_list, default_strategy, default_strategy_config} from './config';
import {strategy_list, gate_list} from '@EmulationLayer/config';
import {cloneDeep} from 'lodash';

export default {
    name: 'StrategyComponent',
    components: {
        StakeSelect,
        [Select.name]: Select,
        [Option.name]: Option,
        [Input.name]: Input,
        [Dialog.name]: Dialog,
        [Form.name]: Form,
        [FormItem.name]: FormItem,
    },
    props: {
        strategyModeType: {
            type: Number,
            default: 2,
        },
        strategyList: {
            type: Array,
        },
        stakenumberlist: {
            type: Array,
            default: () => [],
        },
        tollList: {
            type: Array,
            default: () => [],
        },
        flowInfo: {
            type: Object,
        },
        laneConfig: {
            type: Array,
            default: () => [],
        },
    },
    setup(props, {emit}) {

        console.log('strategyModeType00===', props.strategyModeType);
        const entryVisible = ref(false);
        const exportVisible = ref(false);

        const exportForm = ref(null);

        const entryConfig = ref({
            controlDuration: '',
            flowLimit: '',
            vehicleType: '',
        });
        const exportConfig = ref({
            controlDuration: '',
            bypassRate: '',
        });

        const validatorRate = (rule, value, callback) => {
            if (!value) {
                return Promise.reject('数值不能为空');
            }
            else if (!Number(value)) {
                return Promise.reject('必须是数字');
            }
            else if (Number(value) && (value < 0 || value > 100)) {
                return Promise.reject('必须在0-100之间');
            }
            return Promise.resolve();
        };

        const exportRule = ref({
            bypassRate: [
                {validator: validatorRate, trigger: 'blur'},
            ],
        });

        const curIndex = ref(1);
        let prevStrategy = new Map();


        const strategyMode = computed(() => props.strategyModeType);


        const curStrategyInfo = computed(() => {
            if (props.strategyList?.length) {
                return props.strategyList[curIndex.value - 1];
            }
            return [];
        });

        // 策略选择
        const chooseStrategy = (item, index) => {
            const cur = curStrategyInfo.value.findIndex(t => t.type === item.key);
            let key = `${item.type}_ ${index}`;
            if (cur > -1) {
                const list = curStrategyInfo.value.splice(cur, 1);
                prevStrategy.set(key, list[0]);
            }
            else {
                curStrategyInfo.value.push({
                    // 添加默认数据
                    ...(prevStrategy.get(key) || {
                        ...default_strategy[item.key],
                    }),
                });
            }
        };

        // 行车道管控
        const onControlLane = item => {
            const findIndex = curStrategyInfo.value.findIndex(t => t.type === 2);
            const lanes = curStrategyInfo.value[findIndex].lane.split(',');
            const cur = lanes.findIndex(t => t === item);
            if (cur > -1) {
                lanes.splice(cur, 1);
            }
            else {
                lanes.push(item);
            }
            curStrategyInfo.value[findIndex].lane = lanes.join(',') || '';
        };

        const onChangeControlType = (item, index) => {
            const findIndex = curStrategyInfo.value.findIndex(t => t.type === 2);
            const lanes = curStrategyInfo.value[findIndex].laneControlType.split(',');
            lanes[index] = item;
            curStrategyInfo.value[findIndex].laneControlType = lanes.join(',') || '';
        };

        const onLimitSpeedChange = (value, index) => {
            const findIndex = curStrategyInfo.value.findIndex(t => t.type === 2);
            const limits = curStrategyInfo.value[findIndex].limitSpeed.split(',');
            limits[index] = value;
            curStrategyInfo.value[findIndex].limitSpeed = limits.join(',') || '';
        };

        const onGate = item => {
            const findIndex = curStrategyInfo.value.findIndex(t => t.type === 3);
            const entrances = curStrategyInfo.value[findIndex].entranceExitType.split(',');
            const cur = entrances.findIndex(t => t === item.key);
            if (cur > -1) {
                entrances.splice(cur, 1);
            }
            else {
                entrances.push(item.key);
            }
            if (entrances.includes('2')) {
                curStrategyInfo.value[findIndex].entryConfig = entryConfig.value;
            }
            else {
                curStrategyInfo.value[findIndex].entryConfig = {};
            }
            if (entrances.includes('3')) {
                curStrategyInfo.value[findIndex].exportConfig = exportConfig.value;
            }
            else {
                curStrategyInfo.value[findIndex].exportConfig = {};
            }
            curStrategyInfo.value[findIndex].entranceExitType = entrances.join(',') || '';
        };

        const setConfig = item => {
            if (+item.key === 2) {
                entryVisible.value = true;
            }
            else if (+item.key === 3) {
                exportVisible.value = true;
            }
        };

        //  入口限流配置 ｜ 出口分流配置
        const onConfirm = type => {
            if (type === 2) {
                entryVisible.value = false;
            }
            else if (type === 3) {
                exportVisible.value = false;
                // exportForm.value.validate(valid => {
                //     if (valid) {
                //         exportVisible.value = false;
                //     }
                //     else {
                //         return false;
                //     }
                // });
            }
        };

        // 添加策略
        const addStrategy = () => {
            let copy_data = cloneDeep(default_strategy_config);
            props.strategyList.push(copy_data);
        };

        const stakeChange = (val, type) => {
            emit('stakeChange', val, type);
        };

        const onStrategyModeChange = val => {
            console.log('val---', val);
            emit('onStrategyModeChange', val);
        };
        return {
            curIndex,
            strategyMode,
            strategy_list,
            gate_list,
            curStrategyInfo,
            entryVisible,
            exportVisible,
            vehicle_type_list,
            exportRule,
            exportForm,
            entryConfig,
            exportConfig,
            onStrategyModeChange,
            chooseStrategy,
            stakeChange,
            onControlLane,
            onChangeControlType,
            onLimitSpeedChange,
            onGate,
            setConfig,
            onConfirm,
            addStrategy,
        };
    },

};
</script>
<style lang="less" >

    .strategy-page {
        width: 100%;

        .config-modal {
            .el-dialog {
                background: rgba(35, 56, 64, 1);

                .content-box {
                    .edit-item {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 16px;

                        >span {
                            color: #c4cfde;
                            display: block;
                            width: 115px;
                        }

                        .el-input__suffix {
                            line-height: 40px;
                            color: #6f88a7;
                        }

                        .edit-con {
                            flex: 1;
                            display: flex;
                            align-items: center;
                            flex-wrap: wrap;

                            >div {
                                margin: 0 12px;
                                display: flex;
                                align-items: center;
                                margin-top: 16px;

                                span:nth-child(1) {
                                    display: inline-block;
                                    width: 14px;
                                    height: 14px;
                                    border: 1px solid #98b8de;

                                    &.selected {
                                        background: #49c3ff;
                                        position: relative;

                                        &::before {
                                            content: '✓';
                                            color: rgba(10, 28, 53, 1);
                                            position: absolute;
                                            left: 50%;
                                            top: 50%;
                                            transform: translate(-50%, -50%);
                                        }
                                    }
                                }

                                span:nth-child(2) {
                                    font-family: 'FZLTZHJW--GB1-0', sans-serif;
                                    font-size: 14px;
                                    color: #c4cfde;
                                    margin-left: 8px;
                                }

                                .config {
                                    margin-left: 5px !important;
                                    color: #49c3ff !important;
                                }
                            }
                        }
                    }
                }

                .el-form-item__label {
                    color: #fff;
                }

                .el-dialog__header {
                    border-bottom: 1px solid #767c84 !important;
                    background: rgba(35, 56, 64, .9);

                    .el-dialog__title {
                        color: #fff;
                    }
                }

                .dialog-footer {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;

                    span {
                        display: flex;
                        width: 114px;
                        height: 36px;
                        justify-content: center;
                        align-items: center;

                        &:nth-child(1) {
                            border: 1px solid rgba(178, 214, 255, 1);
                            border-radius: 2px;
                            color: #b2d6ff;
                        }

                        &:nth-child(2) {
                            background: rgba(22, 34, 38, 1);
                            border: 1px solid rgba(22, 34, 38, .5);
                            border-radius: 2px;
                            color: #e5f1ff;
                            margin-left: 16px;
                        }
                    }
                }
            }
        }
    }

    .select-area {
        padding: 16px 0;
        display: flex;
        margin-left: 10px;

        >span {
            display: block;
            color: #fff;
            // width: 98px !important;width
        }

        .item {
            flex: 1;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            color: #c4cfde;

            .select-item {
                display: flex;
                align-items: center;
                cursor: pointer;

                span:nth-child(1) {
                    display: inline-block;
                    width: 14px;
                    height: 14px;
                    border: 1px solid #98b8de;
                    border-radius: 50%;
                    margin-right: 8px;
                    margin-left: 16px;
                }

                span:nth-child(2) {
                    color: #c4cfde;
                }

                &.active-item {
                    span:nth-child(1) {
                        border-color: #49c3ff;
                        position: relative;

                        &::before {
                            content: '';
                            width: 8px;
                            height: 8px;
                            background: #49c3ff;
                            border-radius: 50%;
                            position: absolute;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%, -50%);
                        }
                    }

                    span:nth-child(2) {
                        color: #fff;
                    }
                }
            }
        }
    }

    .strategy-content {
        width: 100%;
        height: 40px;
        background-color: #307163;

        .tabs-list {
            display: flex;
            height: 100%;
            cursor: pointer;

            .tabs-item {
                width: 80px;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #fff;
                font-size: 16px;
            }

            .tabs-active-item {
                color: #14e5b3;
            }

            .tabs-item-add {
                color: #14e5b3;
            }
        }
    }

    .small-title {
        font-size: 14px;
        color: #fff;
        position: relative;
        padding-left: 8px;
        display: flex;
        justify-content: space-between;
        cursor: pointer;

        &::before {
            content: '';
            width: 4px;
            height: 12px;
            background: #08d6a5;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translate(0, -50%);
        }

        span:nth-child(2) {
            color: #49c3ff;
        }
    }

    .edit-item {
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        margin-left: 10px;

        >img {
            width: 18px;
            height: 28px;
            margin-left: 12px;
            cursor: pointer;
        }

        .point-btn {
            background: #49c3ff;
            color: #e5f1ff;
            display: inline-block;
            width: 52px;
            height: 28px;
            font-size: 14px;
            border-radius: 4px;
            margin-left: 8px;
            text-align: center;
            line-height: 28px;
        }

        >span {
            display: inline-block;
            width: 115px;
            font-size: 14px;
            color: #c4cfde;
            letter-spacing: 0;
            flex-shrink: 0;
        }

        .suffix-text {
            line-height: 40px;
            padding-right: 5px;
        }

        .edit-con {
            flex: 1;
            width: 100%;
            display: flex;
            align-items: center;
            flex-wrap: wrap;

            .disabled {
                cursor: not-allowed;
            }

            & > div {
                margin: 0 12px;
                display: flex;
                align-items: center;
                margin-top: 16px;
                cursor: pointer;

                span:nth-child(1) {
                    display: inline-block;
                    width: 14px;
                    height: 14px;
                    border: 1px solid #98b8de;
                    color: #c4cfde;

                    &.selected {
                        background: #49c3ff;
                        position: relative;

                        &::before {
                            content: '✓';
                            color: rgba(10, 28, 53, 1);
                            position: absolute;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%, -50%);
                        }
                    }
                }

                span:nth-child(2) {
                    font-family: 'FZLTZHJW--GB1-0', sans-serif;
                    font-size: 14px;
                    color: #c4cfde;
                    margin-left: 8px;
                }

                .config {
                    margin-left: 5px !important;
                    color: #49c3ff !important;
                }
            }
        }
    }

</style>