


import {request} from '@/utils/network-helper/index';
import {BaseVideoHost, BaseApiHost} from '@/store/common';
const isDev = import.meta.env.DEV;
const publicPath = () => {
    console.log('devdev', isDev);
    if (isDev) return '';
    return BaseVideoHost.value || BaseApiHost.value || window.location.origin;
};
window.ApaasMaplayerVideoPlayerStatic = {};
const fetchStatic = () =>
    Promise.all([
        request.get(`${publicPath()}/gs-static/js/flv-min.js`),
        request.get(`${publicPath()}/gs-static/js/playercomponents.min.js`),
        request.get(`${publicPath()}/gs-static/js/player-min.js`),
    ]).then(res => {
        res.forEach(item => {
            new Function(item)();
        });

        Object.assign(window.ApaasMaplayerVideoPlayerStatic, {js: true});
    });

let staticLoaded = null;

// 动态引入css样式资源
export const loadStyles = url => {
    const staticCache = window.ApaasMaplayerVideoPlayerStatic;

    if (staticCache?.css) return Promise.resolve();
    let link = document.createElement('link');
    link.rel = 'stylesheet';
    link.type = 'text/css';
    link.href = url;
    let head = document.getElementsByTagName('head')[0];
    head.appendChild(link);
    Object.assign(window.ApaasMaplayerVideoPlayerStatic, {css: true});
    return Promise.resolve();
};


// 动态引入js资源
export const loadJs = async () => {
    const staticCache = window.ApaasMaplayerVideoPlayerStatic;
    if (staticCache?.js) return Promise.resolve();
    if (!staticLoaded) {
        staticLoaded = fetchStatic();
    }
    return staticLoaded;
};