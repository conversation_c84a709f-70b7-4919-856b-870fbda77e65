<template>
    <div
        class="card fc"
        v-bind="$attrs"
    >
        <div class="top-border h-10 fr jsb px-8">
            <div class="fr ac gap-2">
                <span
                    v-for="i in 3" :key="i"
                    class="dib w-3 h-3"
                ></span>
            </div>
            <div class="fr ac">
                <span class="dib w-72 h-2"></span>
            </div>
        </div>

        <!-- 头部 -->
        <header class="header py-10 pl-10 pr-17 fr jsb">
            <div class="fr ac gap-2">
                <div v-if="icon" class="icon w-24 h-24 p-3 fc">
                    <img class="full" :src="icon">
                </div>
                <div class="title fr ac lh-20">
                    {{ title }}
                </div>
            </div>
            <div class="titleContent">
                <slot name="titleContent"></slot>
                <div
                    title="关闭"
                    @click="close"
                >
                    <icon :class="'icon-close'" name="zengjia"/>
                </div>
            </div>
        </header>

        <!-- 内容区 -->
        <main class="content pr oya px-15 minw-300">
            <slot name="content"></slot>
        </main>
    </div>
</template>
<script setup lang="ts">
import Icon from '../Icon/index.vue';

const emit = defineEmits(['close']);

const close = () => {
    emit('close');
};
defineProps({
    title: {
        type: String,
        default: '详情',
    },
    icon: {
        type: String,
        default: '',
    },
    cancel: {
        type: Object,
        default: null,
    },
});
</script>

<style scoped lang="less">
.card {
    white-space: nowrap;
    background: rgba(8, 35, 79, .8);
}

.top-border {
    background: rgba(77, 135, 255, .3);

    span {
        background: rgb(46, 210, 255);
    }
}

.header {
    background: rgba(36, 104, 242, .3);
    border-bottom: 1px solid rgba(255, 255, 255, .1);
    font-size: 20px;
    font-weight: 700;
    font-family: OPlusSans 3;
}

.titleContent {
    display: flex;
    align-items: center;
}

.close-btn {
    color: rgb(196, 196, 196);
}

.icon {
    border: 1px solid rgba(255, 255, 255, .4);
}
</style>