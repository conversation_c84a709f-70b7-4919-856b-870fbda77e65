<template>
    <div class="health">
        <div class="health-list">
            <!-- <div class="health-list-title">设备健康指数列表</div> -->
            <div class="health-list-table">
                <div class="list-table-search">
                    <div class="list-table-search-left">
                        <div class="search-input">
                            <span>设备查询：</span>
                            <el-input
                                v-model="params.deviceCondition" size="small"
                                placeholder="请输入内容"
                            />
                        </div>
                        <div class="search-input">
                            <maintain-select
                                v-model="params.deviceStake" title="设备桩号："
                                :options="stakeOption"
                            />
                        </div>
                        <div class="search-input">
                            <maintain-select
                                v-model="params.deviceBrand" title="品牌类型："
                                :options="brandOption"
                            />
                        </div>
                        <div class="search-input">
                            <maintain-select
                                v-model="params.deviceType" title="设备类型："
                                :options="typeDict"
                            />
                        </div>
                    </div>
                    <div class="list-table-search-right">
                        <search-buttons
                            @search="searchFn"
                            @reset="resetFn"
                        />
                    </div>
                </div>
                <my-table
                    :data="data" :column="column"
                    :loading="loading"
                    :current-page="params.pageNumber"
                    :page-size="params.pageSize"
                    :total="total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    @export-excel="exportExcel"
                />
            </div>
        </div>
    </div>
</template>

<script>
import {ref} from 'vue';
import {Input} from 'element-ui';
import {useUnit} from '@/utils/hooks/useUnit';
import MaintainTable from '../component/maintainTable.vue';
import MaintainSelect from '../component/MaintainSelect.vue';
// import DeriveCard from '../component/deriveCard.vue';
import SearchButtons from '../component/searchButtons.vue';
import {
    deviceStakeOption,
    deviceBrandOption,
    getDeviceHealthList,
    exportHealthExcel,
} from '@/api/equipment/equipmentdisplay.js';
import {brandDist, typeDict, levelDict} from '@/config/maintain.js';

export default {
    name: 'health',
    components: {
        MyTable: MaintainTable,
        MaintainSelect,
        // DeriveCard,
        [Input.name]: Input,
        SearchButtons,
    },
    setup(props) {
        const params = ref({
            deviceCondition: '',
            deviceStake: '',
            deviceBrand: '',
            deviceType: '',
            pageNumber: 1,
            pageSize: 10,
        });

        // 设备桩号
        const stakeOption = ref([]);
        deviceStakeOption().then(res => {
            stakeOption.value = res.data.map(item => ({value: item, label: item}));
        });

        // 品牌类型
        const brandOption = ref([]);
        deviceBrandOption().then(res => {
            brandOption.value = res.data
                .map(item => ({value: item, label: brandDist.get(item) ?? '暂无信息'}))
                // .filter(item => item.label !== '暂无信息')
                .filter(item => item.value !== '0');
        });

        // 列表获取
        const total = ref(0);
        const data = ref([]);
        const loading = ref(false);
        const searchFn = () => {
            loading.value = true;
            getDeviceHealthList(params.value).then(res => {
                total.value = res.data.total;
                data.value = res.data.records.map(item => {
                    return {
                        ...item,
                        deviceBrand: brandDist.get(item.deviceBrand) || '暂无信息',
                        deviceType: typeDict.find(e => e.value === item.deviceType)?.label || '暂无信息',
                        deviceIndex: levelDict.get(item.deviceIndex) || '暂无信息',
                        deviceVersion: item.deviceVersion || '暂无信息',
                    };
                });
                loading.value = false;
            }).catch(err => {
                loading.value = false;
            });
        };
        searchFn();
        const resetFn = () => {
            params.value = {
                deviceCondition: '',
                deviceStake: '',
                deviceBrand: '',
                deviceType: '',
                pageNumber: 1,
                pageSize: 10,
            };
            searchFn();
        };

        const {ratio} = useUnit();
        const column = ref([
            {
                prop: 'deviceName',
                label: '设备名称',
            },
            {
                prop: 'deviceStake',
                label: '设备桩号',
            },
            {
                prop: 'deviceIp',
                label: '设备IP',
                width: 180 * ratio.value,
            },
            {
                prop: 'deviceBrand',
                label: '设备品牌',
                width: 180 * ratio.value,
            },
            {
                prop: 'deviceType',
                label: '设备类型',
            },
            {
                prop: 'deviceVersion',
                label: '当前版号',
                width: 180 * ratio.value,
            },
            {
                prop: 'deviceIndex',
                label: '健康指数',
                width: 180 * ratio.value,
            },
        ]);

        // const visible = ref(false);

        // 分页器修改
        const handleSizeChange = e => {
            params.value.pageSize = e;
            searchFn();
        };
        const handleCurrentChange = e => {
            params.value.pageNumber = e;
            searchFn();
        };

        // 导出
        const exportExcel = e => {
            exportHealthExcel({
                deviceIdList: e,
            }).then(res => {
                // 下载
                const blob = new Blob([res]);
                const fileName = '设备健康清单.xlsx';
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = fileName;
                link.click();
                URL.revokeObjectURL(link.href);
            });
        };

        return {
            // visible,
            data,
            params,
            typeDict,
            column,
            stakeOption,
            brandOption,
            total,
            loading,
            searchFn,
            resetFn,
            handleSizeChange,
            handleCurrentChange,
            exportExcel,
        };
    },
};
</script>

<style lang="less" scoped>
.health {
    height: 100%;
}
.health-list {
    width: 100%;
    height: 100%;

    .health-list-title {
        width: 100%;
        height: 40px;
        line-height: 40px;
        font-size: 16px;
        font-weight: 500;
        color: #fff;
        padding-left: 16px;
        font-family: 'OPlusSans';
        border-bottom: 1px solid rgba(255, 255, 255, .2);
    }

    .health-list-table {
        padding: 0 24px;
    }

    .list-table-search {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 80px;

        .list-table-search-left {
            display: flex;
            align-items: center;
            flex: 1;

            > div {
                flex: 1;
                display: flex;
                align-items: center;
                margin-right: 24px;

                &:last-child {
                    margin-right: 0;
                }

                span {
                    display: inline-block;
                    width: 120px;
                    margin-right: 10px;
                    font-weight: 400;
                    font-family: 'OPlusSans';
                    font-size: 14px;
                    color: rgba(255, 255, 255, 0.6);
                }

                :deep(.el-input) {
                    input {
                        &::placeholder {
                            color: rgba(255, 255, 255, .6) !important;
                        }
                    }
                }

                :deep(.el-select) {
                    width: 100%;
                }
            }
        }

        .list-table-search-right {
            display: flex;
            justify-content: end;

            > div {
                margin-left: 16px;

                &:first-child {
                    margin-right: 0;
                }
            }
            width: 20%;
        }
    }
}
</style>