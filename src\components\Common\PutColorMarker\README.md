# 地图着色扎点组件

### 参数说明 props
|Prop name|Type|Default|Description|
|---|---|---|---|
|`managerInstace`|`Object`|`必填`|扎点管理器实例|
|`polygonManager`|`Object`|`必填`|多边形管理器实例|
|`lineManager`|`Object`|`必填`|线段管理器实例|
|`list`|`array`|`[]`|地图着色数据列表,三维数组,内包含多个需要着色的二维经纬数据|
|`info`|`object`|`{}`|包含扎点详细数据|

### info 详细参数
|Key name|Type|Default|Description|
|---|---|---|---|
|`color`|`string`|`rgb(255,255,255)`|地图区域颜色|
|`borderColor`|`string`|`rgb(255,255,255)`|地图区域边界线颜色|
|`borderWidth`|`number`|`1`|地图区域边界线宽度|
|`opacity`|`number`|`0.7`|颜色透明度|
|`pointName`|`string`|`PutColorMarker`|扎点名称|
|`iconUrl`|`string`|`normal`|一般情况时显示的图标|
|`position`|`array`|`null`|扎点经纬度|
|`activeIconUrl`|`string`|`null`|hover在扎点时显示的图标|
|`iconOffset`|`array`|`null`|图标的偏移|
|`onClick`|`function`|`null`|点击扎点时触发|
|`onMouseenter`|`function`|`null`|鼠标移入扎点时触发|
|`onMouseleave`|`function`|`null`|鼠标移出扎点时触发|
|`customData`|`object`|`null`|自定义 icon/font size|
|`isBorder`|`boolean`|`true`|是否需要边界线|
|`isOverlap`|`boolean`|`true`|由多个面组成的区域，内部是否需要边界线|

```json
// customData 格式
{
    "width": 100, // icon 宽度
    "height": 100, // icon 高度
    "fontSize": 12, // 字体大小
}
```