export default class EventCenter {
    // 元素集合
    elSet = new Set();
    constructor(eventNameEnum = {}) {
        this.eventNameEnum = eventNameEnum;
    }
    // 绑定事件
    bind(element, type) {
        const addEventListener = type => {
            const eventName = this.eventNameEnum[type];
            const callback = this.options[eventName];
            if (callback && typeof callback === 'function') {
                element.receiveRaycast = true;
                element[type] = callback;
                element.engine.event.bind(element, type, callback);
            }
        };
        this.elSet.add(element);
        // 添加单个元素单个事件
        if (type) {
            addEventListener(type);
            return;
        }
        // 添加单个元素所有事件
        Object.keys(this.eventNameEnum).forEach(eventType => {
            addEventListener(eventType);
        });
    }

    // 移除事件
    unbind(element, type) {
        // 移除所有元素所有事件
        if (!element) {
            this.elSet.forEach(this.unbind);
            return;
        };
        const removeEventListener = type => {
            element.engine.event.unbind(element, type, element[type]);
            element[type] = null;
        };
        this.elSet.delete(element);
        // 移除单个元素单个事件
        if (type) {
            removeEventListener(type);
            return;
        }
        // 移除单个元素所有事件
        Object.keys(this.eventNameEnum).forEach(eventType => {
            removeEventListener(eventType);
        });
    }
}