// 动态导入所有图片
const imageUrls = [];
const files = import.meta.glob('@/assets/images/serviceM/*.png', {eager: true});

Object.keys(files).forEach(fileName => {
    let fileType = fileName.match(/([^/]*?)\.[^/.]+$/)[1]; // 用正则匹配出文件名称
    imageUrls.push({
        name: fileType,
        iconSrc: files[fileName].default,
    });
});
function getUrl(urlname) {
    return imageUrls.find(item => item.name === urlname)?.iconSrc;
}
// 0 - 所有设备  2 - 摄像机 4 - 卡口 5 - 雷达 6 - MEC 11 - 情报板
export const equip = {
    5: {icon: getUrl('lda'), title: '毫米波雷达', translate: 'MILLIMETER WAVE RADAR'},
    2: {icon: getUrl('sxji'), title: '摄像机', translate: 'CAMERA'},
    4: {icon: getUrl('kakou'), title: '卡口', translate: 'BAYONET'},
    6: {icon: getUrl('biany'), title: '边缘计算设备', translate: 'COMPUTING DEVICE'},
    11: {icon: getUrl('qbb'), title: '情报板', translate: 'INTELLIGENCE BOARD'},
};

export const service = {
    1: {icon: getUrl('xjc'), title: '小车停车区', translate: 'Car parking area'.toUpperCase()},
    2: {icon: getUrl('kc'), title: '大客车停车区', translate: 'Bus parking area'.toUpperCase()},
    3: {icon: getUrl('hc'), title: '货车停车区', translate: 'Car parking area'.toUpperCase()},
    4: {icon: getUrl('cdz'), title: '充电桩停车区', translate: 'Charging station'.toUpperCase()},
    5: {icon: getUrl('whp'), title: '危化品停车区', translate: 'hazardous chemicals'.toUpperCase()},
    6: {icon: getUrl('ccc'), title: '超长车停车区', translate: 'LONG-TERM PARKING AREA'.toUpperCase()},
    7: {icon: getUrl('xr'), title: '行人数量', translate: 'Number of pedestrians'.toUpperCase()},
    'enter': {icon: getUrl('ck'), title: '出口流量数', translate: 'Outlet flow'.toUpperCase()},
    'out': {icon: getUrl('rk'), title: '入口流量数', translate: 'Inlet flow'.toUpperCase()},
};
// , // 车辆类型：3小客车、4面包车、5货车、6大巴车、7两轮车、8危化品车、9轿车、10行人、99其他，可不传
//  0 - 所有设备 21 - 车道指示器 53 - 卷闸门 54 - 风机
// 19 - 照明 55 - CO / VI 56 - 亮度仪 2 - 摄像机
// 5 - 雷达 6 - MEC 58 - 情报板 59 - 水泵 60 - 卡口
export const equipOverview = new Map([
    // [21, {url: biany, title: '车道指示器'}],
    // [5, {url: lda, title: '雷达'}],
    // [2, {url: sxji, title: '摄像机'}],
    // [6, {url: biany, title: '边缘计算设备'}],
    // [58, {url: qbb, title: '情报板'}],
]);



import cdz from '@/assets/images/serviceM/cdz.png';
import gjc from '@/assets/images/serviceM/gjc.png';
import ck from '@/assets/images/serviceM/ck.png';
import rk from '@/assets/images/serviceM/rk.png';
import ccc from '@/assets/images/serviceM/ccc.png';
import lw from '@/assets/images/serviceM/lw.png';
import dhc from '@/assets/images/serviceM/dhc.png';
import wxc from '@/assets/images/serviceM/wxc.png';
import xjc from '@/assets/images/serviceM/xjc.png';
export const serviceOverview = new Map([
    [1, {url: xjc, title: '小车停车区'}],
    [2, {url: gjc, title: '大车停车区'}],
    [3, {url: dhc, title: '货车停车区'}],
    [4, {url: cdz, title: '充电桩停车区'}],
    [5, {url: wxc, title: '危险品停车区'}],
    [6, {url: ccc, title: '超长停车区'}],
    ['flow', {
        1: {

            title: '出口流量数',

            url: ck,
        },
        2: {

            title: '入口流量数',

            url: rk,
        },
    }],
]);


export const confirmResult = {
    1: '正报',
    2: '误报',
    3: '待确认',
};
export const vehicleType = {
    1: '一型客车',
    2: '二型客车',
    3: '三型客车',
    4: '四型客车',
    11: '一型货车',
    12: '二型货车',
    13: '三型货车',
    14: '四型货车',
    15: '五型货车',
    16: '六型货车',
    21: '一型专项作业车',
    22: '二型专项作业车',
    23: '三型专项作业车',
    24: '四型专项作业车',
    25: '五型专项作业车',
    26: '六型专项作业车',
    99: '其他',
};
export const vehicleTypeOption = [
    {
        value: '1',
        label: '一型客车',
    },
    {
        value: '2',
        label: '二型客车',
    },
    {
        value: '3',
        label: '三型客车',
    },
    {
        value: '4',
        label: '四型客车',
    },
    {
        value: '11',
        label: '一型货车',
    },
    {
        value: '12',
        label: '二型货车',
    },
    {
        value: '13',
        label: '三型货车',
    },
    {
        value: '14',
        label: '四型货车',
    },
    {
        value: '15',
        label: '五型货车',
    },
    {
        value: '16',
        label: '六型货车',
    },
    {
        value: '21',
        label: '一型专项作业车',
    },
    {
        value: '22',
        label: '二型专项作业车',
    },
    {
        value: '23',
        label: '三型专项作业车',
    },
    {
        value: '24',
        label: '四型专项作业车',
    },
    {
        value: '25',
        label: '五型专项作业车',
    },
    {
        value: '26',
        label: '六型专项作业车',
    },

];

export const carType = {
    3: '小客车',
    4: '面包车',
    5: '货车',
    6: '大巴车',
    7: '两轮车',
    8: '危化品车',
    9: '轿车',
    10: '行人',
    99: '其他',
};
export const typeDict = [
    {
        value: '3',
        label: '小客车',
    },
    {
        value: '4',
        label: '面包车',
    },
    {
        value: '5',
        label: '货车',
    },
    {
        value: '6',
        label: '大巴车',
    },
    {
        value: '7',
        label: '两轮车',
    },
    {
        value: '8',
        label: '危化品车',
    },
    {
        value: '9',
        label: '轿车',
    },
    {
        value: '10',
        label: '行人',
    },
    {
        value: '99',
        label: '其他',
    },

];

export const eventType = {
    1: {title: '重点车辆驶入', icon: 'car'},
    2: {title: '重点车辆驶出', icon: 'car'},
    3: {title: '危险化品车超时', icon: 'whp'},
};
export const sourceType = {
    1: '边缘计算设备',
    2: '重点车辆驶出',
    3: '危险化品车超时',
};
export const boardType = new Map([
    [1, '诱导屏'],
    [2, '车位显示'],
    [3, '门架'],

]);
