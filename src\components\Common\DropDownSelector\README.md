# 下拉选择框
### props传参
|参数名|类型|require|default|含义
|-|-|-|-|-|
`value.sync`|number|true|0|当前选中对象的value,默认选中数组第一个
placeholder|string|false|''|`使用该属性title失效，且不会默认选中数组第一个`
title|string|false|统计指标|标题
multiple|boolean|false|false|是否允许多选
height|number|false|157|控制下拉框高度（单位为px）
list|array|true|[]|[{label: '展示的值', value: '值'}]


### Events
|事件名称|说明|回调参数|
|-|-|-|
change|当绑定值发生变化时触发|multiple为true：（item：点击的对象，items：当前选中的所有对象）；false：item：点击的对象。


# 级联选择器

### props传参
|参数名|类型|require|default|含义
|-|-|-|-|-|
`cascade`|boolean|false|false|切换为级联选择器，暂不支持多选
`value.sync`|number|true|-1|当前选中对象的value,`默认选中数组第一个`
placeholder|string|false|''|`使用该属性title失效，且不会默认选中数组第一个`
height|number|false|157|控制下拉框高度（单位为px）
list|array|true|[]|[{label: '展示的值', value: '值', children: [{label: '展示的值', value: '值'}]}]
title|string|false|统计指标|标题


### Events
|事件名称|说明|回调参数|
|-|-|-|
change|当绑定值发生变化时触发|点击的对象。