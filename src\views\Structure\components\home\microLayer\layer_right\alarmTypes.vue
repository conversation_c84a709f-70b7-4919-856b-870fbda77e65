<template>
    <div class="alarm-types">
        <fokai-line
            v-if="apiType" :data="list"
            y-name="数量"
        />
        <no-data v-else/>
    </div>
</template>

<script>
import {ref, watch, computed} from 'vue';
import {FokaiLine, NoData} from '@/components/Common';
import {showNumWarningMonth, showNumWarningMonthNow, showNumWarningQuarter} from '@/api/structure/index';
import {structureId} from '@/views/Structure/utils/index';
export default {
    name: '结构物告警类型统计',
    props: {
        type: {
            type: String,
            default: '',
        },
    },
    components: {
        FokaiLine, NoData,
    },
    setup(props) {
        const list = ref([
            {
                name: '预警总数',
                color: 'rgb(87, 255, 213)',
                colorStops: ['rgba(88, 255, 220, .3)', 'rgba(88, 255, 220, 0.1)'],
                word: '',
                data: [
                    {name: 'aa', value: 1},
                    {name: 'bb', value: 331},
                    {name: 'cc', value: 24},
                    {name: 'dd', value: 56},
                    {name: 'ee', value: 171},
                    {name: 'ff', value: 500},
                    {name: 'gg', value: 209},
                ],
            },
            {
                name: '一级预警',
                color: 'rgb(255, 85, 85)',
                colorStops: ['rgba(255, 85, 85, .36)', 'rgba(255, 85, 85, 0)'],
                word: 'one',
                data: [
                    {name: 'aa', value: 345},
                    {name: 'bb', value: 64},
                    {name: 'cc', value: 75},
                    {name: 'dd', value: 502},
                    {name: 'ee', value: 378},
                    {name: 'ff', value: 284},
                    {name: 'gg', value: 749},
                ],
            },
            {
                name: '二级预警',
                color: 'rgb(238, 168, 15)',
                colorStops: ['rgba(238, 168, 15, .3)', 'rgba(238, 168, 15, 0)'],
                word: 'two',
                data: [
                    {name: 'aa', value: 33},
                    {name: 'bb', value: 745},
                    {name: 'cc', value: 157},
                    {name: 'dd', value: 91},
                    {name: 'ee', value: 389},
                    {name: 'ff', value: 178},
                    {name: 'gg', value: 481},
                ],
            },
            {
                name: '三级预警',
                color: 'rgb(255, 249, 147)',
                colorStops: ['rgba(255, 249, 147, .3)', 'rgba(255, 249, 147, 0.1)'],
                word: 'three',
                data: [
                    {name: 'aa', value: 771},
                    {name: 'bb', value: 190},
                    {name: 'cc', value: 224},
                    {name: 'dd', value: 516},
                    {name: 'ee', value: 71},
                    {name: 'ff', value: 400},
                    {name: 'gg', value: 409},
                ],
            },
        ]);

        const apiType = ref(false);

        const initData = () => {
            if (props.type === '1') {
                let dates = [];
                let today = new Date();

                // 循环生成近一个月（30天）的日期
                for (let i = 29; i >= 0; i--) {
                    let date = new Date(today);
                    date.setDate(today.getDate() - i);
                    // 格式化日期为 "MM.DD" 格式，注意月份和日期需要补零
                    // eslint-disable-next-line max-len, vue/max-len
                    let formattedDate = `${String(date.getMonth() + 1).padStart(2, '0')}.${String(date.getDate()).padStart(2, '0')}`;
                    // 将格式化后的日期字符串添加到数组中
                    dates.push({name: formattedDate, value: 0});
                }

                list.value = list.value.map(item => ({
                    ...item,
                    data: dates,
                }));
            }
            // 月取月份
            else if (props.type === '2') {

                let months = [];
                let today = new Date();
                let currentMonth = today.getMonth();

                // 循环生成从本年1月到当前月份的月份数字
                for (let month = 0; month <= currentMonth; month++) {
                    // 月份从0开始，所以要加1，并且保证格式为两位数
                    let formattedMonth = String(month + 1).padStart(2, '0');
                    months.push({name: formattedMonth + '月', value: 0});
                }

                list.value = list.value.map(item => ({
                    ...item,
                    data: months,
                }));
            }
            else {
                // 季度取季度
                list.value = list.value.map(item => ({
                    ...item,
                    // eslint-disable-next-line max-len, vue/max-len
                    data: [{name: 'Q1', value: 0}, {name: 'Q2', value: 0}, {name: 'Q3', value: 0}, {name: 'Q4', value: 0}],
                }));
            }
        };

        const apiFn = computed(() => {
            switch (props.type) {
                case '1':
                    return () => {
                        showNumWarningMonth(structureId.value).then(res => {
                            apiType.value = true;
                            if (res.data) {
                                list.value = list.value.map(item => {
                                    // eslint-disable-next-line max-nested-callbacks
                                    const data = item.data.map(value => {
                                        // eslint-disable-next-line max-len, vue/max-len, max-nested-callbacks
                                        const obj = res.data.find(e => e.dateTimeScope.split('-').slice(1).join('.') === value.name);

                                        return obj
                                            ? {name: value.name, value: obj[item.word + 'warningNum']
                                                || obj[item.word + 'WarningNum']}
                                            : {name: value.name, value: 0};
                                    });
                                    return {
                                        ...item,
                                        data,
                                    };
                                });
                            }
                        });
                    };
                case '2':
                    return () => {
                        showNumWarningMonthNow(structureId.value).then(res => {
                            apiType.value = true;
                            if (res.data) {
                                list.value = list.value.map(item => {
                                    // eslint-disable-next-line max-nested-callbacks
                                    const data = item.data.map(value => {
                                        // eslint-disable-next-line max-len, vue/max-len, max-nested-callbacks
                                        const obj = res.data.find(e => value.name.includes(e.dateTimeScope.split('-')[1]));

                                        return obj
                                            ? {name: value.name, value: obj[item.word + 'warningNum']
                                                || obj[item.word + 'WarningNum']}
                                            : {name: value.name, value: 0};
                                    });
                                    return {
                                        ...item,
                                        data,
                                    };
                                });
                            }
                        });
                    };
                case '3':
                    return () => {
                        showNumWarningQuarter(structureId.value).then(res => {
                            apiType.value = true;
                            if (res.data) {
                                list.value = list.value.map(item => {
                                    // eslint-disable-next-line max-nested-callbacks
                                    const data = item.data.map(value => {
                                        // eslint-disable-next-line max-len, vue/max-len, max-nested-callbacks
                                        const obj = res.data.find(e => e.dateTimeScope.split(' ')[1] === value.name);

                                        return obj
                                            ? {name: value.name, value: obj[item.word + 'warningNum']
                                                || obj[item.word + 'WarningNum']}
                                            : {name: value.name, value: 0};
                                    });
                                    return {
                                        ...item,
                                        data,
                                    };
                                });
                            }
                        });
                    };
                default:
                    return null;
            }
        });

        watch(() => props.type, () => {
            if (!apiFn.value) return;
            initData();
            apiFn.value();
        }, {immediate: true});

        return {
            list,
            apiType,
        };
    },
};
</script>

<style lang="less" scoped>
.alarm-types {
    width: 100%;
    height: 322px;
}
</style>