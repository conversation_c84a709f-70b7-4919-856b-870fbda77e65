<template>
    <Card
        class="toll-station-info-card"
        title="收费站信息"
        card-type="card-long-2"
    >
        <template #content>
            <div class="toll-station-info-card__content">
                <div class="toll-station-info-card__content-top">
                    <div class="toll-station__name">
                        <div>
                            <Icon name="toll-station"/>
                        </div>
                        <div>{{ cardData.stationName }}</div>
                    </div>
                    <!-- <div class="btn-red">开通车道</div> -->
                </div>
                <div class="toll-station-info-card__content-bottom">
                    <el-row :gutter="ratio * 8">
                        <el-col
                            v-for="card in getCardInfo"
                            :key="card"
                            :span="8"
                        >
                            <div class="toll-station-info-card__item">
                                <span>{{ card.label }}</span>
                                <span>{{ card.value }}</span>
                            </div>
                        </el-col>
                    </el-row>
                </div>
            </div>

        </template>
    </Card>
</template>

<script setup>
import {Card, Icon} from '@/components/Common';
import {Row as ElRow, Col as ElCol} from 'element-ui';
import {useUnit} from '@/utils';
import {ref, computed, onMounted} from 'vue';
import {tollStation} from '@/views/TollStation/store';
import {getTollInfo} from '@/api/tollStation';
import {directionMap} from '@/config';

const {ratio} = useUnit();

const cardData = ref({
    direction: '',
    stationStakeNumber: '',
    enterEtcAndMtcLaneNum: '',
    enterLaneNum: '',
    enterFlow: '',
    exitFlow: '',
    exitEtcAndMtcLaneNum: '',
    exitLaneNum: '',
    exitMixLaneNum: '',
    sectionCode: '',
    sectionName: '',
    stationName: '',
});

const cardConfig = [
    {
        label: '控制方向',
        field: 'direction',
    },
    {
        label: '位置桩号',
        field: 'stationStakeNumber',
    },
    {
        label: '入口ETC/MTC车道数',
        field: 'enterEtcAndMtcLaneNum',
    },
    {
        label: '入口车道数',
        field: 'enterLaneNum',
    },
    {
        label: '入口车道流量',
        field: 'enterFlow',
    },
    {
        label: '出口车道流量',
        field: 'exitFlow',
    },
    {
        label: '出口ETC/MTC车道数',
        field: 'exitEtcAndMtcLaneNum',
    },
    {
        label: '出口车道数',
        field: 'exitLaneNum',
    },
    {
        label: '出口混合车道数',
        field: 'exitMixLaneNum',
    },
];

const getCardInfo = computed(() => {
    return cardConfig.map(item => {
        return {
            label: item.label,
            value: cardData.value[item.field],
            unit: item.unit || '',
        };
    });
});

async function fetchData() {
    const {data} = await getTollInfo({
        stationId: tollStation.value.stationId,
        stationName: tollStation.value.stationName,
    });
    cardData.value = {
        direction: directionMap[data.direction],
        stationStakeNumber: data.stationStakeNumber,
        enterEtcAndMtcLaneNum: `${data.enterEtcLaneNum}个 / ${data.enterMtcLaneNum}个`,
        enterLaneNum: `${data.enterLaneNum}个`,
        enterFlow: `${data.enterFlow}辆 / 天`,
        exitFlow: `${data.exitFlow}辆 / 天`,
        exitEtcAndMtcLaneNum: `${data.exitEtcLaneNum}个 / ${data.exitMtcLaneNum}个`,
        exitLaneNum: `${data.exitLaneNum}个`,
        exitMixLaneNum: `${data.exitMixLaneNum}个`,
        sectionCode: data.sectionCode,
        sectionName: data.sectionName,
        stationName: data.stationName,
    };
}

onMounted(() => {
    fetchData();
});

</script>

<style lang="less" scoped>
.toll-station-info-card {
    /deep/ .el-col {
        &:not(:nth-last-child(-n+2)) {
            margin-bottom: 8px;
        }
    }

    &__content {
        height: 172px;
        background-color: rgba(18, 74, 166, .3);
        backdrop-filter: blur(10px);
        border-top: 1px solid;
        padding: 16px 24px;
        border-image:
            linear-gradient(
                to right,
                rgb(36, 104, 242),
                rgba(1, 255, 229, .5)
            ) 1;

        &-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 18px;
            color: #fff;
        }

        &-bottom {
            margin-top: 12px;
        }
    }

    &__item {
        height: 28px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: rgba(0, 98, 167, .3);
        padding: 0 8px;
    }
}

.toll-station__name {
    display: flex;

    div:nth-child(1) {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 28px;
        height: 28px;
        border: 1px	solid rgba(255, 255, 255, .4);
    }

    div:nth-child(2) {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 4px;
        height: 28px;
        padding: 0 16px;
        font-size: 16px;
        background-color: rgb(36, 104, 242);
    }
}

// .btn-red {
//     height: 28px;
//     line-height: 28px;
//     text-align: center;
//     padding: 0 20px;
//     font-size: 16px;
//     background-color: rgb(236, 62, 62);
// }
</style>