

import {getGreatCircleDistance} from '@/utils/common';
import {debounce} from 'lodash';
import {
    gltfLoaderEnhanced,
    geojsonUtils,
} from '@baidu/mapv-three';
import {shallowRef, watch} from 'vue';

const main = (map, props, cameraCenter) => {
    const tollStations = shallowRef({});
    const tollModel = shallowRef({});

    // 获取重点路段模型数据
    const getModelJsonByMain = async () => {
        const url = import.meta.env.MODE === 'release'
            ? 'https://gjdt.private.gdcg.cn/twin/web/fe/02/data/json/modelInfos.json'
            : 'https://bj.bcebos.com/v1/static-resources/models/广东四维仿真/收费站和停车区/modelInfos.json';
        const data
            = await fetch(url,
                {
                    method: 'GET',
                    mode: 'cors',
                }).then(response => response.json());
        if (data) {
            return data;
        }
        return {};
    };
    // 获取非重点路段模型数据
    const getModelJsonByNotMain = async () => {
        const url = import.meta.env.MODE === 'release'
            ? 'https://gjdt.private.gdcg.cn/data/web/fe/02/data/json/modelLocations.json'
            : 'https://bj.bcebos.com/v1/static-resources/models/广东四维仿真/收费站和停车区/modelLocations.json';
        const data
            = await fetch(url,
                {
                    method: 'GET',
                    mode: 'cors',
                })
                .then(response => response.json());
        if (data) {
            return data;
        }
        return {};
    };

    // 添加收费站模型
    const addTollStation = () => {
        // 重点路段
        const filterMainData = data => {
            let result = {};
            Object.keys(data).forEach(keyP => {
                const parent = data[keyP];
                Object.keys(parent).forEach(keyH => {
                    const highway = parent[keyH];
                    Object.keys(highway).forEach(key => {
                        if (key === '收费站' || key === '服务区') {
                            Object.keys(highway[key])?.forEach(name => {
                                highway[key][name].isMain = true;
                                if (!highway[key][name].lnglat_position) {
                                    const lnglat_position = geojsonUtils.unprojectPointArr(highway[key][name].position);
                                    highway[key][name].lnglat_position = lnglat_position;
                                }
                            });
                            result = Object.assign({}, result, highway[key]);
                        }
                    });
                });
            });

            return result;
        };

        // 非重点路段
        const filterNotMainData = data => {
            let result = {};
            if (data) {
                data?.forEach?.(item => {
                    const {path} = item;
                    result[path] = {
                        ...item,
                        path: `${path}.glb`,
                        isMain: false,
                        lnglat_position: geojsonUtils.unprojectPointArr(item.position),
                    };
                });
            }
            return result;
        };

        Promise.all([getModelJsonByMain(), getModelJsonByNotMain()]).then(responses => {
            const [mainData, notMainData = []] = responses;
            const tollMainData =  filterMainData(mainData);
            const tollnotMainData = filterNotMainData(notMainData);
            tollStations.value = Object.assign({}, tollMainData, tollnotMainData) || {};
        });
    };

    if (props.options?.showTollStations) {
        addTollStation();

        watch(() => cameraCenter.value, debounce(() => {
            if (Object.values(cameraCenter.value)) {
                // eslint-disable-next-line no-use-before-define
                computeModelsRange();
            }
        }, 200));
    }

    const addModelNext = (url, position, key = 1, scale = 7, name) => {
        if (url) {
            const _key = `${name}-${key}`;
            gltfLoaderEnhanced.load(url, gltf => {
                if (!tollModel.value) {
                    tollModel.value = {};
                }
                tollModel.value[_key] = gltf.scene;
                tollModel.value[_key].position.x = position[0];
                tollModel.value[_key].position.y = position[1];
                tollModel.value[_key].scale.setScalar(scale);
                tollModel.value[_key].rotation.x = Math.PI / 2;

                tollModel.value[_key].name = name;
                // console.log(tollModel.value[_key], name, '==========正在渲染的收费站/服务区模型');
                map.value.add(tollModel.value[_key]);
            });
        }
    };

    // 计算视角范围内的收费站模型
    const computeModelsRange = (RANGE = 2000) => {
        const renderModels = {};
        const {x, y, z} = cameraCenter.value;
        Object.keys(tollStations.value).forEach(name => {
            const info = tollStations.value[name];
            const {lnglat_position} = info;
            if (!lnglat_position || !cameraCenter.value) {
                return;
            }
            const _key = `${name}-toll`;
            if (tollModel.value?.[_key]) {
                return;
            }
            const distance = getGreatCircleDistance(lnglat_position, [x, y, z]);
            if (distance <= RANGE) {
                renderModels[name] = tollStations.value[name];
            }
        });

        Object.keys(renderModels).forEach(name => {
            const info = renderModels[name];
            const {position, path, isMain} = info;
            const url = import.meta.env.MODE === 'release'
                ? `https://gjdt.private.gdcg.cn${isMain ? '/twin/data/web/models/' : '/data/web/models/param-models/'}${path}`
                : `https://bj.bcebos.com/v1/static-resources/models/广东四维仿真/收费站和停车区/${path}`;
            addModelNext(url, position, 'toll', 1, name);
        });
    };
};

export default main;