<script>
import {onMounted, watch, onBeforeUnmount, computed} from 'vue';
export default {
    name: 'MapPolygon',
    props: {
        list: {
            type: Array,
            default: () => [],
        },
        info: {
            type: Object,
            default: () => ({}),
        },
        polygonInstace: {
            type: Object,
            required: true,
        },
    },
    setup(props) {
        const name = computed(() => props.info.name || 'MapPolygon');
        const color = computed(() => props.info.color || 'rgb(255, 255, 255)');
        const transparent = computed(() => props.info.hasOwnProperty('opacity'));
        const opacity = computed(() => props.info.opacity ?? 1);

        const addPolygon = () => {
            if (!props.list.length) {
                return;
            }
            props.polygonInstace.addPolygon(name.value, props.list, {
                color: color.value,
                transparent: transparent.value,
                opacity: opacity.value,
            });
        };

        const removePolygon = name => {
            props.polygonInstace.removePolygonByName(name);
        };

        watch(() => [
            props.list,
            name.value,
            color.value,
            transparent.value,
            opacity.value,
        ], (_, oldData) => {
            const [, name] = oldData;
            removePolygon(name);
            addPolygon();
        });

        onMounted(() => {
            addPolygon();
        });

        onBeforeUnmount(() => removePolygon(name.value));
    },
};
</script>
