# EmulationLayer 四维仿真图层

### 介绍
提供了EmulationLayer图层组件，能够快速构建仿真图层并提供了默认组件，以及各子组件，可使用其中的子组件进行二次开发。

### 安装教程

1.  npm i apaas-maplayer
2. import {EmulationLayer} from 'apaas-maplayer'

### 使用方法
> 先在外部初始化组件
```javascript
import {EmulationLayer, initEmulationLayerConfig} from 'apaas-maplayer'

> 注册组件
```javascript
components: {
  EmulationLayer
}
```
> template中声明

```html
<template>
  <section>
    <map-component
        ref="mapRef"
        :options="{
            center: [113.55, 23.5555],
            showSatelliteMap: true,
        }"
        class="map-box"
        :style="{
            height: height + 'px',
        }"
        @mapLoaded="mapLoaded"
    >
        <emulation-layer
            v-if="mapShow"
        />
    </map-component>
  </section>
</template>
```

### 注意事项
需要先调用 `initEmulationLayerConfig()` 方法进行初始化配置，地图初始化执行完毕后再让结构物健康监测图层渲染。示例如下：

```html
<template>
  <section>
     <map-component
        ref="mapRef"
        :options="{
            center: [113.55, 23.5555],
            showSatelliteMap: true,
        }"
        class="map-box"
        :style="{
            height: height + 'px',
        }"
        @mapLoaded="mapLoaded"
    >
        <emulation-layer
            v-if="mapShow"
        />
    </map-component>
  </section>
</template>

<script setup>
    const mapShow = ref(false);

    // 地图加载完成后执行的回调函数（此处由使用方控制）
    const mapLoaded = () => {
        initEmulationLayerConfig({
            engine: mapRef.value.map,
            apiHost,
        });
        mapShow.value = true;
    };

</script>
```

## EmulationLayer
### 参数说明  props
|Prop name|Type|Default|Description|
|---|---|---|---|
|`engine`|`object`|`required`|地图实例|
|`apiHost`|`string`|`window.location.origin`|api接口服务ip地址|
|`wsHost`|`string`|`window.location.host`|api接口ws服务ip地址|
|`BaseVideoHost`|`string`|`可选 默认为window.location.origin`|视频组件服务地址|

## 仿真子组件
### 仿真孪生组件
#### 使用
```html
<script>
    import {EmulationLayer_EmulationTwin} from'apaas-maplayer'

    components: {
        EmulationLayer_EmulationTwin
    }
</script>

<template>
    <EmulationLayer_EmulationTwin :id="id" :emulationType="1" />
</template>
```

#### 所需参数
|参数名称|数据类型|是否必传|参数含义|
|---|---|---|---|
|id|`number`|`true`|策略id|
|emulationType|`number`|`默认为1 1-实时事件`|仿真类型|

#### 参数详细数据
id：
```js
106
```

### 仿真孪生 - 事件管控 区域绘制组件
#### 使用
```html
<script>
    import {EmulationLayer_DrewItem} from'apaas-maplayer'

    components: {
        EmulationLayer_DrewItem
    }
</script>

<template>
    <EmulationLayer_DrewItem :id="id" />
</template>
```

#### 所需参数
|参数名称|数据类型|是否必传|参数含义|
|---|---|---|---|
|data|`object`|`true`|策略详情|
|engine|`object`|`true`|地图实例|
|emulationId|`number`|`true`|仿真Id|

#### 参数详细数据
id：
```js
data: {
    "strategy": {
        "controlDuration": null,
        "controlLength": 111,
        "controlStake": "K7+100",
        "lane": "1,2",
        "laneControlType": "1,2",
        "limitSpeed": "111,233",
        "position": ";",
        "type": 2,
        "rampName": null,
        "entranceExitType": null,
        "entryConfig": null,
        "exportConfig": null
    },
    "centerPoint": "113.2500874535461,23.600206326616483,104.254794896729",
    "canvasMap": {
        "1": [
            "113.25000109057525,23.6003399243364,104.28754232229",
            "113.25003295967397,23.60030862137151,104.243909776788",
            "113.25011465498842,23.600230550078464,104.133474584794",
            "113.25011463199121,23.60023057312064,104.133474584794",
            "113.2502427865873,23.600108540828817,103.884795557544",
            "113.25033709201814,23.600020830319792,103.721718701979",
            "113.2503370245522,23.60002088000391,103.721718701979",
            "113.2505014554322,23.599869329494002,103.374640822605",
            "113.25047472285631,23.59984469727084,103.374640822605",
            "113.2503102919763,23.59999624775293,103.721718701979",
            "113.25031022451036,23.599996297437027,103.721718701979",
            "113.2502159190792,23.600084007930008,103.884795557544",
            "113.2500874531753,23.60020633316214,104.133474584794",
            "113.25008743017808,23.60020635620431,104.133474584794",
            "113.25000573486251,23.600284427483274,104.243909776788",
            "113.24997350942724,23.60031607566236,104.28754232229",
            "113.2499760939904,23.6003133580245,104.415359567177",
            "113.25000547638865,23.600284655758305,104.376124254669",
            "113.2500874535461,23.600206326616483,104.254794896729",
            "113.25008743098913,23.60020634591817,104.254794896729",
            "113.25021453707957,23.60008530220877,104.016181356525",
            "113.25031033105743,23.59999620698891,103.853573713283",
            "113.25028436787292,23.599972499909985,103.853573713283",
            "113.25018857389475,23.60006159511411,104.016181356525",
            "113.25006116597721,23.600182922854707,104.254794896729",
            "113.25006114342024,23.600182942156376,104.254794896729",
            "113.2499791662617,23.600261271284545,104.376124254669",
            "113.24994950601196,23.60029024197436,104.415359567177"
        ],
        "2": [
            "113.25000109057525,23.6003399243364,104.28754232229",
            "113.25003295967397,23.60030862137151,104.243909776788",
            "113.25011465498842,23.600230550078464,104.133474584794",
            "113.25011463199121,23.60023057312064,104.133474584794",
            "113.2502427865873,23.600108540828817,103.884795557544",
            "113.25033709201814,23.600020830319792,103.721718701979",
            "113.2503370245522,23.60002088000391,103.721718701979",
            "113.2505014554322,23.599869329494002,103.374640822605",
            "113.25047472285631,23.59984469727084,103.374640822605",
            "113.2503102919763,23.59999624775293,103.721718701979",
            "113.25031022451036,23.599996297437027,103.721718701979",
            "113.2502159190792,23.600084007930008,103.884795557544",
            "113.2500874531753,23.60020633316214,104.133474584794",
            "113.25008743017808,23.60020635620431,104.133474584794",
            "113.25000573486251,23.600284427483274,104.243909776788",
            "113.24997350942724,23.60031607566236,104.28754232229",
            "113.2499760939904,23.6003133580245,104.415359567177",
            "113.25000547638865,23.600284655758305,104.376124254669",
            "113.2500874535461,23.600206326616483,104.254794896729",
            "113.25008743098913,23.60020634591817,104.254794896729",
            "113.25021453707957,23.60008530220877,104.016181356525",
            "113.25031033105743,23.59999620698891,103.853573713283",
            "113.25028436787292,23.599972499909985,103.853573713283",
            "113.25018857389475,23.60006159511411,104.016181356525",
            "113.25006116597721,23.600182922854707,104.254794896729",
            "113.25006114342024,23.600182942156376,104.254794896729",
            "113.2499791662617,23.600261271284545,104.376124254669",
            "113.24994950601196,23.60029024197436,104.415359567177"
        ]
    }
}
```



### 仿真孪生 - 交通仿真方案列表组件
#### 使用
```html
<script>
    import {EmulationLayer_EmulationList} from'apaas-maplayer'

    components: {
        EmulationLayer_EmulationList
    }
</script>

<template>
    <EmulationLayer_EmulationList 
        :emulation-type="1"
        :platform-source="1"
        @add="createCase"
        @edit="editCase" />
</template>
```

#### 所需参数
|参数名称|数据类型|是否必传|参数含义|
|---|---|---|---|
|emulationType|`string|number`|`可选 默认为1`|仿真类型 1-实时事件 3-施工区域|
|platformSource|`string|number`|`可选 默认为1`|平台来源 1-仿真图层 2-xxx|
|titleConfig|`object`|`可选`|头部标题配置 是否显示按钮|
|createConfig|`object`|`可选`|创建方案配置|
|info|`object`|`可选`|列表数据配置|
|@add|`Fuction`|`可选`|点击新增回调|
|@edit|`Fuction`|`可选`|点击编辑回调|
|@onCopy|`Fuction`|`可选`|点击复制回调|
|@onRun|`Fuction`|`可选`|点击运行回调|
|@onCheck|`Fuction`|`可选`|点击查看回调|
|@onDel|`Fuction`|`可选`|点击删除回调|
|@onReport|`Fuction`|`可选`|点击仿真报告回调|
|@onPrev|`Fuction`|`可选`|点击列表分页上一页回调|
|@onNext|`Fuction`|`可选`|点击列表分页下一页回调|

#### 参数详细数据
titleConfig：
```js
{
    title: '交通仿真方案列表',
    btnName: '新增' // 不展示按钮 可设置为空或null
}
```
createConfig
```js
// 是否是创建仿真方案
{
    show: false, // 可以用于控制是否显示创建方案组件
    type: 'add', // 创建方案类型 add-新增 edit-编辑 create-创建
    id: null, // 编辑时需要传id 注意退出编辑模式时 需要重置此值为空
}
```

info：
```js
// 接口详细说明见服务接口说明
// 接口名称 emulation/schemeList
{
    'pageNo': 1, // 当前页
    'pageSize': 10, // 每页条数
    'total': 75, // 总条数
    'items': [ // 列表
        {
            'sumoCity': '广东省广州市',
            'id': 107,
            'eventId': null,
            'name': 'test',
            'areaChoose': 1,
            'highSpeedName': 'G1523',
            'tollName': null,
            'emulationStartStake': 'K1167+400',
            'emulationEndStake': 'K1361+100',
            'emulationStartTime': '2023-11-27 12:47:58',
            'emulationEndTime': '2023-11-27 13:47:58',
            'weatherScene': 1,
            'emulationType': 1,
            'flowInputType': 1,
            'flowDistance': 1000,
            'customFlowStartTime': '2023-11-27 12:47:58',
            'customFlowEndTime': '2023-11-27 13:47:58',
            'strategyInputType': 2,
            'tollSentryEdgeJson': null,
            'status': 1,
            'eventList': [
                {
                    'closeLane': '2',
                    'closeLanePosition': '',
                    'constructionCornerGeoList': null,
                    'constructionGeoList': null,
                    'constructionLength': 0,
                    'direction': 2,
                    'downstreamTransitionLength': 0,
                    'duration': 60,
                    'eventLocationType': 1,
                    'eventPosition': '113.29042704434144, 23.58144392059125',
                    'eventStartStake': 'K253+500',
                    'eventStartTime': '2023-11-27 12:47:58',
                    'eventType': 0,
                    'limitSpeed': 0,
                    'upstreamTransitionLength': 0,
                    'highSpeedName': 'G0423',
                    'eventEndTime': '2023-11-27 13:47:58',
                    'eventHectometreStake': 'K253+500',
                    'eventEndStake': 'K253+500',
                    'eventAltitude': 0,
                    'visibility': 0,
                    'influencesLength': 0,
                    'addLaneNum': 1,
                },
            ],
            'flowList': [
                {
                    'sumoCity': '广东省广州市',
                    'highSpeedName': null,
                    'seq': 1,
                    'flowInputType': 1,
                    'rampName': 'G0423(K252+500)',
                    'startStakeNumber': 'K252+500',
                    'startEdgeId': 'sub9-10837467236082583068',
                    'direction': 1,
                    'startTime': '2023-11-27 12:47:58',
                    'endTime': '2023-11-27 13:47:58',
                    'endStakeNumber': '',
                    'endEdgeId': '',
                    'carInfoList': [
                        {
                            'seq': 1,
                            'carFlow': 1000,
                            'etcRatio': 80,
                            'smallProbability': null,
                            'startTime': '2023-11-27 12:47:58',
                            'endTime': '2023-11-27 13:47:58',
                        },
                    ],
                },
                {
                    'sumoCity': '广东省广州市',
                    'highSpeedName': null,
                    'seq': 2,
                    'flowInputType': 1,
                    'rampName': 'G0423(K254+500)',
                    'startStakeNumber': 'K254+500',
                    'startEdgeId': 'sub2-11561788101877320267',
                    'direction': 2,
                    'startTime': '2023-11-27 12:47:58',
                    'endTime': '2023-11-27 13:47:58',
                    'endStakeNumber': '',
                    'endEdgeId': '',
                    'carInfoList': [
                        {
                            'seq': 1,
                            'carFlow': 1000,
                            'etcRatio': 80,
                            'smallProbability': null,
                            'startTime': '2023-11-27 12:47:58',
                            'endTime': '2023-11-27 13:47:58',
                        },
                    ],
                },
            ],
            'strategyList': [
                [
                    {
                        'controlDuration': 222,
                        'controlLength': 10,
                        'controlStake': 'K6+0',
                        'lane': null,
                        'laneControlType': null,
                        'limitSpeed': '122',
                        'position': '112.979754,25.291191',
                        'type': 1,
                        'rampName': null,
                        'entranceExitType': null,
                        'entryConfig': null,
                        'exportConfig': null,
                    },
                ],
            ],
            'laneConfig': {
                'laneTotal': 0,
                'defaultConfig': {
                    'entranceLaneList': [],
                    'existLaneList': [],
                },
                'contrastConfig': {
                    'entranceLaneList': [],
                    'existLaneList': [],
                },
            },
            'model': {
                'normalAcceleration': null,
                'normalDeceleration': null,
                'maxDeceleration': null,
                'smallCarHighestSpeed': null,
                'bigCarHighestSpeed': null,
                'bigCarRate': 10,
                'singleLanePassLimit': null,
                'smallVehicleAccel': 2.6,
                'smallVehicleDecel': 4.5,
                'smallVehicleEmergencyDecel': 9,
                'smallVehicleMaxSpeed': 120,
                'bigVehicleAccel': 1.3,
                'bigVehicleDecel': 4,
                'bigVehicleEmergencyDecel': 7,
                'bigVehicleMaxSpeed': 100,
            },
            'strategyCanvas': null,
        },
    ],
}
```
