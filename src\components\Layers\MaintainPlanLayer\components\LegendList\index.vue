<template>
    <div class="legend-list-container">
        <div
            v-for="item in getOptions"
            :key="item.name"
            class="legend-list-item"
            @click="handleLegendClick(item)"
        >
            <img :src="item.isActive ? item.activeIcon : item.icon" :act="item.name">
        </div>
    </div>
</template>

<script>
import {computed, ref, unref, watch} from 'vue';
import {
    legendList,
} from '@MaintainPlanLayer/config/index';
import {isEqual, cloneDeep} from 'lodash';

export default {
    name: 'LegendList',
    components: {},
    props: {
        value: {
            type: Array,
            default: () => legendList.map(i => i.key),
        },
    },
    setup(props, {emit}) {
        const selectedKeys = ref([]);
        // 组件数据
        const getOptions = computed(() => (
            cloneDeep(legendList).map(i => ({
                ...i,
                isActive: unref(selectedKeys).includes(i.key),
            }))
        ));

        watch(
            () => props.value,
            (newVal, oldVal) => {
                if (isEqual(newVal, oldVal)) return;
                selectedKeys.value = newVal;
            },
            {
                immediate: true,
                deep: true,
            }
        );

        function handleLegendClick({key}) {
            const findIndex = unref(selectedKeys).findIndex(k => k === key);
            if (findIndex === -1) {
                unref(selectedKeys).push(key);
                return;
            }
            unref(selectedKeys).splice(findIndex, 1);
            emit('input', unref(selectedKeys));
            emit('change', unref(selectedKeys));
        }

        return {
            handleLegendClick,
            getOptions,
        };
    },
};
</script>

<style lang="less" scoped>
/* 组件样式 */
.legend-list-container {
    position: absolute;
    right: 20px;
    bottom: 20px;
    display: flex;
    flex-direction: column;

    .legend-list-item {
        width: 48px;
        height: 48px;
        margin-top: 10px;
        cursor: pointer;

        img {
            width: 100%;
            height: 100%;
        }
    }
}
</style>