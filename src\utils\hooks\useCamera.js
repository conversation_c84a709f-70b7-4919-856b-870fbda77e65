import {ref, watch, onBeforeUnmount} from 'vue';

import {throttle} from 'lodash';
import {geojsonUtils} from '@baidu/mapv-three';

const cameraMap = new WeakMap();


const main = (map, cb = () => {}) => {
    const center = ref({});
    const mapCenter = ref({});
    const zoom = ref(null);
    const pitch = ref(null);
    const heading = ref(null);

    const viewChange = e => {
        const {x, y, z} = e.camera.position;
        const [lng, lat, alt] = geojsonUtils.unprojectPointArr([x, y, z]);
        const [mapLng, mapLat, mapAlt] = map.value.map?.getCenter();

        zoom.value = map.value.map?.getZoom();
        pitch.value = map.value.map?.getPitch();
        heading.value = map.value.map?.getHeading();

        center.value = {
            x: lng,
            y: lat,
            z: alt,
        };
        mapCenter.value = {
            x: mapLng,
            y: mapLat,
            z: mapAlt,
        };
        cb?.(center.value, mapCenter.value);
    };

    const viewChanged = throttle(
        viewChange,
        500,
        {
            trailing: true,
            leading: true,
        }
    );

    const renderCallback = (e, a) => {
        if (a.viewChanged) {
            viewChanged(e, a);
        }
    };

    const addEventListener = () => {
        map.value.rendering.addPrepareRenderListener(renderCallback);
    };

    const removeEventListener = () => {
        map.value.rendering.removePrepareRenderListener(renderCallback);
        cameraMap.delete(map);
    };

    const removeCb = () => {
        const cameraMoveBacks = cameraMap.get(map);
        cameraMoveBacks?.forEach((c, index) => {
            if (c === cb) {
                cameraMoveBacks.splice(index, 1);
            }
        });
        if (!cameraMoveBacks.length) {
            removeEventListener();
        }
    };

    const init = () => {
        const cameraMoveBacks = [];
        cameraMap.set(map, cameraMoveBacks);
        addEventListener();
    };

    const addViewChangeCallback = () => {
        if (!cameraMap.has(map)) {
            init();
        }
        viewChange(map.value);
        const cameraMoveBacks = cameraMap.get(map);
        cameraMoveBacks.push(cb);
    };

    onBeforeUnmount(() => {
        removeCb();
    });

    watch(map, v => {
        if (v) {
            addViewChangeCallback();
        }
    }, {
        immediate: true,
    });

    return {
        center,
        mapCenter,
        zoom,
        pitch,
        heading,
    };
};




export default main;