<template>
    <div class="incident-list">
        <api-table
            :api="bridgeEvent"
            :columns="columns"
            :height="172"
            :pagination="false"
            show-scroll
            :request-options="{
                listField: '',
            }"
            :request-params="{
                eventStatus,
                eventType,
            }"
        >
            <template #eventStatus="{row}">
                <div class="incident-list__eventStatus">{{ row.eventStatus === 0 ? '未处置' : '已处置' }}</div>
            </template>
        </api-table>
    </div>
</template>

<script>
import {ApiTable} from '@/components/Common';
import {bridgeEvent} from '@/api/structure/index';
import {ref} from 'vue';
import {useUnit} from '@/utils';
import {trafficTypeDict} from '@/config/structure';
export default {
    name: '交通事件列表',
    props: {
        eventStatus: {
            type: [Number, null],
        },
        eventType: {
            type: [Number, null],
        },
    },
    components: {
        ApiTable,
    },
    setup(props) {
        const {ratio} = useUnit();
        const columns = ref([
            {
                label: '事件类型',
                prop: 'eventType',
                width: `${380 * ratio.value}px`,
                format: e => {
                    return trafficTypeDict.get(e.row.eventType);
                },
                align: 'left',
            },
            {
                label: '方向位置',
                prop: 'direction',
                width: `${140 * ratio.value}px`,
                align: 'left',
                format: e => {
                    return e.row.direction === 1 ? '广州方向' : '开平方向';
                },
            },
            {
                label: '桩号',
                prop: 'stake',
                width: `${130 * ratio.value}px`,
                align: 'center',
            },
            {
                label: '时间',
                prop: 'dateTime',
                width: `${250 * ratio.value}px`,
                align: 'center',
                format: e => {
                    return e.row.dateTime;
                },
            },
            {
                label: '桥梁名称',
                prop: 'bridgeName',
                width: `${150 * ratio.value}px`,
                align: 'center',
            },
            {
                label: '状态',
                prop: 'eventStatus',
                width: `${95 * ratio.value}px`,
                align: 'center',
                slotName: 'eventStatus',
            },
        ]);

        return {
            bridgeEvent,
            columns,
        };
    },
};
</script>

<style lang="less" scoped>
.incident-list {
    width: 100%;
    height: 172px;

    &__eventStatus {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 70px;
        height: 32px;
        border: 1px solid rgb(0, 255, 229);
        font-size: 16px;
        font-weight: 400;
        font-family: 'PingFang';
        color: rgb(0, 255, 229);
    }
}
</style>