<template>
    <div v-if="list.length" class="bridge-technical-ranking">
        <single-data-bar
            y-name="评价等级评分"
            :data="list"
            :need-legend="false"
        />
    </div>
</template>

<script>
import {ref, onMounted} from 'vue';
import {SingleDataBar} from '@/components/Common/index';
import {bridgeGradeList} from '@/api/equipment/facilitydisplay';

export default {
    name: 'bridgeTechnicalRanking',
    components: {
        SingleDataBar,
    },
    setup() {
        const list = ref([]);

        const init = () => {
            bridgeGradeList().then(res => {
                list.value = res.data.map(item => ({
                    ...item,
                    name: item.brdgeName,
                    value: item.mqi || 0,
                    color: 'rgba(87, 255, 213, .6)',
                }));
            });
        };

        onMounted(() => init());

        return {
            list,
        };
    },
};
</script>

<style lang="less" soped>
.bridge-technical-ranking {
    width: 556px;
    height: 320px;
}
</style>