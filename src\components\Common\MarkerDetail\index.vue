<!-- <template>
    <Card title="事件详情" v-bind="$attrs">
        <template #content>
            <div class="fc gap-12 py-15">
                <div class="gr cols-2 gap-12">
                    <span
                        v-for="(v, k) in detailInfo.info" :key="k"
                        class=""
                    >
                        <span class="title">{{ k }}：</span>{{ v }}
                    </span>
                </div>
                <div class="fr gap-12">
                    <div class="w-260 h-190 fc gap-12">
                        <div class="title">事件图片</div>
                        <Image :src="imgSrc"/>
                    </div>
                    <div class="w-260 h-190 fc gap-5" @click="liveVisible = true">
                        <div class="title">实时视频</div>
                        <FlvPlayer
                            :url="detailInfo.liveSrc"
                        />
                    </div>
                    <div class="w-260 h-190 fc gap-5" @click="replayVisible = true">
                        <div class="title">视频回放</div>
                        <VideoPlayer
                            :options="{
                                src: detailInfo.replaySrc,
                            }"
                        />
                    </div>
                </div>
                <template v-if="footer">
                    <div class="fr gap-12 jc">
                        <div class="btn info" @click="emit('onMistake')">
                            误报
                        </div>
                        <div class="btn info" @click="emit('onIgnore')">
                            忽略
                        </div>
                        <div class="btn primary" @click="emit('onResources')">
                            应急资源
                        </div>
                        <div class="btn primary" @click="emit('openDisposal')">
                            应急处置
                        </div>
                    </div>
                </template>
            </div>
        </template>
    </Card>
    <Modal
        :visible="liveVisible"
        centered
        title="实时视频"
        :width="1000 * ratio"
        :footer="false"
        @cancel="liveVisible = false"
    >
        <FlvPlayer
            class=""
            :url=" detailInfo.liveSrc"
        />
    </Modal>
    <Modal
        :visible="replayVisible"
        centered
        title="回放视频"
        :width="1000 * ratio"
        :footer="false"
        @cancel="replayVisible = false"
    >
        <VideoPlayer
            class=""
            :options="{
                src: detailInfo.replaySrc,
            }"
        />
    </Modal>
</template>

<script setup lang="ts">
import {ref, computed} from 'vue';
import Card from '@/components/Card/markerCard.vue';
import VideoPlayer from '@/components/video/videoPlayer.vue';
import FlvPlayer from '@/components/video/flvPlayer.vue';

import {Image, message, Tooltip, Modal} from 'ant-design-vue';
import {pxToRem} from '@/utils/util';
const {ratio} = pxToRem();

const props = defineProps<{
    detailInfo: any,
    footer?: boolean
}>();
const emit = defineEmits(['onMistake', 'onIgnore', 'onResources', 'openDisposal']);
const liveVisible = ref(false);
const replayVisible = ref(false);

const imgSrc = computed(() => props.detailInfo.imgSrc.replace(/^\[|\]$/g, ''));

console.log(imgSrc, '123456');
</script>
<style scoped lang="less">
.title {
    color: rgba(231, 239, 255, .7);
}

.btn {
    display: flex;
    width: 140px;
    height: 40px;
    justify-content: center;
    align-items: center;
    font-size: 18px;
    font-weight: 400;
    font-family: PingFang SC;
    color: rgb(1, 255, 229);
    border: 1px	solid rgb(1, 255, 229);
    cursor: pointer;

    &.primary{
        background: rgb(1, 255, 229);
        color: rgb(0, 0, 0);
    }
}
</style> -->