<template>
    <div class="map-line">
        <map-line
            v-for="item in roadLine"
            :key="item"
            :line-instace="lineManager"
            :list="item"
            :info="{
                name: item,
                color: '#86ff00',
            }"
            dynamic-width
        />
    </div>
</template>

<script>
import {getSectionAlt} from '@/api/tollStation';
import {MapLine} from '@/components/Common';
import {lineManager} from '@/views/TollStation/utils';
import {onMounted, ref} from 'vue';

export default {
    components: {
        MapLine,
    },
    setup() {

        const roadLine = ref([]);

        async function initRoadLine() {
            const {data} = await getSectionAlt();
            roadLine.value = data?.sectionList || [];
        }

        onMounted(() => {
            initRoadLine();
        });

        return {
            lineManager,
            roadLine,
        };
    },
};
</script>