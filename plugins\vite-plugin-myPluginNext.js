const suffixWhitelist = ['vue', 'less', 'css'];

export default options => {
    const {mode} = options;
    return {
        name: 'myPluginNext',
        apply: 'build', // 仅在构建时生效
        // enforce: 'post',
        transform(code, id) {
            if (mode !== 'production') return code;
            // if (!suffixWhitelist.some(item => id.endsWith('.' + item))) return;
            const prefix = 'el';
            const replacePrefix = 'my';
            code = code.replaceAll(`.${prefix}-`, `.${replacePrefix}-`)
                .replaceAll(`"${prefix}-`, `"${replacePrefix}-`)
                .replaceAll(`'${prefix}-`, `'${replacePrefix}-`)
                .replaceAll(` ${prefix}-`, ` ${replacePrefix}-`)
                .replaceAll(`=${prefix}-`, `=${replacePrefix}-`)
                .replaceAll(`:${prefix}-`, `:${replacePrefix}-`)
                .replaceAll(`<${prefix}-`, `<${replacePrefix}-`)
                .replaceAll(`</${prefix}-`, `</${replacePrefix}-`);
            return {
                code,
                map: null,
            };
        },
    };
};