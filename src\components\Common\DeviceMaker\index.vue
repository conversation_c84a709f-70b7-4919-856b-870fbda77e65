<!-- 设备设施扎点 -->
<template>
    <div>
        <div
            ref="marker"
            class="marker-container"
            @click="onHandle"
        >
            <slot v-if="$slots.default"></slot>
            <template v-else>
                <div class="marker-dom" :class="statusClassName">
                    <p>{{ info.labelName }}</p>
                </div>
            </template>
        </div>
    </div>
</template>
<script>
import {ref, onMounted, onBeforeUnmount, computed, watch} from 'vue';
import {isEqualWith, isFunction} from 'lodash';
export default {
    props: {
        // 基本信息 包括位置、名称 自定义数据 点击回调等
        info: {
            type: Object,
            default: () => null,
        },
        // 中观管理实例
        managerInstace: {
            type: Object,
            default: () => null,
        },
    },
    setup(props, {slots}) {
        const marker = ref(null);
        const iconName = ref(null);

        const status = computed(() => {
            return props.info?.status || 'normal';
        });
        const size = computed(() => {
            return props.info?.size || 'normal';
        });
        const type = computed(() => {
            return props.info?.type || '桥梁';
        });
        const statusClassName = computed(() => {
            return status.value;
        });
        const addIcon = () => {
            if (!props.info.position) {
                return;
            }
            if (!props.managerInstace || !marker.value) {
                return;
            }
            iconName.value = props.info.pointName;
            props.managerInstace.addWarningPoint(
                iconName.value,
                props.info.position,
                {
                    labelDom: marker.value,
                    size: size.value,
                    status: status.value,
                    type: type.value,
                    bubbleColor: props.info?.bubbleColor,
                    circleColor: props.info?.circleColor,
                    circleBorderColor: props.info?.circleBorderColor,
                    customData: props.info.customData,
                    clickCallback: props.info.clickCallback,
                }
            );
        };

        const removeIcon = () => {
            iconName.value && props.managerInstace.removeWarningPointByName(iconName.value);
        };

        const onHandle = () => {
            props.info.clickCallback(props.info);
        };

        watch(() => props.info, (newVal, oldVal) => {
            // 比较数值是否相等，忽略比较函数
            const equal = isEqualWith(newVal, oldVal, (objValue, othValue) => {
                if (isFunction(objValue) && isFunction(othValue)) {
                    return true;
                }
            });
            if (equal) return;
            addIcon();
        });

        onMounted(() => {
            addIcon();
        });

        onBeforeUnmount(() => {
            removeIcon();
        });
        return {
            marker,
            statusClassName,
            onHandle,
        };
    },
};
</script>
<style lang="less" scoped>
@import url('@/assets/css/maker.less');

.marker-container {
    width: max-content;
    height: max-content;
    position: absolute;
    left: 99999px;
    top: 99999px;

    .marker-dom {
        width: max-content;
        height: max-content;
        box-sizing: border-box;
        border-radius: 2px;
        padding: 4px 12px;
        position: relative;
        white-space: pre;
        cursor: pointer;
        user-select: none;

        &.warning {
            background: radial-gradient(605.13% 55.23% at 46% 47%, rgb(56, 27, 41), rgb(118, 54, 67) 100%);

            &::before {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                z-index: -1;
                margin: -1px;
                border-radius: 2px;
                background: linear-gradient(to right, #ff9095, #f55b62);
            }
        }

        &.normal {
            background: rgba(15, 56, 60, .8);

            &::before {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                z-index: -1;
                margin: -1px;
                border-radius: 2px;
                background: linear-gradient(to right, #d6fcdf, #5de47e);
            }
        }

        &.yellow {
            background: rgba(222, 185, 55, .3);
            border: 1px solid rgb(222, 185, 55, 1);

            &::before {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                z-index: -1;
                margin: -1px;
                border-radius: 2px;
                // background: linear-gradient(to right, rgb(223, 191, 75), rgb(192, 170, 89));
            }
        }

        &.green {
            background: rgba(65, 177, 86, .3);
            border: 1px solid rgb(65, 177, 86);

            &::before {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                z-index: -1;
                margin: -1px;
                border-radius: 2px;
                // background: linear-gradient(to right, rgb(223, 191, 75), rgb(192, 170, 89));
            }
        }

        &.gold {
            background: rgba(222, 185, 55, .3);
            border: 1px solid rgb(222, 185, 55);

            &::before {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                z-index: -1;
                margin: -1px;
                border-radius: 2px;
                // background: linear-gradient(to right, rgb(223, 191, 75), rgb(192, 170, 89));
            }
        }

        &.sandybrown {
            background: rgba(254, 165, 102, .3);
            border: 1px solid rgb(254, 165, 102);

            &::before {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                z-index: -1;
                margin: -1px;
                border-radius: 2px;
                // background: linear-gradient(to right, rgb(223, 191, 75), rgb(192, 170, 89));
            }
        }

        &.tomato {
            background: rgba(236, 84, 58, .3);
            border: 1px solid rgb(236, 84, 58);

            &::before {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                z-index: -1;
                margin: -1px;
                border-radius: 2px;
                // background: linear-gradient(to right, rgb(223, 191, 75), rgb(192, 170, 89));
            }
        }
        &.sky {
            background: rgba(55, 171, 222, 0.3);
            border: 1px solid rgb(55, 171, 222);

            &::before {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                z-index: -1;
                margin: -1px;
                border-radius: 2px;
                // background: linear-gradient(to right, rgb(223, 191, 75), rgb(192, 170, 89));
            }
        }

        &.blue {
            background-color: rgba(32, 71, 118, .61);
            border: 1px solid rgb(63, 145, 245);
            backdrop-filter: blur(18.29px);
        }

        p {
            font-size: 14px;
            color: #fff;
            line-height: 20px;
        }
    }
}

</style>