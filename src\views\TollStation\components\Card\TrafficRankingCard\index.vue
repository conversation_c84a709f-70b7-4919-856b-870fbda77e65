<template>
    <Card title="收费站月均车流量排名" card-type="card-short-2">
        <template #content>
            <api-table
                :columns="columns"
                :api="getTollFlowRank"
                :height="314"
                :pagination="false"
            >
                <template #ranking="{$index}">
                    <div :class="{ranking: $index < 3}">{{ $index + 1 }}</div>
                </template>
            </api-table>
        </template>
    </Card>
</template>

<script setup>
import {ApiTable, Card} from '@/components/Common';
import {useUnit} from '@/utils';
import {getTollFlowRank} from '@/api/tollStation';

const {ratio} = useUnit();

const columns = [
    {
        label: '排名',
        prop: 'props1',
        width: `${62 * ratio.value}px`,
        align: 'center',
        slotName: 'ranking',
    },
    {
        label: '名称',
        prop: 'stationName',
        width: `${155 * ratio.value}px`,
        align: 'center',
    },
    {
        label: '总车流量',
        prop: 'flow',
        width: `${111.2 * ratio.value}px`,
        align: 'center',
    },
    {
        label: '出口流量',
        prop: 'exitFlow',
        width: `${111.2 * ratio.value}px`,
        align: 'center',
    },
    {
        label: '入口流量',
        prop: 'enterFlow',
        width: `${111.2 * ratio.value}px`,
        align: 'center',
    },
];

</script>

<style lang="less" scoped>
.ranking {
    display: inline-block;
    width: 32px;
    height: 24px;
    line-height: 24px;
    background-image: url('@/assets/images/base/ranking-bg.png');
    background-size: 100% 100%;
    font-family: RoboData;
}
</style>