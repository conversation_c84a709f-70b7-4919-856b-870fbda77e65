import {ref} from 'vue';

// 公共服务地址
export const BaseApiHost = ref(import.meta.env.VITE_API_URL || window.location.origin);
//  公共ws服务地址
export const BaseWsHost = ref(import.meta.env.VITE_WS_URL || `${['test', 'development'].includes(import.meta.env.MODE) ? 'ws' : 'wss'}://${window.location.host}`);
// 公共视频静态资源地址
export const BaseVideoHost = ref(import.meta.env.VITE_VIDEOPLAYER_STATIC_URL || window.location.origin);