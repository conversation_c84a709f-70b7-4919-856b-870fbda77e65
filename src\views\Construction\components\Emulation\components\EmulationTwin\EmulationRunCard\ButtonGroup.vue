<template>
    <div class="emulation-button-group">
        <div class="emulation-button" @click="handleChangeState">
            <icon
                :size="18"
                :name="state === 'play' ? 'pause' : 'play'"
                color="rgb(128, 144, 187)"
            />
            <span>{{ state === 'play' ? '暂停' : '继续' }}</span>
        </div>
        <div class="emulation-button" @click="handleClickStop">
            <icon
                :size="18"
                name="stop"
                color="rgb(128, 144, 187)"
            />
            <span>结束</span>
        </div>
    </div>
</template>

<script setup>
import {Icon} from '@/components/Common';
import {ref} from 'vue';

const emit = defineEmits(['pause', 'play', 'stop']);

const state = ref('play');

function handleChangeState() {
    state.value = state.value === 'pause' ? 'play' : 'pause';
    emit(state.value, state.value);
}

function handleClickStop() {
    emit('stop');
}

</script>

<style lang="less" scoped>
.emulation-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background-color: rgba(36, 104, 242, .3);
    clip-path:
        polygon(
            0 0,
            100% 0,
            100% calc(100% - 5px),
            calc(100% - 5px) 100%,
            0 100%
        );
    cursor: pointer;
    user-select: none;

    &:not(:first-child) {
        margin-left: 16px;
    }

    &:active {
        opacity: .8;
    }

    span {
        margin-top: 6px;
        font-size: 16px;
        color: rgba(255, 255, 255, .6);
    }

    &-group {
        display: flex;
        justify-content: flex-end;
    }
}
</style>