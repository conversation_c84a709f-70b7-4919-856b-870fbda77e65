<template>
    <el-dialog
        :visible="fileVisible"
        width="60vw"
        class="file-modal"
        :modal-append-to-body="false"
        @close="onClose"
    >
        <div slot="title" class="model-header">
            <el-popover
                placement="top-center"
                title=""
                width="200"
                trigger="hover"
                :content="evaluateTips"
            >
                <span slot="reference" class="title">仿真评价: <b>{{ evaluateTips }}</b></span>
            </el-popover>
            <div
                :class="{
                    'select-item': true,
                    'active-item': isHasPolicy,

                }"
                @click="isHasPolicy = !isHasPolicy"
            >
                <span></span>
                <span class="fs-14" style=" color: #e5f1ff;">策略下发对比</span>
            </div>
        </div>
        <div v-loading="loading" class="content-box">
            <div class="echarts-content">
                <draggable
                    v-model="chartList"
                    class="echarts-content"
                    animation="200"
                    group="module"
                >
                    <div
                        v-for="item in chartList" :key="item.name"
                        class="item"
                        :class="['module', item.key]"
                        :style="{'--duration': -Math.random() + 's'}"
                    >
                        <div class="search">
                            <span>{{ item.name }}</span>
                            <div class="form-warp">
                                <search-form
                                    :emulation-time="emulationTime" :type="item.code"
                                    @search="searchData"
                                />
                            </div>
                        </div>
                        <div class="echarts-box">
                            <echarts-items
                                :key="item.key"
                                :data-props="item.data"
                                :type="item.code"
                                :name="item.key"
                                :color="item.color"
                                :echarts-data="{
                                    data: evaluateData[item.key],
                                    flag: isHasPolicy,
                                }"
                            />
                        </div>
                    </div>
                </draggable>
            </div>
        </div>
    </el-dialog>
</template>

<script>
import {Dialog, Popover} from 'element-ui';
import EchartsItems from './Charts/EchartsItem.vue';
import SearchForm from './SearchForm.vue';
import {getEvaluateReport} from '../../api';
import draggable from 'vuedraggable';
export default {
    components: {SearchForm, EchartsItems, draggable, [Dialog.name]: Dialog, [Popover.name]: Popover},
    props: {
        schemeId: {
            type: [Number, String],
        },
        fileVisible: {
            type: Boolean,
            default: false,
        },
        emulationTime: {
            type: Object,
        },

    },
    data() {
        return {
            isHasPolicy: false,
            searchInfo: {},
            evaluateScore: 0,
            evaluateTips: '',
            evaluateData: {},
            loading: false,
            chartList: [
                {
                    code: 1,
                    name: '排队长度',
                    key: 'queueInfos',
                    data: 'queueList',
                    color: '#01FFE5',
                },
                {
                    code: 2,
                    name: '拥堵里程',
                    key: 'congestLengthInfos',
                    data: 'lengthList',
                    color: '#0077FF',
                },
                {
                    code: 3,
                    name: '拥堵指数',
                    key: 'congestIndexInfos',
                    data: 'indexList',
                    color: '#CFCC7F',
                },
                {
                    code: 4,
                    name: '平均速度',
                    key: 'avgSpeedInfos',
                    data: 'avgSpeedList',
                    color: '#842FFF',
                },
                {
                    code: 5,
                    name: '流量趋势',
                    key: 'flowTrendInfos',
                    data: 'flowList',
                    color: '#01FFE5',
                },
                {
                    code: 6,
                    name: '服务水平',
                    key: 'serveLevelInfos',
                    data: 'serveList',
                    color: '#0077FF',
                },
                {
                    code: 7,
                    name: '路段饱和度',
                    key: 'saturationInfos',
                    data: 'saturationList',
                    color: '#CFCC7F',
                },
                {
                    code: 8,
                    name: '路段拥堵排名',
                    key: 'congestRankings',
                    data: 'congestRankingDetailList',
                    color: '#842FFF',
                },
                {
                    code: 9,
                    name: '平均排队长度',
                    key: 'queueRankings',
                    data: 'queueRankingDetailList',
                    color: '#842FFF',
                },
            ],
        };
    },
    comments: {
        SearchForm,
        [Dialog.name]: Dialog,
        [Popover.name]: Popover,
    },
    watch: {},
    methods: {
        async getData() {
            // this.loading = true;
            try {
                // const {data} = await evaluateStatistics(this.schemeId);
                const {data} = await getEvaluateReport({
                    schemeId: this.schemeId,
                });
                this.evaluateTips = data.evaluateTips;
                this.evaluateData = data;
            }
            finally {
                // this.loading = false;
            }
        },
        async searchData(params) {
            const paramsData = {
                schemeId: this.schemeId,
                ...params,
            };
            // const {data} = await evaluateQuery(paramsData);
            const {data} = await getEvaluateReport(paramsData);
            const key = this.chartList.find(item => item.code === params.evaluateType)?.key;
            this.evaluateData[key] = data[key];
            this.evaluateScore = data.evaluateScore;

        },
        onClose() {
            this.$emit('close');
        },
    },
    mounted() {
        this.getData();
        this.searchInfo = {
            ...this.emulationTime,
        };
    },

};
</script>

<style scoped lang='less'>
.file-modal {
    z-index: 2022;

    /deep/.el-dialog {
        position: absolute;
        left: 50%;
        top: 55%;
        transform: translate(-50%, -50%);
        background: rgba(123, 169, 238, .12);
        backdrop-filter: blur(10px);
        margin-top: 0 !important;

        .el-dialog__header {
            padding: 0 24px !important;
            background-image: linear-gradient(270deg, rgba(19, 71, 62, .5) 1%, rgba(0, 255, 190, .24) 100%);
            height: 56px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid rgba(0, 251, 191, .6);
            backdrop-filter: blur(10px);

            .model-header {
                display: flex;
                width: 100%;
                justify-content: space-between;

                .el-popover__reference.title {
                    font-size: 18px;
                    color: #f7fbff;
                    width: 960px;
                    display: flex;
                    flex-wrap: wrap;

                    b {
                        font-size: 16px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }
                }

                .select-item {
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                    padding-right: 20px;
                    width: 146px;

                    span:nth-child(1) {
                        display: inline-block;
                        width: 14px;
                        height: 14px;
                        border: 1px solid #98b8de;
                        // border-radius: 50%;
                        margin-right: 8px;
                        margin-left: 16px;
                    }

                    span:nth-child(2) {
                        font-size: 14px;
                        color: #e5f1ff;
                    }

                    &.active-item {
                        span:nth-child(1) {
                            border-color: #49c3ff;
                            position: relative;

                            &::before {
                                content: '';
                                width: 8px;
                                height: 8px;
                                background: #49c3ff;
                                // border-radius: 50%;
                                position: absolute;
                                left: 50%;
                                top: 50%;
                                transform: translate(-50%, -50%);
                            }
                        }

                        span:nth-child(2) {
                            color: #e5f1ff;
                        }
                    }
                }
            }
        }

        .el-dialog__headerbtn {
            top: auto;

            &:hover i {
                color: #fff !important;
            }
        }

        .el-dialog__body {
            padding: 32px !important;
            padding-top: 0 !important;

            .content-box {
                color: #e5f1ff;
                font-size: 16px;

                .echarts-content {
                    width: 100%;
                    display: flex;
                    justify-content: space-between;
                    flex-wrap: wrap;
                    height: 600px;
                    overflow-y: auto;

                    .item {
                        width: 48%;
                        // padding: 16px;
                        margin-top: 32px;

                        .search {
                            width: 100%;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;

                            >span {
                                margin-right: 8px;
                                width: 160px;
                                white-space: nowrap;
                                text-overflow: ellipsis;
                                overflow: hidden;
                                position: relative;
                                padding-left: 12px;

                                &::before {
                                    content: '';
                                    width: 4px;
                                    height: 12px;
                                    background: #49c3ff;
                                    position: absolute;
                                    left: 0;
                                    top: 50%;
                                    transform: translate(0, -50%);
                                }
                            }

                            .search-form {
                                width: 100%;
                                display: flex;
                                justify-content: space-between;
                            }
                        }

                        .echarts-box {
                            width: 100%;
                            height: 228px;
                            // background: rgba(0,10,40,0.3);
                            // border: 1px solid rgba(45, 83, 151, .24);
                            margin-top: 8px;
                            background-image: url('/src/assets/images/grid-bg.png');
                            background-size: 100% 100%;
                        }
                    }
                }
            }
        }

        .el-dialog__footer {
            padding: 10px 24px 20px !important;
        }

        .dialog-footer {
            span {
                display: inline-block;
                padding: 7px 24px;
                color: rgba(178, 214, 255, 1);
            }

            span:nth-child(1) {
                border: 1px solid rgba(178, 214, 255, 1);
                border-radius: 2px;
                margin-right: 16px;
            }

            span:nth-child(2) {
                background: rgba(26, 132, 255, .8);
                border: 1px solid rgba(52, 146, 255, 1);
                border-radius: 2px;
            }
        }
    }
}

</style>