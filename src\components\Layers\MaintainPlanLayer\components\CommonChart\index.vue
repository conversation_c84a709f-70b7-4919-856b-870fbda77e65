<template>
    <!-- 统一获取并使用echart样式。该组件单纯echart组件，无其他效果（尝试性这种操作是否可行） -->
    <div ref="structureChartRef" class="structure-chart"></div>
</template>

<script>
import * as echarts from 'echarts';
import chartStyle from '@/assets/json/chartStyle.json';
echarts.registerTheme('structure', chartStyle);
export default {
    props: {
        option: {
            type: Object,
            required: true,
        },
        needStyle: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            myChart: null,
        };
    },
    mounted() {
        // 判断是否需要这个样式主题
        if (this.needStyle) this.myChart = echarts.init(this.$refs.structureChartRef, 'structure');
        else this.myChart = echarts.init(this.$refs.structureChartRef);
        this.init();
    },
    methods: {
        init() {
            if (!this.myChart) return;
            this.myChart.setOption(this.option);
        },
    },
    watch: {
        option: {
            handler() {
                this.init();
            },
            deep: true,
            immediate: true,
        },
    },
};
</script>



<style lang="less" scoped>
.structure-chart {
    width: 100%;
    height: 100%;
}
</style>