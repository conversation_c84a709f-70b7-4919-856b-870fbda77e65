@fontColor: #FFFFFF;
@borderColor: rgb(41, 86, 157);
@backgroundColor: rgba(123, 169, 238, .12);
@background: rgba(36, 104, 242, 0.1);
@iconBackground: rgb(15, 23, 49);
@borderRadius: 2px;
@borderColorHover: @borderColor;

// @backgroundColorDropdow: rgba(123, 169, 238, .12);
@backgroundColorDropdow: rgba(18, 74, 166, .8);
@fontColorDropdow: #93B4B7;

@backgroundColorDropdowHover: rgba(255, 255, 255, .1);
@fontColorDropdowHover: #EFFFFF;

@borderColorDisabled: rgba(24, 37, 58, .6);
@backgroundColorDisabled: rgba(134, 170, 241, 0.12);

@fontColorPicker: #A5ADBA;
@backgroundColorPickerRange: rgba(7, 85, 175, 0.3);

@fontColorButtonCancle: #7792b3;
@ButtonColor: rgba(36, 104, 242, 0.35);
@ButtonColorHover: #2468f2;

input::-webkit-input-placeholder { /* WebKit, Blink, Edge */
    color: rgba(#fff, .4) !important;
    font-size: 14px;
}

.el-select:hover .el-input__inner,
.el-input__inner:hover,
.el-input__inner:focus {
    border-color: @borderColorHover !important;
}


// 带前置图标的select样式设置
.el-select {
    &.el-icon-select {
        height: 24px;

        &:hover,
        &:focus,
        &:active,
        &:visited {
            input {
                border: 1px solid rgba(@borderColor, .5) !important;
            }
        }

        input {
            height: 100%;
            font-weight: 500;
            font-size: 14px;
            color: @fontColor;
            font-family: 'OPlusSans';
            border: 1px solid rgba(@borderColor, .5);
            padding-left: 32px;
            background-color: @background;

            &:hover,
            &:focus,
            &:active,
            &:visited {
                border: 1px solid @borderColor !important;
            }

            &::placeholder {
                color: #fff !important;
                font-family: 'OPlusSans';
                font-weight: 500;
                font-size: 14px;
            }
        }

        .el-input {
            height: 100%;

            .el-input__prefix {
                justify-content: center;
                margin-left: -5px;
                width: 24px;
                background-color: @iconBackground;
                border: 1px solid @borderColor;
            }

            &__suffix,
            &__prefix {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100%;
            }

            &.is-disabled .el-input__inner:hover {
                border-color: @borderColorDisabled;
            }

            .el-input__suffix {
                .el-input__suffix-inner {
                    height: 100%;

                    .el-input__icon {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 100%;
                    }
                }
            }
        }
    }
}

.el-input__inner {
    background: @background;
    border-radius: 0;
    font-size: 14px;
    color: @fontColor;
    border: 1px solid @borderColor;

    // &::placeholder {
    //     font-size: 12px;
    // }
}

.el-input.is-disabled .el-input__inner {
    background-color: @backgroundColorDisabled;
    border: 1px solid @borderColorDisabled;
    color: rgba(@fontColor, .6);
}

// 时间区间选择框的样式设置
.el-range-editor {
    height: 24px;
    background-color: @background;
    border: 1px solid @borderColor;

    &.el-input__inner {
        padding: 0;
        height: 24px;
    }

    // 前置图标
    .el-range__icon {
        width: 24px;
        height: 100%;
        background-color: @iconBackground;
        border: 1px solid @borderColor;
        margin-left: 0;
    }

    .el-range__close-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 28px;
        height: 100%;
    }

    .el-range-input {
        height: 100%;
        font-weight: 500;
        font-size: 14px;
        color: @fontColor;
        font-family: 'OPlusSans';
        background-color: transparent;

        &::placeholder {
            color: #fff !important;
            font-family: 'OPlusSans';
            font-weight: 500;
            font-size: 14px;
        }
    }

    span,
    .el-range-separator {
        color: @fontColor;
    }

    .el-range-separator {
        display: flex;
        align-items: center;
    }
}

// 时间选择框的样式设置
.el-date-editor {
    height: 24px;
    background-color: @background;
    border: 1px solid @borderColor;

    .el-input__prefix {
        margin-left: -5px;
        width: 24px;
        background-color: @iconBackground;
        border: 1px solid @borderColor;
    }

    .el-input__inner {
        height: 100%;
        font-weight: 500;
        font-size: 14px;
        color: @fontColor;
        font-family: 'OPlusSans';
        padding-left: 36px;
        background-color: transparent;

        &::placeholder {
            color: #fff !important;
            font-family: 'OPlusSans';
            font-weight: 500;
            font-size: 14px;
        }
    }

    .el-input__suffix,
    .el-input__prefix {
        .el-input__suffix-inner {
            height: 100%;

            .el-input__icon {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100%;
                line-height: normal;
            }
        }
    }

    .el-input__prefix .el-input__icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        line-height: normal;
    }
}

// 文字类型删除按钮
.el-button--text {
    &.delete {
        color: #ff6565;

        &:hover {
            color: #ff5b02;
        }
    }
}

.el-button.is-disabled.is-plain,
.el-button.is-disabled.is-plain:focus,
.el-button.is-disabled.is-plain:hover {
    background-color: @background;
}

.el-button--primary.is-plain {
    background-color: rgba(156 186 223 / 10%);
    border: 1px solid #000;
}

.el-button--primary {
    background-color: @ButtonColorHover;
    border: none;

    &:hover {
        background-color: @ButtonColorHover !important;
    }

    &:focus {
        background-color: @ButtonColorHover !important;
    }
}

.el-button--default {
    background-color: @ButtonColor;
    border: none;

    &:hover {
        background-color: @ButtonColor !important;
    }

    &:focus {
        background-color: @ButtonColor !important;
    }
}

.el-popper[x-placement^=bottom] .popper__arrow::after,
.el-popper[x-placement^=bottom] .popper__arrow {
    border-bottom-color: @backgroundColorDropdow;
}

.el-select-dropdown {
    border: 1px solid @borderColor;
    background-color: @backgroundColorDropdow;
    backdrop-filter: blur(8px);
}

.el-select-dropdown__item {
    color: @fontColorDropdow;

    &.selected {
        color: #fff;
    }
}

.el-select-dropdown__item.hover {
    background-color: @backgroundColorDropdowHover;
    color: @fontColorDropdowHover;
}

.el-tabs__item.is-active,
.el-tabs__item:hover {
    color: rgba(1, 255, 229, .8) !important;
}

.el-tabs__active-bar {
    background-color: rgba(1, 255, 229, .8);
}

.el-picker-panel {
    border: 1px solid @borderColor;
    background-color: @backgroundColorDropdow;
    color: @fontColorPicker;
    backdrop-filter: blur(12px);

    .el-date-table__row {
        span {
            &:hover {
                color: #fff;
            }
        }
    }

    .el-picker-panel__link-btn {
        span {
            color: rgba(#fff, .8);

            &:hover {
                color: #fff;
            }
        }
    }
}

.el-card {
    border: 1px solid @borderColor;
    background-color: @backgroundColorDropdow;
    color: @fontColorPicker;
}

.el-table {
    th,
    td {
        padding: 16px 0;
    }

    &__body {
        width: 100% !important;
    }

    &__row {
        td {
            border-top: 1px solid #c4c4c4;
        }
    }

    .is-center {
        text-align: center;
    }

    tr {
        background-color: @backgroundColor;
    }
}

// 分页器样式
.el-pagination {
    height: 42px;
    margin: 8px 0;

    // 共几条数据文本
    span.el-pagination__total {
        height: 100%;
        font-size: 18px;
        font-weight: 400;
        font-family: 'PingFang';
        color: @fontColor;
        line-height: 42px;
    }

    // 页码与翻页按钮
    .btn-prev,
    .btn-next {
        height: 100% !important;
        background-color: rgba(22, 48, 86, .3);
        color: rgba(255, 255, 255, .6);

        &:disabled {
            background-color: rgba(22, 48, 86, .3);
        }

        &:hover {
            color: rgba(255, 255, 255, 1);
        }
    }

    .el-pager li,
    .el-pager .more::before {
        line-height: 38px;
        height: 38px;
        background-color: rgba(22, 48, 86, .3);
        color: rgba(255, 255, 255, .6);
    }

    .el-pager .number.active {
        color: @fontColor;
        background-color: rgb(36, 104, 242);
    }

    // 每页数据下拉框
    .el-pagination__sizes {
        height: 100% !important;

        .el-select {
            height: 100%;

            .el-input {
                height: 100%;

                input {
                    height: 100%;
                    background-color: rgba(22, 48, 86, .3);
                    font-size: 18px;
                    font-family: 'PingFang';
                    font-weight: 500;
                    min-width: 112px;
                    border: none;
                }

                .el-input__suffix {
                    right: -5px;
                }

                .el-select__caret::before {
                    color: @fontColor;
                    font-size: 18px;
                }
            }
        }
    }

    // 跳转输入框
    .el-pagination__jump {
        height: 100%;
        font-size: 18px !important;
        font-family: 'PingFang';
        font-weight: 400;
        color: @fontColor;

        .el-input {
            height: 42px;
            background-color: rgba(22, 48, 86, .3);

            input {
                background-color: transparent;
                min-width: 42px;
                height: 100%;
                border: none;
            }
        }
    }
}

.el-date-table th {
    color: @fontColorPicker;
    border-bottom-color: transparent;
}

.el-date-range-picker__content.is-left {
    border-right: 1px solid @borderColor;
}

.el-date-table td.in-range div {
    background-color: @backgroundColorPickerRange;
}

.el-date-table td.in-range div:hover {
    background-color: transparent;
}

.el-date-picker__header-label,
.el-picker-panel__icon-btn {
    color: @fontColorPicker;
}

.el-date-picker__time-header {
    border-bottom-color: transparent;
}

.el-picker-panel__footer {
    background-color: @backgroundColorDropdow;
    border-top-color: transparent;

    .el-button--default {
        background-color: transparent;
        color: @fontColor;
        border: none;

        &:hover {
            background-color: transparent;
        }
    }
}

.el-year-table td .cell {
    color: @fontColorPicker;
}

.el-time-panel {
    border: 1px solid @borderColor;
    background-color: @backgroundColorDropdow;
    backdrop-filter: blur(8px);

    &__content {
        &::before {
            height: 32px;
            line-height: 32px;
        }
    }

    &__btn.confirm {
        color: #fff;
    }
}

.el-time-spinner__item {
    color: @fontColorDropdow;
    height: 32px;
    line-height: 32px;
}

.el-time-spinner__item.active:not(.disabled) {
    color: @fontColorDropdowHover;
}

.el-time-panel__footer {
    border-top-color: transparent;
}

.el-time-spinner__item:hover:not(.disabled):not(.active) {
    background-color: transparent;
}

.el-time-panel__btn {
    color: @fontColorButtonCancle;
}

// 单选框
.el-radio-group {
    label {
        margin-right: 8px;

        &:last-child {
            margin-right: 0;
        }
    }
}

.el-radio {
    color: rgba(255, 255, 255, 1);
    font-size: 12px;
    font-weight: 400;

    .el-radio__label {
        padding-left: 2px;
    }

    .el-radio__input {
        transform: scale(.84);

        &.is-checked .el-radio__inner {
            border-color: rgb(1, 255, 229);

            &::after {
                width: 6px;
                height: 6px;
            }
        }

        .el-radio__inner {
            background-color: transparent;
        }
    }

    .is-checked {
        .el-radio__inner {
            border-color: rgb(1, 255, 229);
            background-color: var(--bgc);

            &::after {
                background-color: rgb(1, 255, 229);
            }
        }
    }

    .el-radio__input.is-checked {
        & + .el-radio__label {
            color: rgb(1, 255, 229);
        }
    }
}

.custom-tooltip {
    background-color: #111 !important;
    color: #efffff !important;
    border-color: transparent !important;
}

.el-input__icon.el-icon-arrow-down {
    line-height: 30px;
}

.el-cascader__dropdown {
    border: 0;
    background: transparent;

    .el-cascader-menu {
        color: @fontColorDropdow;
        background-color: rgba(32, 72, 63, .78);
        border-radius: 5px;
        border-color: transparent;
    }

    .el-cascader-node:not(.is-disabled):focus,
    .el-cascader-node:not(.is-disabled):hover {
        background: transparent;
        color: #00f4b2;
    }

    .el-cascader-node__postfix {
        color: #00f4b2;
    }

    .el-scrollbar.el-cascader-menu + .el-scrollbar.el-cascader-menu {
        margin-left: 10px;
    }
}

.el-checkbox {
    margin-right: 16px;

    .el-checkbox__inner {
        background: transparent;
        border-color: rgba(#fff, .6);
        width: 14px;
        height: 14px;
        border-radius: 3px;
    }

    .el-checkbox__label {
        font-size: 14px;
        color: #fff;
        padding-left: 3px;
        font-family: 'PingFang-SC';
    }

    .el-checkbox__input {
        &.is-checked {
            .el-checkbox__inner {
                background-color: #01ffe5;
                border-color: #01ffe5;

                &::after {
                    border-color: #000;
                }
            }

            & + .el-checkbox__label {
                color: #01ffe5;
            }
        }

        &.is-focus {
            .el-checkbox__inner {
                border-color: #01ffe5;
            }
        }
    }
}

.el-dropdown-menu {
    background-color: #102e6d;
    color: #fff;

    &__item {
        color: #fff;

        &:focus,
        &:not(.is-disabled):hover {
            background-color: rgba(#fff, .1);
            color: #fff;
        }
    }
}

.el-dialog {
    background-color: rgba(8, 35, 79, .8);
    backdrop-filter: blur(10px);

    &__header {
        position: relative;
        display: flex;
        align-items: center;
        background-color: rgba(36, 104, 242, .3);
        border-top: 10px solid rgba(77, 135, 255, .3);
        padding: 12px 20px;

        &::before {
            content: '';
            position: absolute;
            top: -7px;
            left: 8px;
            width: 16px;
            height: 4px;
            background-image: linear-gradient(to right, rgb(46, 210, 255) 4px, transparent 2px);
            background-size: 6px 100%;
        }

        &::after {
            content: '';
            position: absolute;
            top: -6px;
            right: 8px;
            width: 72px;
            height: 2px;
            background-color: rgb(46, 210, 255);
        }
    }
}

.el-dialog__headerbtn .el-dialog__close {
    color: rgba(255, 255, 255);
}

.el-form-item__label {
    color: rgba(#fff, .6);
}

.el-textarea {
    &.is-disabled &__inner {
        background-color: rgba(255, 255, 255, .25);
    }

    &__inner {
        background: @background;
        border-radius: 0;
        color: @fontColor;

        &:focus {
            border-color: #fff;
        }
    }
}

// el-tabls
.el-tabs {
    &__item {
        color: rgba(#fff, .6);

        &.is-active,
        &:hover {
            color: #ffff;
        }
    }

    &--card > &__header &__nav {
        border-radius: 0;
    }
}

.el-popconfirm {
    &__main {
        padding-bottom: 16px;
    }
}

.el-popover {
    .popper__arrow::after {
        border-bottom-color: #fff !important;
    }
}

.el-tooltip {
    &__popper {
        color: #fff !important;
        background-color: rgba(3, 31, 68, .5) !important;
        backdrop-filter: blur(12px);
    }
}
