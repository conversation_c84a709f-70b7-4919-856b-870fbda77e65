<template>
    <map-component
        ref="mapRef"
        :options="{
            center: [113.29290479974627, 23.57957115989944],
            showSatelliteMap: true,
        }"
        class="map-box"
        @mapLoaded="mapLoaded"
        @clickMap="clickMap"
    >
        <!-- <layer-top
            title="仿真车辆图层"
            style="z-index: 9; position: absolute; top: 0;"
        /> -->
        <!-- <emulation-layer
            :engine="engine"
        /> -->
        <emulation-layer v-if="mapInitStatus"/>
    </map-component>

</template>

<script>
import {onMounted, ref} from 'vue';
import {Map, EmulationLayer} from '@/index';

// import LayerTop from '@/components/Common/LayerTop/index.vue';
import {initEmulationLayerConfig} from '@/components/Layers/EmulationLayer/utils/index';

export default {
    components: {
        MapComponent: Map,
        EmulationLayer,
        // LayerTop,
    },
    setup() {
        const mapRef = ref(null);
        const height = ref(1080);
        const mapInitStatus = ref(false);
        // const apiHost = import.meta.env.MODE === 'test' ? 'http://************:9004' : location.host;
        // const wsHost = import.meta.env.MODE === 'test' ? 'ws://************:9004' : `wss://${location.host}`;

        // 地图加载完成后执行的回调函数
        const mapLoaded = () => {
            // 初始化配置
            initEmulationLayerConfig({
                engine: mapRef.value.map,
                // wsHost: import.meta.env.MODE !== 'release' ? 'ws://************:9016' : '',
            });
            mapInitStatus.value = true;
        };

        // 地图点击后执行的回调函数
        const clickMap = e => {
            console.log('clickMap===点击地图，参数：', e);
        };

        onMounted(() => {
            height.value = window.innerHeight;
            document.title = '仿真车辆图层';

        });

        return {
            mapRef,
            height,
            mapInitStatus,
            mapLoaded,
            clickMap,
        };
    },
};
</script>

<style scoped>
.map-box {
    position: relative;
    width: 100vw;
}
</style>
