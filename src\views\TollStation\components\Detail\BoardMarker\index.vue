<template>
    <div>
        <BubbleMarker
            v-for="board in boardList"
            :key="board.key"
            :info="board"
            bubble-color="rgb(57, 182, 0)"
            icon-name="xianshiping"
            icon-color="#fff"
            :manager-instace="domManager"
        />
    </div>
</template>

<script setup>
import {getTollInfoboard} from '@/api/tollStation';
import {BubbleMarker} from '@/components/Common';
import {domManager} from '@/views/TollStation/utils';
import {onMounted, ref} from 'vue';
import {tollStation} from '@/views/TollStation/store';
import {viewToPoint} from '@/utils';

const boardList = ref([]);

async function fetchData() {
    const {data} = await getTollInfoboard({
        stationId: tollStation.value.stationId,
        stationName: tollStation.value.stationName,
    });
    boardList.value = data.map(item => ({
        position: [item.lng, item.lat, item.alt],
        key: item.assetsCode,
        name: item.infoBoardName,
        clickCallback: () => {
            viewToPoint([item.lng, item.lat, item.alt]);
        },
    }));
}

onMounted(() => {
    fetchData();
});

</script>