<template>
    <div class="mmaintain-search">
        <span>{{ title }}</span>
        <el-select
            v-model="value"
            placeholder="请选择"
            size="small"
            clearable
            @change="changeFn"
        >
            <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
        </el-select>
    </div>
</template>

<script>
import {Select, Option} from 'element-ui';
export default {
    props: {
        options: {
            type: Array,
            default: () => ([]),
        },
        value: {
            type: String,
            default: '',
        },
        title: {
            type: String,
            default: '',
        },
    },
    components: {
        [Select.name]: Select,
        [Option.name]: Option,
    },
    model: {
        prop: 'value',
        event: 'update:value',
    },
    setup(props, {emit}) {
        const changeFn = e => {
            emit('update:value', e);
            emit('change', e);
        };
        return {
            changeFn,
        };
    },
};
</script>

<style lang="less" scoped>
.mmaintain-search {
                display: flex;
                align-items: center;
                margin-right: 24px;
                width: 100%;

                &:last-child {
                    margin-right: 0;
                }

                span {
                    flex-shrink: 0;
                    // width: 120px;
                    margin-right: 8px;
                    font-weight: 400;
                    font-family: 'OPlusSans';
                    font-size: 14px;
                    color: rgba(255, 255, 255, 0.6);
                }

                :deep(.el-input) {
                    input {
                        background: transparent;

                        &::placeholder {
                            color: rgba(255, 255, 255, .6) !important;
                        }
                    }
                }

                :deep(.el-select) {
                    width: 100%;
                    .el-input {
                        background-color: rgba(#fff, .05);
                        backdrop-filter: blur(4px);
                    }
                }
            }
</style>