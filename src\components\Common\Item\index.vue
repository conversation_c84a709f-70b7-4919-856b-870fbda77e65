<template lang="">
    <p class="item" :class="{split}">
        <span class="label">
            <slot v-if="$slots.label" name="label"></slot>
            <template v-else>{{ info.label }}</template>
        </span>
        <span class="value">
            <slot v-if="$slots.value" name="value"></slot>
            <!-- radio展示 -->
            <el-radio-group v-else-if="info.radioMode" :value="+info.value">
                <el-radio
                    v-for="item in info.radioMode" :key="item.value"
                    :label="item.value"
                >{{ item.label }}</el-radio>
            </el-radio-group>
            <template v-else><span>{{ info.value }}</span></template>
        </span>
    </p>
</template>
<script>
import {Radio, RadioGroup} from 'element-ui';

export default {
    name: 'Item',
    components: {
        [Radio.name]: Radio,
        [RadioGroup.name]: RadioGroup,
    },
    props: {
        info: {
            type: Object,
            required: true,
            default: () => ({}),
        },
        split: {
            type: Boolean,
            default: false,
        },
    },
};
</script>
<style lang="less" scoped>
.item {
    display: flex;
    justify-content: space-between;
    width: 100%;
    color: #fff;
    padding: 5px 16px;
    font-size: 14px;
    margin-bottom: 4px;
    font-weight: 500;
border-bottom: 1px solid rgba(255, 255, 255, .2);
    &:last-child {
        margin-bottom: 0;
    }

    .label {
        opacity: .5;
        color: rgb(231, 239, 255);

    }

    .value {
        flex: 1;
        text-align: right;
        color: rgb(255, 255, 255);
        overflow-wrap: anywhere;
    }
}

.split {
    background-color: transparent;
    padding: 0;
    gap: 11px;

    .label,
    .value {
        flex: 1;
        padding: 5px 15px;
        background-color: rgba(255, 255, 255, .15);
    }
}
</style>