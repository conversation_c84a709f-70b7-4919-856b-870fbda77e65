<template>
    <div ref="echartsRoot" class="chart"></div>
</template>

<script>
import * as echarts from 'echarts';
import 'echarts/lib/component/dataZoom';
import {areaColor} from './config';
import chartStyle from '@/assets/json/chartStyle.json';
import {onMounted, onUnmounted, ref, watch} from 'vue';
import dayjs from 'dayjs';
import {emulationInfo} from '@EmulationLayer/store/index';
import {useUnit} from '@/utils';

echarts.registerTheme('emulation', chartStyle);

const ECHARTSCOLLECTION = {};

export default {
    props: {
        title: {
            type: String,
            default: '',
        },
        echartsData: {
            type: Array,
            default: () => {},
        },
        name: {
            type: String,
        },
        dataProps: {
            type: String,
        },
        type: {
            type: Number,
        },
        color: {
            type: String,
        },
        isHasPolicy: {
            type: <PERSON>olean,
        },
    },
    setup(props, {emit}) {
        const {ratio} = useUnit();
        const options = ref();
        const echartsRoot = ref(null);


        watch(() => props.echartsData, val => {
            if (!val || !val.data || val.data?.length === 0) {
                return;
            }
            const startTime = emulationInfo.value.emulationStartTime;
            const endTime = emulationInfo.value.emulationEndTime;
            // console.log(startTime, endTime, '222');
            const startTimeUnix = dayjs(startTime).valueOf();
            const endTimeUnix = dayjs(endTime).valueOf();
            const seriesData = val.data.filter(item => item.length > 0);

            options.value = {
                tooltip: {
                    trigger: 'axis',
                },
                legend: {
                    show: true,
                    // selectedMode: false,
                    icon: 'diamond',
                    right: '5%',
                    top: 10 * ratio.value,
                    itemWidth: 8 * ratio.value,
                    itemHeight: 8 * ratio.value,
                    orient: 'horizontal',
                    itemGap: 20 * ratio.value,
                    textStyle: {
                        color: '#F9FBFF',
                        fontSize: 12 * ratio.value,
                    },
                    // data: legendData,
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true,
                },
                xAxis: {
                    type: 'time',
                    min: startTimeUnix,
                    max: endTimeUnix,
                    'boundaryGap': false,
                    axisLine: {
                        show: true,
                        type: 'solid',
                        color: 'rgba(78,141,237,0.7)',
                        width: 1 * ratio.value,
                    },
                    splitLine: {
                        show: false,
                    },
                    axisLabel: {
                        showMinLabel: true,
                        showMaxLabel: true, // x轴最后的显示
                        color: '#98B8DE',
                        fontSize: 12 * ratio.value,
                        fontFamily: 'DINAlternate-Bold',
                        formatter: (val, index) => {
                            return dayjs(val).format('HH:mm');
                        },
                    },
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        show: false,
                    },
                    nameTextStyle: {
                        color: '#F9FBFF',
                        fontSize: 12 * ratio.value,
                        fontFamily: 'FZLTZHJW--GB1-0',
                    },
                    axisTick: {
                        show: false,
                    },
                    // splitNumber: 4,
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(78,141,237,0.2)',
                            type: 'dash',
                            width: 1 * ratio.value,
                        },
                    },
                    axisLabel: {
                        show: true,
                        color: '#98B8DE',
                        fontSize: 12 * ratio.value,
                        fontFamily: 'DINAlternate-Bold',

                        // formatter: '{value}m',
                    },
                },
                series: seriesData?.map((item, index) => {
                    const isLast = index === (seriesData.length - 1);
                    return {
                        name: isLast ? '无策略' : `策略${index + 1}`,
                        type: 'line',
                        smooth: true,
                        symbol: 'none',
                        data: item,
                        color: props.color[index],
                        lineStyle: {
                            width: 2 * ratio.value,
                        },
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1 * ratio.value,
                                colorStops: areaColor[index + 1],
                                global: false, // 缺省为 false
                            },
                            opacity: 0.2,
                        },
                    };
                }),
            };
            setTimeout(() => {
                ECHARTSCOLLECTION[props.name].setOption(options.value, true);
            }, 0);

        }, {
            deep: true,
            immediate: true,
        });

        const init = () => {

            ECHARTSCOLLECTION[props.name] = echarts.init(echartsRoot.value, 'emulation', {
                renderer: 'svg',
            });
            if (options.value) {
                ECHARTSCOLLECTION[props.name].setOption(options.value, true);
            }

        };

        onMounted(() => {
            init();
            window.addEventListener('resize', () => {
                ECHARTSCOLLECTION[props.name]?.resize();
            });
        });

        onUnmounted(() => {
            ECHARTSCOLLECTION[props.name]?.dispose();
            window.removeEventListener('resize', () => {
                ECHARTSCOLLECTION[props.name]?.resize();
            });
        });


        return {
            options,
            echartsRoot,
        };
    },
};
</script>

<style lang="less" scoped>
.chart {
    width: 100%;
    height: 100%;
}

</style>