<template>
    <div class="tool-button-group">
        <!-- 收缩面板/展开面板 -->
        <el-tooltip
            v-if="showCollapse"
            placement="right"
            :content="collapseState ? '展开面板' : '收缩面板'"
            popper-class="tool-button-tooltip"
        >
            <div
                :class="['tool-button', collapseState ? 'btn-expand' : 'btn-shrink']"
                @click="handleCollapse"
            ></div>
        </el-tooltip>
        <!-- 地图复位 -->
        <el-tooltip
            v-if="showRecover"
            placement="right"
            content="地图复位"
            popper-class="tool-button-tooltip"
        >
            <div class="tool-button btn-recover" @click="handleRecover"></div>
        </el-tooltip>
        <!-- 返回 -->
        <el-tooltip
            v-if="showBack"
            placement="right"
            content="返回"
            popper-class="tool-button-tooltip"
        >
            <div class="tool-button btn-back" @click="handleBack"></div>
        </el-tooltip>
        <!-- 地图维度 -->
        <template v-if="showMapLat">
            <!-- 二维地图 -->
            <el-tooltip
                placement="right"
                content="二维地图"
                popper-class="tool-button-tooltip"
            >
                <div
                    :class="[
                        'tool-button btn-map2d',
                        {'btn-map2d__active': mapLatState === mapLatType.MAP_2D},
                    ]"
                    @click="handleChangeMapLat(mapLatType.MAP_2D)"
                ></div>
            </el-tooltip>
            <!-- 三维地图 -->
            <el-tooltip
                placement="right"
                content="三维地图"
                popper-class="tool-button-tooltip"
            >
                <div
                    :class="[
                        'tool-button btn-map3d',
                        {'btn-map3d__active': mapLatState === mapLatType.MAP_3D},
                    ]"
                    @click="handleChangeMapLat(mapLatType.MAP_3D)"
                ></div>
            </el-tooltip>
        </template>
        <!-- 路网 -->
        <el-tooltip
            v-if="showRoadNet"
            placement="right"
            content="路网"
            popper-class="tool-button-tooltip"
        >
            <div
                :class="[
                    'tool-button btn-road-net',
                    {'btn-road-net__active': roadNetState},
                ]"
                @click="handleRoadNet"
            ></div>
        </el-tooltip>
    </div>
</template>

<script>
import {ref, watch} from 'vue';
import {Tooltip} from 'element-ui';
import {viewToMac} from '@/utils/map';

const mapLatType = {
    MAP_2D: 'MAP_2D',
    MAP_3D: 'MAP_3D',
};

export default {
    components: {
        [Tooltip.name]: Tooltip,
    },
    props: {
        showCollapse: {
            type: Boolean,
            default: true,
        },
        showRecover: {
            type: Boolean,
            default: true,
        },
        showBack: {
            type: Boolean,
            default: true,
        },
        showMapLat: {
            type: Boolean,
            default: false,
        },
        showRoadNet: {
            type: Boolean,
            default: false,
        },
        collapse: {
            type: Boolean,
            default: false,
        },
        mapLat: {
            validator: val => [
                mapLatType.MAP_2D,
                mapLatType.MAP_3D,
            ].includes(val),
            default: 'MAP_2D',
        },
        roadNet: {
            type: Boolean,
            default: false,
        },
    },
    setup(props, {emit}) {
        const collapseState = ref(false);
        const mapLatState = ref(mapLatType.MAP_2D);
        const roadNetState = ref(false);

        watch(
            () => props.collapse,
            (newVal, oldVal) => {
                if (newVal === oldVal) return;
                collapseState.value = newVal;
            },
            {
                immediate: true,
            }
        );

        watch(
            () => props.mapLat,
            (newVal, oldVal) => {
                if (newVal === oldVal) return;
                mapLatState.value = newVal;
            },
            {
                immediate: true,
            }
        );

        watch(
            () => props.roadNet,
            (newVal, oldVal) => {
                if (newVal === oldVal) return;
                roadNetState.value = newVal;
            },
            {
                immediate: true,
            }
        );

        function handleCollapse() {
            collapseState.value = !collapseState.value;
            emit('update:collapse', collapseState.value);
            emit('changeCollapse', collapseState.value);
        }

        function handleRecover() {
            viewToMac();
            emit('recover');
        }

        function handleBack() {
            emit('back');
        }

        function handleChangeMapLat(lat) {
            if (lat === mapLatState.value) return;
            mapLatState.value = lat;
            emit('update:mapLat', mapLatState.value);
            emit('changeMapLat', mapLatState.value);
        }

        function handleRoadNet() {
            roadNetState.value = !roadNetState.value;
            emit('update:roadNet', roadNetState.value);
            emit('changeRoadNet', roadNetState.value);
        }

        return {
            collapseState,
            mapLatState,
            roadNetState,
            mapLatType,
            handleCollapse,
            handleRecover,
            handleBack,
            handleChangeMapLat,
            handleRoadNet,
        };
    },
};
</script>
<style lang="less" scoped>
.tool-button {
    width: 36px;
    height: 36px;
    background-size: 100% 100%;
    transition: .15s linear;
    cursor: pointer;

    &:not(:last-of-type) {
        margin-bottom: 9px;
    }
}

@btnAssets: shrink, expand, recover, back, map2d, map3d, road-net;
each(@btnAssets,{
    .btn-@{value}{
        background-image: url('@/assets/images/toolButton/@{value}.png');
        &__active,
        &:hover {
            background-image: url('@/assets/images/toolButton/@{value}-active.png');
        }
    }
});
</style>
<style>
.tool-button-tooltip {
    background-color: rgba(12, 45, 38, .91) !important;
}
</style>