// 设备设施高速总览接口
import {request} from '@/utils/network-helper/index';
import {baseConfig} from '@/utils/common';
const {basePrefix} = baseConfig;

const baseStructure = `${basePrefix}/v1`;

// 1.1 高速总览静态数据展示
export const getSectionOverView = async (sectionId = 'G0015440090') => {
    const {data, code} = await request.get(
        baseStructure + '/section/overview/getSectionOverView',
        {
            sectionId,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 1.2 摄像枪icon(摄像枪扎点显示)
export const getCameraList = async (sectionId = 'G0015440090') => {
    const {data, code} = await request.get(
        baseStructure + '/section/overview/getCameraList',
        {
            sectionId,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 1.3 情报板icon(情报板扎点显示)
export const getInfoboardList = async (sectionId = 'G0015440090') => {
    const {data, code} = await request.get(
        baseStructure + '/section/overview/getInfoboardList'
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 1.4 互通立交icon(互通立交扎点显示)
export const getInterChangeList = async (sectionId = 'G0015440090') => {
    const {data, code} = await request.get(
        baseStructure + '/section/overview/getInterChangeList'
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 1.5 桥梁icon(桥梁扎点显示)
export const getBridgeList = async (sectionId = 'G0015440090') => {
    const {data, code} = await request.get(
        baseStructure + '/section/overview/getBridgeList',
        {
            sectionId,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 1.6、收费站icon(收费站扎点显示)
export const getTollStationList = async (sectionId = 'G0015440090') => {
    const {data, code} = await request.get(
        baseStructure + '/section/overview/getTollStationList'
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 1.7、服务区icon(服务区扎点显示)
export const getServiceAreaList = async () => {
    const {data, code} = await request.get(
        baseStructure + '/section/overview/getServiceAreaList'
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 1.8、查看摄像枪扎点详情
export const getCameraInfo = async cameraId => {
    const {data, code} = await request.get(
        baseStructure + '/section/overview/getCameraInfo',
        {
            cameraId,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 1.9、查看情报板扎点详情
export const getInfoboardInfo = async assetsCode => {
    const {data, code} = await request.get(
        baseStructure + '/section/overview/getInfoboardInfo',
        {
            assetsCode,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 1.10、查看互通立交扎点详情
export const getInterChangeInfo = async sicId => {
    const {data, code} = await request.get(
        baseStructure + '/section/overview/getInterChangeInfo',
        {
            sicId,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 1.11、查看桥梁扎点详情
export const getBridgeInfo = async params => {
    const {data, code} = await request.get(
        baseStructure + '/section/overview/getBridgeInfo',
        params
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 1.12、查看收费站扎点详情
export const getTollStationInfo = async stationId => {
    const {data, code} = await request.get(
        baseStructure + '/section/overview/getTollStationInfo',
        {
            stationId,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 1.13、查看服务区扎点详情
export const getServiceAreaInfo = async serviceAreaId => {
    const {data, code} = await request.get(
        baseStructure + '/section/overview/getServiceAreaInfo',
        {
            serviceAreaId,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 1.14、地图高速路线渲染
export const getSectionPath = async (sectionId = 'G0015440090') => {
    const {data, code} = await request.get(
        baseStructure + '/section/overview/applySectionPath',
        {
            sectionId,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 1.15、显示所有桥梁扎点
export const getGetAllBridgeList = async () => {
    const {data, code} = await request.get(
        baseStructure + '/section/overview/getAllBridgeList'
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};