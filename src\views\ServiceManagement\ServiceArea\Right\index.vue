<template>
    <div class="right-com">
        <service-area-overview :data="serviceIcon"/>
        <div class="right">
            <card v-model="radio" title="设备状态">
                <template #content>
                    <div v-if="equipIcon.length" class="icon-list">
                        <item
                            v-for="item in equipIcon" :key="item"
                            :info="item" @click.native="equipClcik(item)"
                        />
                    </div>
                    <NoData v-else/>
                </template>
            </card>
            <video-card/>

            <modal-card
                :visible="showDialog" disabled-confirm
                :title="'设备详情'" :width="606"
                @close="showDialog = false"
            >
                <div class="dailog-content">
                    <equip-dialog :list="equipDetail"/>
                </div>

            </modal-card>


            <CardFlow
                class="grid-item"
            />

        </div>
    </div>

</template>

<script setup>
import {ref, onMounted} from 'vue';
import {Card, NoData} from '@/components/Common/index';
import Item from '@/views/ServiceManagement/Card/IconList.vue';
import EquipDialog from './equipDialog.vue';
import {equipOverview, serviceOverview} from '@/config/serviceMap.js';
import {getDeviceDetail, getDeviceState} from '@/api/serviceManager';
import {Dialog as ElDialog} from 'element-ui';
import {ModalCard} from '@/components/Common';
import CardFlow from '@/views/ServiceManagement/Card/cardFlow.vue';
import VideoCard from '@/views/ServiceManagement/Card/VideoCard.vue';
import {useRoute, useRouter} from '@/utils';
import ServiceAreaOverview from '@/views/ServiceManagement/Card/ServiceAreaoverView.vue';
import {Ws} from '@/utils/ws';

const props = defineProps({
    serviceIcon: {
        type: Object,
        default: () => ({}),
    },
    equipIcon: {
        type: Array,
        default: () => ([]),
    },
});

const showDialog = ref(false);
const equipDetail = ref([]);
const router = useRouter();
const equipClcik = async e => {
    console.log('output->e', e);
    try {
        const prames = {
            deviceType: e.deviceType,
        };
        const res = await getDeviceState(prames);
        equipDetail.value = res.data;

    }
    catch (error) {
        equipDetail.value = [];
    }
    showDialog.value = true;
};
const handleClick = e => {
    router.push({name: 'carFlow', params: {id: e.id, info: e}});
    console.log('output-');
};
//   const url = `${import.meta.env.VITE_WS_URL}/congestion`;
//     const {ws: wsInstance, send, onMessage, connected, disconnect} = useWebSocket(url, {
//         immediate: true,
//         onMessage(_, {data}) {
//             console.log('output->wsdatawsdatawsdata', data);
//             message.value = data;
//         },


</script>

<style lang="less" scoped>
.right-com {
    display: flex;
}

:deep(.dailog-box) {
    width: 615px;
}

:deep(.modal-card__foot) {
    display: none;
}

.dailog-content {
    display: flex;
    justify-content: space-between;
    max-height: 520px;
    overflow-y: auto;
}

.right {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    margin-left: 24px;

    .grid-item {
        grid-column: 1 / span 2;
    }

    .icon-list {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px 24px;
        height: 320px;
        overflow: auto;
    }
}

.view {
    color: #28c282;
    cursor: pointer;
}
</style>