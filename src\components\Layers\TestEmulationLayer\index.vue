<!-- 气象监测预警 -->
<template>
    <div class="weathe-warning-layer">
        <div class="btn">
            <div @click="onCreate">模拟点击左侧列表创建方案</div>
        </div>
        <template v-if="!isEmulationTwin">
            <!-- 仿真列表 -->
            <!-- platform-source参数先默认随便给一个 现在后端还没有定义出来 后面在改 -->
            <div v-show="!createConfig.show" class="create-emulation-content">
                <emulation-list
                    :platform-source="'emulation'"
                    @add="createCase"
                    @edit="editCase"
                />
            </div>
            <!-- 创建仿真方案组件 -->
            <!-- platform-source参数先默认随便给一个 现在后端还没有定义出来 后面在改 -->
            <div v-if="createConfig.show" class="create-emulation-content">
                <emulation-create
                    :info="detailInfo"
                    :platform-source="'emulation'"
                    @close="onClose"
                />
            </div>
        </template>

        <template v-else>
            <emulation-twin go-back="goBack"/>
        </template>
    </div>
</template>

<script>

import {onMounted, watch, ref, computed} from 'vue';
import {engine} from '@/store/engine';
import EmulationList from '@/components/Layers/EmulationLayer/components/EmulationList/index.vue';
import EmulationCreate from '@/components/Layers/EmulationLayer/components/EmulationCreate/index.vue';
import EmulationTwin from '@/components/Layers/EmulationLayer/components/EmulationTwin/index.vue';

// 初始化全局数据的接口方法
import {strategyId, initUuid, init_road_info} from '@EmulationLayer/store/index';
import {createConfig} from '@EmulationLayer/store/emulationCreate';

export default {
    name: 'WeatheWarningLayer',
    components: {
        EmulationList,
        EmulationCreate,
        EmulationTwin,
    },
    props: { },
    setup(props) {


        const detailInfo = ref(null);

        const isEmulationTwin = computed(() => strategyId.value);

        // 点击左侧事件列表 进行直接创建方案
        const onCreate = () => {
            createConfig.value.show = true;
            createConfig.value.type = 'create';
            createConfig.value.id = '';
            // mock数据
            detailInfo.value = {
                'id': 2,
                'sumoCity': '广东省广州市',
                'eventId': 0,
                'emulationType': 1,
                'eventCategory': 1,
                'eventType': 0,
                'eventLocationType': 3,
                'eventStartTime': '2023-02-02 18:45:24',
                'eventEndTime': '2023-02-02 19:45:24',
                'duration': 60,
                'direction': 1,
                'eventStartStake': 'K250+100',
                'eventEndStake': 'K250+100',
                'eventPosition': '113.260629,23.594334',
                'eventAltitude': 0,
                'closeLaneNum': 1,
                'closeLane': '1',
                'closeLanePosition': '',
                'visibility': 0,
                'influencesLength': 0,
                'upstreamTransitionLength': 0,
                'constructionLength': 0,
                'downstreamTransitionLength': 0,
                'limitSpeed': 0,
                'highSpeedName': 'G0423',
                'highSpeedNameCn': '乐广高速',
                'tollName': null,
                'tollStakeNumber': null,
                'tollLongitude': null,
                'tollLatitude': null,
                'entranceLaneNum': null,
                'entranceEtcLaneNum': null,
                'entranceMtcLaneNum': null,
                'entranceMixLaneNum': null,
                'exitLaneNum': null,
                'exitEtcLaneNum': null,
                'exitMtcLaneNum': null,
                'exitMixLaneNum': null,
            };
        };

        // 新增仿真方案
        const createCase = () => {
            createConfig.value.show = true;
            createConfig.value.type = 'add';
            createConfig.value.id = '';
            detailInfo.value = null;
        };

        // 修改仿真方案
        const editCase = (id, item) => {
            createConfig.value.show = true;
            createConfig.value.type = 'edit';
            createConfig.value.id = id;
            detailInfo.value = item;
        };

        // 取消创建仿真方案
        const onClose = () => {
            createConfig.value.show = false;
            createConfig.value.type = '';
            createConfig.value.id = '';
        };

        // 仿真点击返回
        const goBack = () => {
            strategyId.value = '';
            onClose();
        };

        // 地图实例
        watch(() => engine.value, val => {
                  if (val) {
                      engine.value.map.setPitch(20);
                      engine.value.map.setZoom(4.3);
                      engine.value.map.setHeading(10);
                  }
              },
              {
                  immediate: true,
              }
        );

        onMounted(() => {
            // 初始化全局数据 这一步必须
            initUuid();
            init_road_info();
        });

        return {
            EmulationList,
            isEmulationTwin,
            createConfig,
            detailInfo,
            onCreate,
            goBack,
            createCase,
            editCase,
            onClose,
        };
    },
};
</script>

<style lang="less" scoped>
.weathe-warning-layer {
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 2;
    top: 0;
    pointer-events: none;

    & > * {
        pointer-events: auto;
    }

    .create-emulation-content {
        position: absolute;
        right: 20px;
        top: 120px;
    }

    .btn {
        position: absolute;
        top: 120px;
        left: 20px;
        width: 180px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        background-color: goldenrod;
        border-radius: 4px;
        cursor: pointer;
    }
}
</style>
