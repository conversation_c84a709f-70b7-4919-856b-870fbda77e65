<template>
    <!-- 柱状图 -->
    <div ref="rootCharts" class="rootCharts"></div>
</template>
<script>

import {echarts} from './common';
import {BarChart} from 'echarts/charts';
import {onBeforeUnmount, onMounted, ref, watch} from 'vue';

echarts.use([BarChart]);

export default {
    data() {
        return {
        };
    },
    props: {
        data: {
            type: Array,
        },
        // 单位
        unit: {
            type: String,
            default: '个',
        },
        // 指标
        seriesName: {
            type: String,
            default: '数量',
        },
        // 柱形背景颜色
        bgColor: {
            type: String,
            default: 'rgba(159, 159, 159, 0.3)',
        },
        // tooltip自定义显示文本内容
        tooltipCallback: {
            type: Function,
            default: null,
        },
    },
    setup(props, {emit}) {

        let chart = null;
        const rootCharts = ref(null);

        const resize = () => {
            if (chart) {
                chart.resize();
            }
        };


        // 数据处理
        let labelArr = ref([]);
        let valueArr = ref([]);
        let bgArr = ref([]);
        const dataHandler = data => {
            labelArr.value = [];
            valueArr.value = [];
            bgArr.value = [];
            data.sort((a, b) => (b.count || b.value) - (a.count || a.value)).forEach(item => {
                labelArr.value.push(item.name);
                valueArr.value.push(item.count || item.value || 0);
            });
            // 背景柱形图数据
            if (valueArr[0] !== 0) {
                let max = Math.ceil(valueArr.value[0] / 10) * 10;
                for (let i = 0; i < data.length; i++) {
                    bgArr.value.push(max);
                }
            }
        };

        const update = () => {
            // console.log(props.data, 123);
            dataHandler(props.data);
            chart.setOption({
                grid: {
                    left: '2%',
                    right: '3%',
                    bottom: '5%',
                    top: '5%',
                    containLabel: true,
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'none',
                    },
                    formatter: function (params) {
                        if (props.tooltipCallback) return props.tooltipCallback(params);
                        return params[0].name + '<br/>'
                            // eslint-disable-next-line max-len
                            + `<span style='display:inline-block;margin-right:5px;
                            border-radius:10px;width:9px;height:9px;background-color:rgba(36,207,233,0.9)'></span>`
                            + params[0].seriesName + ' : ' + params[0].value + ' ' + props.unit + '<br/>';
                    },

                },
                // backgroundColor: 'rgb(20,28,52)',
                xAxis: {
                    show: true,
                    type: 'value',
                    axisLabel: {
                        show: true,
                        textStyle: {
                            color: '#fff',
                        },
                    },
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(255, 255, 255, 0.61)',
                        },
                    },
                    axisTick: {
                        show: false,
                    },
                    splitLine: {
                        show: false,
                    },
                },
                yAxis: [{
                    type: 'category',
                    inverse: true,
                    axisLabel: {
                        show: true,
                        textStyle: {
                            color: '#fff',
                        },
                        fontSize: 12,
                        margin: 18, // 刻度标签与轴线之间的距离。
                    },
                    splitLine: {
                        show: false,
                    },
                    axisTick: {
                        show: false,
                    },
                    axisLine: {
                        show: false,
                    },
                    data: labelArr.value,
                }, {
                    type: 'category',
                    inverse: true,
                    axisTick: 'none',
                    axisLine: 'none',
                    splitLine: 'none',
                    show: true,
                    axisLabel: {
                        show: false,
                        textStyle: {
                            color: '#ffffff',
                            fontSize: '12',
                        },
                        fontSize: '12',
                        formatter: function (value) {
                            return value.toLocaleString();
                        },
                    },
                    data: valueArr.value,
                }],
                series: [{
                             name: props.seriesName,
                             type: 'bar',
                             zlevel: 1,
                             itemStyle: {
                                 normal: {
                                     barBorderRadius: 30,
                                     color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                                         offset: 0,
                                         color: 'rgba(218, 255, 242, 0.1)',

                                     }, {
                                         offset: 1,
                                         color: 'rgb(239, 255, 250)',
                                     }]),
                                 },
                             },
                             barWidth: 10,
                             data: valueArr.value,
                         },
                         {
                             name: '背景',
                             type: 'bar',
                             barWidth: 10,
                             barGap: '-100%',
                             data: bgArr.value,
                             //  data: bgArr.value,
                             itemStyle: {
                                 normal: {
                                     color: props.bgColor,
                                     barBorderRadius: 30,
                                 },
                             },
                         },
                ],
            });
        };


        const initChart = () => {
            if (!rootCharts.value) {
                return;
            }
            chart = echarts.init(rootCharts.value, 'emulation');
            window.addEventListener('resize', resize);
        };

        watch(() => props.data, () => {
            update();
        }, {
            // imediate: true,
            // deep: true,
        });

        onMounted(() => {
            initChart();
            update();
        });

        onBeforeUnmount(() => {
            window.removeEventListener('resize', resize);
            chart.dispose();
        });

        return {
            rootCharts,
        };
    },
};

</script>

<style>
.rootCharts {
    width: 100%;
    height: 100%;
}
</style>