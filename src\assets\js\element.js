import ElementUI from 'element-ui';

const formatName = (name, prefix) => {
    return name && prefix + name.split('El').pop();
};

const changeElementName = (prefix = 'My') => {
    Object.keys(ElementUI).forEach(key => {
        if (!(typeof ElementUI[key] === 'object')) return;
        ElementUI[key].name = `${prefix}${key}`;
        if (ElementUI[key].components) {
            Object.keys(ElementUI[key].components)?.forEach(comKey => {
                if (comKey.indexOf('El') === -1) return;
                const newComKey = formatName(comKey, prefix);
                ElementUI[key].components[comKey].name = newComKey;
                // ElementUI[key].components[comKey].componentName = newComKey;
                ElementUI[key].components[newComKey] = ElementUI[key].components[comKey];
                delete ElementUI[key].components[comKey];
            });
        }
        if (ElementUI[key].mixins) {
            ElementUI[key].mixins.forEach(mixin => {
                if (mixin.components) {
                    Object.keys(mixin.components)?.forEach(comKey => {
                        if (comKey.indexOf('El') === -1) return;
                        const newComKey = formatName(comKey, prefix);
                        mixin.components[newComKey] = mixin.components[comKey];
                        delete mixin.components[comKey];
                    });
                }
            });
        }
    });
};

import.meta.env.MODE === 'production' && changeElementName();