// import Request from "./Request";
import request from 'axios';
const path = 'https://gjdt.private.gdcg.cn/serviceArea/api';

export function getBaseCameraInfo(id) {
    return request.get(`${path}/stream/media/getBaseCameraInfo?assetsCode=${id}`);
}
export function getPlayLogListByDeviceId(id) {
    return request.get(`${path}/stream/media/getPlayLogListDeviceId?assetsCode=${id}`);
}
export function getlivePlaySta(data) {
    return request.post(
        `${path}/stream/media/livePlaySta`,
        JSON.stringify(data),
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
}
export function livePlayStop({assetsCode, token}) {
    return request.post(
        `${path}/stream/media/livePlayStop?assetsCode=${assetsCode}&token=${token}`,
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
}

export function getCameraRequestNum(id) {
    return request.get(`${path}/stream/media/getCameraRequestNum?assetsCode=${id}`);
}
