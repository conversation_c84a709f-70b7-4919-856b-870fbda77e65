<template>
    <map-line
        :line-instace="lineManager"
        :list="list"
        :info="info"
        dynamic-width
    />
</template>

<script>
import {MapLine} from '@/components/Common';
import {lineManager} from './index.js';

export default {
    name: 'baseLine',
    props: {
        list: {
            type: Array,
            default: () => ([]),
        },
        info: {
            type: Object,
            default: () => ({}),
        },
    },
    components: {
        MapLine,
    },
    setup() {
        return {
            lineManager,
        };
    },
};
</script>

<style scoped>

</style>