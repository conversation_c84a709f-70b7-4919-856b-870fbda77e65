<script>
import {onMounted, watch, onBeforeUnmount, computed, ref} from 'vue';
import {engine} from '@/store/engine';
import {floor, ceil} from 'lodash';
import {watchDebounced} from '@vueuse/core';

export default {
    name: 'MapLine',
    props: {
        list: {
            type: Array,
            default: () => [],
        },
        info: {
            type: Object,
            default: () => ({}),
        },
        lineInstace: {
            type: Object,
            required: true,
        },
        engine: {
            type: Object,
            default: () => Object.freeze(engine.value),
        },
        dynamicWidth: {
            type: Boolean,
            default: false,
        },
    },
    setup(props) {
        const name = computed(() => props.info.name || 'MapLine');
        const color = computed(() => props.info.color || 'rgb(1, 233, 159)');
        const lineWidth = ref(props.info.lineWidth || 8);
        let state = false;
        const mapZoom = ref();

        const addLine = () => {
            if (!props.list.length) {
                return;
            }
            props.lineInstace.addLine(name.value, props.list, {
                ...props.info,
                color: color.value,
                lineWidth: lineWidth.value,
                clickCallback: props.info?.onClick,
                onMouseenter: props.info?.onMouseenter,
                onMouseleave: props.info?.onMouseleave,
                keepSize: props.dynamicWidth ? lineWidth.value < 12 : true,
            });
            state = true;
        };

        const removeLine = name => {
            props.lineInstace.removeLineByName(name);
        };

        watch(() => [props.list, name.value, props.info], (newData, oldData) => {
            const [, name] = oldData;
            state && removeLine(name);
            addLine();
        }, {deep: true});

        function calcWidth(zoom) {
            if (zoom < 9.32) return 4;
            // 0 ~ 9.65
            if (zoom < 9.65) return 6;
            // 9.65 ~ 17.12
            if (zoom < 17.12) return 8;
            const diff = zoom - 17.12;
            const ratio = ceil(diff / 0.4);
            const rate = 1.15 + (0.04 * ratio);
            const width = 8 + (ratio ** rate) * 2;
            return width > 14 ? 14 : width;
        }

        function updateLineWidth(width) {
            if (width === lineWidth.value) return;
            lineWidth.value = width;
            state && removeLine(name);
            addLine();
        }

        const listenerCallback = e => {
            if (e.rendering.renderState.viewChanged) {
                mapZoom.value = floor(e.map.getZoom(), 2);
            }
        };

        watchDebounced(
            () => mapZoom.value,
            () => {
                let width = calcWidth(mapZoom.value);
                updateLineWidth(width);
            },
            {
                debounce: 40,
                maxWait: 120,
            }
        );

        function registerListener() {
            if (!props.engine) return;
            const zoom = floor(props.engine.map.getZoom(), 2);
            let width = calcWidth(zoom);
            updateLineWidth(width);
            props.engine.rendering.addPrepareRenderListener(listenerCallback);
        }

        function removeListener() {
            if (!props.engine) return;
            props.engine.rendering.removePrepareRenderListener(listenerCallback);
        }

        onMounted(() => {
            addLine();
            if (props.dynamicWidth) {
                registerListener();
            }
        });

        onBeforeUnmount(() => {
            if (props.dynamicWidth) {
                removeListener();
            }
            removeLine(name.value);
        });
    },
};
</script>
