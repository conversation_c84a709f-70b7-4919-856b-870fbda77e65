
import {
    Default3DTiles,
    gltfLoaderEnhanced,
} from '@baidu/mapv-three';
import {onBeforeUnmount, shallowRef, watch} from 'vue';

const main = (map, props) => {
    const satelliteMap = shallowRef(null);
    // 添加3D卫星影像
    const addSatelliteMap = () => {
        let terrainUrl = ['test', 'development'].includes(import.meta.env.MODE) ? 'http://**********:8600/data/terrain_gcj02/tileset.json'
            : 'https://gjdt.private.gdcg.cn/data/terrain_gcj02/tileset.json';

        // 有高程
        satelliteMap.value = map.value.add(new Default3DTiles({
            url: terrainUrl,
            loaders: [
                [/\.gltf$/, gltfLoaderEnhanced],
            ],
        }));
        const multiPorts = [8601, 8602, 8603, 8604, 8605];
        satelliteMap.value.preprocessURL = uri => {
            const random = Math.floor(Math.random() * multiPorts.length);
            uri = new URL(uri);
            uri.port = multiPorts[random];
            return uri.toString();
        };
    };

    const removeSatelliteMap = () => {
        if (satelliteMap.value) {
            satelliteMap.value.visible = false;
        }
        satelliteMap.value && map.value.remove(satelliteMap.value);
        satelliteMap.value = null;
    };

    // watch(() => layerStore.obliqueLayerState, v => {
    //     if (satelliteMap.value) {
    //         satelliteMap.value.visible = v;
    //     }
    // });

    watch(map, v => {
        if (v && props.options?.showSatelliteMap) {
            addSatelliteMap();
        }
    }, {
        immutable: true,
    });

    watch(
        () => props.options?.showSatelliteMap,
        val => {
            val ? addSatelliteMap() : removeSatelliteMap();
        }
    );

    onBeforeUnmount(() => {
        removeSatelliteMap();
    });

    return {
        satelliteMap,
        removeSatelliteMap,
    };
};



export default main;
