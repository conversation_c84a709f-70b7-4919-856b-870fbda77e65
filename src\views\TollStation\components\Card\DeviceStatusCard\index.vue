<template>
    <Card
        class="device-status-card"
        title="设备运行状态"
        card-type="card-long-2"
    >
        <template #content>
            <el-row :gutter="ratio * 16">
                <el-col
                    v-for="(card, index) in cardData"
                    :key="card.field"
                    :span="12"
                >
                    <CardItem
                        :class="{'bg-gradient': index === 0}"
                        :info="card"
                    />
                </el-col>
            </el-row>
        </template>
    </Card>
</template>

<script setup>
import {Card} from '@/components/Common';
import {computed, onMounted, ref} from 'vue';
import {Row as ElRow, Col as ElCol} from 'element-ui';
import {useUnit} from '@/utils';
import CardItem from './CardItem.vue';
import {getTollCameraCount} from '@/api/tollStation';
import {tollStation} from '@/views/TollStation/store';

const {ratio} = useUnit();

const cardData = ref([{}, {}, {}, {}]);

async function fetchData() {
    const {data} = await getTollCameraCount({
        stationId: tollStation.value.stationId,
        stationName: tollStation.value.stationName,
    });
    cardData.value = data;
}

onMounted(() => {
    fetchData();
});

</script>

<style lang="less" scoped>
.device-status-card {
    /deep/ .el-col {
        &:not(:nth-last-child(-n+2)) {
            margin-bottom: 16px;
        }
    }
}

.bg-gradient {
    background-image:
        linear-gradient(
            to right,
            rgba(12, 184, 208, .35),
            rgba(18, 74, 166, .3)
        );
}
</style>