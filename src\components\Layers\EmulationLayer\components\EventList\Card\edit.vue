<template>
    <div class="edit-wrap">
        <div class="title">
            <span>{{ detailInfo.eventType | formatEventType }}</span>
            <div>
                <el-button
                    style="color: #fff;"
                    type="text"
                    @click="handleSave"
                >保存</el-button>
                <el-button
                    style="color: #fff;"
                    type="text"
                    @click="handleCancel"
                >取消</el-button>
            </div>
        </div>
        <div class="edit-content">
            <div v-if="!isConstruction" class="edit-item">
                <span>事件类型：</span>
                <el-select
                    v-model="detailInfo.eventType"
                    placeholder="请选择事件类型"
                    style="flex: 1;"
                    clearable
                    :disabled="isEdit"
                    popper-class="emulation-el-popper"
                >
                    <el-option
                        v-for="item in burst_event_type_list.slice(0, 3)"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code"
                    />
                </el-select>
            </div>
            <div class="edit-item">
                <span>事件位置类型：</span>
                <el-select
                    v-model="detailInfo.eventLocationType"
                    placeholder="请选择事件位置类型"
                    style="flex: 1;"
                    clearable
                    :disabled="isEdit"
                    popper-class="emulation-el-popper"
                >
                    <el-option
                        v-for="item in evnet_location_type"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code"
                    />
                </el-select>
            </div>
            <div class="edit-item">
                <span>事件开始时间：</span>
                <el-date-picker
                    v-model="detailInfo.eventStartTime"
                    type="datetime"
                    :disabled="isEdit"
                    style="flex: 1;"
                    class="h-42"
                    placeholder="请选择事件开始时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    popper-class="emulation-el-popper"
                />
            </div>
            <div class="edit-item">
                <span>预计持续时长：</span>
                <el-input
                    v-model="detailInfo.duration"
                    v-filters
                    :disabled="isEdit"
                    style="flex: 1;"
                    placeholder="请输入"
                >
                    <span slot="suffix" class="suffix-text">分钟</span>
                </el-input>
            </div>
            <div class="edit-item">
                <span>高速名称</span>
                <el-select
                    v-model="detailInfo.highSpeedName"
                    placeholder="请选择高速"
                    style="flex: 1;"
                    filterable
                    clearable
                    popper-class="emulation-el-popper"
                    @change="onSelectChange"
                >
                    <el-option
                        v-for="item in highSpeedInfo"
                        :key="item.highSpeedName"
                        :label="item.highSpeedNameCn"
                        :value="item.highSpeedName"
                    />
                </el-select>
            </div>
            <div class="edit-item">
                <span>事件位置</span>
                <stake-select
                    v-model="detailInfo.eventStartStake"
                    filterable
                    :is-full="true"
                    :list="stakenumberlist"
                    @change="stakeChange"
                />
            </div>
            <div class="edit-item">
                <span style="visibility: hidden;">事件位置</span>
                <el-input
                    ref="eventInput1"
                    v-model="detailInfo.eventPosition" style="flex: 1;"
                    placeholder="请在地图上选择"
                    @focus="handlePositionFocus"
                />
                <img
                    v-if="!positionVisible"
                    src="@/assets/images/position.png"
                    alt=""
                    @click="onPoint"
                >
                <span
                    v-else class="point-btn"
                    @click="pointed"
                >确定</span>

            </div>
            <div class="edit-item">
                <span>事件方向：</span>
                <el-select
                    v-model="detailInfo.direction"
                    placeholder="请选择方向"
                    style="flex: 1;"
                    disabled
                    clearable
                >
                    <el-option
                        v-for="item in road_direction"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code"
                    />
                </el-select>
            </div>
            <div class="edit-item">
                <span>封闭行车道：</span>
                <div class="edit-con" :class="{disabled: isEdit}">
                    <div
                        v-for="item in lane_config" :key="item"
                        @click="onItem(item)"
                    >
                        <span
                            :class="{
                                'selected': !!(closedLane.find(i => i?.lane === item)),
                            }"
                        ></span>
                        <span>{{ `${item === '99' ? '应急' : item}车道` }}</span>
                    </div>
                </div>
            </div>
            <!-- 施工区域 -->

            <temmplate v-if="isConstruction">
                <div class="edit-item">
                    <span>施工限速：</span>
                    <el-input
                        v-model.number="detailInfo.limitSpeed" v-filters
                        class="f-1 h-40"
                        placeholder="请输入"
                    >
                        <span slot="suffix" class="suffix-text">km/h</span>
                    </el-input>
                </div>
                <div class="edit-item">
                    <span>上游过渡区长度：</span>
                    <el-input
                        v-model="detailInfo.upstreamTransitionLength"
                        v-filters
                        class="f-1 h-40"
                        placeholder="请输入"
                    >
                        <span slot="suffix" class="suffix-text">米</span>
                    </el-input>
                </div>
                <div class="edit-item">
                    <span>作业区长度：</span>
                    <el-input
                        v-model="detailInfo.constructionLength"
                        v-filters
                        class="f-1 h-40"
                        placeholder="请输入"
                    >
                        <span slot="suffix" class="suffix-text">米</span>
                    </el-input>
                </div>
                <div class="edit-item">
                    <span>下游过渡区长度：</span>
                    <el-input
                        v-model="detailInfo.downstreamTransitionLength"
                        v-filters
                        class="f-1 h-40"
                        placeholder="请输入"
                    >
                        <span slot="suffix" class="suffix-text">米</span>
                    </el-input>
                </div>
            </temmplate>
        </div>
    </div>
</template>

<script>
import {ref, watch, onMounted, computed} from 'vue';
import {Button, DatePicker, Input, Select, Option, Message} from 'element-ui';
import {evnet_location_type} from '../config';
import {updateEvent, createEvent, getStakeInfo} from '@EmulationLayer/api';
import {road_direction, lane_config, burst_event_type_list} from '@EmulationLayer/config';
import {findEventType} from '@EmulationLayer/utils/index';
import {cloneDeep} from 'lodash';
import {viewTo} from '@/utils/map/index.js';
import {engine} from '@/store/engine.js';
import {stakenumberlist, roadInfo, highSpeedInfo, init_number_list} from '@EmulationLayer/store/index';

import dayjs from 'dayjs';
import store from '@/store';
import StakeSelect from '@EmulationLayer/components/StakeSelect.vue';

export default {
    components: {
        StakeSelect,
        [Button.name]: Button,
        [DatePicker.name]: DatePicker,
        [Input.name]: Input,
        [Select.name]: Select,
        [Option.name]: Option,
    },
    props: {
        detail: {
            type: Object,
            default: () => {},
        },
        isEdit: {
            type: Boolean,
            default: false,
        },
        emulationType: {
            type: [String, Number],
            default: 1,
        },
    },
    filters: {
        formatEventType(val) {
            const cur = findEventType(val);
            return cur ? cur.name : '';
        },
    },
    setup(props, {emit}) {

        const positionVisible = ref(false);
        const eventInput1 = ref(null);
        const detailInfo = ref({
            eventType: null,
        });
        const closedLane = ref([]);
        const clickposition = ref([]);

        const isConstruction = computed(() => props.emulationType === 3);

        const {isEdit, detail} = props;

        const handleCancel = () => {
            emit('cancel');
        };

        const onPoint = () => {
            const eventInput = eventInput1.value;
            eventInput?.focus();
            document.querySelector('body').style.cursor = 'crosshair';
            positionVisible.value = !positionVisible.value;
        };

        const pointed = () => {
            document.querySelector('body').style.cursor = 'pointer';
            positionVisible.value = !positionVisible.value;
            emit('addEvent', detailInfo.value);
        };

        const handleSave = async () => {
            const {emulationType, id, eventCategory, eventType, eventLocationType,
                   eventStartTime, duration, direction, eventStartStake, eventPosition,
                   limitSpeed,
                   upstreamTransitionLength,
                   constructionLength,
                   downstreamTransitionLength,
            } = detailInfo.value;
            const params = {
                id,
                eventCategory,
                eventType,
                eventLocationType,
                eventStartTime,
                duration,
                direction,
                eventStartStake,
                eventPosition,
                emulationType: emulationType || props.emulationType,
                closeLane: closedLane.value.map(item => item.lane).join(','),
                closeLanePosition: closedLane.value.map(item => item.position).join(';'),
            };
            if (isConstruction.value) {
                params.limitSpeed = limitSpeed;
                params.upstreamTransitionLength = upstreamTransitionLength;
                params.constructionLength = constructionLength;
                params.downstreamTransitionLength = downstreamTransitionLength;
            }
            if (!isEdit) {
                delete params.id;
                params.eventCategory = detail.eventCategory;
            }
            const modifyEvent = isEdit ? updateEvent : createEvent;
            const res = await modifyEvent(params);
            if (res.code === 200) {
                Message.success('操作成功');
                emit('save');
            }
        };

        const onItem = item => {
            if (isEdit) {
                return;
            };
            if (closedLane.value.find(i => i?.lane === item)) {
                const index = closedLane.value.findIndex(i => i.lane === item);
                closedLane.value.splice(index, 1);
            }
            else {
                closedLane.value.push({
                    lane: item,
                    position: '',
                });
            }
        };

        const stakeChange = val => {
            const cur = stakenumberlist.value?.find(item => item.stakeNumber === val);
            detailInfo.value.eventPosition = `${cur.longitude},${cur.latitude}`;
        };

        const onSelectChange = async val => {
            if (!val) {
                return;
            };
            await store.dispatch('init_number_list', val);
            detailInfo.value.eventStartStake = stakenumberlist.value[0].stakeNumber;
        };

        const handlePositionFocus = () => {
            let position = [];

            if (!detailInfo.value.eventPosition) {
                Message.warning('事件位置不能为空');
                return;
            }
            position = detailInfo.value.eventPosition.split(',');
            emit('addEvent', detailInfo.value);
            viewTo({
                zoom: 15,
                center: position,
            }, 1000);
        };

        const setInfoData = async () => {
            if (isEdit) {
                const cloneVal = cloneDeep(detail);
                detailInfo.value = cloneVal;
                closedLane.value = cloneVal.closeLane.split(',').map(item => ({lane: item}));
            }
            else {
                detailInfo.value = {
                    eventLocationType: 3,
                    eventStartTime: dayjs().subtract(1, 'hours').format('YYYY-MM-DD HH:mm:ss'),
                    duration: 60,
                    eventStartStake: 'K250+100',
                    eventPosition: '113.260629,23.594334',
                    eventType: isConstruction.value ? 21 : 0,
                    highSpeedName: 'G0423',

                    limitSpeed: 60,
                    upstreamTransitionLength: 100,
                    constructionLength: 100,
                    downstreamTransitionLength: 100,
                };
                const [longitude, latitude] = detailInfo.value.eventPosition.split(',');
                const {code, data} = await getStakeInfo({
                    longitude,
                    latitude,
                    highSpeedName: '',
                });
                if (code === 200 && data) {
                    const info = data.querySumoEdgeDto;
                    detailInfo.value.direction = info.direction || 1;
                    closedLane.value = info.closeLane.split(',').map(item => ({lane: item}));
                }
            }
        };

        watch(() => detail, async () => {
            setInfoData();
        }, {
            deep: true,
            immediate: true,
        });

        watch(() => roadInfo.value, val => {
            if (!val) {
                return;
            }
            detailInfo.value.direction = val.direction || 1;
            closedLane.value = val.closeLane?.split(',')?.map(item => ({lane: item}));
            detailInfo.value.eventStartStake = val.stakeNumber;
        }, {
            deep: true,
        });

        watch(() => clickposition.value, val => {
            detailInfo.value.eventPosition = `${val[0]},${val[1]}`;
        });

        watch(() => stakenumberlist, val => {
            if (!val) {
                return;
            }
            detailInfo.value.eventStartStake = val[0].stakeNumber;
        });

        watch(
            () => [
                detailInfo.value,
                closedLane.value,
            ],
            (newVal, oldVal) => {
                const [info, lane] = newVal;
                emit('addEvent', {
                    ...info,
                    closeLane: lane?.map(item => item.lane).join(','),
                });
            },
            {
                deep: true,
            }
        );

        onMounted(() => {
            init_number_list();
            setInfoData();
            engine.value.event.bind('click', async e => {
                if (e.object?.is3DTiles) {
                    const {point} = e;
                    clickposition.value = point || [];
                    const [lng, lat] = point;
                    const highSpeedName = localStorage.getItem('highSpeedName') || 'G0423';

                    const {code, data: info} = await getStakeInfo({
                        longitude: lng,
                        latitude: lat,
                        highSpeedName,
                    });



                    if (info && code === 200) {
                        roadInfo.value = {...info, ...info.querySumoEdgeDto};
                    }
                }
            });
        });

        return {
            detailInfo,
            positionVisible,
            eventInput1,
            clickposition,
            closedLane,
            road_direction,
            evnet_location_type,
            burst_event_type_list,
            highSpeedInfo,
            stakenumberlist,
            lane_config,
            handleCancel,
            onPoint,
            pointed,
            handleSave,
            onItem,
            stakeChange,
            onSelectChange,
            handlePositionFocus,
            isConstruction,
        };
    },
};
</script>

<style scoped lang="less">
@import url('@EmulationLayer/assets/css/common.less');

.edit-wrap {
    margin-bottom: 8px;
    background: rgba(123, 169, 238, .12);
    border: 1px solid rgba(0, 231, 181, .25);
    padding-bottom: 24px;
    .background-card();

    .title {
        height: 48px;
        background-color: rgba(7, 175, 135, .1);
        border-bottom: 1px solid #07af87;
        display: flex;
        justify-content: space-between;
        padding: 0 24px;
        font-family: "FZLTZHJW--GB1-0", sans-serif;
        cursor: pointer;
        align-items: center;

        > span {
            font-size: 16px;
            color: #e5f1ff;
        }

        .action {
            span {
                font-size: 14px;
                color: #49c3ff;

                &:nth-child(1) {
                    margin-right: 16px;
                }
            }
        }
    }

    .edit-content {
        margin-top: 24px;
        padding-bottom: 50px;

        .edit-item {
            margin-bottom: 16px;
            padding: 0 24px;
            display: flex;
            align-items: center;
            margin-left: 0;

            >img {
                width: 18px;
                height: 28px;
                margin-left: 12px;
                cursor: pointer;
            }

            .point-btn {
                background: #49c3ff;
                color: #e5f1ff;
                display: inline-block;
                width: 52px;
                height: 28px;
                font-size: 14px;
                border-radius: 4px;
                margin-left: 8px;
                text-align: center;
                line-height: 28px;
            }

            >span {
                display: inline-block;
                width: 115px;
                font-size: 14px;
                letter-spacing: 0;
                flex-shrink: 0;
                color: #93b4b7;
            }

            /deep/.el-input__suffix {
                line-height: 40px;
            }

            .edit-con {
                flex: 1;
                display: flex;
                align-items: center;
                flex-wrap: wrap;

                &.disabled {
                    // pointer-events: none;
                    cursor: not-allowed;
                }

                >div {
                    margin-right: 16px;
                    margin-top: 8px;

                    span:nth-child(1) {
                        display: inline-block;
                        width: 14px;
                        height: 14px;
                        border: 1px solid rgba(0, 231, 181, .25);

                        &.selected {
                            background: #07af87;
                            position: relative;

                            &::before {
                                content: '✓';
                                color: #efffff;
                                position: absolute;
                                left: 50%;
                                top: 50%;
                                transform: translate(-50%, -50%);
                            }
                        }
                    }

                    span:nth-child(2) {
                        font-family: 'FZLTZHJW--GB1-0', sans-serif;
                        font-size: 14px;
                        color: #c4cfde;
                        margin-left: 8px;
                    }
                }
            }
        }
    }
}

</style>