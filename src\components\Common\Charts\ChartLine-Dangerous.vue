<template>
    <!-- 折线图 -->
    <div ref="rootCharts" class="rootCharts"></div>
</template>
<script>

import {echarts} from './common';
import {LineChart} from 'echarts/charts';
import {onBeforeUnmount, onMounted, ref, watch} from 'vue';
import dayjs from 'dayjs';
echarts.use([LineChart]);

export default {
    name: 'ChartLineDangerous',
    props: {
        data: {
            type: Array,
        },
    },
    setup(props, {emit}) {

        let chart = null;
        const rootCharts = ref(null);

        const resize = () => {
            if (chart) {
                chart.resize();
            }
        };

        const update = value => {
            const xAxis = [];
            const xName = [];
            const yAxis = [];

            value?.forEach((item, i) => {
                xName.push(item.date);
                const y = dayjs().year();
                xAxis.push(dayjs(`${y}/12/${i + 1}`).unix() * 1000);
                yAxis.push(item);
            });

            chart.setOption({
                legend: {
                    icon: 'circle',
                    type: 'plain',
                    textStyle: {
                        fontSize: 11,
                        color: 'rgba(255, 255, 255, .8)',
                    },
                    size: 5,
                    itemGap: 10,
                    itemWidth: 8,
                },
                grid: {
                    top: 40,
                    bottom: 20,
                    right: 20,
                    left: 30,
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'line',
                        lineStyle: {
                            color: '#75FFCE',
                            type: 'dashed',
                        },
                    },
                    className: 'custom-tooltip',
                    formatter: params => {
                        console.log(params);
                        // return a;
                        return `<div style="display: flex;flex-direction: column;">
                                <div>${xName[params[0].dataIndex]}<div/>

                                <div style="display: flex;flex-direction: column;">
                                    ${params.map(e => {
                                            const {seriesName, marker, value} = e;
                                            return `<div
                                            style="display: flex;
                                            flex-direction: row;
                                            justify-content: space-between; gap: 30px">
                                                    <div>${marker} ${seriesName}</div>       
                                                    <span>${value[1]}</span>
                                                </div>`;
                                        }).join('')
                        }
                                </div>
                            </div>
                        `;
                    },
                    valueFormatter(v, a) {
                        console.log('111', v, a);
                        return v;
                    },
                },
                color: ['#75FFCE', '#FCC832', '#FF8419', '#00BAFF'],
                xAxis:
                    {
                        type: 'time',
                        boundaryGap: false,
                        data: xAxis,
                        axisLabel: {
                            // showMinLabel: true,
                            // showMaxLabel: true, // x轴最后的显示
                            color: 'rgba(255,255,255,.6)',
                            fontSize: 10,
                            fontFamily: 'DIN Alternate',
                            fontWeight: 700,
                            formatter: (val, index) => {
                                const i = xAxis.indexOf(val);
                                return xName[i];
                            },
                        },
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: 'rgba(255,255,255,.4)',
                            },
                        },
                        axisTick: {
                            // interval: 9,
                            inside: true,
                            lineStyle: {
                                // color: 'rgba(255,255,255,.4)',
                            },
                        },
                        minorTick: {
                            show: true,
                            // splitNumber: 0,
                            inside: true,
                            length: 3,
                            lineStyle: {
                                color: 'rgba(255, 255, 255, .2)',
                            },
                        },
                        // minorSplitLine: {
                        //     show: true,
                        // },
                    },

                yAxis: {
                    type: 'value',
                    axisLine: false,
                    splitLine: {
                        show: true,
                        lineStyle: {
                            type: 'dashed',
                            color: 'rgba(239, 255, 255, .1)',
                        },
                    },
                    // min: 0,
                    // max: 120,
                    axisLabel: {
                        fontSize: 10,
                        fontFamily: 'DIN Alternate',
                        fontWeight: 700,
                        color: 'rgba(255,255,255,.6)',
                    },
                },
                series: [
                    {
                        name: '三急一速总数',
                        type: 'line',
                        Symbol: 10,
                        showSymbol: false,
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 5,
                                shadowColor: 'rgba(117, 255, 206, 1)',
                            },
                        },
                        data: yAxis.map((e, i) => {
                            console.log(123123, [xAxis[i], e.total]);
                            return [xAxis[i], e.total];
                            // return {
                            //     name: xAxis[i],
                            //     value: e,
                            // };
                        }),
                        areaStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(
                                    0,
                                    0,
                                    0,
                                    1,
                                    [
                                        {
                                            offset: 0,
                                            color: 'rgba(117, 255, 206, .35)',
                                        },
                                        {
                                            offset: 1,
                                            color: 'rgb(117, 255, 206, .0)',
                                        },
                                    ],
                                    false
                                ),
                            },
                        },
                    },
                    {
                        name: '超速次数',
                        type: 'line',
                        showSymbol: false,
                        data: yAxis.map((e, i) => {
                            return [xAxis[i], e.over_cnt];
                        }),
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 5,
                                shadowColor: 'rgba(252, 200, 50, 1)',
                            },
                        },
                        areaStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(
                                    0,
                                    0,
                                    0,
                                    1,
                                    [
                                        {
                                            offset: 0,
                                            color: 'rgba(252, 200, 50, .35)',
                                        },
                                        {
                                            offset: 1,
                                            color: 'rgba(252, 200, 50, .0)',
                                        },
                                    ],
                                    false
                                ),
                            },
                        },
                    },
                    {
                        name: '急加速次数',
                        type: 'line',
                        showSymbol: false,
                        data: yAxis.map((e, i) => {
                            return [xAxis[i], e.acc_cnt];
                        }),
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 5,
                                shadowColor: 'rgba(255, 132, 25, 1)',
                            },
                        },
                        areaStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(
                                    0,
                                    0,
                                    0,
                                    1,
                                    [
                                        {
                                            offset: 0,
                                            color: 'rgba(255, 132, 25, .35)',
                                        },
                                        {
                                            offset: 1,
                                            color: 'rgb(255, 132, 25, .0)',
                                        },
                                    ],
                                    false
                                ),

                            },
                        },
                    },
                    {
                        name: '急减速次数',
                        type: 'line',
                        showSymbol: false,
                        data: yAxis.map((e, i) => {
                            return [xAxis[i], e.dec_cnt];
                        }),
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 5,
                                shadowColor: 'rgba(0, 186, 255, 1)',
                            },
                        },
                        areaStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(
                                    0,
                                    0,
                                    0,
                                    1,
                                    [
                                        {
                                            offset: 0,
                                            color: 'rgba(0, 186, 255, 0.35)',
                                        },
                                        {
                                            offset: 1,
                                            color: 'rgba(0, 186, 255, .0)',
                                        },
                                    ],
                                    false
                                ),

                            },
                        },
                    },
                ],
            });
        };

        const initChart = () => {
            if (!rootCharts.value) {
                return;
            }
            chart = echarts.init(rootCharts.value, 'traffic');
            window.addEventListener('resize', resize);

            update(props.data);
        };

        watch(() => props.data, update, {
        }, {
            immediate: true,
        });

        onMounted(initChart);

        onBeforeUnmount(() => {
            window.removeEventListener('resize', resize);
            chart.dispose();
        });

        return {
            rootCharts,
        };
    },
};

</script>

<style>
.rootCharts {
    width: 100%;
    height: 100%;
}
</style>