<template>
    <div class="comp-left">
        <div class="comp-left-item">
            <card title="收费设施">
                <template #content>
                    <tollFacility/>
                </template>
            </card>
            <card title="设施养护">
                <template #titleContent>
                    <router-link to="/maintain" class="button">查看更多</router-link>
                </template>
                <template #content>
                    <facilityMaintenance/>
                </template>
            </card>
        </div>
        <div class="comp-left-item">
            <card class="card-long-2" title="机电养护">
                <template #content>
                    <electromechanical/>
                </template>
            </card>
        </div>
    </div>
</template>

<script>
import {Card} from '@/components/Common/index';
import tollFacility from './tollFacility.vue';
import facilityMaintenance from './facilityMaintenance.vue';
import electromechanical from './electromechanical.vue';
export default {
    name: '设备管理左',
    components: {
        Card,
        tollFacility,
        facilityMaintenance,
        electromechanical,
    },
};
</script>

<style lang="less" scoped>
.comp-left {
    width: 100%;
    height: calc(100% - 152px);
    .comp-left-item {
        display: flex;

        .button {
            border: 1px solid rgba(1, 255, 229, .8);
            color: rgba(1, 255, 229, .8);
            padding: 3px 8px;
            font-size: 16px;
            font-family: 'PingFang';
        }

        &:first-child {
            margin-bottom: 23px;
        }

        > div {
            &:first-child {
                margin-right: 24px;
            }
        }
    }
}
</style>