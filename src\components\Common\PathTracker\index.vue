<template lang="">
    <div class="path-marker">
        <template v-if="iconList && iconList.length > 0">
            <div v-for="item in iconList" :key="item.id">
                <device-maker
                    v-if="!!item[iconInfo.lng] && !!item[iconInfo.lat]"
                    :manager-instace="managerInstace"
                    :info="{
                        status: item.status || 'normal',
                        position: [item[iconInfo.lng], item[iconInfo.lat]],
                        pointName: item[iconInfo.id],
                        type: item.type || '龙门架',
                        labelName: item[iconInfo.name],
                        customData: item,
                    }"
                />
            </div>
        </template>
    </div>
</template>

<script>
import {DeviceMaker} from '@/components/Common';
import {onUnmounted} from 'vue';
export default {
    props: {
        pathTracker: {
            type: Object,
            required: true,
        },
        managerInstace: {
            type: Object,
            required: true,
        },
        lineManager: {
            type: Object,
            required: true,
        },
        list: {
            type: Array,
            default: () => [],
        },
        iconList: {
            type: Array,
            default: () => [],
        },
        lineInfo: {
            type: Object,
            default: () => {},
        },
        iconInfo: {
            type: Object,
            default: () => ({
                name: 'gantryName',
                lng: 'gantryLng',
                lat: 'gantryLat',
                id: 'gantryId',
            }),
        },
        viewMode: {
            type: String,
            default: 'unlock',
        },
        modelType: {
            type: String,
            default: 'maplayer/assets/models/kache.glb',
        },
    },
    components: {
        DeviceMaker,
    },
    setup(props) {
        props.pathTracker.addPathTracker(props.lineInfo.name, {
            position: props.list[0],
            positions: props.list,
            color: props.lineInfo.color,
            opacity: props.lineInfo.opacity,
            lineWidth: props.lineInfo.lineWidth,
            modelType: props.modelType,
            viewMode: props.viewMode,
        });

        onUnmounted(() => {
            props.pathTracker.clear();
        });
    },
};
</script>

<style scoped>

</style>