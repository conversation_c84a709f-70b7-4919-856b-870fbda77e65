<template>
    <div id="my-select">
        <my-select
            v-model="monitoring"
            :options="monitoringOptions"
            placeholder="请选择监控系统"
            @change="changeMonitoringFn"
        />
        <my-select
            v-model="value"
            :options="stationOptions"
            placeholder="请选择交调站"
            @change="changeStationFn"
        />
    </div>
</template>

<script>
import {ref} from 'vue';
import {MySelect} from '@/views/EquipmentFacilities/components/common/index';
export default {
    name: 'cascadeSelect',
    props: {
        placeholder: {
            type: String,
            default: '请选择',
        },
        value: {
            type: String,
            default: '',
        },
    },
    components: {
        MySelect,
    },
    model: {
        prop: 'value',
        event: 'update:value',
    },
    setup(props, {emit}) {
        const monitoring = ref('1');

        // 监控系统下拉框数组数据
        const monitoringOptions = [
            {
                value: '1',
                label: '收费监控',
                children: [
                    {
                        value: '17',
                        label: '指收费车道',
                    },
                    {
                        value: '18',
                        label: '收费广场摄像机',
                    },
                ],
            },
            {
                value: '2',
                label: '道路监控',
                children: [
                    {
                        value: '2',
                        label: '枪式摄像机',
                    },
                    {
                        value: '3',
                        label: '快球摄像机',
                    },
                    {
                        value: '4',
                        label: '卡口摄像机',
                    },
                    {
                        value: '54',
                        label: '一体化抓拍摄像机',
                    },
                ],
            },
            {
                value: '3',
                label: '可变情报板',
                children: [
                    {
                        value: '11',
                        label: '门架屏',
                    },
                    {
                        value: '10',
                        label: '悬臂屏服务区',
                    },
                    {
                        value: '107',
                        label: '灯杆显示屏',
                    },
                ],
            },
        ];

        // 交调站下拉框数组数据
        const stationOptions = ref([]);
        stationOptions.value = monitoringOptions[0].children;

        const changeStationFn = e => {
            emit('update:value', e);
            emit('change', e);
        };
        const changeMonitoringFn = e => {
            stationOptions.value = monitoringOptions[e - 1].children;
            changeStationFn(stationOptions.value[0].value);
        };

        changeStationFn(monitoringOptions[0].children[0].value);
        return {
            monitoringOptions,
            monitoring,
            stationOptions,
            changeStationFn,
            changeMonitoringFn,
        };
    },
};
</script>

<style lang="less" scoped>
    #my-select {
        display: flex;
        align-items: center;
        justify-content: end;

        > div:last-child {
            margin-left: 16px;
        }

        :deep(.el-select) {
            min-width: 164px;
        }
    }
</style>