<template>
    <card title="收费运营数据">
        <template #content>
            <div class="list">
                <div class="item">
                    <span class="label">车流量：</span>
                    <span class="value">{{ info.carFlow }}</span>
                </div>
                <div class="item">
                    <span class="label">绿通数据：</span>
                    <span class="value">{{ info.greenWayNum }}</span>
                </div>
                <div class="item">
                    <span class="label">稽查数据：</span>
                    <span class="value">{{ info.checkNum }}</span>
                </div>
                <div class="item">
                    <span class="label">关键设备运行情况：</span>
                    <span class="value">{{ info.deviceStatus }}</span>
                </div>
                <div class="item">
                    <span class="label">通信链路情况：</span>
                    <span class="value">{{ info.communicateStatus }}</span>
                </div>
                <div class="item">
                    <span class="label">软件版本：</span>
                    <span class="value">{{ info.version }}</span>
                </div>
            </div>
        </template>
    </card>
</template>

<script>
import {Card} from '@/components/Common';

export default {
    components: {
        Card,
    },
    props: {
        info: {
            type: Object,
            default: () => ({}),
        },
    },
};
</script>