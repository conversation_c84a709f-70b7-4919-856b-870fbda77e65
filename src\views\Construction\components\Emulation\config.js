export const emulationStatusMap = {
    /** 待开始 */
    1: {
        text: '待开始',
        className: 'emulation-tag-orange',
    },
    /** 仿真中 */
    2: {
        text: '仿真中',
        className: 'emulation-tag-blue',
    },
    /** 已完成 */
    3: {
        text: '已完成',
        className: 'emulation-tag-gray',
    },
};

// 事件类型
export const eventTypeOptions = [
    {
        value: 21,
        label: '占道施工',
    },
    {
        value: 23,
        label: '道路扩建',
    },
];

// 事件位置类型
export const evnetLocationTypeOptions = [
    {value: 1, label: '入口匝道'},
    {value: 2, label: '出口匝道'},
    {value: 3, label: '路段'},
    {value: 4, label: '隧道'},
];

// 事件方向
export const evnetDirectionOptions = [
    {
        value: 0,
        label: '未知',
    },
    {
        value: 1,
        label: '上行',
    },
    {
        value: 2,
        label: '下行',
    },
    {
        value: 3,
        label: '双向',
    },
];

// 关闭车道
export const closeLaneOptions = [
    {value: 1, label: '1车道'},
    {value: 2, label: '2车道'},
    {value: 3, label: '3车道'},
    {value: 99, label: '应急车道'},
];

// 策略输入选项
export const strategyInputOptions = [
    {label: '智能策略', value: 1},
    {label: '手动输入', value: 2},
    {label: '无策略输入', value: 3},
];
// 策略类型选项
export const strategyOptions = [
    {label: '开放应急车道', value: 1},
    {label: '行车道管控', value: 2},
    {label: '出入口管控', value: 3},
];
// 车道选项
export const laneOptions = [
    {label: '1车道', value: 1},
    {label: '2车道', value: 2},
    {label: '3车道', value: 3},
    {label: '应急车道', value: 99},
];
// 收费站配置选项
export const tollConfigOptions = [
    {label: '入口关闭', value: 1},
    {label: '入口限流', value: 2, config: true},
    {label: '出口分流', value: 3, config: true},
];
export const vehicleTypeOptions = [
    {
        value: 1,
        label: '小车',
    },
    {
        value: 2,
        label: '大车',
    },
];

export const flowInputOptions = [
    {
        label: '在线生成',
        value: 1,
    },
    {
        label: '手动输入',
        value: 2,
    },
];

export const colorBaseArr1 = [
    '#FFFFFF',
    '#FFFFFF',
    '#FFFFFF',
    '#FFFFFF',
    '#FFFFFF',
    '#FFFFFF',
    '#D82121',
    '#6C5A47',
    '#FFD021',
    '#1F4896',
];

export const colorBaseArr2 = ['#D9D9D9', '#DE5757', '#007623', '#FFFFFF'];


export const areaSelectOption = [
    {label: '路段选择', value: 1},
    {label: '自定义框选', value: 2},
];