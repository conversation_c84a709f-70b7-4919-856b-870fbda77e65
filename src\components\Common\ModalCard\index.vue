<template>
    <transition
        name="el-fade-in"
    >
        <div
            v-if="visible"
            :class="['modal-card', {'modal-card-center': fullscreenCenter}]"
            :style="{
                width: `${width * ratio}px`,
            }"
        >
            <div class="modal-card__head">
                <div class="modal-card__head-left">
                    <span v-if="!$slots.title" class="modal-card__title">{{ title }}</span>
                    <slot v-else name="title"></slot>
                </div>
                <div class="modal-card__head-right">
                    <slot name="head-right"></slot>
                    <div class="modal-card__close" @click.stop="handleClose">
                        <i class="el-icon-close"></i>
                        <span>关闭</span>
                    </div>
                </div>
            </div>
            <div class="modal-card__body">
                <slot></slot>
            </div>
            <div v-if="showFoot" class="modal-card__foot">
                <div class="btn-default" @click.stop="handleClose">{{ cancelText }}</div>
                <div
                    :class="['btn-default', `${disabledConfirm ? 'btn-disabled' : 'btn-confirm'}`]"
                    @click.stop="handleConfirm"
                >
                    {{ confirmText }}
                </div>
            </div>
        </div>
    </transition>
</template>

<script>
import {getCurrentInstance, onMounted, onUnmounted} from 'vue';
import {useUnit} from '@/utils';

export default {
    props: {
        visible: Boolean,
        width: {
            type: Number,
            default: 380,
        },
        title: String,
        fullscreenCenter: {
            type: Boolean,
            default: true,
        },
        appendTo: {
            type: HTMLElement,
            default: document.querySelector('body'),
        },
        showFoot: {
            type: Boolean,
            default: true,
        },
        disabledConfirm: Boolean,
        confirmText: {
            type: String,
            default: '确认',
        },
        cancelText: {
            type: String,
            default: '取消',
        },
    },
    setup(props, {emit}) {
        const vm = getCurrentInstance();

        const {ratio} = useUnit();

        function appendToBody() {
            props.appendTo?.appendChild(vm.proxy.$el);
        }

        function removeFromBody() {
            props.appendTo?.removeChild(vm.proxy.$el);
        }

        function handleClose() {
            emit('update:visible', false);
            emit('close');
        }

        function handleConfirm() {
            emit('confirm');
        }

        onMounted(() => {
            if (props.fullscreenCenter) {
                appendToBody();
            }
        });

        onUnmounted(() => {
            if (props.fullscreenCenter) {
                removeFromBody();
            }
        });

        return {
            handleClose,
            handleConfirm,
            ratio,
        };
    },
};
</script>

<style lang="less" scoped>
.modal-card {
    backdrop-filter: blur(10px);
    color: #fff;
    background-color: rgba(8, 35, 79, .8);

    &.modal-card-center {
        position: fixed;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        z-index: 200;
    }

    &__head {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: rgba(36, 104, 242, .3);
        height: 58px;
        border-top: 10px solid rgba(77, 135, 255, .3);
        border-bottom: 1px solid rgba(255, 255, 255, .1);
        padding: 0 20px;

        &::before {
            content: '';
            position: absolute;
            top: 3px;
            left: 8px;
            width: 16px;
            height: 4px;
            background-image: linear-gradient(to right, rgb(46, 210, 255) 4px, transparent 2px);
            background-size: 6px 100%;
        }

        &::after {
            content: '';
            position: absolute;
            top: 4px;
            right: 8px;
            width: 72px;
            height: 2px;
            background-color: rgb(46, 210, 255);
        }

        &-left {
            font-size: 20px;
        }

        &-right {
            display: flex;
        }
    }

    &__close {
        font-size: 12px;
        color: rgba(#fff, .6);
        cursor: pointer;
        display: flex;
        align-items: center;

        &:hover {
            color: #fff;
        }

        .el-icon-close {
            font-size: 16px;
            margin-bottom: 2px;
            margin-right: 2px;
        }
    }

    &__body {
        padding: 8px;
    }

    &__foot {
        padding-bottom: 16px;
        display: flex;
        justify-content: center;

        & > *:not(:first-child) {
            margin-left: 16px;
        }

        .btn-default {
            width: 120px;
            height: 32px;
            line-height: 32px;
            text-align: center;
            border: 1px solid rgba(#fff, .2);
            background-color: rgba(#2c2c2c, .9);
            cursor: pointer;
            user-select: none;

            &:active {
                opacity: .85;
            }
        }

        .btn-disabled {
            background-color: #4b4b4b;
            border: none;
            cursor: not-allowed;
            pointer-events: none;

            &:active {
                opacity: 1;
            }
        }

        .btn-confirm {
            background-color: #27b279;
            border: none;
            color: #000;
        }
    }
}
</style>