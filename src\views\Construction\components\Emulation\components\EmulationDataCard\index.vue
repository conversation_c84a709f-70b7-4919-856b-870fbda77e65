<template>
    <div class="emulation-data-wrapper">
        <div class="emulation-data-card">
            <div class="emulation-data-card__head">路网饱和度</div>
            <div class="emulation-data-card__body">{{ emulationTargetData.saturation }}</div>
        </div>
        <div class="emulation-data-card">
            <div class="emulation-data-card__head">服务水平</div>
            <div class="emulation-data-card__body">{{ emulationTargetData.serviceLevel }}</div>
        </div>
        <div class="emulation-data-card">
            <div class="emulation-data-card__head">平均速度(km/h)</div>
            <div class="emulation-data-card__body">{{ emulationTargetData.avgSpeed }}</div>
        </div>
    </div>
</template>

<script>
import {emulationTargetData} from '../EmulationTwin/index';

export default {
    setup() {
        return {
            emulationTargetData,
        };
    },
};
</script>

<style lang="less" scoped>
.emulation-data {
    &-wrapper {
        display: flex;
        justify-content: space-between;
    }

    &-card {
        width: 115px;

        &__head {
            height: 28px;
            line-height: 28px;
            text-align: center;
            background-color: rgba(#fff, .1);
            backdrop-filter: blur(12px);
            border-bottom: 2px solid #4d4d4d;
        }

        &__body {
            height: 67px;
            line-height: 67px;
            text-align: center;
            font-size: 48px;
            font-family: 'Neue';
            background-color: rgba(#fff, .2);
        }
    }
}
</style>