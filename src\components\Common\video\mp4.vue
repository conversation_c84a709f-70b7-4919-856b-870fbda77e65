<template>
    <div class="video-player-mp4">
        <video
            ref="videoPlayer"
            :src="url"
            :muted="true"
            :loop="true"
            :poster="poster"
            autoplay
            controls
            controlsList="nofullscreen"
        ></video>
        <div v-if="loading" class="loading">
            <svg
                focusable="false" data-icon="loading"
                width="1em" height="1em"
                fill="currentColor" aria-hidden="true"
                viewBox="0 0 1024 1024"
            >
                <path
                    d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9
                    437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1
                    0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2
                    40.2 199.3.1 19.9-16 36-35.9 36z"
                />
            </svg>
        </div>
    </div>
</template>
<script setup>
import {ref, watch, onMounted} from 'vue';

const props = defineProps({
    url: {
        type: String,
        default: '',
    },
    poster: String,
});

const videoPlayer = ref(null);

const loading = ref(true);

watch(() => props.url, () => {
    loading.value = true;
});

onMounted(() => {
    videoPlayer.value.addEventListener('play', () => {
        loading.value = false;
    });

    videoPlayer.value.addEventListener('error', () => {
        loading.value = true;
    });
});
</script>
<style lang="less" scoped>
.video-player-mp4 {
    width: 100%;
    height: 100%;
    position: relative;

    video {
        width: 100%;
        height: 100%;
        position: relative;
        object-fit: fill;
        max-width: 100%;
        aspect-ratio: 16 / 9;
    }

    .loading {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, .5);
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 40px;
        color: #fff;

        svg {
            animation: spin 1s linear infinite;
        }
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }
}
</style>