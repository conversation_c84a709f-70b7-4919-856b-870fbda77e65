import ellipse_active from '@EmulationLayer/assets/images/ellipse-active.png';
import ellipse_default from '@EmulationLayer/assets/images/ellipse-default.png';
import diamond_active from '@EmulationLayer/assets/images/diamond-active.png';
import diamond_default from '@EmulationLayer/assets/images/diamond-default.png';
import area_active from '@EmulationLayer/assets/images/area-active.png';
import area_default from '@EmulationLayer/assets/images/area-default.png';


export const edit_legend = [
    {
        code: 1,
        type: 'circle',
        active: ellipse_active,
        default: ellipse_default,
        title: '圆形',
    },
    {
        code: 2,
        type: 'react',
        active: diamond_active,
        default: diamond_default,
        title: '矩形',
    },
    {
        code: 3,
        type: 'polygon',
        active: area_active,
        default: area_default,
        title: '多边形',
    },
    // {
    //     code: 4,
    //     type: 'complete',
    //     // active: area_active,
    //     // default: area_default,
    //     title: '编辑完成',
    // },
];