<template>
    <div class="construction-area">
        <map-polygon
            :info="{
                name: `POLYGON__${areaId}`,
                color: '#00FFEE',
            }"
            :list="list"
            :polygon-instace="polygonManager"
        />
        <map-line
            :info="{
                name: `LINE__${areaId}`,
                lineWidth: 0.2,
                color: '#00FFEE',
                transparent: true,
                opacity: 1,
                dashed: true,
                keepSize: false,
                dashArray: 1,
                dashRatio: .65,
            }"
            :list="list"
            :line-instace="lineManager"
        />
    </div>
</template>

<script>
import {
    MapLine,
    MapPolygon,
} from '@/components/Common';
import {lineManager, polygonManager} from '@/views/Construction/utils';
import {v4 as uuidV4} from 'uuid';

export default {
    components: {
        MapLine,
        MapPolygon,
    },
    props: {
        list: Array,
    },
    setup() {
        const areaId = uuidV4();

        return {
            areaId,
            lineManager,
            polygonManager,
        };
    },
};
</script>

