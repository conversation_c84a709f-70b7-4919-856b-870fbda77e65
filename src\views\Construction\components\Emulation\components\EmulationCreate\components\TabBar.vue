<template>
    <div class="x-tabbar">
        <template
            v-for="item in list"
        >
            <div
                :key="`x-tabbar__item__${item.key}`"
                :class="['x-tabbar__item', {'x-tabbar__item__active': item.key === value}]"
                @click="handleClick(item)"
            >
                {{ item.label }}
            </div>
        </template>
    </div>
</template>

<script>
export default {
    props: {
        value: Number,
        list: {
            type: Array,
            default: () => ([]),
        },
    },
    setup(props, {emit}) {

        function handleClick(e) {
            emit('input', e.key);
            emit('change', e);
        }

        return {
            handleClick,
        };
    },
};
</script>

<style lang="less" scoped>
.x-tabbar {
    display: flex;
    align-items: end;
    justify-content: space-between;

    &__item {
        position: relative;
        width: 113px;
        height: 36px;
        line-height: 36px;
        text-align: center;
        cursor: pointer;
        border-radius: 2px 2px 0 0;
        font-size: 16px;
        color: rgba(#fff, .6);
        background-color: rgba(36, 104, 242, .3);
        border: 1px	solid rgb(41, 86, 157);
        clip-path:
            polygon(
                0 0,
                100% 0,
                100% calc(100% - 5px),
                calc(100% - 5px) 100%,
                0 100%,
            );

        &::after {
            position: absolute;
            right: -1.5px;
            bottom: 0;
            content: '';
            width: 8px;
            height: 1.5px;
            background-color: rgb(41, 86, 157);
            transform: rotate(-45deg);
        }

        &__active {
            border-color: rgba(#fff, .3);
            background-image:
                linear-gradient(
                    to right,
                    rgb(36, 104, 242),
                    rgba(1, 132, 255, .7),
                );

            &::after {
                background-color: rgba(#fff, .3);
            }
        }
    }
}
</style>