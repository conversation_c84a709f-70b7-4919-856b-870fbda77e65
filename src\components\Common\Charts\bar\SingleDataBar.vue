<template>
    <div ref="singRef" class="singBar c-50">
        <div
            v-if="data.length && needName"
            ref="legendRef" class="legend"
        >
            <span>{{ yName }}</span>
            <div
                v-if="needLegend && legendList.length >= 1"
            >
                <div
                    v-for="item in legendList" :key="item.name"
                    class="legend-item"
                >
                    <span
                        class="dib"
                        :style="{
                            backgroundColor: item.color,
                        }"
                    ></span>
                    <span>{{ item.name }}</span>
                </div>
            </div>
        </div>
        <div class="echart">
            <ul
                v-if="data.length"
                ref="yAxisRef"
                class="yAxis"
            >
                <li
                    v-for="i in (splitNumber + 1)" :key="i"
                    class="yAxis-item"
                >
                    {{ transformStep(i) }}
                </li>
            </ul>
            <div
                v-show="data.length"
                ref="chartRef"
                :style="yAxixStyle"
            ></div>
        </div>
        <NoData v-if="!data.length"/>
    </div>
</template>

<script>
import {ref, watch, computed} from 'vue';
import {useResizeObserver} from '@vueuse/core';
import useChartCommon from '../useChartCommon';
import {singleTooltipFormatter} from '../util';
import {echarts} from '../common';
import {useUnit} from '@/utils/hooks/useUnit';
import NoData from '../../NoData/index.vue';

export default {
    props: {
        yName: {
            type: String,
        },
        data: {
            type: Array,
            default: () => ([]),
        },
        legendData: {
            type: Array,
            default: () => ([]),
        },
        tooltipFormatter: {
            type: Function,
            default: singleTooltipFormatter,
        },
        boundaryGap: {
            type: Boolean, // 两边是否留白
            default: true,
        },
        barWidth: {
            type: Number,
            default: 24,
        },
        splitNumber: {
            type: Number,
            default: 5,
        },
        needName: {
            type: Boolean, // 是否需要y轴名称
            default: true,
        },
        needLegend: {
            type: Boolean, // 是否需要legend
            default: true,
        },
    },
    setup(props) {
        const {ratio} = useUnit();
        const step = ref(5);
        const fontColor = 'rgba(255, 255, 255, .7)';
        const white = 'rgb(255, 255, 255)';
        const transparent = 'rgba(255, 255, 255, .0)';
        const chartRef = ref(null);
        const yAxisRef = ref(null);
        const singRef = ref(null);
        const legendRef = ref(null);

        const highlightXName = ref('');

        /**
         * 转换步骤值
         *
         * @param v 原始步骤值
         * @returns 转换后的步骤值，如果为'0.0'则返回'0'
         */
        const transformStep = v => {
            const s = step.value;
            const n = (s > 1 || s === 1) ? (v - 1) * s : ((v - 1) * s).toFixed(1);
            return n === '0.0' ? '0' : n;
        };

        const yAxixStyle = ref({});

        useResizeObserver(yAxisRef, entries => {
            const entry = entries[0];
            const {width} = entry.contentRect;
            yAxixStyle.value = {
                width: `calc(${singRef.value.clientWidth}px - ${width + 12 * ratio.value}px)`,
                height: '100%',
                right: 0,
            };
        });

        /**
         * 获取图表配置选项
         *
         * @returns 返回一个包含图表配置选项的对象
         */
        const getOptions = () => {
            const max = Math.max(...props.data.map(item => item.value), 1);
            const s = Number((max / props.splitNumber).toFixed(1));
            step.value = s > 1 ? Math.ceil(s) : s;
            const xAxis = props.data.map(item => item.name);
            return {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        lineStyle: {
                            color: white,
                            width: 2 * ratio.value,
                            type: 'solid',
                        },
                    },
                    className: 'statistics-chart-tooltip',
                    position: function (point, params, dom, rect, size) {
                        const {contentSize, viewSize} = size;
                        let x;
                        if (point[0] + contentSize[0] < viewSize[0]) {
                            x = Math.min(point[0], viewSize[0] - contentSize[0]);
                        }
                        else {
                            x = point[0] - contentSize[0];
                        }
                        // 固定在顶部
                        return [x, 3];
                    },
                    formatter: props.tooltipFormatter,
                },
                grid: {
                    top: 0,
                    right: 0,
                    bottom: 0,
                    left: 0,
                    containLabel: true,
                },
                xAxis: {
                    type: 'category',
                    show: true,
                    offset: -3,
                    boundaryGap: props.boundaryGap, // 两边是否留白
                    axisTick: {
                        show: false,
                    },
                    axisLabel: {
                        interval: 'auto',
                        formatter: (_, index) => {
                            return xAxis[index];
                        },
                        fontSize: 14 * ratio.value,
                        color: fontColor,
                    },
                    axisLine: {
                        show: false,
                    },
                    axisPointer: {
                        type: 'shadow',
                        shadowStyle: {
                            color: new echarts.graphic.LinearGradient(
                                0,
                                0,
                                0,
                                1,
                                [
                                    {
                                        offset: 0,
                                        color: white,
                                    },
                                    {
                                        offset: 1,
                                        color: transparent,
                                    },
                                ],
                                false
                            ),
                            opacity: 0.3,
                        },
                    },
                    data: xAxis.map(item => {
                        if (item === highlightXName.value) {
                            return {
                                value: item,
                                textStyle: {
                                    color: white,
                                    padding: [2 * ratio.value, 6 * ratio.value],
                                    borderWidth: 1 * ratio.value,
                                    borderRadius: 1 * ratio.value,
                                    borderColor: fontColor,
                                    backgroundColor: new echarts.graphic.LinearGradient(
                                        0,
                                        0,
                                        0,
                                        1,
                                        [
                                            {
                                                offset: 0,
                                                color: 'rgb(36, 104, 242)',
                                            },
                                            {
                                                offset: 1,
                                                color: 'rgba(1, 255, 229, 0.5)',
                                            },
                                        ],
                                        false
                                    ),
                                },
                            };
                        }
                        return {
                            value: item,
                            textStyle: {
                                padding: [2 * ratio.value, 6 * ratio.value],
                                borderWidth: 1 * ratio.value,
                            },
                        };
                    }),
                },
                yAxis: {
                    type: 'value',
                    min: 0,
                    max: props.splitNumber * step.value,
                    interval: step.value,
                    splitNumber: props.splitNumber,
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(255, 255, 255, 0.1)',
                        },
                    },
                    axisLabel: {
                        show: false,
                        fontSize: 14 * ratio.value,
                        color: fontColor,
                    },
                    splitArea: {
                        show: true,
                        areaStyle: {
                            color: ['rgba(0, 58, 134, 0.3)'],
                        },
                    },
                },
                series: [
                    {
                        name: props.yName,
                        type: 'bar',
                        data: props.data.map(item => {
                            return {
                                ...item,
                                itemStyle: {
                                    color: item.color,
                                },
                            };
                        }),
                        barWidth: props.barWidth * ratio.value,
                        markLine: {
                            silent: true,
                            symbol: 'none',
                            animation: false,
                            data: Array(2).fill(null).map(() => ({
                                yAxis: 0,
                                lineStyle: {
                                    color: white,
                                    type: 'solid',
                                    width: 1 * ratio.value,
                                },
                                label: {
                                    show: false,
                                },
                            })),
                        },
                    },
                ],
            };
        };

        const {forceRender, chart} = useChartCommon(chartRef, {
            getOptions,
        });

        const setName = name => {
            highlightXName.value = name;
        };

        watch(chart, () => {
            if (chart.value) {
                chart.value.on('highlight', ({batch: [{dataIndex}]}) => {
                    const {name} = props.data[dataIndex];
                    setName(name);
                });
                chart.value.on('downplay', () => setName(''));
            }
        });

        watch(() => [
            props.data,
            props.tooltipFormatter,
            highlightXName.value,
            ratio.value,
        ], () => {
            forceRender();
        });

        const legendList = computed(() => {
            return props.legendData.length ? props.legendData : props.data;
        });

        return {
            chartRef, singRef, yAxisRef, legendRef,
            yAxixStyle, legendList,
            transformStep,
        };
    },
};
</script>

<style lang="less" scoped>
.singBar {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 100%;

    .legend {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        margin-bottom: 12px;

        > div {
            display: flex;
            align-items: center;

            .legend-item {
                margin-right: 24px;

                &:last-child {
                    margin-right: 0;
                }

                span {
                    &.dib {
                        display: inline-block;
                        width: 10px;
                        height: 10px;
                        margin-right: 5px;
                        // background-color: red;
                    }
                }
            }
        }

        span {
            color: rgba(#fff, .8);
            font-family: 'PingFang';
            font-size: 14px;
        }
    }

    .echart {
        flex: 1;
        display: flex;
        align-items: center;
        position: relative;
        width: 100%;

        .yAxis {
            display: flex;
            flex-direction: column-reverse;
            justify-content: space-between;
            align-items: center;
            height: calc(100% - 25px);
            margin-bottom: 25px;

            li {
                font-family: 'RoboData';
                &:last-child {
                    margin-top: 0 !important;
                }
            }
        }

        div {
            position: absolute;
        }
    }
}
.c-50 {
    color: rgba(255, 255, 255, .7);
}

:deep(.ant-empty) {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}
</style>

<style lang="less">
@import '../util.less';
.statistics-chart-tooltip {
    #chart-tooltip();
    border-color: rgba(8, 74, 178, 0.35) !important;
    background-color: rgba(3, 17, 36, 0.79) !important;
    backdrop-filter: blur(10.87px);
    .label {
        font-family: 'PingFang';
    }

    .value {
        font-family: 'RoboData';
    }
}

.yAxis-item {
    width: 100%;
    background-color: rgba(255, 255, 255, .1);
    border-radius: 1px;
    text-align: center;
    padding: 2px 3px;
}
</style>