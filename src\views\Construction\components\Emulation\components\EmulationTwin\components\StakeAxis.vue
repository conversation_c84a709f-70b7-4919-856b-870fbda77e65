<template>
    <div class="stake-axis">
        <el-slider
            v-model="sliderState"
            :show-tooltip="true"
            :step="1"
            :max="getMax"
            :marks="getMarks"
            :format-tooltip="formatTooltip"
            @change="handleChange"
        />
    </div>
</template>

<script>
import {viewTo} from '@/utils';
import {Slider} from 'element-ui';
import {computed, ref, watch} from 'vue';
import {getStakenumberlistCache} from '../../EmulationCreate';

export default {
    components: {
        [Slider.name]: Slider,
    },
    props: {
        stake: String,
        highSpeedName: String,
    },
    setup(props, {emit}) {
        const sliderState = ref();
        const stakeList = ref([]);
        const max = 100;

        const getMax = computed(() => stakeList.value?.length);
        const getMarks = computed(() => {
            if (!stakeList.value?.length) return {};
            const first = stakeList.value[0];
            const len = stakeList.value?.length - 1;
            const last = stakeList.value[len];
            const marks = {
                0: first.stake,
                [len]: last.stake,
            };
            // const targetIndex = stakeList.value.findIndex(item => item.stake === props.stake);
            // if (targetIndex !== -1) {
            //     marks[targetIndex] = stakeList.value[targetIndex].stake;
            // };
            return marks;
        });

        function formatTooltip(value) {
            return stakeList.value[value]?.stake;
        }

        function handleChange(value) {
            const {position} = stakeList.value[value];
            viewTo({
                zoom: 15,
                center: position,
            }, 1000);
        }
        async function fetchStakeList() {
            const {highSpeedName, stake} = props;
            const stakeData = await getStakenumberlistCache(highSpeedName);
            stakeList.value = stakeData.map(item => ({
                stake: item.stakeNumber,
                position: [item.longitude, item.latitude],
            }));
            const index = stakeList.value.findIndex(item => item.stake === stake);
            sliderState.value = index;
        }

        watch(
            () => props.highSpeedName,
            () => {
                fetchStakeList();
            },
            {
                immediate: true,
            }
        );

        return {
            sliderState,
            max,
            handleChange,
            getMax,
            getMarks,
            formatTooltip,
        };
    },
};
</script>

<style lang="less" scoped>
.stake-axis {
    width: 100%;
    padding: 0 16px;

    /deep/ .el-slider__bar,
    /deep/ .el-slider__runway {
        height: 16px;
        background-color: rgba(40, 194, 130, .9);
        border-radius: 0;
    }

    /deep/ .el-slider__button-wrapper {
        top: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /deep/ .el-slider__button {
        width: 20px;
        height: 30px;
        border-radius: 1px;
        border: 1px	solid rgb(0, 255, 149);
        background-color: rgb(40, 194, 130);
        transform: scale(1) !important;

        &:active,
        &:hover {
            &::after,
            &::before {
                opacity: 1;
            }
        }

        &::after,
        &::before {
            position: absolute;
            content: '';
            width: 20px;
            height: 22px;
            background-image: url('@/assets/images/slider-arrow.svg');
            background-size: 100% 100%;
            top: 50%;
            margin-top: -10px;
            opacity: 0;
            transition: opacity .2s ease-in;
        }

        &::after {
            right: -24px;
        }

        &::before {
            transform: rotate(180deg);
            left: -24px;
        }
    }

    /deep/ .el-slider__stop {
        background-color: transparent;
    }

    /deep/ .el-slider__marks {
        position: relative;
        width: 100%;

        &-text {
            font-size: 14px;
            color: rgba(239, 255, 255, .9);
            margin-top: 1px;
            margin-left: 20px;
            z-index: 100;

            &:last-child {
                left: auto !important;
                right: -12px;
            }
        }
    }
}
</style>