<template>
    <div class="monitor-card">
        <div class="monitor-card__left">
            <div class="monitor-card__left__title">
                <div class="cnTitle">{{ info.title }}</div>
            </div>
            <div class="monitor-card__left__value">
                <span>{{ info.value }}</span>
            </div>
        </div>
        <div class="monitor-card__right">
            <div class="monitor-card__right__level">
                <div class="monitor-card__right__health">
                    <span>健康度等级</span>
                    <span class="health-level" :class="getLevel(info.health)">{{ getLevelName(info.health) }}级</span>
                </div>
                <div class="monitor-card__right__health__progress">
                    <div class="one" :class="{'active': info.health === 1}"></div>
                    <div class="two" :class="{'active': info.health === 2}"></div>
                    <div class="three" :class="{'active': info.health === 3}"></div>
                    <div class="four" :class="{'active': info.health === 4}"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
defineProps({
    info: {
        type: Object,
        default: () => ({}),
    },
});

const getLevel = health => {
    return {
        1: 'one',
        2: 'two',
        3: 'three',
        4: 'four',
    }[health];
};


const getLevelName = health => {
    return {
        1: '一',
        2: '二',
        3: '三',
        4: '四',
    }[health];
};
</script>

<style lang="less" scoped>
.monitor-card {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    width: 274px;
    height: 82px;
    background-color: rgba(18, 74, 166, 0.24);
    backdrop-filter: blur(10px);
    padding: 11px 16px 12px;
    border-top: 1px solid transparent;
    border-image: linear-gradient(
                to right,
                rgb(36, 104, 242),
                rgba(1, 255, 229, 0.5)
            ) 1 repeat;

    &__left {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-start;
        height: 100%;

        &__title {
            .cnTitle {
                font-size: 16px;
                font-family: 'PingFang';
                font-weight: 500;
                color: #fff;
            }
        }

        &__value span {
            font-size: 28px;
            font-family: 'RoboData';
            font-weight: 400;
            color: rgba(#fff, .9);
        }
    }

    &__right {
        width: 150px;
        margin-bottom: 6px;
        &__health {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            font-family: 'PingFang';
            font-weight: 400;
            color: rgba(#fff, .5);

            .health-level {
                &.one {
                    color: rgb(93, 228, 126);
                }

                &.two {
                    color: rgb(238, 168, 15);
                }

                &.three {
                    color: rgb(233, 115, 18);
                }

                &.four {
                    color: rgb(255, 85, 85);
                }
            }

            &__progress {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: space-between;
                width: 100%;
                height: 4px;
                margin-top: 12px;

                > div:not(.clip) {
                    width: 25%;
                    height: 100%;

                    &.active {
                        border: 0.5px solid rgba(255, 255, 255, 0.6);
                    }

                    &.one {
                        background-color: rgba(93, 228, 126, .3);
                        &.active {
                            background-color: rgb(93, 228, 126);
                        }
                    }

                    &.two {
                        background-color: rgba(238, 168, 15, .3);
                        &.active {
                            background-color: rgb(238, 168, 15);
                        }
                    }

                    &.three {
                        background-color: rgba(233, 115, 18, .3);
                        &.active {
                            background-color: rgb(233, 115, 18);
                        }
                    }

                    &.four {
                        background-color: rgba(255, 85, 85, .3);
                        &.active {
                            background-color: rgb(255, 85, 85);
                        }
                    }
                }
            }
        }
    }
}
</style>