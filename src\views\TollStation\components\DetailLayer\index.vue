<template>
    <div class="detail-layer">
        <toll-station-warn
            :station-id="stationId"
            :station-name="stationName"
            @confirm="handleWarnConfirm"
        />
        <vidicon-marker
            v-for="vidicon in vidiconList"
            :key="vidicon.key"
            :info="vidicon"
            :manager-instace="domManager"
        />
        <release-baidu-modal :visible.sync="baiduModal" @confirm="handleBaiduModal"/>
        <release-board-modal :visible.sync="boardModal" @confirm="handleBoardModal"/>
    </div>
</template>

<script>
import TollStationWarn from './components/TollStationWarn/index.vue';
import {VidiconMarker} from '@/components/Common';
import {
    domManager,
} from '@/views/TollStation/utils';
import ReleaseBaiduModal from './components/ReleaseBaiduModal/index.vue';
import ReleaseBoardModal from './components/ReleaseBoardModal/index.vue';
import {onMounted, ref} from 'vue';
import {getTollCameraList} from '@/api/tollStation';
import {Message} from 'element-ui';

export default {
    components: {
        TollStationWarn,
        VidiconMarker,
        ReleaseBaiduModal,
        ReleaseBoardModal,
    },
    props: {
        stationId: String,
        stationName: String,
    },
    setup(props) {
        const baiduModal = ref(false);
        const boardModal = ref(false);
        const vidiconList = ref([]);

        function handleWarnConfirm(type) {
            if (type === 1) {
                baiduModal.value = true;
            }
            else {
                boardModal.value = true;
            }
        }

        async function initVidiconMarker() {
            if (!props.stationId || !props.stationName) return;
            const {data} = await getTollCameraList(props);
            vidiconList.value = data.map(item => ({
                position: [item.longitude, item.latitude],
                sectionName: item.sectionName,
                deviceStake: item.stakeNumber,
                key: item.deviceCode,
            }));
        }

        function handleBaiduModal() {
            Message.success('发布成功');
            baiduModal.value = false;
        }

        function handleBoardModal() {
            Message.success('发布成功');
            boardModal.value = false;
        }

        onMounted(() => {
            initVidiconMarker();
        });

        return {
            domManager,
            handleWarnConfirm,
            baiduModal,
            boardModal,
            vidiconList,
            handleBaiduModal,
            handleBoardModal,
        };
    },
};
</script>

<style lang="less" scoped>
.detail-layer {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
}
</style>