<template>
    <div class="app-layout">
        <div class="app-layout__header">
            <layout-header/>
        </div>
        <div class="app-layout__body">
            <router-view/>
        </div>

        <systemEntry/>
    </div>
</template>

<script>
import Header from './Header/index.vue';
import systemEntry from './systemEntry/index.vue';

export default {
    components: {
        LayoutHeader: Header,
        systemEntry,
    },
};
</script>

<style lang="less" scoped>
.app-layout {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;

    :deep(.system-wrap) {
        position: fixed;
        bottom: -250px;
        left: 50%;
        transform: translate(-50%, 0);
        z-index: 9999;
    }

    &::before {
        content: '';
        position: absolute;
        width: 100%;
        z-index: 11;
        pointer-events: none;
        height: 300px;
        top: 0;
        background-image:
            linear-gradient(
                to bottom,
                #000 0,
                #000 50%,
                rgba(0, 0, 0, .4) 65%,
                rgba(0, 0, 0, .2) 75%,
                transparent
            );
    }

    &::after {
        content: '';
        position: absolute;
        width: 100%;
        z-index: 11;
        pointer-events: none;
        height: 200px;
        bottom: 0;
        background-image:
            linear-gradient(
                to top,
                #000 0,
                #000 2%,
                rgba(0, 0, 0, .4) 35%,
                rgba(0, 0, 0, .2) 55%,
                transparent
            );
    }

    &__header {
        position: absolute;
        width: 100%;
        z-index: 99;
        top: 0;
        pointer-events: none;
    }

    &__body {
        position: relative;
        color: #fff;
        width: 100%;
        height: 100%;

        &::before {
            content: '';
            position: absolute;
            width: 150px;
            z-index: 10;
            pointer-events: none;
            height: 100%;
            top: 0;
            left: 0;
            background-image:
                linear-gradient(
                    to right,
                    rgba(0, 0, 0, .5) 0%,
                    rgba(0, 0, 0, .4) 20%,
                    rgba(0, 0, 0, .2) 50%,
                    rgba(0, 0, 0, .1) 70%,
                    transparent 100%,
                );
        }

        &::after {
            content: '';
            position: absolute;
            width: 150px;
            z-index: 10;
            pointer-events: none;
            height: 100%;
            top: 0;
            right: 0;
            background-image:
                linear-gradient(
                    to left,
                    rgba(0, 0, 0, .5) 0%,
                    rgba(0, 0, 0, .4) 20%,
                    rgba(0, 0, 0, .2) 50%,
                    rgba(0, 0, 0, .1) 70%,
                    transparent 100%,
                );
        }
    }

    &__content {
        position: relative;
        flex: 1;
        overflow-y: auto;
        padding: 0 24px 24px 24px;
    }
}
</style>