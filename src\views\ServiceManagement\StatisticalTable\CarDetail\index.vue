<!-- 服务区车辆详情 -->
<template>
    <div class="carDetail">
        <div class="health-list-title">
            <icon name="shebei"/>
            <span class="roadname">服务区事件详情</span>
        </div>
        <div class="content">
            <div class="item card1">
                <div class="topTitle">
                    {{ '基本信息' }}
                </div>
                <div class="itemContent">
                    <div class="text">
                        <p> <span>车牌号：</span> {{ routeInfo.plate ?? '粤A9999' }}</p>
                        <p> <span>驶入时间：</span> {{ routeInfo.enterTime ?? '2024-01-09 23:09:09' }}</p>
                        <p> <span>驶出时间：</span> {{ routeInfo.exitTime ?? '2024-01-09 23:09:09' }}</p>
                        <p> <span>位置桩号：</span>{{ routeInfo.stake ?? 'K235+150' }} </p>
                        <p> <span>事件位置： </span>{{ routeInfo.placeName ?? '雅姚服务区' }} </p>
                        <p> <span>细分来源： </span>{{ routeInfo.ra ?? '边缘计算机设备' }} </p>
                        <p> <span>确认人：</span> {{ routeInfo.confirmRealNa ?? '百度 路方测试' }}</p>
                        <p> <span>确认时间：</span> {{ routeInfo.confirmTime ?? '2024-01-09 23:09:09' }}</p>
                    </div>
                </div>

            </div>
            <div class="item card2">
                <div class="topTitle">
                    {{ '视频信息' }}
                </div>
                <div class="itemContent">
                    <div class="video">
                        <div class="video-item">
                            <div class="box">
                                <flvPlayer :camera-info-id="routeInfo.deviceCode"/>
                            </div>
                            <div class="title">实时视频</div>

                        </div>
                        <div class="video-item">
                            <div class="box">
                                <back-video-player
                                    :id="1212"
                                    :url="'https://gjdt.private.gdcg.cn/airplatform-admin/2024-06/34e75613-f026-3337-8437-424f750836a2.mp4'"
                                    :autoplay="true"
                                />
                            </div>
                            <div class="title">视频回放</div>
                        </div>

                    </div>
                </div>

            </div>
            <div class="item card3">
                <div class="topTitle">
                    {{ '车辆图片' }}
                    <p class="total">{{ total || '12' }}张</p>

                </div>
                <div class="itemContent">
                    <swiper/>
                </div>
            </div>

        </div>
    </div>
</template>

<script>
import {useRoute, useRouter} from '@/utils';

import {Card, flvPlayer} from '@/components/Common/index';
import {Icon} from '@/components/Common/index.js';
import Swiper from './swiper.vue';
import VideoItem from './followVideo.vue';
import BackVideoPlayer from '@/components/Common/BackVideoPlayer/index.vue';
import {ref} from 'vue';
export default {
    name: 'carDetail',
    components: {
        Card,
        Icon,
        Swiper,
        VideoItem,
        flvPlayer,
        BackVideoPlayer,
    },
    props: {
        info: {
            type: Object,
            default: () => ({}),
        },
    },
    setup(props) {
        const route = useRoute();
        const id = route.params.id;
        const routeInfo = ref([]);
        routeInfo.value = route.params.info;
        console.log('output->info', routeInfo, props.info);
        return {
            routeInfo,
        };
    },
};
</script>

<style lang="less" scoped>
.carDetail {
    height: 100%;
    width: 100%;
    border: 1px solid rgba(255, 255, 255, .2);

    .health-list-title {
        width: 100%;
        height: 40px;
        line-height: 40px;
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 500;
        color: #fff;
        padding-left: 16px;
        font-family: 'OPlusSans';
        border-bottom: 1px solid rgba(255, 255, 255, .2);

        .roadname {
            margin-left: 10px;
        }
    }
}

.item {
    margin-bottom: 16px;

    .itemContent {
        width: 100%;
        margin-left: -1px;
        padding: 24px 16px;
    }

    .topTitle {
        background-color: rgba(18, 74, 166, .3);
        border-left: 2px solid rgba(1, 255, 229, .5);
        height: 40px;
        text-indent: 16px;
        font-size: 14px;
        display: flex;
        align-items: center;
    }

    &:last-child {
        margin-bottom: 0;
    }
}

.content {
    .total {
        margin-left: 8px;
        color: rgba(255, 255, 255, .5);
    }

    .card1 {
        height: 152px;

        .text {
            display: grid;
            grid-template-rows: 1fr 1fr;
            color: #fff;
            gap: 12px;

            span {
                color: rgba(255, 255, 255, .5);
            }

            gap: 18px 0;
            grid-template-columns: repeat(6, 1fr);
        }

        .text2 {
            justify-content: start;
        }
    }

    .card2 {
        height: 350px;
    }

    .card3 {
        height: 350px;
    }
}

.video {
    display: flex;

    .video-item {
        &:last-child {
            margin-left: 16px;
        }

        .box {
            height: 230px;
            width: 408px;
        }

        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 265px;
        color: rgba(255, 255, 255, .8);
    }
}
</style>
