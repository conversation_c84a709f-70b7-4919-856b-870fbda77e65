
import {PathTracker, GLTFLoader} from '@baidu/mapv-three';
import {shallowRef, ref, watch, inject} from 'vue';

import {engine} from '@/store/common.js';


const modes = ['follow', 'lock', 'unlock', 'activeFrame'];


const main = () => {
    const is3D = inject('is3D');
    const pathTracker = shallowRef();
    const dataSource = shallowRef(null);
    const trackState = ref('stop'); // 'ing, stop, pause';
    const mode = ref('activeFrame'); // follow lock unlock;
    let isRunning = false;


    const stopCallback = () => {
        if (!pathTracker.value?._isPause && !isRunning) {
            trackState.value = 'stop';
        }
    };

    const listener = () => {
        Object.defineProperties(pathTracker.value, {
            _isRunning: {
                get() {
                    return isRunning;
                },
                set(v) {
                    isRunning = v;
                    stopCallback();
                    return;
                },
            },
        });
    };

    let model;

    const initPathTracker = () => {
        if (!is3D.value) {
            if (!engine.value) {
                return;
            }
            pathTracker.value = engine.value?.add(new PathTracker());
            if (!pathTracker.value) {
                return;
            }
            pathTracker.value.interpolateDirectThreshold = 50;
            pathTracker.value.viewMode = mode.value;
            listener();
        }
    };

    const setPath = path => {
        dataSource.value = path;
    };

    const start = (options = {
        duration: 6000,
        distance: 100,
        time: 100,
        id: 464,
    }) => {
        if (!dataSource.value) {
            return console.warn('请先设置数据源');
        }
        if (!is3D.value) {
            if (!pathTracker.value) {
                return console.warn('请先初始化PathTracker');
            }

            pathTracker.value.track = dataSource.value;

            pathTracker.value.start(options);
            pathTracker.value.pointHandle = 'curve';
        }
        else {
            engine.value?.navigateByKeypoints(dataSource.value, {
                speed: options.speed ?? 200,
                time: options.time,
                lockAll: options.lockAll ?? true,
                // callback: () => {
                //     trackState.value = 'stop';
                //     console.log('callback');
                // },
                finishCallback: () => {
                    trackState.value = 'stop';
                    console.log('finishCallback');
                },
            });
        }
        trackState.value = 'ing';
    };

    const startParade = id => {
        if (id) {
            // eslint-disable-next-line no-undef
            assets.startParade(id, pathTracker.value);
            trackState.value = 'ing';
        }
    };

    const pause = () => {
        if (!is3D.value) {
            pathTracker.value?.pause();
        }
        else {
            engine.value.pauseNavigation({
                patrolType: 'default',
            });
        }
        trackState.value = 'pause';
    };

    const replay = () => {
        if (!is3D.value) {
            pathTracker.value?.replay();
        }
        else {
            engine.value.resumeNavigation({
                patrolType: 'default',
            });
        }
        trackState.value = 'ing';
    };

    const stop = () => {
        if (!is3D.value) {
            pathTracker.value?.stop();
        }
        else {
            engine.value.stopNavigation(100);
        }

        trackState.value = 'stop';
    };

    const setMode = (type = 'follow') => {
        mode.value = type;
    };

    watch(engine, v => {
        if (v) {
            initPathTracker();
        }
    }, {
        immediate: true,
    });

    watch(mode, (v, o) => {
        if (modes.includes(v) && pathTracker.value) {
            pathTracker.value.viewMode = mode.value;
        }
        else {
            mode.value = o;
        }
    });

    return {
        setPath,
        start,
        stop,
        pause,
        replay,
        setMode,
        trackState,
        mode,
        startParade,
    };
};


export default main;