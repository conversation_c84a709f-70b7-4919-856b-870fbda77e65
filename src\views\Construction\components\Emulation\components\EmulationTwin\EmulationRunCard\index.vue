<template>
    <Card title="仿真方案运行" card-type="card-long-2">
        <template #content>
            <div class="card-content">
                <ButtonGroup
                    @play="$emit('play')"
                    @pause="$emit('pause')"
                    @stop="$emit('stop')"
                />
                <StakeAxis class="mt-10"/>
                <TimeAxis
                    class="mt-20"
                    :start-time="emulationInfo.emulationStartTime"
                    :end-time="emulationInfo.emulationEndTime"
                    @change="$emit('changeTime', $event)"
                />
                <EmulationChart
                    class="mt-30"
                />
            </div>
        </template>
    </Card>
</template>

<script setup>
import {Card} from '@/components/Common';
import ButtonGroup from './ButtonGroup.vue';
import StakeAxis from './StakeAxis.vue';
import TimeAxis from './TimeAxis.vue';
import EmulationChart from './EmulationChart/index.vue';
import {emulationInfo} from '../index';

</script>

<style lang="less" scoped>
.card-content {
    height: 465px;
}

.emulation-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background-color: rgba(36, 104, 242, .3);
    clip-path:
        polygon(
            0 0,
            100% 0,
            100% calc(100% - 5px),
            calc(100% - 5px) 100%,
            0 100%
        );

    &-group {
        display: flex;
        justify-content: end;
    }
}
</style>