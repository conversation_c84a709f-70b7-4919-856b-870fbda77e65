<template>
    <div class="custom-area-wrapper">
        <div class="btn-edit-list">
            <div
                v-for="item in editorList"
                :key="item.type"
                :class="[
                    'btn-edit',
                    {
                        'btn-edit__active': item.type === currentEditType,
                    },
                ]"
                :title="item.title"
                @click="handleSwtich(item)"
            >
                <icon :name="item.type"/>
            </div>
        </div>
        <div
            class="btn-edit"
            title="完成编辑"
            @click="handleComplete"
        >
            <icon name="wancheng"/>
        </div>
        <div
            class="btn-edit"
            title="退出编辑"
            @click="handleBack"
        >
            <icon name="dianyuan"/>
        </div>
    </div>
</template>

<script>
import {ref, getCurrentInstance, onMounted, watch, computed, onBeforeUnmount} from 'vue';
import {Icon} from '@/components/Common';
import {engine} from '@/store/engine';
import {CircleEditor, PolygonEditor, RectEditor} from '@baidu/mapv-three';
import {messageTip} from '@/utils';
import {getCustomHighInfo} from '@/api/emulation';

export default {
    components: {
        Icon,
    },
    props: {
        engine: {
            type: Object,
            default: () => Object.freeze(engine.value),
        },
    },
    setup(props, {emit}) {
        const vm = getCurrentInstance();
        const currentEditType = ref('polygon');

        const editorList = ref([]);

        const getCurrentEditor = computed(() => {
            const {editor} = editorList.value.find(({type}) => type === currentEditType.value);
            return editor;
        });

        function handleSwtich({type}) {
            if (type === 'circle') {
                getCurrentEditor.value.enabled = false;
                messageTip('正在开发中，请等待！', 'info');
                getCurrentEditor.value.enabled = true;
                return;
            }
            currentEditType.value = type;
        }

        function initEditor() {
            const engine = props.engine;
            const editorOptions  = {
                polygonColor: 'rgba(73,195,255,0.08)',
                borderColor: '#49C3FF',
                borderWidth: 6,
            };
            editorList.value = [
                {
                    type: 'circle',
                    title: '圆形',
                    editor: engine.add(new CircleEditor(engine, editorOptions)),
                },
                {
                    type: 'react',
                    title: '矩形',
                    editor: engine.add(new RectEditor(engine, editorOptions)),
                },
                {
                    type: 'polygon',
                    title: '多边形',
                    editor: engine.add(new PolygonEditor(engine, editorOptions)),
                },
            ];
        }

        // 初始化编辑器
        watch(
            () => props.engine,
            val => {
                if (!val) return;
                initEditor();
            },
            {
                immediate: true,
            }
        );

        // 切换编辑器并清空数据
        watch(
            () => currentEditType.value,
            val => {
                editorList.value.forEach(({type, editor}) => {
                    editor.clearAll();
                    editor.enabled = type === val;
                });
            },
            {
                immediate: true,
            }
        );

        function handleBack() {
            getCurrentEditor.value?.clearAll();
            getCurrentEditor.value.enabled = false;
            emit('back');
        }

        async function handleComplete() {
            getCurrentEditor.value?.complete();
            const editData = getCurrentEditor.value?.drawedGraph[0];
            if (!editData) {
                messageTip('请先进行区域框选');
                return;
            }
            const pointList = editData.dataItem.attributes.map(item => {
                const point = item.point;
                return [point[0], point[1]];
            });
            const areaLocation = pointList.join(';');
            const {data: customData} = await getCustomHighInfo({
                areaLocation,
            });
            if (!customData?.length) {
                messageTip('该框选区域暂无信息，请重新选择');
                return;
            }
            getCurrentEditor.value.enabled = false;
            getCurrentEditor.value?.clearAll();
            emit('complete', customData);
        }

        onMounted(() => {
            // 挂载到地图容器上
            // document.querySelector('.mapv-container')?.appendChild(vm.proxy.$el);
        });

        onBeforeUnmount(() => {
            editorList.value.forEach(({editor}) => editor.dispose());
        });

        return {
            editorList,
            currentEditType,
            handleSwtich,
            handleBack,
            handleComplete,
        };
    },
};
</script>

<!-- 挂载到地图容器上进行定位布局 -->
<style lang="less" scoped>
.custom-area-wrapper {
    position: absolute;
    top: 2px;
    right: 60px;
    z-index: 20;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    pointer-events: none;

    & > *:not(:first-child) {
        margin-top: 6px;
    }
}

.btn-edit {
    width: 48px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(3, 31, 68, .8);
    backdrop-filter: blur(4px);
    color: rgba(#fff, .5);
    cursor: pointer;
    pointer-events: auto;

    &-list {
        display: flex;
        border: 1px solid rgba(#fff, .6);
    }

    &__active {
        border: 1px solid #fff;
        color: #fff;
        font-weight: bold;
    }

    &:hover {
        border: 1px solid #fff;
        color: #fff;
    }
}
</style>