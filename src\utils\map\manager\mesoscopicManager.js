import {
    addIcon,
    removeIcon,
    addDOMOverlay,
    removeDOMOverlay,
    addText,
    removeText,
} from '../index';
import EventCenter from './event.js';

const removeMap = {
    Icon: removeIcon,
    DomOverlay: removeDOMOverlay,
    Text: removeText,
};

// 支持绑定的事件
const eventNameEnum = {
    click: 'clickCallback',
    mouseenter: 'onMouseenter',
    mouseleave: 'onMouseleave',
};

// 中观图层管理器
class MesoscopicManager extends EventCenter {
    constructor(engine) {
        super(eventNameEnum);
        this.mesoscopicMap = new Map();
        this.engine = engine;
    }
    addMesoscopic(name, point, options = {}) {
        this.options = options;
        if (this.mesoscopicMap.has(name)) {
            this.removeMesoscopicByName(name);
        }
        const {
            text,
            iconUrl,
            labelDom,
            customData = {},
            renderOrder = Infinity,
        } = options;

        const {
            width = 48,
            height = 55,
            iconOffset = [0, 0],
            textOffset = [0, 0],
        } = customData;

        const map = {};

        const {icon, _engine} = addIcon(point, iconUrl, {
            width,
            height,
            offset: iconOffset,
            customData,
            _engine: this.engine,
            renderOrder,
        });

        if (text) {
            const {_text} = addText(point, text, {
                _engine: this.engine,
                offset: textOffset,
                renderOrder,
            });
            map.Text = _text;
        }

        if (labelDom) {
            const {domOverlay} = addDOMOverlay(point, labelDom, {
                _engine: this.engine,
                offset: [0, -54],
            });
            map.DomOverlay = domOverlay;
        }

        map.Icon = icon;

        this.bind(icon);
        this.mesoscopicMap.set(name, map);
    }

    removeMesoscopicByName(name) {
        const mesoscopicMap = this.mesoscopicMap.get(name);
        if (!mesoscopicMap) return;
        this.unbind(mesoscopicMap.Icon);
        Object.keys(mesoscopicMap).forEach(key => {
            const remove = removeMap[key];
            remove(mesoscopicMap[key], this.engine);
        });
        this.mesoscopicMap.delete(name);
    }

    clear() {
        [...this.mesoscopicMap.keys()].forEach(macro => {
            this.removeMesoscopicByName(macro);
        });
        this.mesoscopicMap.clear();
    }
}

export {
    MesoscopicManager,
};