

import {trafficAddon} from '@baidu/mapv-three/addons';
import {onBeforeUnmount, ref, shallowRef, watch} from 'vue';
import {getAllInfoboard, getInfoboardContent} from '@/api/tollStation';
import {useIntervalFn} from '@vueuse/core';

const main = (map, props) => {
    const assetsScene = shallowRef(null);

    // 轮询间隔
    const POLL_GAP_TIME = 1000 * 60 * 2;

    const boardList = ref([]);

    const boardContentMap = {};

    const {resume} = useIntervalFn(
        () => {
            const models = assetsScene.value.models;
            // 轮询不使用缓存
            setBoardContent(models, false);
        },
        POLL_GAP_TIME,
        {
            immediate: false,
        }
    );

    async function fetchData() {
        const {data} = await getAllInfoboard();
        boardList.value = data.map(board => {
            const position = [board.longitudeRectify, board.latitudeRectify, board.alt];
            return {
                position,
                assetsCode: board.assetsCode,
                name: board.infoBoardName,
                infoBoardWidth: board.infoBoardWidth,
                infoBoardHeight: board.infoBoardHeight,
                fontSize: Math.max(board.infoBoardHeight / 3, 12),
                uuid: board.uuid,
            };
        });
    }

    function getBoardObj3D(models, uuid) {
        for (const model of models) {
            const obj3d = model.children.find(m => m.uuid === uuid);
            if (obj3d) {
                return obj3d;
            }
        }
    }

    async function getContent(assetsCode, useCache) {
        const content = boardContentMap[assetsCode];
        if (content && useCache) return content;
        const {data} = await getInfoboardContent(assetsCode);
        boardContentMap[assetsCode] = data;
        return data;
    }

    function setContent(obj3d, info, content) {
        let repeat = 20;
        let timer = setInterval(() => {
            if (!repeat--) {
                clearInterval(timer);
                timer = null;
            }
            // 情报板模型已渲染
            if (obj3d.loaded) {
                clearInterval(timer);
                timer = null;
                assetsScene.value.handleActiveDevice(
                    info.uuid,
                    'guidanceScreen',
                    {
                        text: content,
                        fontSize: info.fontSize,
                        fontColor: 'red',
                        layout: 'center',
                        screenLength: info.infoBoardWidth,
                        screenHeight: info.infoBoardHeight,
                    }
                );
            }
        }, 100);
    }

    function setBoardContent(models, useCache) {
        if (!models.length) return;
        for (const board of boardList.value) {
            const obj3d = getBoardObj3D(models, board.uuid);
            if (obj3d) {
                getContent(board.assetsCode, useCache).then(content => {
                    setContent(obj3d, board, content);
                });
            }
        }
    }

    function sceneViewAddCallback({models}) {
        // sceneViewAdd回调优先使用缓存
        setBoardContent(models, true);
    }

    function addSceneViewAddListener() {
        assetsScene.value.addEventListener('sceneViewAdd', sceneViewAddCallback);
    }

    function removeSceneViewAddListener() {
        assetsScene.value
        && assetsScene.value.removeEventListener('sceneViewAdd', sceneViewAddCallback);
    }

    // 添加设备资产
    const addAssetsScene = () => {
        const sceneId = 152;
        const host = import.meta.env.MODE !== 'release' ? 'https://dtmap.baidu.com' : 'https://gjdt.private.gdcg.cn';
        assetsScene.value = map.value.add(
            new trafficAddon.AssetsScene(// 厂内环境
                {
                    sceneId,
                    host,
                    renderOption: {
                        radius: 1000, // 开启区域加载
                        anchor: 'cameraPosition',
                        useDefault3DTileLayer: false,
                        useDefaultLodTileLayer: false,
                    },
                    // auth: {
                    //     ak: 'WWCJBGIPQP',
                    //     appid: 345721,
                    //     sk: 'd2pv8y8FSju3ITgBoaIu',
                    //     url: 'https://ace.apollo.auto',
                    //     path: '/bff',
                    // },
                })
        );
        fetchData();
        addSceneViewAddListener();
        resume();
    };

    const removeAssetsScene = () => {
        if (assetsScene.value) {
            map.value?.remove(assetsScene.value);
            assetsScene.value = null;
        }
    };

    watch(map, v => {
        if (v && props.options?.showAssetsScene) {
            addAssetsScene(assetsScene.value);
        }
    }, {
        immediate: true,
    });

    onBeforeUnmount(() => {
        removeAssetsScene();
        removeSceneViewAddListener();
    });

    return {
        assetsScene,
        addAssetsScene,
    };
};

export default main;
