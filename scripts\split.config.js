import path from 'path';
import theme from '../theme';
import {inputConfig, splitChunkNames, externalPackages} from './common';

export const splitConfig = () => {
    return {
        build: {
            lib: {
                entry: path.resolve('./', 'src/index.js'),
                name: 'apaas-maplayer',
                formats: ['es'],
                fileName: format => 'index.js',
            },
            css: {
                preprocessorOptions: {
                    less: {
                        modifyVars: Object.assign({}, theme),
                        javascriptEnabled: true,
                    },
                },
            },
            rollupOptions: {
                external: externalPackages,
                experimentalImportMetaResolve: true,
                input: {
                    // 主包
                    index: path.resolve('./', 'src/index.js'),
                    // 分包
                    ...inputConfig,
                },
                output: {
                    entryFileNames: ({name}) => {
                        if (splitChunkNames.includes(name)) {
                            return `lib/${name}/index.js`;
                        }
                        if (name === 'index') {
                            return `${name}.js`;
                        }
                    },
                    chunkFileNames: '[name]', // 动态引入的文件名
                    manualChunks: () => 'common.js', // 将所有模块打包到一个 chunk 文件中
                },
            },
        },
    };
};