<template>
    <div class="app-navbar">
        <app-menu :list="getMenuList" @select="selectMenu"/>
    </div>
</template>

<script>
import {computed} from 'vue';
import Menu from './Menu.vue';
import {constantRouterMap, EXTERNAL_PATH_PREFIX} from '@/router/router.config';
import {useRouter} from '@/utils';

export default {
    components: {
        AppMenu: Menu,
    },
    setup() {
        const router = useRouter();

        const routeList = constantRouterMap
            .find(route => route.name === 'DEFAULT_LAYOUT')?.children || [];

        function formatMenuList(list) {
            return list.filter(item => {
                if (Reflect.has(item.meta, 'hiddenMenu')
                    && item.meta.hiddenMenu) {
                    return false;
                }

                if (item.children?.length) {
                    item.children = formatMenuList(item.children);
                }

                return true;
            });
        }

        const getMenuList = computed(() => {
            return formatMenuList(routeList);
        });

        function selectMenu(path) {
            // 外部链接
            if (path.startsWith(EXTERNAL_PATH_PREFIX)) {
                path = path.replace(EXTERNAL_PATH_PREFIX, '');
                path && window.open(path, '_self');
                return;
            }
            router.push({
                path,
            });
        }

        return {
            getMenuList,
            selectMenu,
        };
    },
};
</script>