<template>
    <div class="api-table-wrapper">
        <el-table
            v-bind="$attrs"
            :data="tableData"
            :height="height * ratio"
            :class="{'show-scroll': showScroll}"
            v-on="$listeners"
        >
            <el-table-column
                v-for="column in columns"
                :key="column.prop"
                v-bind="column"
            >
                <template slot-scope="scope">
                    <!-- 示例：columns对应props额外枚举字段  enumData: { 0: '未完成', 1: '已完成' }  -->
                    <template v-if="!column.slotName">
                        <span v-if="column.enumData && column.enumData[scope.row[column.prop]]">
                            {{ column.enumData[scope.row[column.prop]] }}
                        </span>
                        <span v-else>
                            {{ column.format ? column.format(scope) : scope.row[column.prop] }}
                        </span>
                    </template>
                    <slot
                        v-else
                        :name="column.slotName"
                        v-bind="{
                            ...scope,
                            prop: column.prop,
                        }"
                    ></slot>
                </template>
            </el-table-column>
        </el-table>
        <div v-if="pagination" class="api-pagination">
            <div class="api-pagination__total">{{ getTotalText }}</div>
            <el-pagination
                background
                layout="prev, pager, next, sizes"
                :current-page.sync="pageInfo.current"
                :page-size.sync="pageInfo.size"
                :total="pageInfo.total"
                @size-change="fetchData"
                @current-change="fetchData"
            />
        </div>
    </div>
</template>

<script setup>
import {useUnit} from '@/utils';
import {Table as ElTable, TableColumn as ElTableColumn, Pagination as ElPagination} from 'element-ui';
import {isFunction} from 'lodash-es';
import {computed, ref, watch} from 'vue';
import {get} from 'lodash';

const {ratio} = useUnit();

const props = defineProps({
    api: {
        type: Function,
        required: true,
    },
    columns: {
        type: Array,
        required: true,
    },
    showScroll: {
        type: Boolean,
        default: false,
    },
    testData: {
        type: Object,
        default: null,

    },
    height: {
        type: Number,
        default: 250,
    },
    requestParams: {
        type: Object,
        default: () => ({}),
    },
    requestOptions: {
        type: Object,
        default: () => ({
            pageField: 'current',
            sizeField: 'size',
            listField: 'records',
            totalField: 'total',
        }),
    },
    immediate: {
        type: Boolean,
        default: true,
    },
    pagination: {
        type: Boolean,
        default: true,
    },
});

const pageInfo = ref({
    current: 1,
    size: 10,
    total: 0,
});

const tableData = ref([]);

const getTotalText = computed(() => {
    const {total, current, size} = pageInfo.value;
    const textList = [
        `总共 ${total}条`,
    ];
    if (total > 0) {
        const start = (current - 1) * size + 1;
        const end = start + size - 1 > total ? total : start + size - 1;
        textList.unshift(`第 ${start}-${end} 条`);
    }
    return textList.join('/');
});

async function fetchData() {
    const {
        api,
        requestParams,
        requestOptions,
        pagination,
    } = props;
    if (!isFunction(api)) return;
    let params = {
        ...requestParams,
    };
    if (pagination) {
        params = {
            ...requestParams,
            [requestOptions.pageField]: pageInfo.value.current,
            [requestOptions.sizeField]: pageInfo.value.size,
        };
    }
    const {data} = props.testData ? props.testData : await api(params);
    pageInfo.value.total = get(data, requestOptions.totalField);
    tableData.value = requestOptions.listField ? get(data, requestOptions.listField) : data;
}

watch(
    () => props.requestParams,
    () => {
        fetchData();
    },
    {
        deep: true,
        immediate: props.immediate,
    }
);

</script>

<style lang="less" scoped>
.api-table-wrapper {
    /deep/ .el-table {
        color: #fff;
        background-color: transparent;

        &::before {
            display: none;
        }

        &__header {
            width: 100% !important;
            background-color: rgba(0, 98, 167, .3);

            tr {
                background-color: rgba(0, 98, 167, .3);
                color: rgba(231, 239, 255, .9);
                font-size: 18px;
            }

            th {
                padding: 6px 0;
            }
        }

        &__body {
            border-spacing: 0 8px;
            font-size: 20px;
        }

        &__row {
            background-color: rgba(0, 98, 167, .3);

            &:hover {
                background-image:
                    linear-gradient(
                        25deg,
                        rgba(18, 74, 166, .8),
                        rgba(0, 255, 229, .3),
                    );
            }

            td {
                border: none;
                padding: 10px 0;
                // text-align: center;
            }
        }

        .is-right {
            text-align: right;
        }
    }
}

.api-pagination {
    display: flex;
    align-items: center;
    justify-content: end;
    margin-top: 8px;

    &__total {
        font-size: 18px;
        color: #fff;
        margin-right: 8px;
    }

    /deep/ .el-pagination.is-background {
        .btn-next,
        .btn-prev {
            margin: 0;
            border-radius: 0;
            background-color: rgba(22, 48, 86, .3);

            &:hover {
                color: #fff;
            }
        }

        .el-pager {
            li {
                margin: 0;
                border-radius: 0;
                background-color: rgba(22, 48, 86, .3);

                &:hover {
                    color: #fff;
                }

                &.active {
                    color: #fff;
                    background-color: rgb(36, 104, 242);
                }
            }
        }

        &__sizes {
            .el-input__inner {
                border: none;
            }
        }
    }
}
</style>