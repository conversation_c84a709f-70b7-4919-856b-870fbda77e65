<template>
    <div class="time-axis">
        <!-- 实时进度 -->
        <el-slider
            v-model="sliderTime"
            class="slider-time mt-6"
            show-stops
            :max="stepMax"
            :step="1"
            :disabled="wsDisabale"
            :marks="marks"
            :format-tooltip="timeFormatTooltip"
            @mousedown.native="sliderMouseDown"
            @change="changeSliderTime"
        />
        <!-- 缓冲进度 -->
        <el-slider
            v-model="bufferTime"
            class="buffer-slider"
            show-stops
            :max="stepMax"
            :step="1"
        />
    </div>
</template>

<script>
import {ref, computed, watch, onMounted} from 'vue';
import {Slider} from 'element-ui';
import dayjs from 'dayjs';
import {
    emulationInfo,
    simulationTime,
    stakenumberlist,
    simulationBufferTime,
    init_number_list,
} from '@EmulationLayer/store/index';
export default {
    props: {
        emulationnowtime: {
            type: Number,
            default: () => 0,
        },
        wsDisabale: {
            type: Boolean,
        },
    },
    components: {
        [Slider.name]: Slider,
    },
    setup(props, {emit}) {
        const sliderTime = ref(1); // 当前选中的桩号位置
        const isNowTimeAllowChange = ref(true);

        const emulationStartTime = computed(() => emulationInfo.value.emulationStartTime);
        const emulationEndTime = computed(() => emulationInfo.value.emulationEndTime);

        // 毫秒时间戳结束时间
        const endTimeUnix = computed(() => dayjs(emulationEndTime.value).valueOf());
        const startTimeUnix = computed(() => dayjs(emulationStartTime.value).valueOf());

        // 1h: 1000 * 20 * 3 2h: 1000 * 20 * 2 3h: 1000 * 20 * 1
        const stepUnix = computed(() => {
            const unixH = dayjs(emulationEndTime.value).startOf('hour').hour()
                - dayjs(emulationStartTime.value).startOf('hour').hour() || 1;
            return 1000 * 20 * unixH;
        });

        // 钟时间戳差值用于显示进度条总步数 1h === 80刻度 / 45
        const stepMax = computed(() => (endTimeUnix.value - startTimeUnix.value) / stepUnix.value);

        // 进度条的标注
        const marks = computed(() => {
            let marker = {};
            marker[0] = dayjs(emulationStartTime.value).format('HH:mm:ss');
            marker[stepMax.value] = dayjs(emulationEndTime.value).format('HH:mm:ss');
            return marker;
        });

        // 缓冲时间
        const bufferTime = computed(() => {
            const time = (simulationBufferTime.value - startTimeUnix.value) / stepUnix.value;
            return Math.floor(time) || 1;
        });

        const timeFormatTooltip = value => {
            let valueType = startTimeUnix.value + value * stepUnix.value;
            return dayjs(valueType).format('HH:mm:ss');
        };

        const formatTooltip = val => {
            return stakenumberlist.value[val] ? stakenumberlist.value[val].stakeNumber : '';
        };

        // 修改进度数据
        const changeSliderTime = value => {
            const unixTime = value * stepUnix.value + startTimeUnix.value;
            const drag = dayjs(unixTime).format('YYYY-MM-DD HH:mm:ss');
            const params = {
                drag,
            };
            sliderTime.value = value;

            simulationTime.value = drag;

            emit('changeSliderTime', JSON.stringify(params));

            setTimeout(() => {
                isNowTimeAllowChange.value = true;
            }, 1000);
        };

        const sliderChange = val => {
            if (!sliderTime.value) {
                return;
            }
            changeSliderTime(sliderTime.value);
        };

        // 点击后停止渲染进度条
        const sliderMouseDown = () => {
            isNowTimeAllowChange.value = false;
        };

        // watch(() => sliderTime.value, () => {
        //     if (!sliderTime.value) {
        //         return;
        //     }
        //     changeSliderTime(sliderTime.value);
        // });

        watch(() => simulationTime.value, () => {
            if (!simulationTime.value) {
                return;
            }
            const time = dayjs(simulationTime.value).valueOf();

            const value = (time - startTimeUnix.value) / stepUnix.value;
            sliderTime.value = Math.floor(value) || 1;

            // const params = {
            //     drag: dayjs(simulationTime.value).format('YYYY-MM-DD HH:mm:ss'),
            // };
            // emit('changeSliderTime', JSON.stringify(params));
        });

        watch(() => props.emulationnowtime, () => {
            if (!isNowTimeAllowChange.value) {
                return;
            }
            const time = (props.emulationnowtime - startTimeUnix.value) / stepUnix.value;
            sliderTime.value = Math.floor(time) || 1;
        });

        onMounted(() => {
            init_number_list();
        });

        return {
            sliderTime,
            bufferTime,
            stepMax,
            marks,
            stakenumberlist,
            emulationStartTime,
            formatTooltip,
            timeFormatTooltip,
            changeSliderTime,
            sliderMouseDown,
            sliderChange,
        };

    },
};
</script>

<style lang="less" scoped>
.time-axis {
    width: calc(100% - 2px);
    height: 50px;
    position: absolute;
    bottom: 1px;
    left: 1px;
    border: 1px solid rgba(8, 214, 165, .46);
    z-index: 2;
    padding: 0 30px;
    box-sizing: border-box;

    .slider-time {
        position: absolute;
        top: 0;
        left: 31px;
        width: calc(100% - 62px);
        z-index: 2;
    }

    .buffer-slider {
        position: absolute;
        top: 6px;
        left: 31px;
        width: calc(100% - 62px);
        z-index: 0;

        /deep/ .el-slider__stop {
            background-color: transparent;
        }

        /deep/ .el-slider__bar {
            background-color: rgba(255, 255, 255, .2);
        }
    }

    /deep/ .el-slider__runway {
        background-color: transparent;
    }

    /deep/ .el-slider__bar {
        background-color: rgba(8, 214, 165, .7);
        border-radius: 0;
    }

    /deep/ .el-slider__stop {
        width: 2px;
        background-color: rgba(8, 214, 165, .3);
    }

    /deep/ .el-slider__button-wrapper {
        display: none;
    }

    /deep/ .el-slider__marks-text {
        font-size: 12px;
        color: #93b4b7;
        margin-top: 12px;

        &:first-child::before,
        &:last-child::before {
            position: absolute;
            top: -28px;
            left: 0;
            width: max-content;
            color: #e4f1ff;
            font-size: 12px;
            transform: scale(.9);
        }

        &:first-child {
            &::before {
                content: '仿真开始';
            }
        }

        &:last-child {
            &::before {
                content: '仿真结束';
            }
        }
    }
}
</style>