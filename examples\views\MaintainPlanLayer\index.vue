<template>
    <map-component
        ref="mapRef"
        :options="{
            center: mapCenter,
            showSatelliteMap: true,
        }"
        class="map-box"
        :style="{
            height: height + 'px',
        }"
        @mapLoaded="mapLoaded"
        @clickMap="clickMap"
    >
        <maintain-plan-layer
            v-if="mapInitStatus"
        />
    </map-component>
</template>

<script>
import {Map, MaintainPlanLayer, initMaintainPlanLayerConfig} from '@/index';
import {ref, onMounted} from 'vue';
import {mapCenter} from '@/store/engine.js';

const apiHost = import.meta.env.MODE === 'test' ? 'http://************:8751' : '';

export default {
    components: {
        MapComponent: Map,
        MaintainPlanLayer,
    },
    setup() {
        const mapRef = ref(null);
        const engine = ref(null);
        const height = ref(1080);
        const mapInitStatus = ref(false);
        // 地图加载完成后执行的回调函数
        const mapLoaded = () => {
            initMaintainPlanLayerConfig({
                engine: mapRef.value.map,
                apiHost,
            });
            mapInitStatus.value = true;
        };

        // 地图点击后执行的回调函数
        const clickMap = e => {
            console.log('clickMap===点击地图，参数：', e);
        };

        onMounted(() => {
            height.value = window.innerHeight;
            document.title = '养护计划管理图层';
        });

        return {
            mapCenter,
            mapRef,
            engine,
            height,
            mapInitStatus,
            mapLoaded,
            apiHost,
            clickMap,
        };
    },
};
</script>

<style scoped>
.map-box {
    position: relative;
    width: 100vw !important;
}
</style>
