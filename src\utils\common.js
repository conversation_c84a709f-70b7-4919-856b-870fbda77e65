import {Message} from 'element-ui';
import {BaseApiHost, BaseWsHost} from '@/store/common';
import {center, featureCollection, point} from '@turf/turf';
import {getCoord} from '@turf/invariant';
import {ref} from 'vue';

// 是否是线上环境
// export const isOnline = process.env.NODE_ENV === 'production';

// 公共配置
export const baseConfig = {
    basePrefix: '/ihs/api',
    baseLayerPrefix: '/ihs/maplayer',
    baseService: process.env.NODE_ENV === 'development' ? origin : BaseApiHost.value,
    baseWsUrl: process.env.NODE_ENV === 'development' ? `ws://${location.host}` : BaseWsHost.value,
};

// 仿真配置
export const emulationConfig = {
    basePrefix: '/ihs/maplayer',
    baseApiHost: import.meta.env.VITE_EMULATION_API_URL,
    baseWsHost: import.meta.env.VITE_EMULATION_WS_URL,
};

export const messageTip = (msg, type = 'warning') => {
    // eslint-disable-next-line @babel/new-cap
    return Message({
        type,
        message: msg,
        duration: 3000,
        onClose() {},
    });
};

// 获取文字宽度
export const getTextWidth = (text, fontSize = 18, fontFamily = 'Arial') => {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    context.font = `${fontSize}px ${fontFamily}`;
    const metrics = context.measureText(text);
    return metrics.width;
};
// 地图绑定鼠标事件
export const bindPointer = mapEl => {
    mapEl.senter = e => {
        document.querySelector('.mapv-canvas').classList.add('addPointer');
    };

    mapEl.sleave = e => {
        document.querySelector('.mapv-canvas').classList.remove('addPointer');
    };
    mapEl.engine.event.bind(mapEl, 'mouseenter', mapEl.senter);
    mapEl.engine.event.bind(mapEl, 'mouseleave', mapEl.sleave);
};

export const unBindPointer = mapEl => {
    mapEl.engine.event.unbind(mapEl, 'mouseenter', mapEl.senter);
    mapEl.engine.event.unbind(mapEl, 'mouseleave', mapEl.sleave);
    mapEl.senter = null;
    mapEl.sleave = null;
};

export const bindPointerAnimate = mapEl => {
    const video = document.querySelector('#streamingVideo');
    mapEl.addEventListener('mousedown', e => {
        console.log(1233122123);
        // video.classList.add('addPointer');
    });
    mapEl.addEventListener('mouseenter', e => {
        console.log(1233122123);
        video.classList.add('addPointer');
    });
    mapEl.addEventListener('mouseleave', e => {
        video.classList.remove('addPointer');

    });
};

/**
 * @abstract 计算中心点
 * @param {Number[]} coordinates 经纬度集合
 * @return {Number[]} 中心点
 */
export const calcCenterByCoordinates = (coordinates = []) => {
    if (!coordinates?.length) return [];
    const points = coordinates.map(e => point(e));
    const features = featureCollection(points);
    const featurePointCenter = center(features);
    return getCoord(featurePointCenter);
};


//
let EARTH_RADIUS = 6378137.0; // 单位M
let PI = Math.PI;

function getRad(d) {
    return d * PI / 180.0;
}
// 获取两个经纬度点位之间的距离
export function getGreatCircleDistance([lng, lat], [lng1, lat1]) {
    let radLat = getRad(lat);
    let radLat1 = getRad(lat1);

    let a = radLat - radLat1;
    let b = getRad(lng) - getRad(lng1);

    let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2)
    + Math.cos(radLat) * Math.cos(radLat1) * Math.pow(Math.sin(b / 2), 2)));
    s = s * EARTH_RADIUS;
    s = Math.round(s * 10000) / 10000.0;
    return s;
}
const env = import.meta.env.MODE;
const onlineModes = ['release', 'production', 'live'];
// 判断是否是线上环境
export const isOnline = () => onlineModes.includes(env);


// 获取地址参数
export const getUrlParams = (url = location.href) => {
    const locationSearch = url.replace(/^[^\?]*\?([^\/]*)\/?.*$/ig, '$1');
    const urlParams = new URLSearchParams(locationSearch);
    return [...urlParams.keys()].reduce((pre, key) => Object.assign(pre, {
        [key]: urlParams.get(key),
    }), {});
};


function baseHexToRgba1(hex, opacity = 1) {
    return hex && hex.replace(/\s+/g, '').length === 7
        ? 'rgba(' + parseInt('0x' + hex.slice(1, 3), 16) + ','
    + parseInt('0x' + hex.slice(3, 5), 16) + ','
    + parseInt('0x' + hex.slice(5, 7), 16) + ',' + opacity + ')' : '';
}

export function hexToRgba(hex, opacity = 1) {
    if (hex.startsWith('#')) return baseHexToRgba1(hex, opacity);
    let rgb = hex.split('(')[1].split(')')[0].split(',');
    return 'rgba(' + rgb[0].trim() + ',' + rgb[1].trim() + ',' + rgb[2].trim() + ',' + opacity + ')';
};

/**
 * 数字类型转换为金钱格式字符串 223456789.22 => 22,345,678.22
 */
export function formatCurrency(number) {
    if (number === 0) return '0';
    // 将数字分割为整数部分和小数部分
    const [integerPart, decimalPart] = number.toString().split('.');
    // 将整数部分转换为每三位添加逗号的格式
    const formattedInteger = integerPart
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    // 如果小数部分存在，则保留两位小数，否则返回空字符串
    const formattedDecimal = decimalPart ? `.${decimalPart}` : '';
    // 返回格式化后的金钱字符串
    return `${formattedInteger}${formattedDecimal}`;
}

export const screenWidth = ref(document.documentElement.clientWidth || document.body.clientWidth);
export const screenHeight = ref(document.documentElement.clientHeight || document.body.clientHeight);