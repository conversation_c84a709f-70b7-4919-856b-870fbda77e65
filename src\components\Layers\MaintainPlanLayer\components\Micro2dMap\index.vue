<template>
    <div class="micro-2d-map">
        <map-line
            v-for="item in getLineList"
            :key="item.key"
            :dynamic-width="true"
            :line-instace="lineManager"
            :list="item.pointList"
            :info="{
                lineWidth: 8,
                name: item.key,
                onClick: () => handleClickLine(item),
            }"
        />
        <!-- 扎点 -->
        <device-maker
            v-for="marker in getMarkerList"
            :key="marker.planId"
            :info="{
                position: marker.position,
                labelName: planNameDictMap[marker.type],
                pointName: marker.planId,
                type: planTypeDictMap[marker.type],
                size: 'small',
                status: marker.warning ? 'warning' : 'blue',
                bubbleColor: marker.warning ? 'rgb(245, 92, 99)' : 'rgb(67, 148, 247)',
                clickCallback: () => handleClickMarker(marker),
            }"
            :manager-instace="warningNextManager"
        />

        <!-- 扎点过滤bar -->
        <legend-list
            v-model="filterKeys"
        />
    </div>
</template>
<script>
import {DeviceMaker, MapLine} from '@/components/Common';
import {lineManager, warningNextManager} from '@MaintainPlanLayer/utils/Map';
import {computed, ref, unref, watch} from 'vue';
import {isEqual, isEmpty, isArray} from 'lodash';
import {viewToMicroTwo} from '@/utils';
import LegendList from '@MaintainPlanLayer/components/LegendList/index.vue';
import {legendList, planTypeDictMap, planTypeEnum, planNameDictMap} from '@MaintainPlanLayer/config';

const defaultSelectedKeys = legendList.map(i => i.key);

export default {
    components: {
        DeviceMaker,
        LegendList,
        MapLine,
    },
    props: {
        list: {
            type: Array,
            default: () => ([]),
        },
    },
    setup(props, {emit}) {
        const activeSection = ref();
        const filterKeys = ref(defaultSelectedKeys);
        const getLineList = computed(() => (
            props.list?.reduce((acc, cur) => {
                const {
                    pointList = [],
                    sectionId,
                } = cur;
                return [
                    ...acc,
                    ...pointList?.map((points, index) => ({
                        pointList: points,
                        sectionId,
                        key: `${sectionId}__${index}`,
                    })),
                ];
            }, [])
        ));

        // 监听选中高速变化，将视角切换至改高速下
        watch(
            () => activeSection.value,
            (newVal, oldVal) => {
                if (isEqual(newVal, oldVal)) return;
                const pointList = newVal?.pointList || [];
                if (isEmpty(pointList)) return;
                // 切换至第一个扎点上，不存在则切换至路段中心
                const [firstPoint] = newVal?.planList;
                if (firstPoint && firstPoint?.position) {
                    viewToMicroTwo(firstPoint?.position);
                    return;
                }
                const [firstLine] = pointList;
                if (!firstLine) return;
                const middleIndex = parseInt(firstLine.length / 2, 10) || 0;
                const sectionCenter = firstLine[middleIndex];
                viewToMicroTwo(sectionCenter);
            },
            {
                immediate: true,
                deep: true,
            }
        ),

        // 监听微观二维数据变化
        watch(
            () => props.list,
            (newVal, oldVal) => {
                if (isEqual(newVal, oldVal)) return;
                if (isArray(newVal) || !isEmpty(newVal)) {
                    activeSection.value = newVal[0];
                }
            },
            {
                immediate: true,
                deep: true,
            }
        );


        const getMarkerList = computed(() => {
            // 多条高速的扎点合并在一个数组
            const mergeMarkerList = props.list.reduce((acc, cur) => {
                return [
                    ...acc,
                    ...cur.planList,
                ];
            }, []);
            // bar过滤显示
            return mergeMarkerList?.filter(i => {
                // 警告过滤
                if (i.warning) {
                    return unref(filterKeys).includes(planTypeEnum.WARNING);
                }
                return unref(filterKeys).includes(i.type);
            });
        });

        // 点击渲染路段
        function handleClickLine({sectionId}) {
            const findData = props.list.find(i => i.sectionId === sectionId);
            if (!findData) return;
            activeSection.value = findData;
        }

        // 点击扎点
        function handleClickMarker(marker) {
            // 进入微观三维视角
            emit('viewMicro', marker);
        }

        return {
            lineManager,
            getMarkerList,
            warningNextManager,
            handleClickMarker,
            filterKeys,
            planTypeDictMap,
            getLineList,
            handleClickLine,
            planNameDictMap,
        };
    },
};
</script>
