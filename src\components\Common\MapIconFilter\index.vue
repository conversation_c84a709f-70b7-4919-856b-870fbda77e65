<template>
    <div class="map-icon-filter-wrapper">
        <!-- 扎点过滤 -->
        <el-tooltip
            v-for="item in list"
            :key="item"
            :content="item.label"
            placement="left"
        >
            <div
                :class="[
                    'map-icon-filter',
                    {
                        'map-icon-filter__active': isActive(item.value),
                    },
                ]"
                @click="handleClick(item)"
            >
                <icon
                    :name="item.icon"
                    :size="16"
                    color="inherit"
                />
            </div>
        </el-tooltip>
        <!-- 地图控件 -->
        <el-tooltip
            v-for="item in defaultList"
            :key="item"
            :content="item.label"
            placement="left"
        >
            <div
                :class="[
                    'map-icon-filter',
                ]"
                @click="item.click"
            >
                <icon
                    :name="item.icon"
                    :size="16"
                    color="inherit"
                />
            </div>
        </el-tooltip>
    </div>
</template>

<script>
import {Icon} from '@/components/Common';
import {engine} from '@/store/engine';
import {viewToFk} from '@/utils';
import {Tooltip} from 'element-ui';
import {isEqual} from 'lodash';
import {ref, watch} from 'vue';

export default {
    components: {
        Icon,
        [Tooltip.name]: Tooltip,
    },
    props: {
        value: [String, Number, Array],
        multiple: Boolean,
        list: {
            type: Array,
            default: () => ([
                {
                    icon: 'qiaoliang',
                    value: 1,
                    label: '桥梁',
                },
                {
                    icon: 'lumian',
                    value: 2,
                    label: '路面',
                },
                {
                    icon: 'lijiao',
                    value: 3,
                    label: '互通立交',
                },
                {
                    icon: 'shebei',
                    value: 4,
                    label: '设备设施',
                },
            ]),
        },
        engine: {
            type: Object,
            default: () => Object.freeze(engine.value),
        },
    },
    setup(props, {emit}) {
        const selectState = ref(props.multiple && []);

        // 地图控件定位、放大、缩小
        const defaultList = [
            {
                icon: 'dingwei1',
                label: '恢复视角',
                click() {
                    viewToFk(props.engine);
                    emit('recoverView');
                },
            },
            {
                icon: 'jia',
                label: '放大地图',
                click() {
                    props.engine?.map?.zoomIn();
                },
            },
            {
                icon: 'jian',
                label: '缩小地图',
                click() {
                    props.engine?.map?.zoomOut();
                },
            },
        ];

        watch(
            () => props.value,
            (newVal, oldVal) => {
                if (isEqual(newVal, oldVal)) return;
                selectState.value = newVal;
            },
            {
                immediate: true,
                deep: true,
            }
        );

        function handleClick(e) {
            // 值相等
            if (isEqual(e.value, selectState.value)) return;
            // 单选
            if (!props.multiple) {
                selectState.value = e.value;
                emit('input', e.value);
                emit('change', e.value);
                return;
            }
            // 多选
            // 选中
            const index = selectState.value.indexOf(e.value);
            if (index === -1) {
                selectState.value.push(e.value);
            }
            // 取消选中
            else {
                selectState.value.splice(index, 1);
            }
            emit('input', selectState.value);
            emit('change', selectState.value);
        }

        function isActive(key) {
            if (!props.multiple) {
                return key === selectState.value;
            }

            return selectState.value.indexOf(key) !== -1;
        }

        return {
            handleClick,
            isActive,
            defaultList,
        };
    },
};
</script>

<style lang="less" scoped>
.map-icon-filter {
    position: relative;
    width: 42px;
    height: 42px;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    background-color: rgba(3, 31, 68, .5);
    color: rgba(#fff, .5);
    cursor: pointer;
    user-select: none;
    backdrop-filter: blur(12px);
    border: 1px	solid rgba(72, 100, 146, .5);

    &:not(:last-of-type) {
        margin-bottom: 16px;
    }

    &__active,
    &:hover {
        color: #fff;
        border-color: rgba(#fff, .3);
    }

    &__active {
        background:
            linear-gradient(
                to right,
                rgb(36, 104, 242),
                rgba(1, 255, 229, .7)
            );
    }
}
</style>