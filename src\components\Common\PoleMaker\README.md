# 设备设施扎点

### 参数说明  props
|Prop name|Type|Description|
|---|---|---|---|
|`managerInstace`|`object`|自定义Domlabel管理器(PoleManager)实例 必填|
|`position`|`Arrray`|经纬度坐标 必填|
|`pointName`|`string`|扎点名称 必填 唯一值不可重复|
|`labelName`|`string`|label文字|
|`active`|`boolean`|是否激活|
|`size`|`string`|尺寸 正常：normal 小型：small 默认：small|
|`status`|`string`|label背景图片状态，以颜色命名 默认：green|
|`bubbleColor`|`string`|气泡点颜色 默认绿色，传16进制字符，如 #fff 或颜色英文单词，如 red|
|`customData`|`object`|自定义数据|
|`clickCallback`|`function`|点击回调函数|

### 补充说明
为了规范化模块化，建议标杆型扎点图片统一放到 `maplayer/assets/image/pole_facilitie` 下，杆子命名为 `pole_颜色状态` 如 `pole_green` ，label背景图命名为 `label_颜色状态` 如 `label_red` 。
- 若想修改杆子图片，则通过传 `status` 对应颜色修改，需要先在图片存放文件夹中保存对应名称的图片
- 若想修改label背景图片，则通过传 `status` 对应颜色修改，需要先在图片存放文件夹中保存对应名称的图片，并在 `index.vue` 中设置对应的类名样式
- 若想要label背景图片有激活效果，则通过传 `active` 对应颜色修改，需要先在图片存放文件夹中保存对应名称的图片，命名为 `label_颜色_active` ，并在 `index.vue` 中设置对应的类名样式