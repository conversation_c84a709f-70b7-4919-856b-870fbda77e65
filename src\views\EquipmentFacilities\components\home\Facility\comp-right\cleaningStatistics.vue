<template>
    <div class="cleaning-statistics">
        <api-table
            :api="roadCleanList"
            :columns="columns"
            :height="320"
            :pagination="false"
            :request-options="{
                listField: '',
            }"
            :request-params="{
                pageAble: true,
            }"
        >
            <template #index="{$index}">
                <div>
                    {{ $index + 1 }}
                </div>
            </template>
            <template #opt="{row}">
                <div class="detail" @click="() => handle(row)">查看详情</div>
            </template>
        </api-table>
    </div>
</template>

<script>
import {ApiTable} from '@/components/Common';
import {useUnit} from '@/utils';
import {roadCleanList, roadCleanInfo} from '@/api/equipment/facilitydisplay';
import {detail, showDetail, width, right, left, top} from '@/views/EquipmentFacilities/utils/index';

export default {
    name: 'cleaningStatistics',
    components: {
        ApiTable,
    },
    setup() {
        const {ratio} = useUnit();

        const cleanMap = new Map([
            ['1', '一级'],
            ['2', '二级'],
            ['3', '三级'],
            ['4', '四级'],
            ['5', '五级'],
        ]);

        const columns = [
            {
                label: '序号',
                prop: 'index',
                width: `${60 * ratio.value}px`,
                slotName: 'index',
                align: 'center',
            },
            {
                label: '路段名称',
                prop: 'roadName',
                width: `${200 * ratio.value}px`,
            },
            {
                label: '技术得分',
                prop: 'cleanGrade',
                width: `${85 * ratio.value}px`,
                format: e => {
                    return cleanMap.get(e.row.cleanGrade + '');
                },
            },
            {
                label: '操作',
                prop: 'opt',
                slotName: 'opt',
                width: `${100 * ratio.value}px`,
            },
        ];

        const detailList = [
            {name: '路段位置', prop: 'roadName'},
            {name: '路面抛洒物数量', prop: 'scatteredTotal'},
            {name: '道路清洁评分等级', prop: 'cleanGrade'},
            {name: '统计时间', prop: 'dateTime'},
        ];

        const handle = e => {
            width.value = 420;
            top.value = 680 * ratio.value + 'px';
            left.value = 'inherit';
            right.value = 1260 * ratio.value + 'px';
            roadCleanInfo(e.id).then(res => {
                detail.value = {
                    name: '路段清洁度详情',
                    data: detailList.map(item => ({
                        label: item.name,
                        value: res.data[item.prop],
                    })),
                };
                showDetail.value = true;
            });
        };

        return {
            columns,
            roadCleanList,
            handle,
        };
    },
};
</script>

<style lang="less" scoped>
.cleaning-statistics {
    height: 320px;
    :deep(.cell) {
        font-size: 20px;
    }

    :deep(thead) {
        .cell {
            font-size: 18px;
        }
    }

    .detail {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 88px;
        height: 32px;
        border: 1px solid rgb(0, 255, 229);
        color: rgb(0, 255, 229);
        font-family: 'PingFang';
        font-size: 18px;
        font-weight: 400;
        cursor: pointer;
    }
}
</style>