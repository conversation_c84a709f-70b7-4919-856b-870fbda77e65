export const padStart = num => num.toString().padStart(2, '0');

export const createTimeList = length => Array.from(Array(length), (_, i) => padStart(i));

export const hourList = createTimeList(24);

export const minuteList = createTimeList(60);

export const dateFormat = 'YYYYMMDD';

export const getFormatPrefix = format => {
    const formatAll = 'YYYYMMDDHHmmss';
    const index = formatAll.indexOf(format[0]);
    return formatAll.substring(0, index);
};
