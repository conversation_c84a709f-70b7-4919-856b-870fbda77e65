<template>
    <div class="maintain-plan-layer" @click.prevent.stop>
        <!-- 顶部标题 -->
        <layer-top class="structure-top" title="养护计划管理图层">
            <template #select>
                <relation-select-card
                    micro-check
                    :meso-key="orgId"
                    :micro-key="sectionId"
                    @handleRefresh="handleRefresh"
                    @mesoChoseFn="mesoChoseFn"
                    @microChoseFn="microChoseFn"
                    @choseSearchBackFn="choseSearchBackFn"
                />
                <div class="period-type-select">
                    <el-select
                        v-model="periodType"
                        size="mini"
                        @change="selectPeriodType"
                    >
                        <el-option
                            v-for="item in periodList"
                            :key="item.value"
                            :label="item.name"
                            :value="item.value"
                        />
                    </el-select>
                </div>
            </template>
        </layer-top>
        <!-- 宏观图层 -->
        <macro-map
            v-if="isMacroMap"
            :list="macroData"
            :callback="macroCallback"
        />
        <!-- 中观图层 -->
        <meso-map
            v-else-if="isMesoMap"
            :list="mesoData"
            :callback="mesoCallback"
        />
        <!-- 微观二维图层 -->
        <micro2d-map
            v-else-if="isMicro2dMap"
            :list="micro2dData"
            @viewMicro="viewMicro"
        />
        <!-- 微观三维图层 -->
        <micro3d-map
            v-else-if="isMicro3dMap"
            :type.sync="micro3dMapType"
            :info="micro3dData"
            :collapse="collapse"
        />
        <transition
            leave-active-class="animate__animated animate__fadeOutLeft collapse-animate-duration"
            enter-active-class="animate__animated animate__fadeInLeft collapse-animate-duration"
        >
            <maintain-statistics-group v-if="!isMicro3dMap && !collapse"/>
        </transition>
        <tool-button-bar
            v-if="showToolBtn"
            :class="['tool-button', {collapse}]"
            :collapse.sync="collapse"
            :show-collapse="!isEmulation"
            :show-back="!isMacroMap"
            @back="handleBack"
        />
    </div>
</template>

<script>
import {
    RelationSelectCard,
    ToolButtonBar,
} from '@/components/Common';
import LayerTop from '@/components/Common/LayerTop/index.vue';
import {onMounted, computed, watch} from 'vue';
import {periodList} from '@MaintainPlanLayer/config/index.js';
import {Select, Option} from 'element-ui';
import MesoMap from '@MaintainPlanLayer/components/MesoMap/index.vue';
import {
    mapType,
    periodType,
    orgId,
    sectionId,
    macroData,
    initMacroMap,
    afterChangePeriodType,
    mesoData,
    initMesoMap,
    handleBack,
    micro3dMapType,
    initMicro2dMap,
    micro2dData,
    initMicro3dMap,
    micro3dData,
    collapse,
    showToolBtn,
} from '@MaintainPlanLayer/store/index.js';
import MacroMap from '@MaintainPlanLayer/components/MacroMap/index.vue';
import MaintainStatisticsGroup from '@MaintainPlanLayer/components/MaintainStatisticsGroup/index.vue';
import Micro2dMap from '@MaintainPlanLayer/components/Micro2dMap/index.vue';
import Micro3dMap from '@MaintainPlanLayer/components/Micro3dMap/index.vue';
import {mapTypeDictMap, micro3dMapTypeDictMap} from '@MaintainPlanLayer/config';
import {isEmpty} from 'lodash';
import {engine} from '@/store/engine';

export default {
    name: 'MaintainPlanLayer',
    components: {
        LayerTop,
        RelationSelectCard,
        MacroMap,
        MesoMap,
        [Select.name]: Select,
        [Option.name]: Option,
        Micro2dMap,
        Micro3dMap,
        MaintainStatisticsGroup,
        ToolButtonBar,
    },
    setup() {
        const isMacroMap = computed(() => mapType.value === mapTypeDictMap.MACRO);
        const isMesoMap = computed(() => mapType.value === mapTypeDictMap.MESO);
        const isMicro2dMap = computed(() => mapType.value === mapTypeDictMap.MICRO_2D);
        const isMicro3dMap = computed(() => mapType.value === mapTypeDictMap.MICRO_3D);
        const isEmulation = computed(() => micro3dMapType.value === micro3dMapTypeDictMap.EMULATION);

        // 地图实例
        watch(
            () => engine.value,
            val => {
                if (val) {
                    engine.value.map.setPitch(20);
                    engine.value.map.setZoom(4.3);
                    engine.value.map.setHeading(10);
                }
            },
            {
                immediate: true,
            }
        );


        const selectPeriodType = value => {
            periodType.value = value;
            afterChangePeriodType();
        };

        // 机构筛选框-刷新回调
        function handleRefresh() {
            orgId.value = '';
            sectionId.value = '';
            initMacroMap();
        }

        function macroCallback({pointId}) {
            orgId.value = pointId;
            initMesoMap();
        }

        function mesoCallback({pointId}) {
            sectionId.value = [pointId];
            initMicro2dMap();
        }

        const mesoChoseFn = item => {
            orgId.value = item.orgId;
            initMesoMap();
        };

        function microChoseFn(_e, _, selectList) {
            const [org, section = []] = selectList;
            orgId.value = org.orgId;
            const secIds = section.map(i => i.sectionId);
            sectionId.value = secIds;
            // 取消了全部高速路选项返回中观
            if (isEmpty(secIds)) {
                initMesoMap();
                return;
            }
            // 进入微观二维
            initMicro2dMap();
        }

        // 选择模糊查询结果回调
        function choseSearchBackFn(e) {
            orgId.value = e.orgId;
            sectionId.value = [e.sectionId];
            initMicro2dMap();
        }


        function viewMicro({planId}) {
            initMicro3dMap(planId);
        }

        onMounted(() => {
            initMacroMap();
        });

        return {
            orgId,
            sectionId,
            handleRefresh,
            macroCallback,
            mesoChoseFn,
            mesoCallback,
            microChoseFn,
            choseSearchBackFn,
            periodList,
            periodType,
            selectPeriodType,
            Select,
            Option,
            macroData,
            mesoData,
            mapType,
            handleBack,
            isMacroMap,
            isMesoMap,
            isMicro2dMap,
            isMicro3dMap,
            micro2dData,
            micro3dMapType,
            viewMicro,
            micro3dData,
            collapse,
            showToolBtn,
            isEmulation,
        };
    },
};
</script>

<style lang="less" scoped>
.maintain-plan-layer {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 2;
    width: 100%;
    height: 100%;
    pointer-events: none;

    & > * {
        pointer-events: auto;
    }

    .period-type-select {
        width: 178px;
        height: 30px;
        margin-left: 16px;

        /deep/.el-select {
            width: 100%;
            height: 30px;

            .el-input,
            .el-input__inner {
                height: 30px;
            }
        }
    }
}

.btn-back {
    margin-left: 16px;
}

.tool-button {
    position: fixed;
    left: 400px;
    top: 130px;
    transition: left .25s ease-in-out;

    &.collapse {
        left: 28px;
    }
}
</style>
