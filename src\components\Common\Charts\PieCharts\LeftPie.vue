<template>
    <div class="container">
        <div ref="chartRef" class="EventRatioChart">
        </div>
        <div class="big-radius"></div>
        <div class="radius2" :class="[pieCenter ? 'radius2' : 'radius']">
            <div class="s-radius"></div>
        </div>
    </div>
</template>
<script>
import {ref, watch} from 'vue';
import usePieShadow from './usePieShadow.js';
import usePieHighlight from './usePieHighlight.js';
import useChartCommon from '../useChartCommon.js';

export default {
    name: 'LeftPie',
    props: {
        data: {
            type: Array,
            default: () => [],
        },
        activeIndex: {
            type: Number,
            default: 0,
        },
        active: {
            type: Boolean,
            default: true,
        },
        colors: {
            type: Array,
            default: () => [],
        },
        pieCenter: {
            type: Object,
            default: () => {},
        },
    },
    components: {},
    setup(props, {emit}) {
        const chartRef = ref(null);
        const getOptions = ratio => {
            const options = {
                title: {
                    show: !!props.pieCenter,
                    text: props.pieCenter?.firstLine,
                    subtext: props.pieCenter?.lastLine,
                    top: '35%',
                    left: 'center',
                    textStyle: {
                        color: '#fff',
                        fontSize: 20 * ratio,
                        fontFamily: 'RoboData',
                    },
                    subtextStyle: {
                        color: 'rgba(255, 255, 255, 0.6)',
                        fontWeight: 500,
                        fontSize: 16 * ratio,
                    },
                },
                tooltip: {
                    trigger: 'axis',
                    show: true,
                    z: 90,
                },
                series: [
                    {
                        name: '',
                        type: 'pie',
                        radius: ['52%', '65%'],
                        center: ['50%', '50%'],
                        clockWise: false,
                        avoidLabelOverlap: true,
                        selectedOffset: 10 * ratio,
                        emphasis: {
                            scaleSize: 3 * ratio,
                        },
                        label: {
                            textStyle: {
                                color: '#fff',
                            },
                            normal: {
                                show: false,
                                position: 'center',
                                textStyle: {
                                    color: '#fff',
                                },
                            },
                        },
                        data: props.data.map((e, i) => {
                            return {
                                ...e,
                                itemStyle: {
                                    normal: {
                                        color: props.colors[i % props.colors.length],
                                    },
                                },
                            };
                        }),
                    },
                ],
            };
            return options;
        };

        const mouseover = e => emit('mouseover', e.dataIndex);
        const mouseout = e => emit('mouseout', e.dataIndex);

        const {chart} = useChartCommon(chartRef, {
            getOptions,
            // eslint-disable-next-line no-use-before-define
            setOptionCb: () => resetHighlight(),
        });

        const {activeIndex, resetHighlight} = usePieHighlight(chart, {
            mouseover,
            mouseout,
            use: props.active,
        });

        if (props.active) {
            watch(() => props.activeIndex, n => activeIndex.value = n);
        }

        // 饼状图周围的阴影
        const {shadowStartDeg, polygonPath} = usePieShadow();
        return {
            chartRef,
            shadowStartDeg, polygonPath,
            chart,
        };
    },
};
</script>

<style lang="less" scoped>
.container {
    position: relative;
}
.EventRatioChart {
    z-index: 99;
    width: 180px;
    height: 180px;
}
.big-radius {
    width: 160px;
    height: 160px;
    border-radius: 50%;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    border: 1px solid rgba(255, 255, 255, .3);

    &::before {
        content: '';
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        border-radius: 50%;
        border: 1px solid #9097a3;
        z-index: 1;
        box-shadow: inset 0 0 0 30px rgba(255, 255, 255, .2);
        clip-path: polygon(v-bind(polygonPath));
        transform: rotate(v-bind(shadowStartDeg));
    }
}

.radius {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 1px solid rgba(255, 255, 255, .3);
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;

    .s-radius {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 1px solid rgba(255, 255, 255, .5);
        position: relative;
        z-index: 2;
    }

    &::after {
        content: '';
        width: 100%;
        height: 1px;
        background: rgba(255, 255, 255, .14);
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
    }

    &::before {
        content: '';
        width: 1px;
        height: 100%;
        background: rgba(255, 255, 255, .14);
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
    }
}
.radius2 {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 1px solid rgba(255, 255, 255, .3);
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
