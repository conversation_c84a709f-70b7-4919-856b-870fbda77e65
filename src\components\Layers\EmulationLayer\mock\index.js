export const twinData = {
    sumoCity: '广东省中山市珠海市',
    id: 5945,
    eventId: 0,
    name: '有策略',
    areaChoose: 1,
    highSpeedName: 'G0425',
    tollName: null,
    emulationStartStake: 'K75+900',
    emulationEndStake: 'K77+600',
    emulationStartTime: '2023-10-18 18:22:26',
    emulationEndTime: '2023-10-18 19:22:26',
    weatherScene: 1,
    emulationType: 1,
    flowInputType: 1,
    flowDistance: 1000,
    customFlowStartTime: '2023-10-18 18:22:26',
    customFlowEndTime: '2023-10-18 19:22:26',
    strategyInputType: 2,
    tollSentryEdgeJson: null,
    status: 1,
    eventList: [
        {
            closeLane: '1',
            closeLanePosition: '',
            constructionCornerGeoList: null,
            constructionGeoList: null,
            constructionLength: 0,
            direction: 1,
            downstreamTransitionLength: 0,
            duration: 180,
            eventLocationType: 3,
            eventPosition: '113.467606,22.517138',
            eventStartStake: 'K79+200',
            eventStartTime: '2023-10-03 09:45:00',
            eventType: 0,
            limitSpeed: 0,
            upstreamTransitionLength: 0,
            highSpeedName: 'G0425',
            eventEndTime: '2023-10-03 12:45:00',
            eventHectometreStake: 'K79+200',
            eventEndStake: 'K79+200',
            eventAltitude: 0,
            visibility: 0,
            influencesLength: 0,
            addLaneNum: 1,
        },
    ],
    flowList: [
        {
            sumoCity: '广东省中山市珠海市',
            highSpeedName: null,
            seq: 1,
            flowInputType: 1,
            rampName: 'G0425(广东城区站)(K79+200)',
            startStakeNumber: 'K79+200',
            startEdgeId: '17483952558701451758',
            direction: 1,
            startTime: '2023-10-03 09:45:00',
            endTime: '2023-10-03 12:45:00',
            endStakeNumber: '',
            endEdgeId: '',
            carInfoList: [
                {
                    seq: 1,
                    carFlow: 1000,
                    etcRatio: 80,
                    smallProbability: null,
                    startTime: '2023-10-03 09:45:00',
                    endTime: '2023-10-03 12:45:00',
                },
            ],
        },
        {
            sumoCity: '广东省中山市珠海市',
            highSpeedName: null,
            seq: 2,
            flowInputType: 1,
            rampName: 'G0425(K78+200)',
            startStakeNumber: 'K78+200',
            startEdgeId: '16303773624814045718',
            direction: 1,
            startTime: '2023-10-03 09:45:00',
            endTime: '2023-10-03 12:45:00',
            endStakeNumber: '',
            endEdgeId: '',
            carInfoList: [
                {
                    seq: 1,
                    carFlow: 1000,
                    etcRatio: 80,
                    smallProbability: null,
                    startTime: '2023-10-03 09:45:00',
                    endTime: '2023-10-03 12:45:00',
                },
            ],
        },
        {
            sumoCity: '广东省中山市珠海市',
            highSpeedName: null,
            seq: 3,
            flowInputType: 1,
            rampName: 'G0425(K80+300)',
            startStakeNumber: 'K80+300',
            startEdgeId: 'sub1-12758151699608676919',
            direction: 2,
            startTime: '2023-10-03 09:45:00',
            endTime: '2023-10-03 12:45:00',
            endStakeNumber: '',
            endEdgeId: '',
            carInfoList: [
                {
                    seq: 1,
                    carFlow: 1000,
                    etcRatio: 80,
                    smallProbability: null,
                    startTime: '2023-10-03 09:45:00',
                    endTime: '2023-10-03 12:45:00',
                },
            ],
        },
        {
            sumoCity: '广东省中山市珠海市',
            highSpeedName: null,
            seq: 4,
            flowInputType: 1,
            rampName: 'G0425(广东城区站)(K79+300)',
            startStakeNumber: 'K79+300',
            startEdgeId: '13466802830337821670',
            direction: 1,
            startTime: '2023-10-03 09:45:00',
            endTime: '2023-10-03 12:45:00',
            endStakeNumber: '',
            endEdgeId: '',
            carInfoList: [
                {
                    seq: 1,
                    carFlow: 1000,
                    etcRatio: 80,
                    smallProbability: null,
                    startTime: '2023-10-03 09:45:00',
                    endTime: '2023-10-03 12:45:00',
                },
            ],
        },
    ],
    strategyList: [
        [
            {
                controlDuration: 222,
                controlLength: 10,
                controlStake: 'K6+0',
                lane: null,
                laneControlType: null,
                limitSpeed: '122',
                position: '112.979754,25.291191',
                type: 1,
                entranceExitType: null,
                rampName: null,
                flowLimit: null,
                vehicleType: null,
                bypassRate: null,
                groupFlow: null,
                letGoRate: null,
            },
            {
                controlDuration: null,
                controlLength: 111,
                controlStake: 'K7+100',
                lane: '1,2',
                laneControlType: '1,2',
                limitSpeed: '111,233',
                position: ';',
                type: 2,
                entranceExitType: null,
                rampName: null,
                flowLimit: null,
                vehicleType: null,
                bypassRate: null,
                groupFlow: null,
                letGoRate: null,
            },
            {
                controlDuration: '11',
                controlLength: null,
                controlStake: null,
                lane: null,
                laneControlType: null,
                limitSpeed: null,
                position: null,
                type: 3,
                entranceExitType: '1,2,3',
                rampName: null,
                flowLimit: null,
                vehicleType: null,
                bypassRate: '50',
                groupFlow: null,
                letGoRate: null,
                entryConfig: {
                    controlDuration: '11',
                    flowLimit: 11,
                    vehicleType: 1,
                },
                exportConfig: {
                    controlDuration: '22',
                    bypassRate: '22',
                },
            },
        ],
    ],
    laneConfig: {
        laneTotal: 0,
        defaultConfig: {
            entranceLaneList: [],
            existLaneList: [],
        },
        contrastConfig: {
            entranceLaneList: [],
            existLaneList: [],
        },
    },
    model: {
        normalAcceleration: null,
        normalDeceleration: null,
        maxDeceleration: null,
        smallCarHighestSpeed: null,
        bigCarHighestSpeed: null,
        bigCarRate: 10,
        singleLanePassLimit: null,
        smallVehicleAccel: 2.6,
        smallVehicleDecel: 4.5,
        smallVehicleEmergencyDecel: 9,
        smallVehicleMaxSpeed: 120,
        bigVehicleAccel: 1.3,
        bigVehicleDecel: 4,
        bigVehicleEmergencyDecel: 7,
        bigVehicleMaxSpeed: 100,
    },
};
