# 设备设施扎点

### 参数说明  props
|Prop name|Type|Description|
|---|---|---|---|
|`managerInstace`|`object`|自定义Domlabel管理器(WarningNextManager)实例 必填|
|`position`|`Arrray`|经纬度坐标 必填|
|`pointName`|`string`|扎点名称 必填|
|`labelName`|`string`|label文字|
|`type`|`string`|icon 类型(设备设施中文名) 默认：桥梁|
|`size`|`string`|尺寸 正常：normal 小型：small 默认：normal|
|`status`|`string`|状态 正常：normal 告警：warning 默认：normal|
|`bubbleColor`|`string`|bubbleColor 默认根据状态变更 优先以传入值为主|
|`circleBorderColor`|`string`|circleBorderColor|
|`circleColor`|`string`|circleColor 默认：#fff|
|`customData`|`object`|自定义数据|
|`clickCallback`|`function`|点击回调函数|

### icon图片说明
默认根据type、size、status 生成对应的icon图片，如果需要自定义icon，请在public/maplayer/assets/image/device_facilitie目录下添加对应type(中文)的图片,图片命名规则为：type(拼音)_size_status.png。

如：桥梁命名为 qiaoliang_normal_normal.png

并且需求在warningNextManager中配置nameMap, 配置格式为：中文：中文拼音