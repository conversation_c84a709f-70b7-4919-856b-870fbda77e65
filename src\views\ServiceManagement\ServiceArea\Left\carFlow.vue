<template>
    <div class="debris">
        <div class="content-title">
            <div class="index">车牌号</div>
            <div class="level">车型</div>
            <div class="num">接入事件</div>
        </div>
        <div class="debris-tbody">
            <div
                v-for="item in list" :key="item"
                class="tbody-item"
            >
                <div class="index">{{ item.plate }}</div>
                <div class="level">{{ item.vehicleType }}</div>
                <div class="num">

                    {{ item.eventType }}
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {ProgressComp} from '@/components/Common/index.js';
import {roadGradeList} from '@/api/equipment/facilitydisplay.js';
import {ref} from 'vue';

export default {
    components: {
        // ProgressComp,
    },
    props: {
        list: {
            type: Array,
            default: () => ([{
                plate: '粤K3867',
                vehicleType: '轿车',
                eventType: '00:00:59',
            }, {
                plate: '粤K3867',
                vehicleType: '轿车',
                eventType: '00:00:59',
            }, {
                plate: '粤K3867',
                vehicleType: '轿车',
                eventType: '00:00:59',
            }, {
                plate: '粤K3867',
                vehicleType: '轿车',
                eventType: '00:00:59',
            }, {
                plate: '粤K3867',
                vehicleType: '轿车',
                eventType: '00:00:59',
            }, {
                plate: '粤K3867',
                vehicleType: '轿车',
                eventType: '00:00:59',
            }]),
        },
    },
    setup() {

        const roadData = ref();
        const initDdata = () => {

        };

        initDdata();

        return {
            roadData,
        };
    },
};
</script>

<style lang="less" scoped>
.debris {
    width: 100%;
    height: 200px;
    overflow: auto;

    .index {
        flex: 1;
    }

    .level {
        width: 36%;
    }

    .num {
        flex: 1;
    }

    .content-title {
        padding-left: 15px;
    }

    .debris-tbody {
        max-height: 510px;
        overflow-y: auto;

        .tbody-item {
            display: flex;
            align-items: center;
            height: 36px;
            padding-left: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, .1);
        }
    }
}
</style>