import {request} from '@/utils/network-helper/index';
import {baseConfig} from '@/utils/common';
const {basePrefix} = baseConfig;

const baseTollStation = `${basePrefix}/toll`;

const Api = {
    GetSectionAlt: `${baseTollStation}/getSectionAlt`,
    GetTollList: `${baseTollStation}/getTollList`,
    GetTollInfo: `${baseTollStation}/getTollInfo`,
    GetTollOperation: `${baseTollStation}/getTollOperation`,
    GetTollCameraList: `${baseTollStation}/getTollCameraList`,
    GetTollCameraCount: `${baseTollStation}/getTollCameraCount`,
    GetTollBlock: `${baseTollStation}/getTollBlock`,
    GetTollLane: `${baseTollStation}/getTollLane`,
    GetDuty: `${baseTollStation}/getDuty`,
    GetRevenue: `${baseTollStation}/getRevenue`,
    GetTollFlowRank: `${baseTollStation}/getTollFlowRank`,
    GetLanePerformance: `${baseTollStation}/getLanePerformance`,
    GetMotherToll: `${baseTollStation}/getMotherToll`,
    GetTollHourFlow: `${baseTollStation}/getTollHourFlow`,
    GetMotherFlow: `${baseTollStation}/getMotherFlow`,
    GetTollRevenue: `${baseTollStation}/getTollRevenue`,
    GetTollgantry: `${baseTollStation}/getTollgantry`,
    GetTollJam: `${baseTollStation}/getTollJam`,
    GetTollInfoboard: `${baseTollStation}/getTollInfoboard`,
    GetGreenRevenue: `${baseTollStation}/getGreenRevenue`,
    GetAllInfoboard: `${baseTollStation}/getAllInfoboard`,
    GetInfoboardContent: `${baseTollStation}/getInfoboardContent`,
};

/**
 * 获取佛开高速路段经纬度坐标集合
 */
export function getSectionAlt() {
    return request.get(Api.GetSectionAlt);
}

/**
 * 获取佛开高速收费站列表
 */
export function getTollList() {
    return request.get(Api.GetTollList);
}

/**
 * 根据收费站id获取收费站详情
 */
export function getTollInfo(params) {
    return request.post(Api.GetTollInfo, params);
}

/**
 * 根据收费站id获取收费站获取收费站运营数据
 */
export function getTollOperation(params) {
    return request.post(Api.GetTollOperation, params);
}

/**
 * 根据收费站id获取收费站获取收费站统计摄像头
 */
export function getTollCameraList(params) {
    return request.post(Api.GetTollCameraList, params);
}

/**
 * 根据收费站id获取收费站获取收费站统计摄像头
 */
export function getTollCameraCount(params) {
    return request.post(Api.GetTollCameraCount, params);
}


/**
 * 根据收费站id获取收费站获取收费站统计摄像头
 */
export function getTollBlock(params) {
    return request.post(Api.GetTollBlock, params);
}

/**
 * 获取佛开高速收费站、车道数量
 */
export function getTollLane(params) {
    return request.get(Api.GetTollLane, params);
}

/**
 * 获取值班人员信息
 */
export function getDuty(params) {
    return request.post(Api.GetDuty, params);
}

/**
 * 佛开收费站 总运营
 */
export function getRevenue(params) {
    return request.post(Api.GetRevenue, params);
}

/**
 * 收费站本月车流排名
 */
export function getTollFlowRank(params) {
    return request.post(Api.GetTollFlowRank, params);
}

/**
 * 获取收费站 车道性能
 */
export function getLanePerformance(params) {
    return request.post(Api.GetLanePerformance, params);
}

/**
 * 收费站近12个月份 月收入
 */
export function getMotherToll(params) {
    return request.post(Api.GetMotherToll, params);
}

/**
 * 获取收费站24小时车流
 */
export function getTollHourFlow(params) {
    return request.post(Api.GetTollHourFlow, params);
}


/**
 * 车流量统计
 */
export function getMotherFlow(params) {
    return request.get(Api.GetMotherFlow, params);
}

/**
 * 收费站运营统计
 */
export function getTollRevenue(params) {
    return request.post(Api.GetTollRevenue, params);
}

/**
 * 获取收费站 门架
 */
export function getTollgantry(params) {
    return request.post(Api.GetTollgantry, params);
}

/**
 * 收费站是否拥堵
 */
export function getTollJam(params) {
    return request.post(Api.GetTollJam, params);
}

/**
 * 获取收费站 情报板
 */
export function getTollInfoboard(params) {
    return request.post(Api.GetTollInfoboard, params);
}

/**
 * 获取收费站 情报板
 */
export function getGreenRevenue(params) {
    return request.post(Api.GetGreenRevenue, params);
}

/**
 * 获取所有收费站情报板
 */
export function getAllInfoboard(params) {
    return request.get(Api.GetAllInfoboard, params);
}

/**
 * 根据情报板code获取情报板内容
 */
export function getInfoboardContent(assetsCode) {
    return request.get(`${Api.GetInfoboardContent}/${assetsCode}`);
}