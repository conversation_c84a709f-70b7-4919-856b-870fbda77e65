<template>
    <div class="region-select">
        <div style="height: 30px;">
            <div class="select-top">
                <div class="title">
                    <div class="name">行政区域 ｜ {{ cityName }}</div>
                </div>
                <div class="el-icon-arrow-down"></div>
            </div>
        </div>
        <div class="select-box">
            <div class="search-box">
                <el-input
                    v-model="searchValue"
                    class="search-box-input"
                    placeholder="请输入关键字搜索"
                    prefix-icon="el-icon-search"
                    @change="handleSearchFn()"
                />
            </div>
            <div class="line"></div>

            <!-- 查询结果 -->
            <div v-show="showSelectBack" class="structure-search-back">
                <div class="top">
                    <div>搜索结果</div>
                    <div class="el-icon-close" @click.stop="closeSelectBackFn"></div>
                </div>
                <div class="search-list">
                    <div
                        v-for="item in searchRelationList"
                        :key="item"
                        class="search-item"
                        @click.stop="choseSearchBackFn(item)"
                    >
                        {{ item.city }}
                    </div>
                </div>
            </div>

            <!-- 选择框 -->
            <div class="choose-box">
                <div class="choose-left">
                    <div
                        v-for="item in regionList" :key="item.value"
                        class="region" :class="[regionActive === item.value ? 'active' : '']"
                        @click="chooseRegion(item)"
                    >
                        {{ item.label }}</div>
                </div>
                <div class="choose-right">
                    <div
                        v-for="item in provinceList" :key="item.value"
                        class="province" :class="[provinceActive === item.city ? 'active' : '']"
                        @click="chooseProvince(item)"
                    >
                        {{ item.city }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import {Input} from 'element-ui';
import {ref} from 'vue';
import {
    getSimpleCityInfoBySelfZone,
    getSimpleCityInfoByKeyWords,
} from './api.js';

export default {
    components: {
        [Input.name]: Input,
    },
    props: {
        cityName: {
            type: String,
            default: '广东省',
        },
    },
    setup(props, {emit}) {
        const regionList = ref([
            {
                label: '粤东',
                value: '粤东',
                type: 0,
            },
            {
                label: '粤北',
                value: '粤北',
                type: 0,
            },
            {
                label: '粤西',
                value: '粤西',
                type: 0,
            },
            {
                label: '珠三角',
                value: '珠三角',
                type: 0,
            },
        ]);
        const regionActive = ref(null);
        const provinceList = ref([]);
        const provinceActive = ref(null);
        // 选择市区
        const chooseProvince = item => {
            if (provinceActive.value === item.city) {
                return;
            }
            provinceActive.value = item.city;
            emit('chooseProvince', item);
        };
        // 选择区域
        const chooseRegion = async item => {
            if (regionActive.value === item.value) {
                return;
            }
            regionActive.value = item.value;
            provinceActive.value = null;
            emit('chooseRegion', item);
            let arr = await getSimpleCityInfoBySelfZone({selfZone: item.value});
            provinceList.value = arr.data;
        };
        const showSelectBack = ref(false);
        const searchRelationList = ref([]);
        const searchValue = ref(null);
        const handleSearchFn = async () => {
            let arr = await getSimpleCityInfoByKeyWords({keyWords: searchValue.value});
            searchRelationList.value = arr.data;
            showSelectBack.value = true;
        };
        const closeSelectBackFn = () => {
            showSelectBack.value = false;
        };
        const choseSearchBackFn = async item => {
            regionActive.value = item.selfZone;
            let arr = await getSimpleCityInfoBySelfZone({selfZone: item.selfZone});
            provinceList.value = arr.data;
            provinceActive.value = item.city;
            showSelectBack.value = false;

            emit('chooseProvince', item);

        };
        const reset = () => {
            regionActive.value = null;
            provinceActive.value = null;
        };
        return {
            searchValue,
            regionList,
            regionActive,
            provinceList,
            provinceActive,
            searchRelationList,
            showSelectBack,
            chooseProvince,
            closeSelectBackFn,
            reset,
            chooseRegion,
            choseSearchBackFn,
            handleSearchFn,
        };
    },
};
</script>
<style lang='less' scoped>
.region-select {
    width: 256px;
    position: relative;

    .el-icon-arrow-down {
        transition: all .3s;
    }

    &:hover {
        .select-box {
            transform: scale(1);
            opacity: 1;
            transition: opacity .3s;
        }

        .el-icon-arrow-down {
            transform: rotate(180deg);
        }
    }

    .select-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 256px;
        height: 30px;
        padding-right: 20px;
        box-sizing: border-box;
        color: #fff;
        border-radius: 3px;
        border: 1px solid rgba(0, 231, 181, .25);
        background-image:
            linear-gradient(
                -66.16deg,
                rgba(34, 120, 105, .58) 7.662%,
                rgba(15, 103, 74, .62) 94.257%
            );
        cursor: pointer;

        .title {
            display: flex;
            align-items: center;
            height: 100%;
        }

        .name {
            display: inline-block;
            white-space: nowrap;
            padding-left: 12px;
            width: 210px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    .select-box {
        position: absolute;
        left: 0;
        bottom: -274px;
        width: 256px;
        height: 263px;
        z-index: 9999;
        transform: scale(0);
        opacity: 0;
        transition: opacity .3s, transform 0s linear .3s;
        box-sizing: border-box;
        background: rgba(32, 72, 63, .78);

        .search-box {
            width: 224px;
            height: 26px;
            margin: 17px auto 0;

            /deep/ .el-input__inner {
                border: 1px solid #379084;
                border-radius: 13px;
                height: 26px;
                background: rgb(48, 113, 99);
            }

            /deep/ .el-input__icon {
                line-height: 26px;
            }
        }

        .line {
            width: 225px;
            height: 1px;
            margin: 11px auto;
            background:
                linear-gradient(
                    90deg,
                    rgb(255, 255, 255) 0%,
                    rgba(90, 255, 161, 0) 100%
                );
        }

        .structure-search-back {
            position: absolute;
            top: 54px;
            left: 50%;
            transform: translateX(-51%);
            width: 90%;
            height: 320px;
            background: linear-gradient(-66.16deg, rgba(34, 120, 105, 1) 7.662%, rgba(15, 103, 74, 1) 94.257%);
            z-index: 9999;

            .top {
                display: flex;
                align-items: center;
                justify-content: space-between;
                height: 50px;
                padding: 0 20px;
                color: #fff;
                font-size: 17px;
                font-style: italic;
                font-weight: 700;
                border-bottom: 1px solid #fff;

                .el-icon-close {
                    cursor: pointer;
                }
            }

            .search-list {
                height: calc(100% - 50px);
                overflow-y: auto;

                .search-item {
                    padding-left: 20px;
                    height: 50px;
                    line-height: 50px;
                    color: #fff;
                    font-size: 15px;
                    cursor: pointer;
                    border-bottom: 1px solid rgba(255, 255, 255, .5);
                }
            }

            &::before {
                content: '';
                position: absolute;
                top: -20px;
                left: 50%;
                width: 0;
                height: 0;
                border-top: 10px solid transparent;
                border-right: 10px solid transparent;
                border-bottom: 10px solid rgba(34, 120, 105, 1);
                border-left: 10px solid transparent;
                z-index: 9999;
            }
        }

        .choose-box {
            margin: 0 auto;
            width: 225px;
            height: 180px;
            display: flex;

            &.show {
                display: none;
            }

            .choose-left {
                width: 74px;
                display: flex;
                flex-direction: column;
                border-right: 1px solid rgba(0, 244, 178, .5);

                .region {
                    width: 63px;
                    height: 40px;
                    border-radius: 3px;
                    border: 1px solid rgb(87, 123, 117);
                    background: rgb(87, 123, 117);
                    margin-bottom: 7px;
                    text-align: center;
                    line-height: 40px;
                    color: #fff;
                    cursor: pointer;

                    &.active {
                        border: 1px solid rgb(0, 244, 178);
                    }
                }
            }

            .choose-right {
                width: 157px;
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                align-content: flex-start;

                .province {
                    margin-left: 21px;
                    margin-right: 12px;
                    color: #fff;
                    height: 31px;
                    line-height: 31px;
                    margin-bottom: 3px;
                    cursor: pointer;

                    &.active {
                        color: rgb(0, 244, 178);
                    }
                }
            }
        }
    }
}
</style>