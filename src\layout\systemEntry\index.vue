<template>
    <div class="system-wrap">
        <div class="handler">
            <Icon name="ji<PERSON><PERSON>_xian<PERSON>" :font-size="60"/>
        </div>
        <SystemComponent :system-list="system"/>
    </div>
</template>

<script setup>
import Icon from './Icon/index.vue';
import SystemComponent from './system.vue';
const system = [
    {
        'name': '车流 数字化',
        'url': '/output/',
        'realUrl': '',
        'target': '_blank',
    },
    {
        'name': '道路 保畅',
        'url': '/x-emergency/',
        'realUrl': '',
    },
    {
        'name': '施工 管理',
        'url': '/platform/#/construction',
        'target': '_blank',
        'realUrl': '/construction',
    },
    {
        'name': '设备 设施',
        'url': '/platform/#/equipment-facility',
        'target': '_blank',
        'realUrl': '/equipment-facility',
    },
    {
        'name': '结构物监测',
        'url': '/platform/#/structure',
        'target': '_blank',
        'realUrl': '/structure',
    },
    {
        'name': '服务区管理',
        'url': '/platform/#/service-area',
        'target': '_blank',
        'realUrl': '/service-area',
    },
    {
        'name': '收费运营管理',
        'url': '/platform/#/toll-station',
        'target': '_blank',
        'realUrl': '/toll-station',
    },
];
</script>

<style lang="less" scoped>
.system-wrap {
    width: max-content;
    height: max-content;
    transition: transform .6s 1s;

    &:hover {
        transform: translate(-50%, -90%) !important;
        transition: transform .6s 0s;
    }

    .handler {
        position: absolute;
        width: 100px;
        height: 60px;
        display: flex;
        justify-content: center;
        align-items: center;
        top: 0%;
        left: calc(50% - 50px);
        transform: translateY(-100%);
        color: #fff;
        background: rgba(0, 0, 0, .3);
        opacity: .1;
        transition: all .3s;

        &:hover {
            opacity: 1;
        }

        .icon {
            transform: rotateZ(-90deg) scaleY(2);
        }
        // background: red;
    }
}
</style>

