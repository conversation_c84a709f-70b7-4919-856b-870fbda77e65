import {addLine, removeLine} from '../index';
import EventCenter from './event.js';

// 支持绑定的事件
const eventNameEnum = {
    click: 'clickCallback',
    mouseenter: 'onMouseenter',
    mouseleave: 'onMouseleave',
};

// line管理器
class LineManager extends EventCenter {
    constructor(engine) {
        super(eventNameEnum);
        this.lineMap = new Map();
        this.engine = engine;
    }
    /**
     * 添加线条
     *
     * @param name - 线条名称
     * @param positions - 线条起点和终点的坐标数组
     * @param options - 配置参数，可选
     */
    addLine(name, positions, options = {}) {
        this.options = options;
        if (this.lineMap.has(name)) {
            this.removeLineByName(name);
        }
        const {
            lineWidth = 10,
            color = '#fff',
            opacity = 1,
            transparent = false,
            customData = {},
            renderOrder = Infinity,
        } = options;
        let {line} = addLine(positions, {
            color,
            lineWidth,
            opacity,
            transparent,
            customData,
            renderOrder,
            ...options,
        }, this.engine);
        this.lineMap.set(name, line);

        // 绑定事件
        this.bind(line);
    }

    removeLineByName(name) {
        const line = this.lineMap.get(name);
        if (!line) return;
        this.unbind(line);
        removeLine(line);
        this.lineMap.delete(name);
    }

    clear() {
        [...this.lineMap.keys()].forEach(line => {
            this.removeLineByName(line);
        });
        this.lineMap.clear();
    }
}

export {
    LineManager,
};