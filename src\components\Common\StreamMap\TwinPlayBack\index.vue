
<!-- 孪生组件 -->
<script>
import useTwinPlayBack from './useTwinPlayBack';

import {computed, watch} from 'vue';
export default {
    name: 'TwinPlayBack',
    props: {
        showPlate: {
            type: Boolean,
            default: false,
        },
        needList: {
            type: Boolean,
            default: false,
        },
        followPlate: {
            type: String,
            default: '',
        },
        backParams: {
            type: Object,
            default: null,
        },
        roadName: {
            type: String,
            default: 'G0423',
        },
        url: {
            type: String,
            required: true,
        },
    },
    setup(props, {emit}) {
        const url = computed(() => props.url);

        const {
            viewTracks,
            followCarInfo,
            track,
            unTrack,
            backPlay,
            changePlateState,
            initTwin,
            destroyTwin,
        } = useTwinPlayBack({
            url,
        });

        watch(
            () => props.followPlate,
            v => {
                if (v) {
                    track({plate: v});
                }
                else {
                    unTrack();
                }
            }, {
                immediate: true,
            }
        );

        watch(() => props.showPlate, v => {
            changePlateState(v);
        }, {
            immediate: true,
        });

        watch(() => props.backParams, v => {
            if (v) {
                initTwin();
                backPlay({
                    ...v,
                    roadName: props.roadName,
                });
            }
            else {
                destroyTwin();
            }
        }, {
            immediate: true,
        });

        watch(followCarInfo, v => {
            emit('onInfoChange', v);
        });

        watch(viewTracks, v => {
            emit('onListChange', v);
        });

        // onBeforeUnmount(() => {
        //     unTrack();
        // });
    },
};
</script>