<template>
    <Card title="收费站车道统计" card-type="card-short-2">
        <template #content>
            <div class="card-list">
                <div
                    v-for="card in getCardInfo"
                    :key="card.title"
                    class="card-item"
                >
                    <DataCard :info="card"/>
                </div>
            </div>
        </template>
    </Card>
</template>

<script setup>
import {Card} from '@/components/Common';
import {getTollLane} from '@/api/tollStation';
import {computed, onMounted, ref} from 'vue';
import DataCard from '../DataCard/index.vue';

const cardConfig = [
    {
        title: '收费站数量',
        enTitle: 'Toll Gate',
        icon: 'shoufeizhan',
        field: 'tollNum',
        unit: '个',
    },
    {
        title: '人工车道数量',
        enTitle: 'Artificial lane',
        icon: 'chedao-rengong',
        field: 'mtcLaneNum',
        unit: '个',
    },
    {
        title: '混合车道数量',
        enTitle: 'Mixed lane',
        icon: 'chedao-hunhe',
        field: 'mixLaneNum',
        unit: '个',
    },
    {
        title: '自助车道数量',
        enTitle: 'Self service lane',
        icon: 'chedao-etc',
        field: 'etcLaneNum',
        unit: '个',
    },
];

const laneData = ref({
    etcLaneNum: '',
    mixLaneNum: '',
    mtcLaneNum: '',
    tollNum: '',
});

const getCardInfo = computed(() => {
    return cardConfig.map(item => {
        const value = laneData.value[item.field];
        return {
            ...item,
            value,
        };
    });
});

async function initData() {
    const {data} = await getTollLane();
    laneData.value = data;
}

onMounted(() => {
    initData();
});

</script>

<style lang="less" scoped>
.card-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .card-item {
        width: calc((100% - 16px) / 2);

        &:not(:nth-last-child(-n+2)) {
            margin-bottom: 16px;
        }
    }
}
</style>