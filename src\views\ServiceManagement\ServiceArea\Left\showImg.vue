<template>
    <div>
        <div
            v-for="item in infoList" :key="item.label"
            :info="item"
            class="item"
        >
            <div class="title">
                <span>|</span><p>{{ item.label }}</p>
            </div>
            <no-data v-if="!item.value" message="暂无数据"/>

            <img
                v-else :src="item.value"
                alt=""
            >
        </div>
    </div>
</template>
<script>
import {onMounted, ref, computed} from 'vue';
import img2 from '@/assets/images/serviceM/2.jpg';
import {NoData} from '@/components/Common';

export default {
    components: {
        NoData,
    },
    props: {
        info: {
            type: Object,
            default: () => {},
        },
    },
    setup(props) {
        const infoList = computed(() => [

            {
                label: '入口照片:',
                value: img2,
            },
            {
                label: '出口照片:',
            },
            {
                label: '停车场照片:',
                value: img2,
            },
        ]);
        return {
            infoList,
        };
    },
};
</script>
<style lang='less' scoped>
.item{
    padding: 8px;
    flex: 1;
    display: flex;
    flex-direction: column;
.title{
    display: flex;
    margin-bottom: 4px;
    p{
      color: rgb(231, 239, 255);
opacity: 0.5;
    }
    span{
        color: #fff;
        margin-right: 6px;
    }
}
img{
    width: 320px;
height: 168px;
border: 0;
}
}

</style>