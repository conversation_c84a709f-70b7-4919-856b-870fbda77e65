<template>
    <div class="electromechanical">
        <div class="electromechanical__infocard">
            <info-card
                v-for="(item, index) in structureList" :key="index"
                :info="item"
                width="584"
                height="100"
                class="infocard__item"
                :style-obj="styleObj"
            />
        </div>

        <div class="electromechanical__chart">
            <left-pie-li v-if="apiType" :data="list">
                <template #li="{item, index}">
                    <div class="electromechanical__chart__li">
                        <div class="li__title">
                            <div class="li__title__icon"><icon :name="item.icon"/></div>
                            <div class="li__title__word">{{ item.name }}</div>
                        </div>
                        <div class="li__ratio">{{ item.point || 0 }}%</div>
                        <div class="li__value">{{ item.value || 0 }}个</div>
                        <div class="li__detail" @click="handleClick(item, index)">查看详情</div>
                    </div>
                </template>
            </left-pie-li>
        </div>
    </div>
</template>

<script>
import {InfoCard} from '@/views/EquipmentFacilities/components/common/index';
import {systemDeviceCount, facilityNum, syStemCount} from '@/api/equipment/facilitydisplay';
import {LeftPieLi, Icon} from '@/components/Common/index';
import {ref, onMounted, onUnmounted} from 'vue';
import {detail, showDetail, width, top, left, right, activeIndex} from '@/views/EquipmentFacilities/utils';
import {useUnit} from '@/utils';

export default {
    name: '机电养护',
    components: {
        InfoCard,
        LeftPieLi,
        Icon,
    },
    setup(props) {
        const structureList = ref([
            {
                value: 0,
                unit: '个',
                cnTitle: '摄像枪',
                enTitle: 'Camera gun',
                icon: 'sheshi-shexiangtou',
            },
            {
                value: 0,
                unit: '个',
                cnTitle: '情报板',
                enTitle: 'Intelligence board',
                icon: 'xianshiping',
            },
        ]);

        const structureConfig = new Map([
            ['摄像枪', 'cameraNum'],
            ['情报板', 'boardNum'],
        ]);

        const styleObj = {
            cnTitleFontSize: 20,
            enTitleFontSize: 16,
            numFontSize: 42,
        };

        const list = ref([
            {
                name: '监控机电设施',
                ratio: 0,
                value: 0,
                systemId: 1,
                icon: 'facility',
            },
            {
                name: '收费机电设施',
                ratio: 0,
                value: 0,
                systemId: 2,
                icon: 'facility',
            },
            {
                name: '隧道机电设施',
                ratio: 0,
                value: 0,
                systemId: 3,
                icon: 'facility',
            },
        ]);

        const progressConfig = new Map([
            ['监控机电设施', 'monitSystem'],
            ['收费机电设施', 'tollSystem'],
            ['隧道机电设施', 'tunnelSystem'],
        ]);

        const apiType = ref(false);
        // 初始化进度条部分的数据
        const init = () => {
            facilityNum().then(res => {
                structureList.value = structureList.value.map(item => ({
                    ...item,
                    value: res.data?.[structureConfig.get(item.cnTitle)] || 0,
                }));
            });
            systemDeviceCount().then(res => {
                apiType.value = true;
                list.value = list.value.map(item => ({
                    ...item,
                    point: res.data?.[progressConfig.get(item.name) + 'Pro'] || 0,
                    ratio: res.data?.[progressConfig.get(item.name) + 'Pro'] / 100,
                    value: res.data?.[progressConfig.get(item.name) + 'Num'] || 0,
                }));
            });
        };

        onMounted(() => {
            init();
        });

        const detailIconConfig = new Map([
            ['cameraNum', {name: '摄像枪', icon: 'sheshi-shexiangtou'}],
            ['boardNum', {name: '情报板', icon: 'xianshiping'}],
            ['bayonetNum', {name: '高清卡口', icon: 'kakou'}],
            ['weaDetectorNum', {name: '气象检测器', icon: 'qixiang'}],
            ['laneMonitNum', {name: '车道监控器', icon: 'monitor'}],
            ['hairpinNum', {name: '发卡器', icon: 'bits'}],
            ['vehicleNum', {name: '车检器', icon: 'lvtongche'}],
        ]);

        const {ratio} = useUnit();
        // 点击查看详情按钮
        const handleClick = (e, index) => {
            width.value = 300;
            top.value = 730 * ratio.value + 'px';
            left.value = 1280 * ratio.value + 'px';
            right.value = 'inherit';
            activeIndex.value = index;
            syStemCount(e.systemId).then(res => {
                const arr = Object.entries(res.data).filter(([k, v]) => {
                    return v !== undefined && v !== null;
                });
                showDetail.value = true;
                detail.value = {
                    name: e.name,
                    data: arr.map(item => ({
                        label: detailIconConfig.get(item[0]).name,
                        icon: detailIconConfig.get(item[0]).icon,
                        value: item[1],
                    })),
                };
            });
        };

        onUnmounted(() => {
            structureConfig.clear();
        });

        return {
            structureList,
            styleObj,
            list,
            activeIndex,
            apiType,
            handleClick,
        };
    },
};
</script>

<style lang="less" scoped>
.electromechanical {
    height: 314px;
    &__infocard {
        display: flex;

        .infocard__item {
            margin-bottom: 8px;
            background-color: rgba(18, 74, 166, 0.8);
            background-image: linear-gradient(to right, rgba(1, 255, 229, .3), rgba(1, 255, 229, 0) 70%);

            &:nth-of-type(2n) {
                margin-left: 6px;
            }
        }
    }

    &__chart {
        width: 100%;
        height: 206px;
        margin-top: 8px;
        background-image: linear-gradient(to right, rgba(36, 104, 242, 0.3), rgba(36, 104, 242, 0));

        :deep(ul) {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding-top: 8px;
            padding-bottom: 8px;
            li {
                height: 48px;
            }
        }

        &__li {
            display: flex;
            align-items: center;
            width: 100%;
            height: 100%;
            padding: 0 24px 0 16px;

            .li__title {
                display: flex;
                align-items: center;

                &__icon {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 20px;
                    height: 20px;
                    border: 1px solid rgba(#fff, .2);
                }

                &__word {
                    font-size: 18px;
                    font-family: 'PingFang';
                    font-weight: 500;
                    margin-left: 12px;
                    color: #fff;
                }
            }

            .li__ratio {
                width: 85px;
                margin-left: 158px;
                color: rgba(#fff, .7);
                font-size: 24px;
                font-family: 'RoboData';
                font-weight: 400;
                text-align: right;
            }

            .li__value {
                width: 120px;
                margin-left: 160px;
                font-family: 'RoboData';
                font-size: 24px;
                color: rgba(#fff, .7);
                text-shadow: 0 2px 4px 0 rgba(0, 3, 7, .3);
                text-align: right;
            }

            .li__detail {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 80px;
                height: 32px;
                border: 1px solid rgb(1, 255, 229);
                font-size: 16px;
                font-weight: 400;
                font-family: 'PingFang';
                color: rgb(1, 255, 229);
                margin-left: 160px;
            }
        }
    }
}
</style>