<template>
    <div class="toll-facility">
        <monitor-card
            v-for="(item, index) in list" :key="index"
            :info="item"
            class="facility__item"
        />
    </div>
</template>

<script>
import {ref, onMounted, watch} from 'vue';
import {MonitorCard} from '@/views/EquipmentFacilities/components/common/index';
import {facilityCountHealth} from '@/api/equipment/facilitydisplay';
export default {
    name: 'facilityMonitoring',
    props: {
        type: {
            type: String,
            default: '1',
        },
    },
    components: {
        MonitorCard,
    },
    setup(props) {
        const list = ref([
            {
                title: '一级设施',
                type: 'one',
                value: 0,
            },
            {
                title: '二级设施',
                type: 'two',
                value: 0,
            },
            {
                title: '三级设施',
                type: 'three',
                value: 0,
            },
            {
                title: '四级设施',
                type: 'four',
                value: 0,
            },
        ]);

        const styleObj = {
            cnTitleFontSize: 20,
            enTitleFontSize: 16,
            numFontSize: 42,
        };

        function getLevel(number) {
            if (number >= 0 && number <= 25) {
                return 1;
            }
            else if (number >= 26 && number <= 50) {
                return 2;
            }
            else if (number >= 51 && number <= 75) {
                return 3;
            }
            return 4;

        }

        const init = (() => {
            facilityCountHealth(props.type).then(res => {
                list.value = list.value.map(item => ({
                    ...item,
                    value: res.data?.[item.type + 'Health'] || 0,
                    health: getLevel(res.data?.[item.type + 'HealthPro']) || 0,
                }));
            });
        });

        onMounted(() => init());

        watch(() => props.type, () => init());

        return {
            list,
            styleObj,
        };
    },
};
</script>

<style lang="less" scoped>
.toll-facility {
    display: flex;
    flex-wrap: wrap;
    width: 556px;

    .facility__item {
        margin-bottom: 8px;

        &:nth-of-type(2n) {
            margin-left: 8px;
        }

        &:nth-of-type(3),
        &:nth-of-type(4) {
            margin-bottom: 0;
        }
    }
}
</style>