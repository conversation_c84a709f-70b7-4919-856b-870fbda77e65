<template>
    <div class="app-breadcrumb">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item
                v-for="item in getList"
                :key="item"
            >
                {{ item }}
            </el-breadcrumb-item>
        </el-breadcrumb>
    </div>
</template>

<script>
import {
    Breadcrumb,
    BreadcrumbItem,
} from 'element-ui';
import {computed} from 'vue';
import {useRoute} from '@/utils';

export default {
    components: {
        [Breadcrumb.name]: Breadcrumb,
        [BreadcrumbItem.name]: BreadcrumbItem,
    },
    setup() {
        const route = useRoute();

        const getList = computed(() => {
            const matched = route.matched;
            const list = matched.map(i => i.meta?.title);
            list.shift();
            return list;
        });

        return {
            getList,
        };
    },
};
</script>

<style lang="less" scoped>
.app-breadcrumb {
    height: 46px;
    padding: 16px 24px;

    & /deep/ .el-breadcrumb__item:last-child {
        .el-breadcrumb__inner {
            color: #fff;
        }
    }
}
</style>