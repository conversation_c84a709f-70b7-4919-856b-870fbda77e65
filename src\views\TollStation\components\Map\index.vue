<template>
    <map-component
        :options="{
            center: mapCenter,
            ...mapOptions,
        }"
        @mapLoaded="mapLoaded"
    >
        <slot v-if="mapLoadState"></slot>
    </map-component>
</template>

<script>
import {Map} from '@/components/Common';
import {ref} from 'vue';
import {mapCenter, engine} from '@/store/engine.js';

export default {
    components: {
        MapComponent: Map,
    },
    props: {
        mapOptions: {
            type: Object,
            default: () => ({
                showSatelliteMap: true,
                showTollStations: true,
                showAssetsScene: true,
            }),
        },
    },
    setup(props, {emit}) {
        const mapLoadState = ref(false);

        function mapLoaded(map) {
            engine.value = map;
            mapLoadState.value = true;
            emit('loaded', map);
        }

        return {
            mapLoaded,
            mapCenter,
            mapLoadState,
        };
    },
};
</script>