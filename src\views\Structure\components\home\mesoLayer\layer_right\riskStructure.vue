<template>
    <div class="risk-structure">
        <div
            v-for="(item, index) in list" :key="index"
            class="risk-structure__item"
        >
            <div class="risk-structure__item__top">
                <div class="top__info">
                    <div class="top__info__icon">
                        <icon name="qiaoliang1"/>
                    </div>
                    <div class="top__info__title">
                        <div class="top__info__title__cnTitle">{{ item.cnTitle }}</div>
                        <div class="top__info__title__enTitle">{{ item.enTitle }}</div>
                    </div>
                </div>
                <div class="top__num">{{ item.value }}</div>
            </div>
            <div class="risk-structure__item__bottom">
                <div class="bottom__high">
                    <div class="info">
                        <span class="bottom__high__word">高</span>
                        <span class="bottom__high__num">{{ item.highCount }}</span>
                    </div>
                    <div class="bottom__high__progress progress" :style="{'--w': item.high}"></div>
                </div>
                <div class="bottom__middle">
                    <div class="info">
                        <span class="bottom__middle__word">中</span>
                        <span class="bottom__middle__num">{{ item.centerCount }}</span>
                    </div>
                    <div class="bottom__middle__progress progress" :style="{'--w': item.middle}"></div>
                </div>
                <div class="bottom__low">
                    <div class="info">
                        <span class="bottom__low__word">低</span>
                        <span class="bottom__low__num">{{ item.lowCount }}</span>
                    </div>
                    <div class="bottom__low__progress progress" :style="{'--w': item.low}"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {ref, onMounted} from 'vue';
import {Icon} from '@/components/Common';
import {facilityCountRisk} from '@/api/structure/index';
export default {
    name: '风险结构物',
    components: {
        Icon,
    },
    setup(props) {
        const list = ref([]);

        const enDict = new Map([
            ['九江大桥', 'Jiu Jiang Bridge'],
            ['北江大桥', 'Bei Jiang Bridge'],
            ['吉利河大桥', 'Jili Hai Bridge'],
            ['潭州大桥', 'Tan Zhen Bridge'],
        ]);

        const styleObj =  {
            cnTitleFontSize: 20,
            numFontSize: 42,
        };

        const init = () => {
            facilityCountRisk().then(res => {
                list.value = res.data.map(item => ({
                    ...item,
                    cnTitle: item.structureName,
                    enTitle: enDict.get(item.structureName),
                    value: item.totalCount,
                    high: (item.highCount / item.totalCount * 100).toFixed(0) + '%',
                    middle: (item.centerCount / item.totalCount * 100).toFixed(0) + '%',
                    low: (item.lowCount / item.totalCount * 100).toFixed(0) + '%',
                    icon: 'qiaoliang1',
                }));
            });
        };

        onMounted(() => init());

        return {
            list,
            styleObj,
        };
    },
};
</script>

<style lang="less" scoped>
.risk-structure {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 338px;
    overflow-y: auto;

    &__item {
        width: 45%;
        height: 338px;

        &__top {
            width: 100%;
            height: 192px;
            background-color: rgba(18, 74, 166, 0.24);
            backdrop-filter: blur(10px);
            border-top: 1px solid transparent;
            border-image: linear-gradient(to right, rgb(36, 104, 242), rgba(1, 255, 229, 0.5));
            padding: 35px 24px;

            .top__info {
                display: flex;
                margin-bottom: 26px;

                &__icon {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 31px;
                    height: 24px;
                    background: url('@/assets/images/base/iconBg.png') no-repeat center center / 100%;
                }

                &__title {
                    margin-left: 10px;
                    &__cnTitle {
                        font-size: 20px;
                        font-family: 'PingFang';
                        font-weight: 500;
                        margin-bottom: 16px;
                        color: rgba(#fff, .9);
                    }
                    &__enTitle {
                        font-size: 14px;
                        font-family: 'RoboData';
                        font-weight: 400;
                    }
                }
            }

            .top__num {
                text-align: right;
                font-size: 42px;
                font-family: 'RoboData';
                font-weight: 400;
                color: rgba(#fff, .9);
            }
        }

        &__bottom {
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            height: 118px;
            background-color: rgba(27, 65, 120, 0.8);
            padding: 24px 16px;
            border-top: 1px solid rgba(36, 104, 242, .6);
            margin-top: 4px;

            &::after {
                content: '';
                display: block;
                position: absolute;
                left: 0;
                bottom: 0;
                width: 100%;
                height: 19px;
                background: url('../../../../images/riskBottom.png') no-repeat center / 100%;
            }

            >div {
                display: flex;
                width: 100%;
                align-items: center;
                justify-content: space-between;
            }

            .bottom__high__word,
            .bottom__middle__word,
            .bottom__low__word{
                font-size: 14px;
                font-family: 'PingFang';
                font-weight: 500;
                color: rgba(#fff, .5);
                margin-right: 5px;
            }
            .bottom__high__num,
            .bottom__middle__num,
            .bottom__low__num{
                font-size: 16px;
                font-family: 'RoboData';
                font-weight: 400;
                color: rgba(#fff, .8);
            }

            .progress {
                position: relative;
                width: 151px;
                height: 4px;
                // margin-left: 30px;
                background-color: rgba(0, 0, 0, .3);

                &::before {
                        content: '';
                        display: block;
                        position: absolute;
                        left: 0;
                        top: 0;
                        height: 100%;
                        background-size: 6px 100%;
                        transform: skew(-45deg);
                    }

                &.bottom__high__progress {
                    &::before {
                        width: var(--w);
                        background-image: linear-gradient(to right, rgb(255, 105, 105) 4px, rgba(0, 0, 0, .3) 4px);
                    }
                }

                &.bottom__middle__progress {
                    &::before {
                        width: var(--w);
                        background-image: linear-gradient(to right, rgb(255, 249, 147) 4px, rgba(0, 0, 0, .3) 4px);
                    }
                }

                &.bottom__low__progress {
                    &::before {
                        width: var(--w);
                        background-image: linear-gradient(to right, rgb(0, 255, 114) 4px, rgba(0, 0, 0, .3) 4px);
                    }
                }
            }
        }
    }
}
</style>