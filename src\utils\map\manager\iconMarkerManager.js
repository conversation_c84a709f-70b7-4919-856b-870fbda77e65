import {
    addIcon,
    removeIcon,
    addBubble,
    removeBubble,
} from '../index';
import EventCenter from './event.js';
import {getPointHeight, useUnit} from '@/utils';

const removeMap = {
    Icon: removeIcon,
    Bubble: removeBubble,
};

// 支持绑定的事件
const eventNameEnum = {
    click: 'onClick',
    mouseenter: 'onMouseenter',
    mouseleave: 'onMouseleave',
};

const {ratio} = useUnit();

// IconMarker管理器
class IconMarkerManager extends EventCenter {
    constructor(engine) {
        super(eventNameEnum);
        this.iconMarkerMap = new Map();
        this.setHaveMap = new Map();
        this.engine = engine;
    }

    async addIcon(name, point, options = {}) {
        this.options = options;

        if (this.iconMarkerMap.has(name)) {
            this.removeIconByName(name);
        }

        if (this.setHaveMap.has(name)) {
            this.setHaveMap.get(name)();
        }

        const next = await this.initHeight(name, point);
        if (!next) return;

        const map = {};
        const {
            url,
            width = 33,
            height = 42,
            offset = [0, -21],
            needBubble = false,
            renderOrder = Infinity,
            customData,
        } = options;
        const {icon, _engine} = addIcon(point, url, {
            width: width * ratio.value,
            height: height * ratio.value,
            offset,
            customData,
            _engine: this.engine,
            renderOrder,
        });

        if (needBubble) {
            const {
                color = '#5DE47E',
                size = 60,
            } = options || {};
            const {bubble} = addBubble(point, {
                size,
                color,
                type: 'Wave',
                _engine: this.engine,
            });
            map.Bubble = bubble;
        }
        map.Icon = icon;

        this.iconMarkerMap.set(name, map);
        this.bind(icon);
    }

    // 初始化高程
    async initHeight(name, point) {
        if (!point[2]) {
            // 解决请求height时await阻塞导致再次添加时无法清除扎点
            let have = true;
            this.setHaveMap.set(name, () => {
                have = false;
            });
            const height = await getPointHeight(point);
            point[2] = height?.data?.[0] || 0;
            if (have) {
                this.setHaveMap.delete(name);
            }
            return have;
        }
        return true;
    }

    removeIconByName(name) {
        const iconMarker = this.iconMarkerMap.get(name);
        if (!iconMarker) return;
        this.unbind(iconMarker.Icon);
        Object.keys(iconMarker).forEach(key => {
            const remove = removeMap[key];
            remove(iconMarker[key]);
        });
        this.iconMarkerMap.delete(name);
    }

    clear() {
        [...this.iconMarkerMap.keys()].forEach(icon => {
            this.removeIconByName(icon);
        });
    }
}

export {
    IconMarkerManager,
};