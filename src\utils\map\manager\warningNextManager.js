import {addBubble, removeBubble, addIcon, removeIcon,
    addCircle, removeCircle, addText, removeText, addDOMOverlay, removeDOMOverlay} from '../index';
import {getPointHeight} from '@/utils';

const nameMap = {
    '桥梁': 'qiaoliang',
    '边坡': 'bianpo',
    '隧道': 'suidao',
    '驻点': 'zhudian',
    '路基': 'luji',
    '路面': 'lumian',
    '交通安全设施': 'sheshi',
    '绿化': 'lvhua',
    '机电设备': 'shebei',
    '龙门架': 'longmenjia',
    '巡检养护': 'xunjian',
    '日常养护': 'richang',
    '修复养护': 'xiufu',
    '应急养护': 'yingji',
    '预防养护': 'yufang',
    '专项养护': 'zhuanxiang',
    '收费站': 'shou<PERSON><PERSON>han',
    '服务区': 'fuwuqu',
    '道路': 'daolu',
    '设备': 'sebei',
    '保洁车': 'baojieche',
    '交安': 'jiaoan',
    '路基养护': 'lujiyanghu',
};
// 计算label的偏移量
const getLabelOffset = (labelDom, size = 'normal') => {
    if (!labelDom) {
        return [0, 0];
    }
    const {width, height} = labelDom.getBoundingClientRect();
    const iconWidth = size === 'normal' ? 48 : 32;
    const iconHeight = size === 'normal' ? 83 : 55;
    const gapLeft = size === 'normal' ? 10 : 5;
    const gapTop = size === 'nomal' ? 42 : 28;
    const offsetLeft = width / 2 + iconWidth / 2 + gapLeft;
    const offsetTop = -(height / 2 - (iconWidth + gapTop) / 2) - iconHeight;
    return [offsetLeft, offsetTop];
};

const baseStatus = ['normal', 'warning'];
// icon 路径 'maplayer/assets/image/device_facilitie/设备类型(中文拼音)_size_status.png
// size: normal small status: normal warning
const getIconUrl = (type, size = 'normal', status = 'normal', iconUrl) => {
    if (iconUrl) {
        return iconUrl;
    }
    const getIconStatus = status => {
        if (baseStatus.includes(status)) {
            return status === 'normal' ? '_normal' : '_warning';
        }
        return `_${status}`;
    };
    size = size === 'normal' ? '_normal' : '_small';
    status = getIconStatus(status);
    type = nameMap[type] || type;
    return `maplayer/assets/image/device_facilitie/${type}${size}${status}.png`;
};

const getBubbleColor = (status, bubbleColor) => {
    if (bubbleColor) {
        return bubbleColor;
    }
    return status === 'normal' ? '#5DE47E' : '#763643';
};
// 告警管理器 构造物等告警展示 label 为自定义dom
class WarningNextManager {
    constructor(engine) {
        this.warningeMap = new Map();
        this.setHaveMap = new Map();
        this.engine = engine;
    }
    async addWarningPoint(name, point, options) {
        if (this.warningeMap.has(name)) {
            this.removeWarningPointByName(name);
        }
        if (this.setHaveMap.has(name)) {
            this.setHaveMap.get(name)();
        }
        const next = await this.initHeight(name, point);
        if (!next) return;

        const {
            labelDom,
            type = '桥梁',
            iconUrl,
            customData,
            bubbleColor,
            text = '',
            circleColor = '#fff',
            circleBorderColor,
            clickCallback,
            size = 'normal',
            status = 'normal',
        } = options || {};

        // 气泡点
        let {bubble} = addBubble(point, {
            size: size === 'normal' ? 60 : 40,
            color: getBubbleColor(status, bubbleColor),
            type: 'Wave',
            _engine: this.engine,
        });

        // 右侧label dom
        let {domOverlay} = addDOMOverlay(point, labelDom, {
            _engine: this.engine,
            offset: getLabelOffset(labelDom, size),
        });

        // icon small: 32 * 55 normal: 48 * 83(默认)
        let {icon, _engine} = addIcon(point, getIconUrl(type, size, status, iconUrl), {
            width: size === 'normal' ? 48 : 32,
            height: size === 'normal' ? 83 : 55,
            offset: size === 'normal' ? [0, -42] : [0, -28],
            customData,
            _engine: this.engine,
        });

        // 告警数字圆
        let {circle} = text && addCircle(point, {
            customData,
            color: circleColor,
            borderColor: circleBorderColor,
            _engine: this.engine,
        }) || {};
        // 告警数字
        let {_text} = text && addText(point, text, {
            customData,
            _engine: this.engine,
        }) || {};

        if (clickCallback && typeof clickCallback === 'function') {
            icon.receiveRaycast = true;
            domOverlay.receiveRaycast = true;
            _engine.event.bind(icon, 'click', clickCallback);
            _engine.event.bind(domOverlay, 'click', clickCallback);
        }

        this.warningeMap.set(name, {
            'Bubble': bubble, // 气泡点
            'Label': domOverlay, // 文字 label
            'Icon': icon, // icon
            'Text': _text, // 告警数字
            'Circle': circle, // 告警数字圆
        });
    }

    // 初始化高程
    async initHeight(name, point) {
        if (!point[2]) {
        // 解决请求height时await阻塞导致再次添加时无法清除扎点
            let have = true;
            this.setHaveMap.set(name, () => {
                have = false;
            });
            const height = await getPointHeight(point);
            point[2] = height?.data?.[0] || 0;
            if (have) {
                this.setHaveMap.delete(name);
            }
            return have;
        }
        return true;
    }

    removeWarningPointByName(name) {
        const warning = this.warningeMap.get(name);
        warning && removeBubble(warning.Bubble, this.engine);
        warning && removeDOMOverlay(warning.Label, this.engine);
        warning && removeIcon(warning.Icon, this.engine);
        warning && removeText(warning.Text, this.engine);
        warning && removeCircle(warning.Circle, this.engine);
        this.warningeMap.delete(name);
    }

    clear() {
        [...this.warningeMap.keys()].forEach(macro => {
            this.removeWarningPointByName(macro);
        });
        this.warningeMap.clear();
    }
}

export {
    WarningNextManager,
};