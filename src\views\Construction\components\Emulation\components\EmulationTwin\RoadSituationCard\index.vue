<template>
    <Card title="路况预测" card-type="card-short-2">
        <template #content>
            <div ref="scrollRef" class="card-content show-scroll">
                <div class="timeline-list">
                    <div
                        v-for="item in emulationPredictList"
                        :key="item"
                        class="timeline-item"
                    >
                        <div class="timeline-content">
                            <div class="timeline-time">{{ item.time }}</div>
                            <div class="timeline-text">{{ item.predict }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </Card>
</template>

<script setup>
import {Card} from '@/components/Common';
import {nextTick, ref, watch} from 'vue';
import {emulationPredictList} from '../index';

const scrollRef = ref();

watch(
    () => emulationPredictList.value,
    () => {
        nextTick(() => {
            scrollRef.value.scrollTo({
                top: scrollRef.value.scrollHeight,
                behavior: 'smooth',
            });
        });
    },
    {
        deep: true,
    }
);

</script>

<style lang="less" scoped>
.card-content {
    height: 750px;
    overflow-y: scroll;
    margin-right: -12px;
    padding: 12px 12px 12px 0;
}

.timeline {
    &-item {
        position: relative;
        padding-left: 36px;

        &::before {
            content: '';
            position: absolute;
            left: 4px;
            top: 26px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            border: 3px solid rgb(0, 237, 255);
            background-color: rgba(8, 35, 79, .5);
            backdrop-filter: blur(8px);
        }

        &::after {
            content: '';
            position: absolute;
            top: 40px;
            left: 8px;
            width: 1px;
            height: calc(100% - 2px);
            transform: translateX(50%);
            background: rgba(196, 196, 196, .2);
        }

        &:last-child {
            &::after {
                display: none;
            }
        }
    }

    &-content {
        display: flex;
        margin-bottom: 16px;
        padding: 16px;
        background-image:
            linear-gradient(
                to right,
                rgba(36, 104, 242, 0),
                rgba(36, 104, 242, .3),
            );
    }

    &-time,
    &-text {
        color: #fff;
    }

    &-time {
        line-height: 26px;
        font-size: 24px;
        font-family: RoboData;
    }

    &-text {
        font-size: 16px;
        line-height: 24px;
        margin-left: 24px;
    }
}
</style>