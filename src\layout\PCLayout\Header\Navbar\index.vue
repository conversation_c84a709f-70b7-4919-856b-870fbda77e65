<template>
    <div class="app-navbar">
        <app-menu :list="pcMenuList" @select="selectMenu"/>
    </div>
</template>

<script>
import Menu from './Menu.vue';
import {pcMenuList, EXTERNAL_PATH_PREFIX} from '@/config/menu';
import {useRouter} from '@/utils';

export default {
    components: {
        AppMenu: Menu,
    },
    setup() {
        const router = useRouter();

        function selectMenu(path) {
            // 外部链接
            if (path.startsWith(EXTERNAL_PATH_PREFIX)) {
                path = path.replace(EXTERNAL_PATH_PREFIX, '');
                path && window.open(path, '_self');
                return;
            }
            router.push({
                path,
            });
        }

        return {
            pcMenuList,
            selectMenu,
        };
    },
};
</script>