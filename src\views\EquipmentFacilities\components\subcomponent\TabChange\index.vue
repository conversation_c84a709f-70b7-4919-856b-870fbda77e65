<template>
    <div class="tab-change">
        <div :class="{'tab-change__left': true, 'active': value === 'Equipment'}" @click="changeTab('Equipment')">
            设备管理
        </div>
        <div :class="{'tab-change__right': true, 'active': value === 'Facility'}" @click="changeTab('Facility')">
            设施管理
        </div>
    </div>
</template>

<script>
import {
    mapFilterData, mapFilterList,
} from '@/views/EquipmentFacilities/utils';

export default {
    name: '',
    props: {
        value: {
            type: String,
            default: 'Equipment',
        },
    },
    model: {
        prop: 'value',
        event: 'update:value',
    },
    setup(props, {emit}) {
        const changeTab = type => {
            if (type === 'Equipment') {
                mapFilterData.value = [
                    'xianshiping',
                    'lijiao',
                    'qiaoliang1',
                    'toll-station',
                    'camera-fuwuqu',
                ];
                mapFilterList.value = [
                    {
                        icon: 'sheshi-shexiangtou',
                        value: 'sheshi-shexiangtou',
                        label: '摄像头',
                    },
                    {
                        icon: 'xianshiping',
                        value: 'xianshiping',
                        label: '显示屏',
                    },
                    {
                        icon: 'lijiao',
                        value: 'lijiao',
                        label: '互通立交',
                    },
                    {
                        icon: 'qiaoliang1',
                        value: 'qiaoliang1',
                        label: '桥梁',
                    },
                    {
                        icon: 'toll-station',
                        value: 'toll-station',
                        label: '收费站',
                    },
                    {
                        icon: 'camera-fuwuqu',
                        value: 'camera-fuwuqu',
                        label: '服务区',
                    },
                ];
            }
            else {
                mapFilterData.value = [
                    'lumian',
                    'lijiao',
                    'qiaoliang1',
                ];
                mapFilterList.value = [
                    {
                        icon: 'lumian',
                        value: 'lumian',
                        label: '路面',
                    },
                    {
                        icon: 'lijiao',
                        value: 'lijiao',
                        label: '互通立交',
                    },
                    {
                        icon: 'qiaoliang1',
                        value: 'qiaoliang1',
                        label: '桥梁',
                    },
                ];
            }
            emit('update:value', type);
            emit('click', type);
        };

        return {
            changeTab,
        };
    },
};
</script>

<style lang="less" scoped>
.tab-change {
    display: flex;
    align-items: center;
    position: fixed;
    top: 180px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 50;

    &__left,
    &__right {
        display: flex;
        align-items: center;
        width: 180px;
        height: 40px;
        margin-right: 6px;
        font-size: 24px;
        font-weight: 500;
        font-family: 'PingFang';
        color: rgba(#fff, .7);
        text-shadow: 0 2px 0 0 rgba(0,0,0,.15);
        cursor: pointer;
    }

    &__left {
        background: url('../../../images/left_inactive.png') no-repeat center center / 100%;
        justify-content: flex-end;
        padding-right: 32px;

        &.active {
            background-image: url('../../../images/left_active.png');
            color: rgba(#fff, 1);
        }
    }

    &__right {
        background: url('../../../images/right_inactive.png') no-repeat center center / 100%;
        padding-left: 32px;

        &.active {
            background-image: url('../../../images/right_active.png');
            color: rgba(#fff, 1);
        }
    }
}
</style>