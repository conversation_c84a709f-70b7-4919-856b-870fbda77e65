
import {computed, getCurrentInstance} from 'vue';

const points = {
    0: [50, 0],
    45: [0, 0],
    90: [0, 50],
    135: [0, 100],
    180: [50, 100],
    225: [100, 100],
    270: [100, 50],
    315: [100, 0],
};

const main = () => {
    const props = getCurrentInstance().proxy.$props;
    const data = computed(() => {
        let s = 0;
        return props.data.map((e, i) => {
            s += props.data[i - 1]?.ratio || 0;
            return Object.assign(e, {s: s.toFixed(2)});
        });
    });

    const shadowStartDeg = computed(() => {
        const s = (data.value[props.activeIndex]?.s || 0) * 360;
        return -s + 'deg';
    });

    const polygonPath = computed((e = 0) => {
        const s = (data.value[props.activeIndex]?.ratio || 0) * 360;
        let path = Object.keys(points).reduce((p, c) => {
            if (s < c) {
                return p;
            }
            return (e = c) && p + `, ${points[c].map(e => e + '%').join(' ')}`;
        }, '50% 50%');

        const angle = s - e;
        let radian;
        if ([45, 135, 225, 315].includes(+e)) {
            radian = 1 - Math.tan((45 - angle) / 180 * Math.PI);
        }
        else {
            radian = Math.tan(angle / 180 * Math.PI);
        }
        let x = radian * 50;
        let endPoint = '';
        if ([0, 315].includes(+e)) {
            endPoint = `, ${points[e][0] - x}% ${points[e][1]}%`;
        }
        else if ([45, 90].includes(+e)) {
            endPoint = `, ${points[e][0]}% ${points[e][1] + x}%`;
        }
        else if ([135, 180].includes(+e)) {
            endPoint = `, ${points[e][0] + x}% ${points[e][1]}%`;
        }
        else {
            endPoint = `, ${points[e][0]}% ${points[e][1] - x}%`;
        }
        return path + endPoint;
    });

    return {
        shadowStartDeg,
        polygonPath,
    };
};

export default main;