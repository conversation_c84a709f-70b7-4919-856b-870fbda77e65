<template>
    <div class="flow-trend">
        <fokai-line
            v-if="list[0].data.length || list[1].data.length" :data="list"
            y-name="流量（辆）"
        />
        <no-data v-else/>
    </div>
</template>

<script>
import {ref, onMounted, watch, computed} from 'vue';
import {FokaiLine, NoData} from '@/components/Common';
import {bridgeFlow} from '@/api/structure/index';
export default {
    name: '交通流量趋势',
    props: {
        bridgeType: {
            type: String,
            default: '',
        },
        dateTime: {
            type: String,
            default: '',
        },
        bridgeTypeOption: {
            type: Array,
            default: () => ([]),
        },
    },
    components: {
        FokaiLine, NoData,
    },
    setup(props) {
        const list = ref([
            {
                name: '实时流量',
                color: 'rgb(36, 104, 242)',
                colorStops: ['rgba(0, 255, 149, 0.35)', 'rgba(0, 255, 149, 0)'],
                data: [],
            },
            {
                name: '历史流量',
                color: 'rgb(0, 255, 149)',
                colorStops: ['rgba(0, 255, 149, 0.35)', 'rgba(0, 255, 149, 0)'],
                data: [],
            },
        ]);

        // 过滤数组比较的格式
        const filterXData = data => {
            if (props.dateTime === '2') {
                return data.split('-')[2];
            }
            return data;
        };

        const initXData = () => {
            let configList = [];
            switch (props.dateTime) {
                case '1':
                    // 生成当天24小时的标签
                    for (let i = 0; i < 24; i++) {
                        configList.push(i.toString());
                    }
                    break;
                case '2':
                    // 生成当月30天的标签
                    for (let i = 1; i <= 30; i++) {
                        configList.push(i.toString());
                    }
                    break;
                case '3':
                    // 生成当年12个月的标签
                    for (let i = 1; i <= 12; i++) {
                        configList.push(i.toString());
                    }
                    break;
                default:
                    break;
            }
            console.log('configList', configList);
            return configList;
        };

        const unit = computed(() => {
            return props.dateTime === '1' ? '时' : props.dateTime === '2' ? '日' : '月';
        });

        const init = () => {
            const configList = initXData();
            bridgeFlow({
                bridgeType: props.bridgeType,
                dateTime: props.dateTime,
            }).then(res => {

                list.value[1].data = configList.map(item => {
                    const obj = res.data.historyFlowVO.find(e => filterXData(e.dateTime) === item);
                    return {
                        name: item + unit.value,
                        value: obj ? obj.flow : 0,
                    };
                });
                list.value[0].data = configList.map(item => {
                    const obj = res.data.nowFlowVO.find(e => filterXData(e.dateTime) === item);
                    return {
                        name: item + unit.value,
                        value: obj ? obj.flow : 0,
                    };
                });
                console.log('list.value', list.value[0].data, list.value[1].data);
            });
        };

        onMounted(() => init());

        watch(() => [props.bridgeType, props.dateTime], () => {
            init();
        });

        return {
            list,
        };
    },
};
</script>

<style lang="less" scoped>
.flow-trend {
    width: 100%;
    height: 190px;
}
</style>