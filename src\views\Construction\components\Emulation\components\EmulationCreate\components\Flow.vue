<template>
    <div class="strategy-config">
        <el-form
            class="emulation-form"
            size="small"
            :label-width="labelWidth"
        >
            <!-- 流量输入方式： -->
            <el-form-item
                label="流量输入方式："
            >
                <el-radio-group
                    v-model="flowInfo.flowInputType"
                    @change="fetchFlowList"
                >
                    <el-radio
                        v-for="item in flowInputOptions"
                        :key="item.value"
                        :label="item.value"
                    >
                        {{ item.label }}
                    </el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item
                v-if="flowInfo.flowInputType === 2"
                label="查询时间区间："
                label-width="auto"
            >
                <el-date-picker
                    v-model="dateRange"
                    type="datetimerange"
                    class="full-content"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    placeholder="查询时间区间"
                    :picker-options="pickerOptions"
                    :clearable="false"
                    @change="handleChangeDateRange"
                />
            </el-form-item>
            <!--  -->
            <div class="flow-list">
                <div
                    v-for="(flowItem, flowIndex) in flowInfo.flowList"
                    :key="flowItem.seq"
                    class="flow-item"
                >
                    <div class="emulation-form__title">
                        <div>{{ flowItem.rampName }}({{ directionMap[flowItem.direction] }})</div>
                        <div class="btn-group">
                            <div
                                v-show="flowItem.isEdit"
                                @click="handleAdd(flowIndex)"
                            >
                                添加
                            </div>
                            <div
                                v-show="flowItem.isEdit"
                                @click="handleSave(flowIndex)"
                            >
                                保存
                            </div>
                            <div
                                v-show="!flowItem.isEdit"
                                @click="handleEdit(flowIndex)"
                            >
                                编辑
                            </div>
                            <div
                                v-show="flowItem.isEdit"
                                @click="handleCancel(flowIndex)"
                            >
                                取消
                            </div>
                        </div>
                    </div>
                    <div class="car-info-list">
                        <div
                            v-for="(carInfo, carInfoIndex) in flowItem.carInfoList"
                            :key="carInfo.seq"
                            class="car-info-item"
                        >
                            <el-form-item label="流量(ve/h)：">
                                <div
                                    v-show="flowItem.isEdit"
                                    class="flow-input-number-wrapper"
                                >
                                    <el-input-number
                                        v-model="carInfo.carFlow"
                                        class="flow-input-number"
                                        :min="0"
                                    />
                                    <div
                                        class="btn-del"
                                        @click="handleDelete(flowIndex, carInfoIndex)"
                                    >
                                        删除
                                    </div>
                                </div>
                                <el-input
                                    v-show="!flowItem.isEdit"
                                    v-model="carInfo.carFlow"
                                    readonly
                                    type="number"
                                />
                            </el-form-item>
                            <el-form-item label="开始时间：">
                                <el-date-picker
                                    v-model="carInfo.startTime"
                                    class="full-content"
                                    type="datetime"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    :readonly="!flowItem.isEdit"
                                />
                            </el-form-item>
                        </div>
                    </div>
                </div>
            </div>
        </el-form>
    </div>
</template>

<script>
import {
    Form,
    FormItem,
    Radio,
    RadioGroup,
    Input,
    DatePicker,
    InputNumber,
} from 'element-ui';
import {
    flowInputOptions,
} from '../../../config';
import {ref, watch, computed, set} from 'vue';
import {cloneDeep, isEqual} from 'lodash-es';
import {messageTip, useUnit} from '@/utils';
import {flowList} from '@/api/emulation';
import dayjs from 'dayjs';
import {directionMap} from '@/config';

export default {
    components: {
        [Form.name]: Form,
        [FormItem.name]: FormItem,
        [Input.name]: Input,
        [InputNumber.name]: InputNumber,
        [Radio.name]: Radio,
        [RadioGroup.name]: RadioGroup,
        [DatePicker.name]: DatePicker,
    },
    props: {
        info: {
            type: Object,
            default: () => ({}),
        },
        // 仿真基础信息，用于生存流量配置
        emulationInfo: Object,
    },
    setup(props, {emit}) {
        const {ratio} = useUnit();

        const pickerOptions = {
            disabledDate(time) {
                const maxToday = new Date();
                maxToday.setHours(23, 59, 59, 999);
                return time.getTime() > maxToday.getTime();
            },
        };

        const dateRange = ref([]);

        const flowInfo = ref({
            flowInputType: 1,
            flowList: [],
        });
        let cacheFlowInfo = null;

        const labelWidth = computed(() => `${160 * ratio.value}px`);

        async function fetchFlowList() {
            const {flowInputType} = flowInfo.value;
            let params = {
                ...props.emulationInfo,
                flowInputType,
            };
            if (flowInputType === 2) {
                const [customFlowStartTime, customFlowEndTime] = dateRange.value;
                params = {
                    ...params,
                    customFlowStartTime,
                    customFlowEndTime,
                };
            }
            const {data} = await flowList(params);
            const {
                autoRampList,
                manualRampList,
            } = data;
            flowInfo.value.flowList = flowInputType === 1 ? autoRampList : manualRampList;
            emit('update:info', flowInfo.value);
        }

        function handleChangeDateRange(val) {
            const [startTime, endTime] = val;
            const startDayjs = dayjs(startTime);
            const endDayjs = dayjs(endTime);
            let timeDiff = endDayjs.diff(startDayjs, 'minute');
            if (timeDiff >  3 * 60 * 60) {
                dateRange.value = [];
                return messageTip('历史时间区间需要满足在三个小时以内');
            }
            dateRange.value = val;
            fetchFlowList();
        }

        function handleAdd(flowIndex) {
            const len = flowInfo.value.flowList[flowIndex].carInfoList.length - 1;
            flowInfo.value.flowList[flowIndex]?.carInfoList.push({
                seq: len - 1 || 1,
                carFlow: 0,
                startTime: '',
            });
        }

        function handleSave(flowIndex) {
            if (!cacheFlowInfo) return;
            cacheFlowInfo.flowList[flowIndex] = flowInfo.value.flowList[flowIndex];
            emit('update:info', cacheFlowInfo);
            cacheFlowInfo = cloneDeep(flowInfo.value);
            set(flowInfo.value.flowList[flowIndex], 'isEdit', false);
        }

        function handleEdit(flowIndex) {
            set(flowInfo.value.flowList[flowIndex], 'isEdit', true);
            cacheFlowInfo = cloneDeep(flowInfo.value);
        }

        function handleCancel(flowIndex) {
            flowInfo.value = cacheFlowInfo;
            set(flowInfo.value.flowList[flowIndex], 'isEdit', false);
        }

        function handleDelete(flowIndex, carInfoIndex) {
            flowInfo.value.flowList[flowIndex].carInfoList.splice(carInfoIndex, 1);
        }

        // 流量输入方式
        watch(
            () => props.emulationInfo,
            (newVal, oldVal) => {
                // 相等不处理
                if (isEqual(newVal, oldVal)) return;
                fetchFlowList();
            }
        );

        // 监听外部信息变化，修改表单信息
        watch(
            () => props.info,
            (newVal, oldVal) => {
                // 值相等不处理
                if (isEqual(newVal, oldVal)) return;
                flowInfo.value = cloneDeep(newVal);
                dateRange.value = [
                    flowInfo.value.customFlowStartTime,
                    flowInfo.value.customFlowEndTime,
                ];
            },
            {
                deep: true,
                immediate: true,
            }
        );

        return {
            flowInfo,
            labelWidth,
            flowInputOptions,
            pickerOptions,
            fetchFlowList,
            dateRange,
            handleChangeDateRange,
            directionMap,
            handleAdd,
            handleSave,
            handleEdit,
            handleCancel,
            handleDelete,
        };
    },
};
</script>

<style lang="less" scoped>
.emulation {
    &-form {
        &__title {
            position: relative;
            margin-bottom: 18px;
            padding-left: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                margin-top: -7px;
                width: 3px;
                height: 14px;
                background-color: rgba(26, 228, 255, .9);
            }
        }

        &__sub {
            margin-bottom: 32px;

            &-item {
                padding-left: 12px;
            }
        }

        /deep/ .el-form {
            &-item {
                margin-bottom: 24px;

                &__label {
                    text-align: left;
                }
            }
        }

        /deep/ .el-radio {
            &:not(:last-of-type) {
                margin-right: 18px;
            }

            &__label {
                padding-left: 4px;
            }
        }

        /deep/ .el-input__inner {
            &[type="number"] {
                &::-webkit-inner-spin-button,
                &::-webkit-outer-spin-button {
                    -webkit-appearance: none;
                    margin: 0;
                }
            }
        }

        .full-content {
            width: 100%;
        }

        .btn-group {
            display: flex;

            div {
                color: #28c282;
                cursor: pointer;
                font-size: 14px;
                user-select: none;

                &:not(:first-child) {
                    margin-left: 6px;
                }
            }
        }

        .flow-input-number {
            flex: 1;

            &-wrapper {
                display: flex;
                align-items: center;
            }
        }

        .btn-del {
            font-size: 14px;
            color: #ff6565;
            cursor: pointer;
            margin-left: 8px;
            user-select: none;
        }
    }
}
</style>