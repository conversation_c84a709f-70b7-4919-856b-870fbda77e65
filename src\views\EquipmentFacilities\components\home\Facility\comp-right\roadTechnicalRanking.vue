<template>
    <div class="roadTechnicalRanking">
        <api-table
            :api="roadGradeList"
            :columns="columns"
            :height="320"
            :pagination="false"
            :request-options="{
                listField: '',
            }"
            :request-params="{
                pageAble: true,
            }"
        >
            <template #index="{$index}">
                <div>
                    {{ $index + 1 }}
                </div>
            </template>
            <template #mqi="{row}">
                <div class="progress-mqi">
                    <progress-comp
                        show-number :number="row.mqi"
                        background-color="rgba(0, 0, 0, .3)"
                        line-color="rgba(0, 255, 114, .5)"
                        :line-width="row.mqi"
                    />
                </div>
            </template>
        </api-table>
    </div>
</template>

<script>
import {ApiTable, ProgressComp} from '@/components/Common';
import {roadGradeList} from '@/api/equipment/facilitydisplay';
import {ref} from 'vue';
import {useUnit} from '@/utils';

export default {
    name: 'roadTechnicalRanking',
    components: {
        ApiTable,
        ProgressComp,
    },
    setup() {
        const {ratio} = useUnit();
        const columns = ref([
            {
                label: '序号',
                prop: 'index',
                width: `${60 * ratio.value}px`,
                slotName: 'index',
                align: 'center',
            },
            {
                label: '道路名称',
                prop: 'roadNameStake',
                width: `${230 * ratio.value}px`,
                align: 'center',
            },
            {
                label: '技术得分',
                prop: 'mqi',
                width: `${150 * ratio.value}px`,
                align: 'center',
                slotName: 'mqi',
            },
        ]);

        return {
            roadGradeList,
            columns,
        };
    },
};
</script>

<style lang="less" scoped>
.roadTechnicalRanking {
    height: 315px;
    :deep(.cell) {
        font-size: 20px;
        text-overflow:ellipsis;/*省略号 */
        white-space:nowrap;/*溢出时不换行 */
        overflow:hidden;/*溢出时隐藏 */
    }

    :deep(thead) {
        .cell {
            font-size: 18px;
        }
    }
}
.progress-mqi {
    display: flex;
    align-items: center;

    p {
        display: block;
        width: 150px;
    }
}
</style>