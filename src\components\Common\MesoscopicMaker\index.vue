
<!-- 中观视角图层 -->
<template>
    <div
        v-if="renderDom"
        ref="marker"
        @click="onHandle"
    >
        <slot v-if="$slots.default"></slot>
        <div v-else class="marker-dom">
            <p>
                {{ info.labelName }}
                <span class="number-text">{{ info.number }}</span>
                <span>{{ info.unit }}</span>
            </p>
        </div>
    </div>
</template>
<script>
import {ref, onMounted, onBeforeUnmount, watch, computed} from 'vue';
export default {
    name: 'MesoscopicMaker',
    props: {
        // 基本信息 包括位置、名称 自定义数据 点击回调等
        info: {
            type: Object,
            default: () => null,
        },
        // 中观管理实例
        managerInstace: {
            type: Object,
            default: () => null,
        },
    },
    setup(props) {
        const marker = ref(null);
        const iconName = ref(() => props.info.pointName || 'MesoscopicMaker');
        const hasActive = computed(() => !!props.info.activeIconUrl || false);
        const renderDom = computed(() => props.info.renderDom ?? true);

        const onClick = e => {
            props.info.clickCallback && props.info.clickCallback(e);
        };

        const onMouseenter = e => {
            if (hasActive.value) {
                addIcon(true);
            }
            props.info.onMouseenter && props.info.onMouseenter(e);
        };

        const onMouseleave = e => {
            if (hasActive.value) {
                removeIcon(iconName.value + '-active');
            }
            props.info.onMouseleave && props.info.onMouseleave(e);
        };

        function addIcon(active) {
            if (!props.info.position) return;
            const Name = active ? iconName.value + '-active' : iconName.value;
            props.managerInstace.addMesoscopic(
                Name,
                props.info.position,
                {
                    iconUrl: (active ? props.info.activeIconUrl : props.info.iconUrl)
                        || 'maplayer/assets/image/base/icons/structure-icon.png',
                    labelDom: renderDom.value && !active ? marker.value : null,
                    text: props.info.text,
                    clickCallback: props.info?.clickCallback ? onClick : null,
                    onMouseenter: props.info?.onMouseenter ? onMouseenter : null,
                    onMouseleave: props.info?.onMouseleave ? onMouseleave : null,
                    customData: props.info.customData,
                    renderOrder: Infinity,
                }
            );
        };

        function removeIcon(name) {
            props.managerInstace.removeMesoscopicByName(name);
        };

        const onHandle = () => {
            props.info.clickCallback(props.info);
        };

        watch(() => props.info, (_, {pointName}) => {
            removeIcon(pointName);
            removeIcon(pointName + '-active');
            addIcon();
        }, {deep: true});

        onMounted(() => {
            addIcon();
        });

        onBeforeUnmount(() => {
            removeIcon(iconName.value);
            removeIcon(iconName.value + '-active');
        });
        return {
            marker,
            onHandle,
            renderDom,
        };
    },
};
</script>
<style lang="less" scoped>
.marker-dom {
    width: 125px;
    height: 39px;
    box-sizing: border-box;
    background: url('/maplayer/assets/image/base/mesoscopicMarker-bg.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    p {
        font-size: 14px;
        color: #fff;

        .number-text {
            color: rgb(255, 216, 0);
            display: inline-block;
        }
    }
}
</style>