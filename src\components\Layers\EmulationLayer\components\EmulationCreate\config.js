export const moduleList = [
    {
        key: 1,
        name: '基本信息',
        component: 'Base',
    },
    {
        key: 2,
        name: '流量信息',
        component: 'Flow',
    },
    {
        key: 3,
        name: '策略输入',
        component: 'Strategy',
    },
    {
        key: 4,
        name: '模型配置',
        component: 'Model',
    }];

export const evnet_location_type = [
    {'code': 1, 'name': '入口匝道'},
    {'code': 2, 'name': '出口匝道'},
    {'code': 3, 'name': '路段'},
    {'code': 4, 'name': '隧道'},
];



export const vehicle_type_list = [
    {
        code: 1,
        name: '小车',
    },
    // {
    //     code: 4,
    //     name: '面包车',
    // },
    // {
    //     code: 5,
    //     name: '货车',
    // },
    {
        code: 2,
        name: '大车',
    },
];
export const strategy_config_data = [
    {
        'controlDuration': 222,
        'controlLength': 10,
        'controlStake': 'K6+0',
        'lane': null,
        'laneControlType': null,
        'limitSpeed': '122',
        'position': '112.979754,25.291191',
        'type': 1,
        'entranceExitType': null,
        'rampName': null,
        'flowLimit': null,
        'vehicleType': null,
        'bypassRate': null,
        'groupFlow': null,
        'letGoRate': null,
    },
    {
        'controlDuration': null,
        'controlLength': 111,
        'controlStake': 'K7+100',
        'lane': '1,2',
        'laneControlType': '1,2',
        'limitSpeed': '111,233',
        'position': ';',
        'type': 2,
        'entranceExitType': null,
        'rampName': null,
        'flowLimit': null,
        'vehicleType': null,
        'bypassRate': null,
        'groupFlow': null,
        'letGoRate': null,
    },
    {
        'controlDuration': '11',
        'controlLength': null,
        'controlStake': null,
        'lane': null,
        'laneControlType': null,
        'limitSpeed': null,
        'position': null,
        'type': 3,
        'entranceExitType': '1',
        'rampName': null,
        'flowLimit': null,
        'vehicleType': null,
        'bypassRate': '50',
        'groupFlow': null,
        'letGoRate': null,
        'entryConfig': {
            controlDuration: '',
            flowLimit: '',
            vehicleType: '',
        },
        'exportConfig': {
            controlDuration: '',
            bypassRate: '',
        },
    },
];

export const default_strategy_config = [
    // {
    //     'controlDuration': 222,
    //     'controlLength': 10,
    //     'controlStake': 'K6+0',
    //     'lane': null,
    //     'laneControlType': null,
    //     'limitSpeed': '122',
    //     'position': '112.979754,25.291191',
    //     'type': 1,
    //     'entranceExitType': null,
    //     'rampName': null,
    //     'flowLimit': null,
    //     'vehicleType': null,
    //     'bypassRate': null,
    //     'groupFlow': null,
    //     'letGoRate': null,
    // },
    // {
    //     'controlDuration': null,
    //     'controlLength': 111,
    //     'controlStake': 'K7+100',
    //     'lane': '1,2',
    //     'laneControlType': '1,2',
    //     'limitSpeed': '111,233',
    //     'position': ';',
    //     'type': 2,
    //     'entranceExitType': null,
    //     'rampName': null,
    //     'flowLimit': null,
    //     'vehicleType': null,
    //     'bypassRate': null,
    //     'groupFlow': null,
    //     'letGoRate': null,
    // },
    // {
    //     'controlDuration': '11',
    //     'controlLength': null,
    //     'controlStake': null,
    //     'lane': null,
    //     'laneControlType': null,
    //     'limitSpeed': null,
    //     'position': null,
    //     'type': 3,
    //     'entranceExitType': '1',
    //     'rampName': null,
    //     'flowLimit': null,
    //     'vehicleType': null,
    //     'bypassRate': '50',
    //     'groupFlow': null,
    //     'letGoRate': null,
    //     'entryConfig': {
    //         controlDuration: '',
    //         flowLimit: '',
    //         vehicleType: '',
    //     },
    //     'exportConfig': {
    //         controlDuration: '',
    //         bypassRate: '',
    //     },
    // },
];

export const default_strategy  = {
    '1': strategy_config_data.find(item => item.type === 1),
    '2': strategy_config_data.find(item => item.type === 2),
    '3': strategy_config_data.find(item => item.type === 3),
};


export const model_list = [
    {
        label: '小车常规加速度',
        key: 'smallVehicleAccel',
        unit: 'm/s²',
    },
    {
        label: '大车常规加速度',
        key: 'bigVehicleAccel',
        unit: 'm/s²',
    },
    {
        label: '小车常规减速度',
        key: 'smallVehicleDecel',
        unit: 'm/s²',
    },
    {
        label: '大车常规减速度',
        key: 'bigVehicleDecel',
        unit: 'm/s²',
    },
    {
        label: '小车最大减速度',
        key: 'smallVehicleEmergencyDecel',
        unit: 'm/s²',
    },
    {
        label: '大车最大减速度',
        key: 'bigVehicleEmergencyDecel',
        unit: 'm/s²',
    },
    {
        label: '小车最高限速',
        key: 'smallVehicleMaxSpeed',
        unit: 'km/h',
    },
    {
        label: '大车最高限速',
        key: 'bigVehicleMaxSpeed',
        unit: 'km/h',
    },
    {
        label: '大车比例',
        key: 'bigCarRate',
        unit: '%',
    },
];