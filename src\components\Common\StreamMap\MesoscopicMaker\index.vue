

<script>
import {ref, onMounted, onBeforeUnmount, watch} from 'vue';
export default {
    props: {
        // 基本信息 包括位置、名称 自定义数据 点击回调等
        info: {
            type: Object,
            default: () => null,
        },
        // 中观管理实例
        managerInstace: {
            type: Object,
            default: () => null,
        },
    },
    setup(props) {

        const iconName = ref(() => props.info.pointName || 'MesoscopicMaker');

        const onClick = e => {
            props.info.clickCallback && props.info.clickCallback(e);
        };

        function addIcon(active) {
            if (!props.info.position) return;

            const Name = iconName.value;
            props.managerInstace.add(
                Name,
                props.info.position,
                {
                    url: props.info.iconUrl
                        || 'maplayer/assets/image/base/icons/structure-icon.png',
                    clickCallback: onClick,
                    total: props.info.number,
                    labelText: props.info.labelName,
                    customData: props.info.customData,
                }
            );
        };

        function removeIcon(name) {
            props.managerInstace.removeByName(name);
        };

        const onHandle = () => {
            props.info.clickCallback(props.info);
        };

        watch(() => props.info, (_, {pointName}) => {
            removeIcon(pointName);
            addIcon();
        }, {deep: true});

        onMounted(() => {
            addIcon();
        });

        onBeforeUnmount(() => {
            removeIcon(iconName.value);
        });
        return {
            onHandle,
        };
    },
};

</script>