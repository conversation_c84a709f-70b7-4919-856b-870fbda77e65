<template>
    <div class="range-desc">
        <div
            v-for="item in list"
            :key="`range-desc__item__${item.color}`"
            class="range-desc__item"
        >
            <div
                :style="{backgroundColor: item.color} "
                class="range-desc__color"
            ></div>
            <div class="range-desc__text">{{ item.text }}</div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        list: Array,
    },
};
</script>

<style lang="less" scoped>
.range-desc {
    position: fixed;
    right: 30px;
    bottom: 90px;

    &__item {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #fff;

        &:not(:last-of-type) {
            margin-bottom: 10px;
        }
    }

    &__color {
        width: 28px;
        height: 26px;
        border: 1px solid #fff;
    }

    &__text {
        margin-left: 10px;
    }
}
</style>