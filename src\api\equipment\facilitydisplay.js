// 设备设施设备管理接口
import {request} from '@/utils/network-helper/index';
import {baseConfig} from '@/utils/common';
const {basePrefix} = baseConfig;

const structure = basePrefix + '/v1';
const structurehealth = basePrefix + (import.meta.env.MODE === 'release' ? '/proxy/structurehealth' : '') + '/v1';

// 3.1、地图高速路线渲染
export const applySectionPath = async (sectionId = 'G0015440090') => {
    const {data, code} = await request.get(
        structure + '/section/overview/applySectionPath',
        {
            sectionId,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 3.2、显示路段线上的结构体和互通立交
export const getStructureList = async () => {
    const {data, code} = await request.get(
        structurehealth + '/facilityShow/getStructureList'
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 3.3、设施设备状态统计
export const getStructureStatus = async () => {
    const {data, code} = await request.get(
        structurehealth + '/facilityShow/getStructureStatus'
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 3.4、设施列表
export const getFacilityList = async (facilityType = 1) => {
    const {data, code} = await request.get(
        structurehealth + '/facilityShow/getFacilityList',
        {
            facilityType,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 3.5、设施告警次数
export const facilityCountAlarm = async () => {
    const {data, code} = await request.get(
        structurehealth + '/facilityShow/facilityCountAlarm'
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 3.6、风险点数量统计
export const facilityCountRisk = async (facilityType = 1) => {
    const {data, code} = await request.get(
        structure + '/facilityShow/facilityCountRisk',
        {
            facilityType,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 3.6、设施健康状态
export const facilityCountHealth = async (facilityType = 1) => {
    const {data, code} = await request.get(
        structure + '/facilityShow/facilityCountHealth',
        {
            facilityType,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 3.7、公路技术状况桥梁ICON
export const bridgeList = async (facilityType = 1) => {
    const {data, code} = await request.get(
        structurehealth + '/facilityShow/bridgeList',
        {
            facilityType,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 3.8、公路技术状况路面ICON
export const roadList = async () => {
    const {data, code} = await request.get(
        structurehealth + '/facilityShow/roadList'
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 3.10、路面评定等级列表
export const roadGradeList = async () => {
    const {data, code} = await request.get(
        structurehealth + '/facilityShow/roadGradeList'
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 3.11、桥梁评定等级列表
export const bridgeGradeList = async () => {
    const {data, code} = await request.get(
        structurehealth + '/facilityShow/bridgeGradeList'
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 3.12、查看桥梁扎点详情
export const getBridgeInfo = async bridgeId => {
    const {data, code} = await request.get(
        structurehealth + '/facilityShow/getBridgeInfo',
        {
            bridgeId,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 3.13、 查看路面扎点详情s
export const getRoadInfo = async params => {
    const {data, code} = await request.post(
        structurehealth + '/facilityShow/getRoadInfo',
        params
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 3.14、道路清洁度路面渲染
export const roadShow = async () => {
    const {data, code} = await request.get(
        structurehealth + '/facilityShow/roadShow'
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 3.15、道路清洁度洒落物统计
export const scatteredCount = async () => {
    const {data, code} = await request.get(
        structurehealth + '/facilityShow/scatteredCount'
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 3.16、道路清洁度等级列表
export const roadCleanList = async () => {
    const {data, code} = await request.get(
        structurehealth + '/facilityShow/roadCleanList'
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 3.17、道路清洁度等级详情
export const roadCleanInfo = async id => {
    const {data, code} = await request.get(
        structurehealth + '/facilityShow/roadCleanInfo',
        {id}
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 3.19、设施设备数量统计展示
export const facilityNum = async () => {
    const {data, code} = await request.get(
        structure + '/facilityShow/facilityNum'
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 3.20、各设施类型数量统计
export const facilityTypeNum = async () => {
    const {data, code} = await request.get(
        structurehealth + '/facilityShow/facilityTypeNum'
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 3.21、机电养护数量统计
export const systemDeviceCount = async () => {
    const {data, code} = await request.get(
        structure + '/facilityShow/systemDeviceCount'
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 3.22、机电养护查看详情
export const syStemCount = async systemId => {
    const {data, code} = await request.get(
        structure + '/facilityShow/syStemCount',
        {systemId}
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 3.23、互通立交设备在线统计
export const getInterchangeStatus = async () => {
    const {data, code} = await request.get(
        structure + '/facilityShow/getInterchangeStatus'
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};