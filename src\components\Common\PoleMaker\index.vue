<!-- 设备设施扎点 -->
<template>
    <div class="pole-marker">
        <div
            ref="marker"
            class="marker-container"
            @click="onHandle"
        >
            <slot v-if="$slots.default"></slot>
            <template v-else>
                <div
                    class="marker-dom" :class="statusClassName"
                >
                    <p>{{ info.labelName }}</p>
                </div>
            </template>
        </div>
    </div>
</template>
<script>
import {ref, onMounted, onBeforeUnmount, computed} from 'vue';
export default {
    props: {
        // 基本信息 包括位置、名称 自定义数据 点击回调等
        info: {
            type: Object,
            default: () => null,
        },
        // 标杆扎点实例
        managerInstace: {
            type: Object,
            default: () => null,
        },
    },
    setup(props, {slots}) {
        const marker = ref(null);
        const iconName = ref(null);

        const status = computed(() => {
            return props.info?.status || 'green';
        });
        const size = computed(() => {
            return props.info?.size || 'small';
        });
        const active = computed(() => {
            return props.info?.active || false;
        });
        const statusClassName = computed(() => {
            return active.value ? status.value + '_active' : status.value;
        });
        const addIcon = () => {
            if (!props.info.position) {
                return;
            }
            if (!props.managerInstace || !marker.value) {
                return;
            }
            iconName.value = props.info.pointName;
            props.managerInstace.addPole(
                iconName.value,
                props.info.position,
                {
                    size: size.value,
                    status: status.value,
                    labelDom: marker.value,
                    iconName: props.info?.status,
                    bubbleColor: props.info?.bubbleColor,
                    customData: props.info?.customData,
                    clickCallback: props.info.clickCallback,
                }
            );
        };

        const removeIcon = () => {
            iconName.value && props.managerInstace.removePoleByName(iconName.value);
        };

        const onHandle = () => {
            props.info.clickCallback && props.info.clickCallback(props.info);
        };

        onMounted(() => {
            addIcon();
        });

        onBeforeUnmount(() => {
            removeIcon();
        });
        return {
            marker,
            statusClassName,
            onHandle,
        };
    },
};
</script>

<style lang="less" scoped>
@import url('@/assets/css/maker.less');

.marker-container {
    width: max-content;
    height: max-content;

    .marker-dom {
        width: max-content;
        height: max-content;
        box-sizing: border-box;
        border-radius: 2px;
        padding: 4px 12px;
        position: relative;
        white-space: pre;
        cursor: pointer;
        user-select: none;

        &.green {
            background: url('/maplayer/assets/image/pole_facilitie/label_green.png') no-repeat;
            background-size: 101% 100%;
            background-position: center;
        }

        &.red {
            background: url('/maplayer/assets/image/pole_facilitie/label_red.png') no-repeat;
            background-size: 101% 100%;
            background-position: center;
        }

        &.red_active {
            background: url('/maplayer/assets/image/pole_facilitie/label_red_active.png') no-repeat;
            background-size: 101% 100%;
            background-position: center;
        }

        p {
            font-size: 14px;
            color: #fff;
            line-height: 20px;
        }
    }
}
</style>