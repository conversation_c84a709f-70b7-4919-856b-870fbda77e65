import {ref, computed} from 'vue';
import {useResizeObserver} from '@vueuse/core';

const main = (dom, percent) => {
    const height = ref(0);
    useResizeObserver(dom, entries => {
        const entry = entries[0];
        const {height: h} = entry.contentRect;
        height.value = h * percent;
    });

    return {
        height: computed(() => height.value),
        px: computed(() => height.value + 'px'),
    };
};

export default main;