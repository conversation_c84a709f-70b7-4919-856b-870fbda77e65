<template>
    <div class="toll-station-warn">
        <bubble-marker
            v-if="showWarnMarker"
            bubble-type="diamond"
            bubble-color="#FF4B4B"
            show-label="true"
            icon-name="yongdu"
            :manager-instace="domManager"
            :info="{
                position: warnInfo.position,
                label: '收费站拥堵',
                clickCallback: handleShowModal,
            }"
        >
            <modal-card
                class="warn-modal"
                title="收费站拥堵告警"
                :visible.sync="visible"
                :fullscreen-center="false"
                :show-foot="false"
                @close="handleClose"
                @confirm="handleConfirm"
            >
                <div class="warn-modal-content">
                    <div>{{ warnInfo.stationName }}</div>
                    <div class="toll-station-info-list">
                        <div class="toll-station-info">
                            <span>收费站位置：</span>
                            <span>{{ warnInfo.stake }}</span>
                        </div>
                        <div class="toll-station-info">
                            <span>所在高速：</span>
                            <span>{{ warnInfo.sectionCode }}（{{ warnInfo.sectionName }}）</span>
                        </div>
                        <div class="toll-station-info">
                            <span>控制方向：</span>
                            <span>{{ warnInfo.directionName }}</span>
                        </div>
                        <div class="toll-station-info">
                            <span>拥堵里程：</span>
                            <span>{{ warnInfo.blockBistance }}M</span>
                        </div>
                        <div class="toll-station-info">
                            <span>更新时间：</span>
                            <span>{{ warnInfo.blockTime }}mins</span>
                        </div>
                    </div>
                </div>
            </modal-card>
        </bubble-marker>
    </div>
</template>

<script setup>
import {BubbleMarker, ModalCard} from '@/components/Common';
import {computed, ref} from 'vue';
import {
    domManager,
} from '@/views/TollStation/utils';
import {getTollJam} from '@/api/tollStation';
import {directionMap} from '@/config';
import {useIntervalFn} from '@vueuse/core';
import {viewToMicro} from '@/utils';
import {tollStation} from '@/views/TollStation/store';

const visible = ref(true);
// 告警扎点信息
const warnInfo = ref({});
// 是否主动关闭
const isActiveClose = ref(false);
// 两分钟
const POLL_GAP_TIME = 60 * 1000 * 2;

const showWarnMarker = computed(() => warnInfo.value.type);

function handleShowModal() {
    visible.value = true;
}

function handleClose() {
    isActiveClose.value = true;
}

async function fetchTollStationWarnStatus() {
    const {data} = await getTollJam({
        stationId: tollStation.value.stationId,
        stationName: tollStation.value.stationName,
    });
    warnInfo.value = {
        ...data,
        position: [data.lng, data.lat, data.alt || 30],
        directionName: directionMap[data.direction],
    };
    if (visible.value) return;
    visible.value = isActiveClose.value ? false : data.type;
    if (visible.value) {
        viewToMicro(warnInfo.value?.position);
    }
}

useIntervalFn(
    fetchTollStationWarnStatus,
    POLL_GAP_TIME,
    {
        immediateCallback: true,
        immediate: true,
    }
);
</script>

<style lang="less" scoped>
.toll-station {
    &-info {
        height: 36px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: rgba(0, 98, 167, .15);
        font-size: 16px;
        color: rgb(231, 239, 255);
        padding: 0 12px;

        &:not(:first-child) {
            margin-top: 4px;
        }
    }
}

</style>