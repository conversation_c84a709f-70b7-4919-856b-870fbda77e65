<template>
    <div>
        <BubbleMarker
            v-for="vidicon in gantryList"
            :key="vidicon.key"
            :info="vidicon"
            bubble-color="rgb(157, 0, 240)"
            icon-name="menjia"
            icon-color="#fff"
            :manager-instace="domManager"
        />
    </div>
</template>

<script setup>
import {BubbleMarker} from '@/components/Common';
import {getTollgantry} from '@/api/tollStation';
import {tollStation} from '@/views/TollStation/store';
import {onMounted, ref} from 'vue';
import {domManager} from '@/views/TollStation/utils';
import {viewToPoint} from '@/utils';

const gantryList = ref([]);

async function fetchData() {
    const {data} = await getTollgantry({
        stationId: tollStation.value.stationId,
        stationName: tollStation.value.stationName,
    });
    gantryList.value = data.map(item => ({
        position: [item.lng, item.lat, item.alt],
        sectionName: item.sectionName,
        key: item.tollGantryId,
        name: item.tollGantryName,
        clickCallback: () => {
            viewToPoint([item.lng, item.lat, item.alt]);
        },
    }));
}

onMounted(() => {
    fetchData();
});

</script>