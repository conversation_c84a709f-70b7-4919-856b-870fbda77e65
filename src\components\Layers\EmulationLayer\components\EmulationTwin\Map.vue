<template>
    <div style="position: relative; width: 100%; height: 100%;">
        <div ref="map_container" style="width: 100%; height: 100%;"></div>
    </div>
</template>

<script>
import {
    Engine,
    Default3DTiles,
    DynamicSky,
    gltfLoaderEnhanced,
} from '@baidu/mapv-three';
export default {
    data() {
        return {
            map: null,
        };
    },
    props: {
        options: {
            type: Object,
            default: () => ({}),
        },
    },
    methods: {
        // 添加天空
        addSky() {
            this.sky && this.map?.remove(this.sky);
            this.sky = this.map.add(new DynamicSky());
            this.sky.time = 3600 * 12;
        },
        // 加载3D高精路面
        initAssets() {
            const _gltfLoader = gltfLoaderEnhanced;
            const tiles = this.map.add(
                new Default3DTiles({
                    url: import.meta.env.VITE_BMAP_URL
                        || 'https://gjdt.private.gdcg.cn/twin/data/hdmap/guangdong_gcj02/tiles-box-plane/top.json',
                    loaders: [[/\.gltf$/, _gltfLoader]],
                })
            );
            tiles.position.z = -0.5;
            this.map.event.bind(tiles, 'click', async e => {
                this.$emit('clickMap', e);
            });
        },
        // 初始化地图
        initMap() {
            if (!this.$refs.map_container) {
                return;
            }
            const engine = new Engine(this.$refs.map_container, {
                rendering: {
                    enableAnimationLoop: true,
                },
                map: {
                    is3DControl: true, // 移动端可控制
                },
            });
            this.options?.center && engine.map.setCenter(this.options.center);
            engine.map.setZoom(14);
            engine.map.setHeading(190);
            engine.map.setPitch(70);

            engine.rendering.shadow.enabled = true;
            engine.rendering.enableAnimationLoop = true;

            this.map = engine;
            // 加载天空
            this.addSky();

            // 加载3D 高精路面
            this.initAssets();

            this.$emit('mapLoaded', engine);
        },
    },
    mounted() {
        this.initMap();
    },
    beforeDestroyed() {
        this.removeSatelliteMap();
    },
};
</script>