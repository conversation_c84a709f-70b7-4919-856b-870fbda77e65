import {
    addIcon,
    removeIcon,
    removeLabel,
    addText,
    removeText,
    addDOMOverlay,
    removeDOMOverlay,
} from '../index';
import EventCenter from './event.js';

const removeMap = {
    Icon: removeIcon,
    Label: removeLabel,
    Text: removeText,
    DomOverlay: removeDOMOverlay,
};

// 支持绑定的事件
const eventNameEnum = {
    click: 'clickCallback',
    mouseenter: 'onMouseenter',
    mouseleave: 'onMouseleave',
};

// 宏观图层管理器
class MacroManager extends EventCenter {
    constructor(engine) {
        super(eventNameEnum);
        this.macroMap = new Map();
        this.engine = engine;
    }
    /**
     * 添加气泡
     *
     * @param name {string}
     * @param point {Array} 经纬度
     * @param options {Object} 配置项
     */
    async addBubble(name, point, options = {}) {
        this.options = options;
        if (this.macroMap.has(name)) {
            this.removeBubbleByName(name);
        }

        const {
            text,
            customData,
            labelDom,
            iconUrl = 'maplayer/assets/image/base/icons/normal.png',
        } = options;
            // 气泡点
        // let {bubble} = addBubble(point, {
        //     _engine: this.engine,
        // });

        const map = {};

        let {icon, _engine} = addIcon(point, iconUrl, {
            width: 180,
            height: 110,
            offset: [0, 0],
            customData,
            _engine: this.engine,
        });

        let {_text} = addText(point, text, {
            _engine: this.engine,
        });

        if (labelDom) {
            const {domOverlay} = addDOMOverlay(point, labelDom, {
                _engine: this.engine,
                offset: [0, -54],
            });
            map.DomOverlay = domOverlay;
        }

        map.Icon = icon;
        map.Text = _text;

        // 绑定事件
        this.bind(icon);
        this.macroMap.set(name, map);
    }
    /**
     * 移除指定名称的宏定义及其相关引用。
     * @param name - 宏定义名。
     */
    removeBubbleByName(name) {
        const macro = this.macroMap.get(name);
        if (!macro) return;
        this.unbind(macro.Icon);
        Object.keys(macro).forEach(key => {
            const remove = removeMap[key];
            remove(macro[key]);
        });
        this.macroMap.delete(name);
    }

    clear() {
        [...this.macroMap.keys()].forEach(macro => {
            this.removeBubbleByName(macro);
        });
        this.macroMap.clear();
    }
}

export {
    MacroManager,
};