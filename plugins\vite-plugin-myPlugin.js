export default {
    name: 'myPlugin',
    // apply: 'build', // 仅在构建时生效
    // enforce: 'post',
    transform(code, id) {
        if (!code.includes('element-ui')) return;
        if (/\.vue$/.test(id)) {
            const rootElementRegex = /<template>([\s\S]*?)<\/template>/g;
            const match = code.match(rootElementRegex);
            if (!match) return code;
            const str = match[0].replace('<template>', '').replace('</template>', '')?.match(/<.*>/)?.[0];
            if (!str) return code;
            let after = '';
            if (str.includes('class="')) {
                const classRegex = /class="([\s\S]*?)"/g;
                const matchClass = str.match(classRegex);
                if (matchClass?.[0]?.includes('apaas-maplayer')) return code;
                after = str.replace('class="', 'class="apaas-maplayer ');
            }
            else if (str.split(' ').length > 1) {
                if (str.includes('<!--') || str.includes('<template') || str.includes('<slot')) return code;
                after = str.replace(str.split(' ')[0], `${str.split(' ')[0]} class="apaas-maplayer"`);
            }
            code = after ? code.replace(str, after) : code;
        }
        return {
            code,
            map: null,
        };
    },
};