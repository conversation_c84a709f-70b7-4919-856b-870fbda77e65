<!-- 四维仿真图层-突发事件列表 -->
<template>
    <div class="emulation-layer-event-list">
        <div class="header">
            <div
                v-for="item in getTableTitle"
                :key="item.key"
                :class="{
                    'title-item': true,
                    'active-item': curTab === item.key,
                }"
                @click="onTab(item.key)"
            >
                {{ item.title }}
            </div>
        </div>
        <div class="content">
            <div class="search">
                <el-select
                    v-if="isEvent"
                    v-model="eventType"
                    placeholder="请选择事件类型"
                    popper-class="emulation-el-popper"
                    class="w-169 h-40 mr-8"
                    clearable
                >
                    <el-option
                        v-for="item in burst_event_type_list.slice(0, 3)"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code"
                    />
                </el-select>
                <el-date-picker
                    v-model="dateRange"
                    class="f-1 h-40"
                    type="daterange"
                    value-format="yyyy-MM-dd"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    popper-class="emulation-el-popper"
                />
            </div>
            <template>
                <content-event
                    :module-type="curTab"
                    :event-type="eventType"
                    :date-range="dateRange"
                    :emulation-type="emulationType"
                />
            </template>
        </div>
    </div>
</template>
<script>
import {Select, Option, DatePicker} from 'element-ui';
import {eventTypeList, burst_event_type_list} from '@EmulationLayer/config';
import {tabTitle, constructionTableTitle} from './config';
import ContentEvent from './Event.vue';

export default {
    name: 'EventList',
    inject: ['engine'],
    props: {
        emulationType: {
            type: [String, Number],
            default: 1,
        },
    },
    computed: {
        engineMap() {
            return this.engine();
        },
        getTableTitle() {
            const key = this.emulationType;
            const map = {
                1: tabTitle,
                3: constructionTableTitle,
            };
            return map[key];
        },
        isEvent() {
            return this.emulationType === 1;
        },
    },
    components: {
        ContentEvent,
        [Select.name]: Select,
        [Option.name]: Option,
        [DatePicker.name]: DatePicker,
    },
    data() {
        return {
            burst_event_type_list,
            tabTitle,
            curTab: 1,
            eventType: '',
            dateRange: '',
            eventTypeList,
        };
    },
    methods: {
        onTab(val) {
            if (this.$children[0].isEdit) {
                this.$message.warning('请先退出编辑状态');
                return;
            }
            this.curTab = val;
        },
    },
    watch: {
        emulationType() {
            this.curTab = 1;
        },
    },
    mounted() {
    },
};
</script>
<style lang='less' scoped>
.emulation-layer-event-list {
    width: 100%;
    // position: absolute;
    // top: 120px;
    // left: 114px;
    // z-index: 2;
    display: flex;
    flex-direction: column;

    .header {
        height: 48px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px;
        background: rgba(32, 72, 63, .6);
        border-radius: 2px;
        cursor: pointer;

        .title-item {
            width: 197px;
            height: 40px;
            font-family: 'FZLTZHJW--GB1-0', sans-serif;
            font-size: 16px;
            color: #93b4b7;
            display: flex;
            justify-content: center;
            align-items: center;

            &.active-item {
                background: #07af87;
                border: 1px solid #08d6a5;
                color: #f9fbff;
            }
        }
    }

    .content {
        margin-top: 12px;
        flex: 1;
        overflow: hidden;
        padding-bottom: 30px;

        .search {
            display: flex;

            /deep/ .el-date-editor {
                height: 40px;
            }

            /deep/ .el-input__inner::placeholder {
                color: #fff !important;
            }
        }
    }
}
</style>