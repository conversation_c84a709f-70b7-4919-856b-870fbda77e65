<template>
    <collapse-panel
        direction="right"
        :collapse="collapse"
        @collapse="$emit('update:collapse', !collapse)"
    >
        <div class="panel-content">
            <el-row :gutter="24 * ratio" class="card-row ">
                <el-col
                    v-for="card in cardList"
                    :key="card.key"
                    :span="card.span"
                >
                    <component :is="card.component" :key="card.key"/>
                </el-col>
            </el-row>
        </div>
    </collapse-panel>
</template>

<script setup>
import {CollapsePanel} from '@/components/Common';
import {Row as ElRow, Col as ElCol} from 'element-ui';
import {useUnit} from '@/utils';
import DeviceStatusCard from '../../Card/DeviceStatusCard/index.vue';
import GreenTrainCard from '../../Card/GreenTrainCard/index.vue';

defineProps({
    collapse: Boolean,
});


const {ratio} = useUnit();

const cardList = [
    {component: DeviceStatusCard, key: 'DeviceStatusCard', span: 24},
    {component: GreenTrainCard, key: 'GreenTrainCard', span: 24},
];

</script>

<style lang="less" scoped>
.card-row {
    /deep/ &.el-row {
        > .el-col:not(:last-child) {
            margin-bottom: 24px;
        }
    }
}

.panel-content {
    width: 1232px;
}
</style>