import path from 'path';
import {terser} from 'rollup-plugin-terser';
import theme from '../theme';
import {LayersName, externalPackages, inputConfig, splitChunkNames} from './common';

const isLayerChunk = name => {
    let flage = false;
    let layerName = null;
    LayersName.forEach(item => {
        if (name.includes(item)) {
            flage = true;
            layerName = item;
        }
    });
    return {
        flage,
        layerName,
    };
};

export const baseConfig = () => {
    return {
        build: {
            target: 'es2015', // 构建目标
            sourcemap: false, // 是否生成源码映射文件
            lib: {
                entry: path.resolve('./', 'src/index.js'),
                name: 'apaas-maplayer',
                formats: ['es'],
                fileName: format => 'index.js',
            },
            css: {
                preprocessorOptions: {
                    less: {
                        modifyVars: Object.assign({}, theme),
                        javascriptEnabled: true,
                    },
                },
            },
            fileSizeLimit: 819200, // 文件大小限制
            rollupOptions: {
                external: externalPackages,
                input: {
                    // 主包
                    index: path.resolve('./', 'src/index.js'),
                    mapUtils: path.resolve('./', 'src/utils/map/index.js'),
                    // 分包
                    ...inputConfig,
                },
                output: {
                    plugins: [terser({
                        compress: {
                            pure_funcs: ['console.log', 'debugger'], // 去除console.log debugger
                        },
                    })],
                    entryFileNames: ({name}) => {
                        // 分包
                        if (splitChunkNames.includes(name)) {
                            return `packages/${name}.js`;
                        }
                        // 主包
                        if (name === 'index') {
                            return `${name}.js`;
                        }
                        // 工具函数
                        if (name === 'mapUtils') {
                            return 'mapUtils/index.js';
                        }
                        return `${name}.js`;
                    },
                    chunkFileNames: '[name]', // 动态引入的文件名
                    manualChunks: () => 'common.js', // 将所有模块打包到一个 chunk 文件中
                    // manualChunks: url => {
                    //     if (url.includes('node_modules')) {
                    //         return 'common/vendor.js';
                    //     }
                    //     else if (isLayerChunk(url).flage) {
                    //         return `common/layers/${isLayerChunk(url).layerName}.js`;
                    //     }
                    //     return 'common/main.js';
                    // },
                },
            },
        },
    };
};