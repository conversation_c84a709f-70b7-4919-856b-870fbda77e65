<template>
    <el-form
        class="emulation-form"
        :model="info"
        size="small"
        :label-width="labelWidth"
    >
        <el-form-item
            v-for="item in formScheme"
            :key="item.field"
            :label="`${item.label}：`"
        >
            <el-input
                v-model="info[item.field]"
                type="number"
                class="full-content"
                :placeholder="`请输入${item.label}`"
            >
                <template #suffix>{{ item.unit }}</template>
            </el-input>
        </el-form-item>
    </el-form>
</template>

<script>
import {
    Form,
    FormItem,
    Input,
} from 'element-ui';
import {computed} from 'vue';
import {useUnit} from '@/utils';

export default {
    components: {
        [Form.name]: Form,
        [FormItem.name]: FormItem,
        [Input.name]: Input,
    },
    props: {
        info: {
            type: Object,
            default: () => ({}),
        },
    },
    setup() {
        const {ratio} = useUnit();

        const formScheme = [

            {
                label: '小车常规加速度',
                field: 'smallVehicleAccel',
                unit: 'm/s²',
            },
            {
                label: '大车常规加速度',
                field: 'bigVehicleAccel',
                unit: 'm/s²',
            },
            {
                label: '小车常规减速度',
                field: 'smallVehicleDecel',
                unit: 'm/s²',
            },
            {
                label: '大车常规减速度',
                field: 'bigVehicleDecel',
                unit: 'm/s²',
            },
            {
                label: '小车最大减速度',
                field: 'smallVehicleEmergencyDecel',
                unit: 'm/s²',
            },
            {
                label: '大车最大减速度',
                field: 'bigVehicleEmergencyDecel',
                unit: 'm/s²',
            },
            {
                label: '小车最高限速',
                field: 'smallVehicleMaxSpeed',
                unit: 'km/h',
            },
            {
                label: '大车最高限速',
                field: 'bigVehicleMaxSpeed',
                unit: 'km/h',
            },
            {
                label: '大车比例',
                field: 'bigCarRate',
                unit: '%',
            },
        ];

        const labelWidth = computed(() => `${160 * ratio.value}px`);

        return {
            labelWidth,
            formScheme,
        };
    },
};
</script>

<style lang="less" scoped>
.emulation {
    &-form {
        /deep/ .el-form {
            &-item {
                margin-bottom: 24px;

                &__label {
                    text-align: left;
                }
            }
        }

        /deep/ .el-input__inner {
            &[type="number"] {
                &::-webkit-inner-spin-button,
                &::-webkit-outer-spin-button {
                    -webkit-appearance: none;
                    margin: 0;
                }
            }
        }

        .full-content {
            width: 100%;
        }
    }
}
</style>