<template>
    <div class="table">
        <!-- <div v-if="length" class="chose-num">
            <p>已选择<span>{{ length }}</span>项</p>
            <p class="link" @click.stop="exportExcel">
                <i class="el-icon-download"></i>
                批量导出
            </p>
        </div> -->
        <el-table
            :data="data" class="maintain-table"
            style="width: 100%;" :height="767 * ratio"
            @selection-change="handleSelectionChange"
        >
            <el-table-column
                type="selection" width="55"
                align="center"
            />
            <el-table-column
                v-for="col in column" :key="col.name"
                :prop="col.prop" :label="col.label"
                v-bind="col"
            />
            <el-table-column
                label="操作" width="180"
                :resizable="false" align="center"
            >
                <template slot-scope="scope">
                    <el-button
                        v-if="!scope.row.pushStatus"
                        type="text" size="small"
                        style="color: rgb(1, 255, 229);"
                        @click="handleEdit(scope.row)"
                    >
                        {{ buttonTitle }}
                    </el-button>
                    <!-- <el-button
                        v-else
                        type="text" size="small"
                        class="push-status"
                        @click="handle(scope.row)"
                    >
                        <span :style="{backgroundColor: scope.row.pushStatus === '成功' ? '#4DE800' : '#FF5555'}">
                        </span>{{ scope.row.pushStatus }}
                    </el-button> -->


                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            :current-page="pages.pageNumber" :page-sizes="[5, 10, 15, 20]"
            :page-size="pages.pageSize"
            layout="total, prev, pager, next, sizes, jumper" :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
    </div>
</template>

<script>
import {Table, TableColumn, Loading, Pagination} from 'element-ui';
import {ref, nextTick, watch} from 'vue';
import {useRoute, useRouter} from '@/utils';

import {useUnit} from '@/utils/hooks/useUnit';
export default {
    props: {
        data: {
            type: Array,
            default: () => ([]),
        },
        column: {
            type: Array,
            default: () => ([]),
        },
        buttonTitle: {
            type: String,
            default: '事件详情',
        },
        loading: {
            type: Boolean,
            default: false,
        },
        pages: {
            type: Object,
            default: () => ({}),
        },
        total: {
            type: Number,
            default: 0,
        },
    },
    components: {
        [Table.name]: Table,
        [TableColumn.name]: TableColumn,
        [Pagination.name]: Pagination,
    },
    setup(props, {emit}) {
        const router = useRouter();
        const length = ref(0);
        const deviceIdList = ref([]);
        const handleSelectionChange = e => {
            length.value = e.length;
            deviceIdList.value = e.map(item => item.deviceId);
        };
        const {ratio} = useUnit();

        const tableLoading = ref(null);

        // 加载动画
        watch(() => props.loading, val => {
            if (val === false) tableLoading.value?.close();
            else {
                nextTick(() => {
                    tableLoading.value = Loading.service({
                        target: '.maintain-table',
                        background: 'rgba(255, 255, 255, .3)',
                    });
                });
            }
        }, {immediate: true});

        // 分页器发生改变时
        const handleSizeChange = e => {
            emit('size-change', e);
        };
        const handleEdit = e => {
            router.push({name: 'CarDetail', params: {id: e.id, info: e}});
            console.log('output->handleEdit', e);
            emit('edit-change', e);
        };
        const handle = e => {
            console.log('output->handleEdit', e);
        };
        const handleDelete = e => {
            console.log('output->handleDelete');
            emit('delet-change', e);
        };
        const handleCurrentChange = e => {
            emit('current-change', e);
        };
        // const handleDelete =    async (row) =>{
        //     this.$confirm('确定删除？', '提示', {
        //         confirmButtonText: '确定',
        //         cancelButtonText: '取消',
        //         type: 'warning',
        //     })
        //         .then(async () => {
        //             const { id } = row;
        //             const params = { id };
        //             const data = await deleteDevice(params);
        //             if (data && data.flag) {
        //                 this.$message.success('操作成功');
        //                 this.paginationInit();
        //                 this.getTableData();
        //             }
        //             else {
        //                 this.$message.error(data && data.msg ? data.msg : '操作失败');
        //             }
        //         })
        //         .catch(() => {
        //             this.$message({
        //                 type: 'info',
        //                 message: '已取消删除',
        //             });
        //         });
        // },
        // 点击导出按钮
        const exportExcel = () => {
            emit('export-excel', deviceIdList.value);
        };

        return {
            length,
            ratio,
            handleDelete,
            handleEdit,
            handle,
            handleSelectionChange,
            handleSizeChange,
            handleCurrentChange,
            exportExcel,
        };
    },
};
</script>

<style lang="less" scoped>
.table {
    .chose-num {
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 48px;
        padding: 0 16.5px;
        font-family: 'OPluSans';
        font-size: 14px;
        font-weight: 400;
        color: rgba(255, 255, 255, .8);
        border: 1px solid rgba(255, 255, 255, .6);
        background-color: rgba(255, 255, 255, .1);
        margin-bottom: 16px;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: rgba(40, 194, 130, .4);
        }

        span {
            margin: 0 8px;
            font-weight: 500;
            color: rgba(255, 255, 255, 1);
        }

        .link {
            color: rgb(0, 255, 149);
            cursor: pointer;
        }
    }

    :deep(.el-pagination) {
        text-align: right;
        margin-top: 10px;

        button,
        .el-pager li,
        input {
            background-color: rgba(255, 255, 255, .1);
            font-family: 'OPlusSans';
        }

        .el-pagination__jump {
            margin-left: 0;
        }

        input {
            border: none;
        }

        button {
            color: #fff;
        }

        .el-pager li {
            color: rgba(255, 255, 255, .6);

            &.active {
                background-color: rgba(255, 255, 255, .2);
                color: #fff;
            }
        }
    }

    :deep(.el-loading-mask) {
        circle {
            stroke: #fff !important;
        }
    }

    :deep(.el-table) {
        background: transparent !important;
        color: rgba(255, 255, 255, .6);
        overflow: visible;

        .el-table__row:hover {
            cursor: pointer;
        }

        .el-table__row {
            td {
                border-top: 0;
            }
        }

        .el-table__body-wrapper {
            scrollbar-gutter: stable both-edges;
            flex: 1;
            overflow-y: auto !important;
            overflow-x: hidden !important;
        }

        td {
            border-bottom: 1px solid rgba(196, 196, 196, .4);
        }

        .cell {
            height: 32px;
            display: flex;
            align-items: center;

            .btn {
                color: #ffd6d3;
                font-size: 12px;
                cursor: pointer;
                transition: filter .3s;

                &:hover {
                    filter: contrast(1.2);
                }

                &:active {
                    filter: contrast(1.5);
                }
            }
        }

        &::before,
        .el-table__fixed-right::before {
            content: '';
            display: none;
        }

        thead {
            height: 48px;
            font-size: 16px;
            color: rgba(255, 255, 255, .6);
            font-weight: 400;
            font-family: 'OPlusSans';
            background: rgba(255, 255, 255, .1);

            .cell {
                height: 100%;
                line-height: 48px;
            }
        }

        tr {
            background: transparent;
        }

        .el-table__cell {
            background: transparent !important;
            height: 32px !important;
            padding: 0 !important;
        }

        .el-table__body-wrapper {
            overflow: visible;
        }

        .el-table__row {
            position: relative;
            height: 50px;
            border: 1px solid red !important;

            .cell {
                height: 100%;
                line-height: 50px;
            }
        }

        .el-table__body {
            border-collapse: separate;
            border-spacing: 0 8px;

            tr {
                border: 1px solid rgba(255, 255, 255, .15);
            }

            .el-table_1_column_1 .cell {
                height: 100%;
                display: flex;
                align-items: center;
            }
        }

        .el-table__empty-block {
            height: calc(100% - 8px) !important;

            .el-table__empty-text {
                color: #e7efffb4;
            }
        }

        .push-status {
            span {
                display: inline-block;
                margin-right: 6px;
                width: 6px;
                height: 6px;
                border-radius: 50%;
            }
        }
    }

    .deletButton {
        margin-left: 12px;
    }
}
</style>
