<template>
    <div class="plan-flow-item">
        <div class="flow-item-name" :class="'flow-item-' + data.status">
            <div class="flow-name">{{ data.flowName }}</div>
        </div>
        <div class="flow-info-box">
            <div
                v-for="(item, index) in flowInfoConfig"
                :key="index"
                class="flow-info-item"
            >
                <div class="flow-info-label">{{ item.name }}:</div>
                <div
                    class="flow-info-text"
                    :style="{
                        color: item.colorMap && getColor(data[item.colorKey], item.colorMap),
                    }"
                >
                    {{ data[item.key] || '-' }}
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {ref} from 'vue';

const flowInfoConfigMap = {
    1: [
        {
            name: '创建人',
            key: 'operator',
        },
        {
            name: '养护类别',
            key: 'maintainTypeText',
        },
        {
            name: '创建时间',
            key: 'createTime',
        },
        {
            name: '计划开始时间',
            key: 'planStartTime',
        },
        {
            name: '计划结束时间',
            key: 'planEndTime',
        },
        {
            name: '计划封闭车道',
            key: 'closeLane',
        },
        {
            name: '养护计划施工路段',
            key: 'maintainRoadName',
        },
    ],
    2: [
        {
            name: '调整人',
            key: 'operator',
        },
        {
            name: '调整时间',
            key: 'adjustTime',
        },
        {
            name: '养护类别',
            key: 'maintainTypeText',
        },
        {
            name: '计划开始时间',
            key: 'planStartTime',
        },
        {
            name: '计划结束时间',
            key: 'planEndTime',
        },
        {
            name: '计划封闭车道',
            key: 'closeLane',
        },
        {
            name: '养护计划施工路段',
            key: 'maintainRoadName',
        },
    ],
    3: [
        {
            name: '审核人',
            key: 'operator',
        },
        {
            name: '审核时间',
            key: 'auditTime',
        },
        {
            name: '审核状态',
            key: 'auditStatusText',
            colorKey: 'auditStatus',
            colorMap: {
                0: 'rgb(52, 146, 255)',
                1: 'rgb(222, 248, 172)',
                2: 'rgb(255, 132, 25)',
            },
        },
    ],
    4: [
        {
            name: '审核人',
            key: 'operator',
        },
        {
            name: '审核时间',
            key: 'auditTime',
        },
        {
            name: '审核状态',
            key: 'auditStatusText',
            colorKey: 'auditStatus',
            colorMap: {
                0: 'rgb(52, 146, 255)',
                1: 'rgb(222, 248, 172)',
                2: 'rgb(255, 132, 25)',
            },
        },
    ],
    5: [
        {
            name: '计划执行状态',
            key: 'executeStatusText',
            colorKey: 'executeStatus',
            colorMap: {
                0: 'rgb(52, 146, 255)',
                1: 'rgb(255, 132, 25)',
                2: 'rgb(222, 248, 172)',
            },
        },
        {
            name: '计划实际执行时间',
            key: 'executeTime',
        },
        {
            name: '是否按计划执行',
            key: 'executePlanText',
        },
    ],
};

export default {
    name: 'PlanFlowItem',
    components: {
    },
    props: {
        data: {
            type: Object,
            default: () => null,
        },
    },
    setup(props, {emit}) {
        const flowInfoConfig = ref(flowInfoConfigMap[props.data.status]);

        function getColor(key, map) {
            return map[key];
        }

        return {
            flowInfoConfig,
            getColor,
        };
    },
};
</script>

<style lang="less" scoped>
/* 组件样式 */
.plan-flow-item {
    padding: 0 18px 0 10px;

    .flow-item-name {
        background: url("@MaintainPlanLayer/assets/img/plan-bg-1.png") no-repeat;
        background-size: 100% auto;
        height: 54px;
        width: 100%;
        display: flex;
        align-items: center;
        padding-left: 60px;

        &.flow-item-1 {
            background: url("@MaintainPlanLayer/assets/img/plan-bg-3.png") no-repeat;
            background-size: 100% auto;
        }

        &.flow-item-2 {
            background: url("@MaintainPlanLayer/assets/img/plan-bg-2.png") no-repeat;
            background-size: 100% auto;
        }

        &.flow-item-3 {
            background: url("@MaintainPlanLayer/assets/img/plan-bg-4.png") no-repeat;
            background-size: 100% auto;
        }

        &.flow-item-4 {
            background: url("@MaintainPlanLayer/assets/img/plan-bg-3.png") no-repeat;
            background-size: 100% auto;
        }

        &.flow-item-5 {
            background: url("@MaintainPlanLayer/assets/img/plan-bg-1.png") no-repeat;
            background-size: 100% auto;
        }

        .flow-name {
            font-size: 16px;
            font-weight: 500;
            background-image: linear-gradient(to bottom, rgb(255, 255, 255), rgb(146, 248, 212)); // 背景线性渐变
            color: #fff; // 兜底颜色，防止文字裁剪不生效
            background-clip: text;
            -webkit-background-clip: text; // 背景被裁减为文字的前景色
            -webkit-text-fill-color: transparent; // 文字填充为透明，优先级比color高。
        }
    }

    .flow-info-box {
        position: relative;
        display: flex;
        flex-direction: column;
        margin-left: 44px;
        padding: 10px 0 10px 10px;
        font-size: 12px;
        color: #fff;
        font-weight: normal;
        background: linear-gradient(to right, rgba(255, 255, 255, .15), rgba(255, 255, 255, 0));

        .flow-info-item {
            display: flex;
            line-height: 20px;

            .flow-info-text {
                flex: 1;
                text-align: right;
            }
        }

        &::before {
            content: '';
            display: block;
            position: absolute;
            top: 0;
            left: -18px;
            width: 2px;
            height: 100%;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(255, 255, 255, .5));
        }

        &::after {
            content: '';
            display: block;
            position: absolute;
            top: calc(50% - 20px);
            left: -38.5px;
            width: 50px;
            height: 60px;

            /* 使用逗号分隔多个背景图片 */
            background-image:
                url("@MaintainPlanLayer/assets/img/sanjiao1.png"),
                url("@MaintainPlanLayer/assets/img/sanjiao2.png");

            /* 设置背景大小和位置 */
            background-size: 23px 15px, 33px 23px;
            background-position: 6px 18px, 0 0;
            transform: scale(.5);

            /* 设置背景重复，如果需要的话 */
            background-repeat: no-repeat, no-repeat;
        }
    }
}
</style>