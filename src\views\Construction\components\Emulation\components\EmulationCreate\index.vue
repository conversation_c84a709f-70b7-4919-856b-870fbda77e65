<template>
    <div class="emulation-create-wrapper">
        <div v-show="!showCustomArea" class="emulation-create">
            <div class="emulation-create__header">
                <span class="emulation-create__title">{{ isEdit ? '编辑' : '创建' }}仿真方案</span>
                <span
                    :class="[
                        'emulation-create-close',
                        {show: showScheme},
                    ]"
                    @click="toggle"
                >
                    <i class="el-icon-caret-bottom"></i>
                </span>
            </div>
            <el-collapse-transition>
                <div v-show="showScheme" class="emulation-create__main">
                    <div class="emulation-create__body show-scroll">
                        <tab-bar
                            v-model="currenTabKey"
                            class="emulation-tabbar"
                            :list="tabList"
                        />
                        <!-- 基本信息 -->
                        <base-info
                            v-show="currenTabKey === 1"
                            :info.sync="baseInfo"
                            :emulation-type="emulationType"
                            @customeArea="handleCustomArea"
                        />
                        <!-- 流量输入 -->
                        <flow
                            v-show="currenTabKey === 2"
                            :info.sync="flowInfo"
                            :emulation-info="{
                                emulationStartStake: baseInfo.emulationStartStake,
                                emulationEndStake: baseInfo.emulationEndStake,
                                emulationStartTime: baseInfo.emulationStartTime,
                                emulationEndTime: baseInfo.emulationEndTime,
                                eventPosition: baseInfo.eventPosition,
                                eventStartStake: baseInfo.eventStartStake,
                                highSpeedName: baseInfo.highSpeedName,
                                flowDistance: baseInfo.flowDistance,
                                emulationType: baseInfo.emulationType,
                            }"
                        />
                        <!-- 策略输入 -->
                        <strategy
                            v-show="currenTabKey === 3"
                            :high-speed-name="baseInfo.highSpeedName"
                            :emulation-start-stake="baseInfo.emulationStartStake"
                            :emulation-end-stake="baseInfo.emulationEndStake"
                            :direction="directionMap[baseInfo.direction]"
                            :info.sync="strategyInfo"
                        />
                        <!-- 模型配置 -->
                        <model v-show="currenTabKey === 4" :info.sync="modelInfo"/>
                    </div>
                    <div class="emulation-create__footer">
                        <div class="scheme-name">
                            <span>方案名称：</span>
                            <el-input
                                v-model="baseInfo.name"
                                class="scheme-name__input"
                                size="mini"
                                placeholder="请输入方案名称"
                            />
                        </div>
                        <div class="btn-group">
                            <div @click="handleRun">仿真运行</div>
                            <div @click="handleSave">保存方案</div>
                            <div @click="handleCancel">取消方案</div>
                        </div>
                    </div>
                </div>
            </el-collapse-transition>
        </div>

        <custom-area
            v-if="showCustomArea"
            @back="handleBackCustom"
            @complete="handleCompleteCustom"
        />
    </div>
</template>

<script>
import {Input} from 'element-ui';
import {ref, unref, watch} from 'vue';
import TabBar from './components/TabBar.vue';
import Base from './components/Base.vue';
import Flow from './components/Flow.vue';
import Strategy from './components/Strategy.vue';
import Model from './components/Model.vue';
import {directionMap} from '@/config';
import {
    baseDefaultInfo,
    flowDefaultInfo,
    strategyDefaultInfo,
    modelDefaultInfo,
} from './config';
import {cloneDeep, isEmpty} from 'lodash-es';
import {createScheme, updateScheme} from '@/api/emulation';
import {messageTip} from '@/utils';
import CustomArea from '../CustomArea/index.vue';
import CollapseTransition from 'element-ui/lib/transitions/collapse-transition';
import {showScheme} from '../..';

export default {
    components: {
        [Input.name]: Input,
        TabBar,
        BaseInfo: Base,
        Flow,
        Strategy,
        Model,
        CustomArea,
        [CollapseTransition.name]: CollapseTransition,
    },
    props: {
        isEdit: Boolean,
        // 仿真方案默认数据
        defaultInfo: Object,
        emulationType: {
            type: Number,
            default: 2,
        },
        emulationPlatform: {
            type: String,
            default: 'TEST_MAP',
        },
    },
    setup(props, {emit}) {
        const currenTabKey = ref(1);
        const tabList = [
            {label: '基础信息', key: 1},
            {label: '流量信息', key: 2},
            {label: '策略输入', key: 3},
            {label: '模型配置', key: 4},
        ];
        // 展示自定义区域
        const showCustomArea = ref(false);
        // 基础信息
        const baseInfo = ref(cloneDeep(baseDefaultInfo));
        // 流量信息
        const flowInfo = ref(cloneDeep(flowDefaultInfo));
        // 策略信息
        const strategyInfo = ref(cloneDeep(strategyDefaultInfo));
        // 模型信息
        const modelInfo = ref(cloneDeep(modelDefaultInfo));

        // 获取仿真方案信息
        function getSchemeInfo() {
            const base = unref(baseInfo);
            const flow = unref(flowInfo);
            const strategy = unref(strategyInfo);
            const model = unref(modelInfo);
            let info = {
                /** 基础--开始 */
                ...base,
                mapLayer: props.emulationPlatform,
                emulationType: props.emulationType,
                closeLane: base.closeLane?.join(','),
                eventList: [
                    {
                        eventType: base.eventType,
                        eventLocationType: base.eventLocationType,
                        duration: base.duration,
                        eventStartStake: base.eventStartStake,
                        eventPosition: base.eventPosition,
                        direction: base.direction,
                        closeLane: base.closeLane?.join(','),
                        closeLanePosition: base.closeLanePosition?.join?.(';') || ';',
                        influencesLength: base.influencesLength,
                        limitSpeed: base.limitSpeed,
                        eventStartTime: base.eventStartTime,
                        highSpeedName: base.highSpeedName1,
                        // 施工仿真
                        constructionCornerGeoList: props.emulationType === 3
                            ? base.constructionCornerGeoList : undefined,
                        constructionGeoList: props.emulationType === 3
                            ? base.constructionGeoList : undefined,
                    },
                ],
                /** 基础--结束 */
                /** 流量--开始 */
                flowInputType: flow.flowInputType,
                flowList: flow.flowList,
                customFlowStartTime: flow.customFlowStartTime,
                customFlowEndTime: flow.customFlowEndTime,
                /** 流量--结束 */
                /** 策略--开始 */
                strategyInputType: strategy.strategyInputType,
                strategyList: [],
                /** 策略--结束 */
                /** 模型--开始 */
                model,
                /** 模型--结束 */
            };
            // 处理手动输入的策略
            if (strategy.strategyInputType === 2) {
                const strategyList = [];
                const strategyType = strategy.strategyType;
                // 开放应急车道
                if (strategyType.includes(1)) {
                    strategyList.push({
                        ...strategy.emergencyConfig,
                        type: 1,
                    });
                }
                // 行车道管控
                if (strategyType.includes(2)) {
                    const laneControlConfig = strategy.laneControlConfig;
                    strategyList.push({
                        type: 2,
                        position: laneControlConfig.position,
                        controlStake: laneControlConfig.controlStake,
                        controlLength: laneControlConfig.controlLength,
                        laneControlType: laneControlConfig.configList.map(i => i.laneControlType).join(','),
                        limitSpeed: laneControlConfig.configList.map(i => i.limitSpeed || 0).join(','),
                        lane: laneControlConfig.configList.map(i => i.lane).join(','),
                    });
                }
                // 出入口管控
                if (strategyType.includes(3)) {
                    const entryAndExitConfig  = strategy.entryAndExitConfig;
                    const entranceExitType = entryAndExitConfig.configList.map(i => i.entranceExitType).join(',');
                    const entryConfig = entryAndExitConfig.configList.find(i => i.entranceExitType === 2);
                    const exportConfig = entryAndExitConfig.configList.find(i => i.entranceExitType === 3);
                    strategyList.push({
                        type: 3,
                        entranceExitType,
                        exportConfig,
                        entryConfig,
                        rampName: entryAndExitConfig.rampName,
                    });
                }
                info.strategyList = [strategyList];
            }

            return info;
        }

        async function saveScheme() {
            if (!baseInfo.value.name) {
                messageTip('请输入仿真方案名称');
                return Promise.reject('请输入仿真方案名称');
            }
            const fetchApi = props.isEdit ? updateScheme : createScheme;
            const schemeInfo = getSchemeInfo();
            const {data} = await fetchApi(schemeInfo);
            return data;
        }

        async function handleRun() {
            const data = await saveScheme();
            const id = props.isEdit ? baseInfo.value.id : data.id;
            emit('run', {
                ...data,
                id,
            });
        }

        async function handleSave() {
            const data = await saveScheme();
            messageTip('保存成功', 'success');
            emit('save', data);
        }

        function handleCancel() {
            emit('cancel');
        }

        function concatRefData(ref, data) {
            ref.value = {
                ...ref.value,
                ...data,
            };
        }

        watch(
            () => props.defaultInfo,
            val => {
                if (!val || isEmpty(val)) return;
                if (val.baseInfo) concatRefData(baseInfo, val.baseInfo);
                if (val.flowInfo) concatRefData(flowInfo, val.flowInfo);
                if (val.strategyInfo) concatRefData(strategyInfo, val.strategyInfo);
                if (val.model) concatRefData(modelInfo, val.model);
            },
            {
                deep: true,
                immediate: true,
            }
        );

        function handleCustomArea() {
            showCustomArea.value = true;
            showScheme.value = false;
        }

        function handleBackCustom() {
            showCustomArea.value = false;
            showScheme.value = true;
            baseInfo.value.areaChoose = 1;
        }

        async function handleCompleteCustom(customData) {
            showCustomArea.value = false;
            showScheme.value = true;
            baseInfo.value = {
                ...baseInfo.value,
                highSpeedName: customData.highSpeedName,
                emulationStartPtake: customData.emulationStartStake,
                emulationEndStake: customData.emulationEndStake,
            };
        }

        // 切换折叠
        function toggle() {
            showScheme.value = !showScheme.value;
        }

        return {
            currenTabKey,
            tabList,
            handleRun,
            handleSave,
            handleCancel,
            baseInfo,
            flowInfo,
            strategyInfo,
            modelInfo,
            directionMap,
            showCustomArea,
            handleCustomArea,
            handleBackCustom,
            handleCompleteCustom,
            showScheme,
            toggle,
        };
    },
};
</script>


<style lang="less" scoped>
.emulation {
    &-create {
        width: 508px;
        // display: flex;
        // flex-direction: column;
        backdrop-filter: blur(4px);
        font-size: 16px;

        /deep/ .el-input__inner {
            color: #d6d6d6;
            font-size: 16px;
        }

        /deep/ .el-date-editor {
            height: 32px;
            line-height: 32px;

            input {
                height: 32px;
                line-height: 32px;
            }
        }

        /deep/ .el-checkbox .el-checkbox__label,
        /deep/ .el-radio__label {
            font-size: 16px;
        }

        /deep/ .el-form {
            &-item {
                font-size: 16px;

                &__label {
                    font-size: 18px;
                }
            }
        }

        &-wrapper {
            position: relative;
        }

        &__header {
            height: 36px;
            padding: 0 10px 0 42px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 22px;
            background-image: url('@/assets/images/base/modal-head-bg.svg');
            background-size: 100% 100%;
        }

        &__main {
            height: 800px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        &__body {
            margin-top: 4px;
            flex: 1;
            width: 100%;
            padding: 16px;
            overflow-y: scroll;
            background-color: rgba(3, 31, 68, .5);
            backdrop-filter: blur(12px);
            border: 1px	solid rgba(72, 100, 146, .35);
            border-bottom-color: rgb(41, 86, 157);
        }

        &__footer {
            padding: 24px;
            background-color: rgba(18, 74, 166, .3);
            font-size: 18px;
            flex-shrink: 0;
        }
    }

    &-tabbar {
        margin-bottom: 24px;
    }
}

.scheme-name {
    display: flex;
    align-items: center;

    &__input {
        flex: 1;
    }
}

.btn-group {
    display: flex;
    justify-content: space-between;
    margin-top: 24px;
    padding: 0 17px;

    div {
        width: 120px;
        height: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid rgb(1, 255, 229);
        cursor: pointer;
        font-weight: 500;
        color: rgb(1, 255, 229);

        &:active {
            opacity: .85;
        }

        &:first-child {
            border: none;
            color: #000;
            background-color: rgb(1, 255, 229);
        }
    }
}

.emulation-create-close {
    color: #fff;
    width: 12px;
    height: 12px;
    line-height: 12px;
    text-align: center;
    border: 1px	solid rgba(46, 210, 255, .3);
    font-size: 10px;
    cursor: pointer;

    &.show i {
        transform: rotate(-180deg);
    }

    i {
        transform-origin: center;
        transition: .16s linear;
    }
}
</style>