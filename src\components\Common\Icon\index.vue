<template>
    <svg class="icon" aria-hidden="true">
        <use :xlink:href="`#${type}-${name}`"/>
    </svg>
</template>

<script>
import '@/assets/iconfont/index.js';
import '@/assets/iconfont/weather.js';
import '@/assets/iconfont/common.js';
import {useUnit} from '@/utils';
import {computed} from 'vue';

export default {
    name: 'Icon',
    props: {
        type: {
            type: String,
            default: () => 'common',
        },
        name: {
            type: String,
            required: true,
        },
        size: {
            type: Number,
            default: 16,
        },
        color: {
            type: String,
            default: '#fff',
        },
    },
    setup(props) {
        const {px2rem} = useUnit();

        const getSize = computed(() => `${px2rem(props.size)}rem`);

        const getColor = computed(() => props.color);

        return {
            getSize,
            getColor,
        };
    },
};
</script>
<style lang="less" scoped>
.icon {
    display: inline-block;
    width: v-bind(getSize);
    height: v-bind(getSize);
    color: v-bind(getColor);
    aspect-ratio: 1;
    fill: currentColor;
    // vertical-align: -.1em;
}
</style>