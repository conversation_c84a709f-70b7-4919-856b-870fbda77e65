import * as mapvthree from '@baidu/mapv-three';
import {
    MeshStandardMaterial,
    TextureLoader,
    DoubleSide,
    Color,
    RepeatWrapping,
    Vector2,
} from 'bmap-three';

const textureLoader = new TextureLoader();


/* eslint-disable max-len */
export default class MaterialManager extends mapvthree.Default3DTilesMaterialManager {

    onInit() {
        this.addMaterials();
    }

    createPbrMaterial = (textureName, textureChannels, repeat, initParameters = {}, extraParameters = {}) => {
        if (!repeat) {
            repeat = new Vector2(0.4, 0.4);
        }
        if (!textureChannels) {
            textureChannels = {
                albedo: true,
                normal: false,
                roughness: false,
                ao: false,
            };
        }
        const parameters = {
            ...initParameters,
            envMapIntensity: 2,
        };
        if (textureChannels.albedo) {
            parameters.map = this.getTexture(textureName, 'albedo', repeat, extraParameters);
        }
        if (textureChannels.normal) {
            parameters.normalMap = this.getTexture(textureName, 'normal', repeat, extraParameters);
        }
        if (textureChannels.roughness) {
            parameters.roughnessMap = this.getTexture(textureName, 'roughness', repeat, extraParameters);
        }
        if (textureChannels.ao) {
            parameters.aoMap = this.getTexture(textureName, 'ao', repeat, extraParameters);
        }
        if (textureChannels.emissive) {
            parameters.emissiveMap = this.getTexture(textureName, 'emissive', repeat, extraParameters);
        }
        const roadMaterial = new MeshStandardMaterial(parameters);
        return roadMaterial;
    };

    getTexture = (key, type, repeat, extraParameters, callback) => {
        const texture = textureLoader.load((`textures/${key}.${extraParameters.format || 'jpg'}`), callback);
        texture.wrapS = texture.wrapT = RepeatWrapping;
        texture.repeat = repeat;
        return texture;
    };

    addMaterials() {
        this._materrialMap.set('road', this.createPbrMaterial('road', null, null, {
            color: 0x333333,
            roughness: 0.8,
            side: DoubleSide,
            emissiveIntensity: 0,
            emissive: new Color(0x0e0a03),
        }));

        this._materrialMap.set('greenbelt', this.createPbrMaterial('greenbelt', null, null, {
            color: 0x99aa66,
            roughness: 1,
        }));

        this._materrialMap.set('green', this.createPbrMaterial('green', null, null, {
            color: 0x99aa66,
            roughness: 1,
        }));

        const waterMaterial = new mapvthree.WaterMaterial();
        this._materrialMap.set('water', waterMaterial);
        const engine = this.engine;
        engine.addBeforeRenderObject(waterMaterial);
        waterMaterial.waterColor = new Color(0x0b2a3f);
        waterMaterial.sunColor = new Color(0xffffff);
        waterMaterial.reflectionColor = new Color(0xfcfcff);
        waterMaterial.size = 0.1;

    }

}

export {MaterialManager};


