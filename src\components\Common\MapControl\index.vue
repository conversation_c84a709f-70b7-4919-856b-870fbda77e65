<template>
    <div
        v-if="wrapList?.length"
        class="map-control-wrap"
        :data-newStyle="true"
        :style="{'--borderImage': getBorderImage(wrapList)}"
    >
        <div
            v-for="(el, index) in wrapList"
            :key="el.name + index"
            class="list"
            :class="{
                active: getActive(el),
                hasMore: el.children?.length,
            }"
            @mouseenter="handlerMouseover(...arguments, el)"
            @click="job(el)"
        >
            <icon v-if="el.icon" :name="el.icon"/>
            <span v-if="el.name" class="small">{{ el.name }}</span>
        </div>

        <div
            class="fac-box"
            :style="{
                '--subPosTop': subPosTop + 'px',
            }"
        >
            <div
                class="fac-list mini"
                :style="{
                    '--borderImage': getBorderImage(children),
                }"
            >
                <div
                    v-for="item in children"
                    :key="item.name"
                    class="list"
                    :class="{
                        active: getActive(item),
                    }"
                    @click="job(item)"
                >
                    <icon v-if="item.icon" :name="item.icon"/>
                    <span class="small">{{ item.name }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {reactive, toRefs, computed} from 'vue';
import Icon from '@/components/Common/Icon/index.vue';

// import {mapMutations} from 'vuex';
export default {
    name: 'MapControl',
    props: {
        list: {
            type: Array,
            default: () => [],
        },
    },
    components: {
        Icon,
    },
    setup(props) {
        const state = reactive({
            subList: [],
            subPosTop: 0,
        });

        const wrapList = computed(() => props.list.filter(e => !e.hide?.()));

        const children = computed(() => state.subList.filter(e => !e.hide?.()));

        const job = el => el.job?.();

        const getActive = el => ((typeof el.active === 'function') ? el.active() : el.active);

        const getBorderImage = list => {
            const step = (1 / list.length).toFixed(3);
            return list.reduce((str, cur, i) => {
                const eg = +(step * `${i}50`).toFixed(3) + '%';
                const color = getActive(cur) ? '#00FFC2' : 'rgba(218, 247, 251, .3)';
                if (i === 0) str += `${color} 0%`;
                if (i === list.length - 1) return `${str}, ${color} ${eg}, ${color} 100%`;
                return `${str}, ${color} ${eg}`;
            }, 'linear-gradient(to bottom, ') + ')';
        };

        const handlerMouseover = (e, item) => {

            if (item.children?.length && item.children !== state.subList) {
                state.subList = item.children || [];
                state.subPosTop = e.currentTarget?.offsetTop - 1 || 0;
            }
        };

        return {
            ...toRefs(state),
            children,
            job,
            wrapList,
            getActive,
            getBorderImage,
            handlerMouseover,
        };
    },
};
</script>

<style lang="less" scoped>
@text-color: #E7EFFF;
@active-text-color: #01FFE5;
@border-color: #01FFE5;
@box-bg-color:rgba(34, 120, 106, .58);

.map-control-wrap {
    width: 48px;
    font-size: 12px;
    line-height: 15px;
    color: @text-color;
    border: 1px solid @border-color;
    background: @box-bg-color;
    border-radius: 1px;
    box-sizing: border-box;

    --bgColor: transparent;

    &[data-newStyle] {
        border: 1px solid;
        border-image: var(--borderImage) 1;
        background-clip: border-box;
        border-radius: 4px;

        .fac-list {
            border: 1px solid;
            border-image: var(--borderImage) 1;
        }
    }

    .list {
        box-sizing: border-box;
        width: 100%;
        min-height: 46px;
        padding: 8px 0;
        transition: filter .5s;
        cursor: pointer;
        position: relative;
        z-index: 2;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: var(--bgColor);
        // color: #00FFC2;

        // 更多的角标
        &.hasMore {
            &::before {
                content: '';
                width: 0;
                height: 0;
                border-top: 2.5px solid transparent;
                border-right: 2.5px solid transparent;
                border-left: 2.5px solid #fff;
                border-bottom: 2.5px solid #fff;
                position: absolute;
                left: 1px;
                bottom: 1px;
            }

            &:hover ~ .fac-box {
                display: block;
            }
        }
        // 分割线
        & + .list {
            border-top: 1px solid rgba(241, 241, 241, .1);
        }

        &.active {
            color: @active-text-color;

            &.hasMore::before {
                border-left: 2.5px solid @active-text-color;
                border-bottom: 2.5px solid @active-text-color;
            }
        }

        &:hover {
            filter: contrast(2);
        }

        span {
            position: relative;
            z-index: 2;
        }

        .small {
            text-align: center;
            width: ~"max(0.075rem, 24px)";
        }

        .icon {
            display: block;
            width: 14px;
            height: 14px;
            background-size: cover;
            margin: 0 auto 3px;
        }
    }

    .fac-box {
        position: absolute;
        top: var(--subPosTop);
        right: calc(100% - 43px);
        width: calc(48px + var(--width, 100%));
        box-sizing: content-box;
        display: none;
        z-index: 1;
        transition: top .2s ease-in;

        &:hover {
            display: block;
        }

        .fac-list {
            width: var(--width, 48px);
            overflow-y: auto;
            background: @box-bg-color;
            // border: 1px solid @border-color;
            border-radius: 1px;
            transition: top .2s ease-in;
            max-height: 650px;

            > div {
                .small {
                    text-align: center;
                    width: calc(~"max(0.0375rem, 12px)" * 2);
                }
            }
        }
    }
}
</style>