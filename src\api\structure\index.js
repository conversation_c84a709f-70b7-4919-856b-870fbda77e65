// 结构物接口
import {request} from '@/utils/network-helper/index';
import {baseConfig} from '@/utils/common';
const {basePrefix} = baseConfig;

const structure = import.meta.env.MODE === 'release' ? basePrefix + '/proxy/structurehealth' : basePrefix;
const bearings = import.meta.env.MODE === 'release' ? basePrefix + '/proxy/bearings' : basePrefix;
const base = `${basePrefix}`;

// 中观地图扎点获取
export const getstructureNumByorgId = async (orgId = '019d6a009f624ce0a1956d1be6d6a88e') => {
    const {data, code} = await request.get(
        structure + '/dataassetsSectionCompanyRelation/getstructureNumByorgId',
        {
            orgId,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 结构物列表
export const showStructureList = async ({
    orgId = '019d6a009f624ce0a1956d1be6d6a88e',
    structureType = 1,
    sectionId = ['G0015440090'],
} = {}) => {
    const {data, code} = await request.post(
        structure + '/structureListShow/showStructureList',
        {
            orgId,
            structureType,
            sectionId,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 获取结构物传感器数量
export const getSensorStatusCount = async ({
    orgId = '019d6a009f624ce0a1956d1be6d6a88e', sectionId = ['G0015440090'],
} = {}) => {
    const {data, code} = await request.post(
        structure + '/sensorStatusCount/getSensorStatusCount',
        {
            orgId,
            sectionId,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 10.1、统计结构物预警统计
export const struWarnCount = async () => {
    const {data, code} = await request.get(
        structure + '/sensorWarning/struWarnCount'
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 10.2、结构物风险统计
export const facilityCountRisk = async () => {
    const {data, code} = await request.get(
        basePrefix + '/sensorWarning/facilityCountRisk'
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 10.3、结构物事件列表
export const bridgeEvent = async params => {
    const {data, code} = await request.get(
        structure + '/bridge/bridgeEvent', params
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 10.8、结构物事件za dian
export const eventDot = async () => {
    const {data, code} = await request.get(
        structure + '/bridge/eventDot'
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 10.4、结构物交通流量
export const bridgeFlow = async params => {
    const {data, code} = await request.post(
        bearings + '/oglIvisTfcEtcRtLocal/bridgeFlow',
        params
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 10.5、结构物轴载数据
export const bridgeAxle = async params => {
    const {data, code} = await request.post(
        bearings + '/oglIvisTfcEtcRtLocal/bridgeAxle',
        params
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 获取微观二维扎点数据
export const structureListApi = async ({
    sectionName = ['佛开高速'], structureTypeList = [1, 2, 3],
} = {}) => {
    const {data, code} = await request.post(
        structure + '/structure/list',
        {
            sectionName,
            structureTypeList,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 获取告警前10年
export const countWainingYear = async ({
    structureTypeList = [1], orgId = '019d6a009f624ce0a1956d1be6d6a88e',
    sectionId = ['G0015440090'], sectionName = ['佛开高速'],
} = {}) => {
    const {data, code} = await request.post(
        structure + '/sensorWainingCount/countWainingYear',
        {
            structureTypeList,
            orgId,
            sectionId,
            sectionName,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 获取告警前10月
export const countWainingMonth = async ({
    structureTypeList = [1], orgId = '019d6a009f624ce0a1956d1be6d6a88e',
    sectionId = ['G0015440090'], sectionName = ['佛开高速'],
} = {}) => {
    const {data, code} = await request.post(
        structure + '/sensorWainingCount/countWainingMonth',
        {
            structureTypeList,
            orgId,
            sectionId,
            sectionName,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 获取告警前10年
export const countWainingQuarter = async ({
    structureTypeList = [1], orgId = '019d6a009f624ce0a1956d1be6d6a88e',
    sectionId = ['G0015440090'], sectionName = ['佛开高速'],
} = {}) => {
    const {data, code} = await request.post(
        structure + '/sensorWainingCount/countWainingQuarter',
        {
            structureTypeList,
            orgId,
            sectionId,
            sectionName,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 获取微观二维告警扎点数据
export const structureSensorWaininglist = async (sectionName = ['佛开高速']) => {
    const {data, code} = await request.post(
        structure + '/structure/structureSensorWaininglist',
        {
            sectionName,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 获取微观二维高速路线数据
export const applySectionBySectionId = async (sectionId = ['G0015440090']) => {
    const {data, code} = await request.post(
        structure + '/section/info/applySectionBySectionId',
        {
            sectionId,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};


/**
 * 微观二维告警扎点模块
 */
// 查询结构物告警信息列表
export const selectWarningMessage = async payload => {
    const {data, code} = await request.post(
        structure + '/sensorWarning/selectWarningMessage', payload);
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

/**
 * 微观三维模块
 */
// 传感器在线率统计（左下）
export const countSensorStatusBystructureId = async payload => {
    const {data, code} = await request.get(
        structure + '/sensor/countSensorStatusBystructureId',
        payload
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 传感器实时监测数据（左上）
export const getSensorDataByStructureId = async payload => {
    const {data, code} = await request.get(
        structure + '/sensorMonitor/getSensorDataByStructureId',
        payload
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 根据传感器名称获取对应的测量类型列表（左中左侧下拉框）
export const getStationMeasureValueList = async params => {
    const {data, code} = await request.post(
        structure + '/structure/getStationMeasureValueList',
        params
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 根据测量类型获取对应的传感器名称列表（左中右侧下拉框）
export const getStationLocationList = async params => {
    const {data, code} = await request.post(
        structure + '/structure/getStationLocationList',
        params
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 实时与历史检测曲线（左中折线图）
export const selectSensorListDay = async params => {
    const {data, code} = await request.get(
        structure + '/structure/selectSensorListDay',
        params
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 结构物告警趋势——日（右中）
export const showNumWarningMonth = async structureId => {
    const {data, code} = await request.get(
        structure + '/structure/showNumWarningMonth',
        {
            structureId,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 结构物告警趋势——月（右中）
export const showNumWarningMonthNow = async structureId => {
    const {data, code} = await request.get(
        structure + '/structure/showNumWarningMonthNow',
        {
            structureId,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 结构物告警趋势——季度（右中）
export const showNumWarningQuarter = async structureId => {
    const {data, code} = await request.get(
        structure + '/structure/showNumWarningQuarter',
        {
            structureId,
        }
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 结构物告警类型统计（右上）
export const selectWarningTypeCountByStructureId = async params => {
    const {data, code} = await request.post(
        structure + '/sensorWarning/selectWarningTypeCountByStructureId',
        params
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 视频图像（右下）
export const getCamera = async params => {
    const {data, code} = await request.get(
        base + '/mdcamera/active/getCamera',
        params
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};
export const getVideoByStructureId = async params => {
    const {data, code} = await request.get(
        structure + '/mdcamera/active/getVideoByStructureId',
        params
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 根据结构物ID查询最新的一条告警信息(三维视角右上角)
export const selectWarningMessageToday = async payload => {
    const {data, code} = await request.post(
        structure + '/sensorWarning/selectWarningMessageToday', payload);
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 10.6、桥梁门架扎点显示
export const bridgeGantry = async () => {
    const {data, code} = await request.get(
        basePrefix + '/bridge/bridgeGantry');
    if (+code === 200) {
        return {data, code};
    }
    return {};
};