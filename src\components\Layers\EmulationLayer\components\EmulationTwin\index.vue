<template>
    <div class="emulation-twin">
        <div v-if="emulationIds" class="content">
            <main-component
                v-for="(emulationId, index) in emulationIds"
                :id="id"
                ref="mainRefs"
                :key="emulationId"
                :emulation-id="emulationId"
                :index="index"
                :if-self="emulationIds.length === 1"
                :is-finish="isFinish"
                :emulation-type="emulationType"
                :class="`${index !== 0 ? 'ml-15' : 'ml-0'}`"
                @changeSliderTime="changeSliderTime"
            />
        </div>
        <tool-button-bar
            class="go-back"
            :show-collapse="false"
            :show-recover="false"
            @back="goBack"
        />
        <emulation-chart v-if="emulationIds && showReport" :id="id"/>
    </div>
</template>

<script>
import {onMounted, ref, computed, watch} from 'vue';
import Main from './Main.vue';
import EmulationChart from '@EmulationLayer/components/EmulationChart/index.vue';
import {emulationStart, getSchemeInfoById, seeTravelResult} from '@EmulationLayer/api';
import {emulationIds, uuid, strategyId, emulationInfo, simulationTime,
        simulationBufferTime, initUuid} from '@EmulationLayer/store/index';
import {ToolButtonBar} from '@/components/Common';

export default {
    components: {
        MainComponent: Main,
        EmulationChart,
        ToolButtonBar,
    },
    props: {
        id: {
            type: String,
            default: () => null,
        },
        emulationType: {
            type: Number,
            default: 1,
        },
        showReport: {
            type: Boolean,
            default: true,
        },
    },
    setup(props, {emit}) {
        const isFinish = ref(false);
        const id = computed(() => props.id || strategyId.value);

        const mainRefs = ref(null);

        const getSchemeInfo = async (isStop = false) => {
            const response = await getSchemeInfoById(id.value);
            emulationInfo.value = response.data;
            // eslint-disable-next-line no-use-before-define
            !isStop && getEmulationIds();
        };

        // 获取仿真id
        const getEmulationIds = async () => {
            if (!uuid.value) {
                await initUuid();
            }
            // status： 1 待开始 3 已完成
            const API = emulationInfo.value.status === 1 ? emulationStart : seeTravelResult;
            if (API === '') {
                return;
            }
            const params = {
                id: id.value,
                uuid: uuid.value,
            };
            // eslint-disable-next-line @babel/new-cap
            const {data} = await API(params);
            if (!data || !data.emulationId) {
                return;
            }
            emulationIds.value = typeof data.emulationId === 'string' ? [data.emulationId] : data.emulationId;
            emulationInfo.value.status === 1 && getSchemeInfo(true);
        };

        // 释放当前仿真资源
        const disposeData = () => {
            strategyId.value = null;
            emulationIds.value = null;
            emulationInfo.value = null;
            simulationTime.value = null;
            simulationBufferTime.value = null;
        };

        const goBack = () => {
            isFinish.value = true;
            // 无仿真id数据时 清除仿真相关数据并返回
            !emulationIds.value?.length && disposeData();
            emit('goBack');
        };

        const init = () => {
            id.value && getSchemeInfo();
        };

        const changeSliderTime = time => {
            mainRefs.value?.forEach(ref => {
                ref.sendSliderTime(time);
            });
        };

        watch(() => id.value, () => {
            init();
        });

        onMounted(() => {
            init();
        });

        return {
            emulationIds,
            isFinish,
            mainRefs,
            goBack,
            changeSliderTime,
        };
    },
};
</script>

<style lang="less" scoped>
.emulation-twin {
    width: 100%;
    height: 100%;
    background-color: black;
    overflow-y: scroll;
    pointer-events: auto;
    position: relative;
    top: 0;
    left: 0;
    z-index: 100;

    .go-back {
        position: fixed;
        top: 20px;
        left: 20px;
        z-index: 99;
    }

    .content {
        display: flex;
    }
}
</style>