<template>
    <Card title="施工事件统计" card-type="card-long-2">
        <template #content>
            <div class="card-content ">
                <StatisticsCard
                    v-for="card in getCardInfo"
                    :key="card.title"
                    :info="card"
                />
            </div>
        </template>
    </Card>
</template>

<script setup>
import {Card} from '@/components/Common';
import StatisticsCard from '../StatisticsCard/index.vue';
import {computed, onMounted, ref} from 'vue';
import {getStatusNum} from '@/api/construction';

const cardConfig = [
    {
        title: '开工工程数量',
        unit: '个',
        field: 'start',
    },
    {
        title: '完工工程数量',
        unit: '个',
        field: 'complete',
    },
    {
        title: '交工工程数量',
        unit: '个',
        field: 'hand',
    },
    {
        title: '完成投资数量',
        unit: '个',
        field: 'invest',
    },
];

const cardData = ref({
    start: '',
    complete: '',
    hand: '',
    invest: '',
});

const getCardInfo = computed(() => (
    cardConfig.map(item => ({
        ...item,
        value: cardData.value[item.field] || 0,
    }))
));

async function fetchData() {
    const {data} = await getStatusNum();
    // 1开工 2完工 3交工 4完成投资
    const dataMap = data.reduce((acc, cur) => {
        return {
            ...acc,
            [cur.projectStatus]: cur.num,
        };
    }, {});
    cardData.value = {
        start: dataMap[1],
        complete: dataMap[2],
        hand: dataMap[3],
        invest: dataMap[4],
    };
}

onMounted(() => {
    fetchData();
});

</script>

<style lang="less" scoped>
.card-content {
    height: 314px;
    display: flex;
    justify-content: space-between;
}
</style>