<template>
    <div class="sensors-monitor">
        <api-table
            :api="getSensorDataByStructureId"
            :columns="columns"
            :height="314"
            :pagination="false"
            :request-options="{
                listField: '',
            }"
            :request-params="{
                structureId: structureId,
                dateTime: dateTime,
            }"
        />
    </div>
</template>

<script>
import {ApiTable} from '@/components/Common';
import {getSensorDataByStructureId} from '@/api/structure/index';
import {ref} from 'vue';
import {useUnit} from '@/utils';
import {structureId} from '@/views/Structure/utils/index';
import dayjs from 'dayjs';

export default {
    name: '传感器实时监测数据',
    components: {
        ApiTable,
    },
    setup(props) {
        const {ratio} = useUnit();
        const columns = ref([
            {
                label: '数据类型',
                prop: 'dataType',
                width: `${140 * ratio.value}px`,
                align: 'left',
            },
            {
                label: '测点位置',
                prop: 'sensorName',
                width: `${110 * ratio.value}px`,
                align: 'left',
            },
            {
                label: '采集时间',
                prop: 'dataTime',
                width: `${110 * ratio.value}px`,
                align: 'center',
                format: e => {
                    return e.row.dataTime.split('.')[0];
                },
            },
            {
                label: '监测数据',
                prop: 'monitorValue',
                width: `${110 * ratio.value}px`,
                align: 'center',
                format: e => {
                    return e.row.monitorValue + e.row.unit;
                },
            },
        ]);

        const dateTime = dayjs(new Date()).format('YYYY-MM-DD');

        return {
            getSensorDataByStructureId,
            columns,
            structureId,
            dateTime,
        };
    },
};
</script>

<style lang="less" scoped>
.sensors-monitor {
    width: 100%;
    height: 314px;
}
</style>