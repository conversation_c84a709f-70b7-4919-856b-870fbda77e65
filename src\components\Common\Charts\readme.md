# ChartBar 横向柱形图
## 传参
|参数名|类型|是否必传|含义
|-|-|-|-|
|data|Array|true|数据[{name:'', count: 20}]|
|unit|String|false|单位（移动到柱形展示）|
|seriesName|String|false|指标|
|bgColor|String|false|柱形背景颜色|
|tooltipCallback|Function|false|tooltip自定义显示文本内容|

# VerticalBarChart 竖向柱状图
## 传参
|参数名|类型|是否必传|含义
|-|-|-|-|
|info|object|false|柱状图数据对象|
|tooltipCallback|Function|false|tooltip自定义显示文本内容|

## 参数详情
info: 
```js
{
    xAxisData: ['Mon', 'Tue', 'Wed'], // x轴坐标数据，非必传，不传则使用seriesData的name属性
    legendData: ['Mon', 'Tue', 'Wed'], // 图例，非必传，不传则使用seriesData的name属性
    yAxisName: '单位：台', // y轴单位
    // 柱状图数据，需要包含name名称、数据data、柱体颜色和鼠标悬停柱体颜色。后两者非必传
    seriesData: [
        {
            name: '设备运行数量',
            data: 10,
            itemColor: 'rgba(68, 218, 30, 0.6)',
            emphasisColor: 'rgb(68, 218, 30)',
        },
        {
            name: '设备运行数量',
            data: 20,
            itemColor: 'rgba(68, 218, 30, 0.6)',
            emphasisColor: 'rgb(68, 218, 30)',
        },
        {
            name: '设备运行数量',
            data: 30,
            itemColor: 'rgba(68, 218, 30, 0.6)',
            emphasisColor: 'rgb(68, 218, 30)',
        },
    ],
}
```