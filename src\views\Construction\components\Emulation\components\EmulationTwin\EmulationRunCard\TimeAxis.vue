<template>
    <div class="emulation-time-wrapper">
        <div class="emulation-time-text">
            <span>仿真开始</span>
            <span>仿真结束</span>
        </div>
        <div class="emulation-time-bar">
            <el-slider
                v-model="progressState"
                show-stops
                show-tooltip
                :debounce="300"
                :format-tooltip="formatTooltip"
                :marks="marks"
                :step="1"
                :max="stepMax"
                @change="changeSliderTime"
            />
            <el-slider
                :value="bufferTime"
                class="buffer-slider"
                :step="1"
                :max="stepMax"
            />
        </div>
    </div>
</template>

<script>
import {computed, ref, watch} from 'vue';
import {Slider} from 'element-ui';
import dayjs from 'dayjs';
import {emulationInfo} from '../index';

export default {
    components: {
        [Slider.name]: Slider,
    },
    props: {
        nowTimeUnix: String,
        startTime: String,
        endTime: String,
    },
    setup(props, {emit}) {
        let loading = false;
        const progressState = ref(0);
        const startTimeUnix = computed(() => dayjs(props.startTime).valueOf());
        const endTimeUnix = computed(() => dayjs(props.endTime).valueOf());

        // 1h: 1000 * 20 * 3 2h: 1000 * 20 * 2 3h: 1000 * 20 * 1
        const stepUnix = computed(() => {
            const unixH = dayjs(props.endTime).startOf('hour').hour()
                - dayjs(props.startTime).startOf('hour').hour() || 1;
            return 1000 * 20 * unixH;
        });

        // 钟时间戳差值用于显示进度条总步数 1h === 80刻度 / 45
        const stepMax = computed(() => (endTimeUnix.value - startTimeUnix.value) / stepUnix.value);

        // 进度条的标注
        const marks = computed(() => {
            let marker = {};
            marker[0] = dayjs(props.startTime).format('HH:mm:ss');
            marker[stepMax.value] = dayjs(props.endTime).format('HH:mm:ss');
            return marker;
        });

        const bufferTime = computed(() => {
            const time = (emulationInfo.value.emulationBufferTimeUnix - startTimeUnix.value) / stepUnix.value;
            return Math.floor(time) || 1;
        });

        function formatTooltip(value) {
            let valueType = startTimeUnix.value + value * stepUnix.value;
            return dayjs(valueType).format('HH:mm:ss');
        }

        function changeSliderTime(value) {
            const unixTime = value * stepUnix.value + startTimeUnix.value;
            const drag = dayjs(unixTime).format('YYYY-MM-DD HH:mm:ss');
            loading = true;
            progressState.value = value;
            emit('change', drag);
            setTimeout(() => {
                loading = false;
            }, 1000);

        };

        watch(
            () => props.nowTimeUnix,
            val => {
                if (loading) return;
                const time = (val - startTimeUnix.value) / stepUnix.value;
                progressState.value = Math.floor(time) || 1;
            }
        );

        return {
            progressState,
            marks,
            stepMax,
            formatTooltip,
            changeSliderTime,
            bufferTime,
        };
    },
};
</script>

<style lang="less" scoped>
.emulation-time {
    &-text {
        display: flex;
        justify-content: space-between;
        color: rgba(#fff, .9);
    }

    &-bar {
        position: relative;

        .buffer-slider {
            position: absolute;
            width: 100%;
            top: 0;
            z-index: -1;

            /deep/ .el-slider__button-wrapper {
                display: none;
            }

            /deep/ .el-slider__runway {
                background-color: rgb(0, 0, 0);
            }
        }

        /deep/ .el-slider__bar,
        /deep/ .el-slider__runway {
            height: 16px;
            border-radius: 0;
        }

        /deep/ .el-slider__bar {
            background-color: rgba(87, 255, 213, .4);
            backdrop-filter: blur(42px);
        }

        /deep/ .el-slider__runway {
            background-color: transparent;
        }

        /deep/ .el-slider__button-wrapper {
            top: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /deep/ .el-slider__button {
            width: 2px;
            height: 24px;
            border-color: transparent;
            background-color: #00ff95;
            border-radius: 0;
            transform: scale(1) !important;
        }

        /deep/ .el-slider__stop {
            width: 2px;
            height: 16px;
            background-color: rgba(#fff, .6);
            border-radius: 0;
        }

        /deep/ .el-slider__marks-text {
            top: 4px;

            &:first-child {
                transform: translateX(0);
            }

            &:last-child {
                transform: translateX(-100%);
            }
        }
    }
}
</style>