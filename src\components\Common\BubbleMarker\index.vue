<template>
    <div
        v-show="getShowMarker"
        ref="marker"
        class="bubble-marker"
    >
        <div
            :class="[
                'bubble-marker__inner',
                {
                    'bubble-marker__hover': needHover,
                },
            ]"
            @click="handleClickMarker"
            @mouseenter="handleMouseEnter"
            @mouseleave="handleMouseLeave"
        >
            <div class="bubble-marker__bubble">
                <icon
                    class="bubble-marker__bubble-bg"
                    type="common"
                    :name="`bubble-${bubbleType}`"
                    :color="bubbleColor"
                    :size="42"
                />
                <icon
                    class="bubble-marker__bubble-border"
                    color="#ffffff"
                    type="common"
                    :name="`bubble-${bubbleType}-border`"
                    :size="42"
                />
                <icon
                    class="bubble-marker__bubble-icon"
                    :type="iconType"
                    :name="iconName"
                    :color="iconColor"
                    :size="20"
                />
                <div v-if="showBadge" class="bubble-marker__bubble-count">
                    {{ info.badge }}
                </div>
            </div>
            <div v-if="showLabel" class="bubble-marker__label">
                <div class="bubble-marker__name">{{ info.label }}</div>
                <div v-if="needDetail" class="bubble-marker__detail">查看详情</div>
            </div>
        </div>
        <div class="bubble-marker__slot" :style="{top: getSlotTop}">
            <slot></slot>
        </div>
    </div>
</template>

<script>
import {computed, onMounted, onUnmounted, ref, watch} from 'vue';
import {Icon} from '@/components/Common';
import {v4 as uuidV4} from 'uuid';
import {isFunction} from 'lodash';
import {getTextWidth, useUnit} from '@/utils';

export default {
    components: {
        Icon,
    },
    props: {
        info: Object,
        managerInstace: {
            type: Object,
            required: true,
        },
        showBadge: Boolean,
        showLabel: Boolean,
        bubbleType: {
            type: String,
            default: 'rect',
        },
        bubbleColor: {
            type: String,
            default: '#009EFF',
        },
        iconType: {
            type: String,
            default: 'common',
        },
        iconName: {
            type: String,
            default: 'camera-4',
        },
        iconColor: {
            type: String,
            default: '#ffffff',
        },
        needDetail: {
            type: Boolean,
            default: true,
        },
        needHover: {
            type: Boolean,
            default: true,
        },
    },
    setup(props) {
        const marker = ref();
        const markerId = uuidV4();

        const {ratio} = useUnit();

        const getLabelWidth = computed(() => {
            if (!props.showLabel) return '0px';
            const textWidth = getTextWidth(props.info.label, 16 * ratio.value);
            return `${textWidth + 14 * ratio.value}px`;
        });

        const getLabelHoverWidth = computed(() => {
            if (!props.showLabel) return '0px';
            const textWidth = getTextWidth(props.info.label, 16 * ratio.value);
            return props.needDetail ? `${textWidth + 92 * ratio.value}px` : `${textWidth + 14 * ratio.value}px`;
        });

        const getSlotTop = computed(() => `${(props.showLabel ? 40 : 0) * ratio.value}px`);

        const getShowMarker = computed(() => {
            const {position} = props.info;
            const {managerInstace} = props;
            return position?.length >= 2 && managerInstace;
        });

        function addMarker() {
            if (!getShowMarker.value) return;
            props.managerInstace.addDOM(
                markerId,
                props.info.position,
                marker.value,
                {
                    offset: [0, 0],
                }
            );
        }

        function removeMarker() {
            if (!props.managerInstace) return;
            props.managerInstace.removeDOMByName(markerId);
        }

        function handleClickMarker(e) {
            if (!isFunction(props?.info.clickCallback)) return;
            props.info.clickCallback(props?.info);
        }

        function handleMouseEnter() {
            if (!isFunction(props?.info.mouseenterCallback)) return;
            props.info.mouseenterCallback(props?.info);
        }

        function handleMouseLeave() {
            if (!isFunction(props?.info.mouseleaveCallback)) return;
            props.info.mouseleaveCallback(props?.info);
        }

        watch(
            () => props.info.position,
            () => {
                removeMarker();
                addMarker();
            }
        );

        onMounted(() => {
            addMarker();
        });

        onUnmounted(() => {
            removeMarker();
        });

        return {
            marker,
            getLabelWidth,
            getLabelHoverWidth,
            handleClickMarker,
            handleMouseEnter,
            handleMouseLeave,
            getSlotTop,
            getShowMarker,
        };
    },
};
</script>

<style lang="less" scoped>
.bubble-marker {
    display: flex;
    position: relative;

    &__hover {
        cursor: pointer;
    }

    &__hover:hover &__bubble-border,
    &:hover &__detail {
        opacity: 1;
    }

    &__hover:hover &__label {
        width: v-bind(getLabelHoverWidth);
    }

    &__hover:hover &__bubble-count,
    &__hover:hover &__name {
        color: #fff;
        border-color: #fff;
    }

    &__icon {
        margin-bottom: 2px;
    }

    &__bubble {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        position: relative;
        width: 42px;
        height: 42px;

        &-bg,
        &-border {
            position: absolute;
            left: 0;
            top: 0;
        }

        &-border {
            opacity: 0;
            transition: opacity .2s;
        }

        &-icon {
            position: relative;
            margin-bottom: 4px;
        }

        &-count {
            position: absolute;
            right: 0;
            top: 0;
            min-width: 20px;
            height: 16px;
            padding: 0 6px;
            line-height: 16px;
            font-size: 12px;
            color: rgba(255, 255, 255, .8);
            background-color: #000;
            border-radius: 4px;
            transform: translate(60%, -50%);
            z-index: 10;
            border: 1px solid #4b4b4b;
        }
    }

    &__label {
        z-index: 10;
        position: absolute;
        top: -1px;
        left: 40px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        margin-left: 2px;
        border-radius: 4px;
        background-color: rgba(0, 0, 0, .8);
        padding: 10px 8px;
        font-size: 16px;
        white-space: nowrap;
        overflow: hidden;
        width: v-bind(getLabelWidth);
        transition: width .2s linear;
    }

    &__name {
        color: rgba(255, 255, 255, .8);
    }

    &__detail {
        flex-shrink: 0;
        position: relative;
        margin-left: 4px;
        padding-left: 4px;
        color: #02e8d8;
        width: 60px;
        opacity: 0;

        &::before {
            position: absolute;
            left: 0;
            top: 50%;
            margin-top: -4px;
            content: '';
            width: 1px;
            height: 8px;
            background-color: #505050;
        }
    }

    &__slot {
        position: absolute;
        left: 44px;
        z-index: 1;
    }
}
</style>