<template>
    <div class="app">
        <router-view/>
    </div>
</template>

<script>
import {screenWidth, screenHeight} from '@/utils/common.js';
import {onMounted, onBeforeUnmount} from 'vue';
export default {
    name: 'App',
    setup() {
        const resizeFn = () => {
            screenWidth.value = document.documentElement.clientWidth || document.body.clientWidth;
            screenHeight.value = document.documentElement.clientHeight || document.body.clientHeight;
        };

        onMounted(() => {
            resizeFn();
            window.addEventListener('resize', resizeFn);
        });

        onBeforeUnmount(() => {
            window.removeEventListener('resize', resizeFn);
        });
    },
};
</script>

<style lang='less' scoped>
.app {
    width: 100%;
    height: 100%;
}

::v-deep .mapv-overlay-pane {
    height: 0 !important;
    width: 0 !important;
}
</style>