<template>
    <div class="data-card">
        <div class="data-card__top">
            <div class="data-card__icon">
                <icon :name="info.icon"/>
            </div>
            <div>{{ info.title }}</div>
        </div>
        <div class="data-card__middle">{{ info.enTitle }}</div>
        <div class="data-card__bottom">
            <span>{{ info.value }}</span>
            <span>{{ info.unit }}</span>
        </div>
    </div>
</template>

<script setup>
import {Icon} from '@/components/Common';

defineProps({
    info: {
        type: Object,
        default: () => ({}),
    },
});

</script>

<style lang="less" scoped>
.data-card {
    padding: 24px;
    height: 150px;
    background-color: rgba(18, 74, 166, .3);
    border-top: 1px solid;
    border-image: linear-gradient(to right, rgb(36, 104, 242), rgba(1, 255, 229, .5)) 1;
    backdrop-filter: blur(10px);

    &__top {
        display: flex;
        align-items: center;
        font-size: 20px;
        font-family: 'PingFang';
        color: #fff;
    }

    &__middle {
        font-family: 'RoboData';
        text-transform: uppercase;
        margin-top: 6px;
        padding-left: 42px;
        font-size: 14px;
        white-space: nowrap;
    }

    &__bottom {
        text-align: right;
        margin-top: 14px;

        span:nth-child(1) {
            font-size: 42px;
            font-family: 'RoboData';
            color: #fff;
            margin-right: 4px;
        }
    }

    &__icon {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #fff;
        border-top-width: 2px;
        margin-right: 8px;
    }
}
</style>