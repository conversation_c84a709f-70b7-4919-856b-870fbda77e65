<template>
    <div class="failure-distribution">
        <multiple-data-bar
            v-if="list[0].data.length && list[1].data.length"
            :data="list" type="multiple"
            y-name="单位：台"
            :tooltip-formatter="tooltipFormatter"
        />
        <no-data v-else/>
    </div>
</template>

<script>
import {MultipleDataBar, NoData} from '@/components/Common';
import {getDeviceFault} from '@/api/equipment/equipmentdisplay';
import {ref} from 'vue';
export default {
    name: 'failureDistribution',
    props: {
        deviceType: {
            type: String,
            default: '',
        },
    },
    components: {
        NoData,
        MultipleDataBar,
    },
    setup(props) {
        const list =  ref([
            {
                name: '设备数量',
                color: 'rgb(36, 104, 242)',
                data: [],
            },
            {
                name: '故障数量',
                color: 'rgb(87, 255, 213)',
                data: [],
            },
        ]);

        const init = () => {
            getDeviceFault(props.deviceType).then(res => {
                if (res.data && res.data.length) {
                    list.value[0].data = res.data.map(item => ({
                        ...item,
                        name: item.stake,
                        value: item.deviceNum,
                    }));
                    list.value[1].data = res.data.map(item => ({
                        ...item,
                        name: item.stake,
                        value: item.faultNum,
                    }));
                }
                else {
                    list.value[0].data = [];
                    list.value[1].data = [];
                }
            });
        };

        const tooltipFormatter = e => {
            return `<div class="tooltip-format">
                <div class="title-rb">${e[0].data.stake}</div>
                <div class="info">
                    <div class="item">
                        <div class="item-name">故障数量</div>
                        <div class="item-info">${e[0].data.faultNum}</div>
                    </div>
                    <div class="item">
                        <div class="item-name">设备数量</div>
                        <div class="item-info">${e[0].data.deviceNum}</div>
                    </div>
                    <div class="item">
                        <div class="item-name">故障率</div>
                        <div class="item-info">${e[0].data.faultRate}%</div>
                    </div>
                </div>
            </div>`;
        };

        return {
            list,
            init,
            tooltipFormatter,
        };
    },
};
</script>

<style lang="less" scoped>
.failure-distribution {
    width: 1184px;
    height: 190px;
}
</style>