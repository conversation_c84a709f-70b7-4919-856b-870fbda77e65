<template>
    <div class="maintain">
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="设备健康指数列表" name="Health"><health/></el-tab-pane>
            <el-tab-pane label="故障处理清单列表" name="Breakdown"><breakdown/></el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
import {ref} from 'vue';
import Breakdown from './Breakdown/index.vue';
import Health from './Health/index.vue';
import {Tabs, TabPane} from 'element-ui';
export default {
    name: 'Maintain',
    components: {
        Breakdown,
        Health,
        [Tabs.name]: Tabs,
        [TabPane.name]: TabPane,
    },
    setup(props) {
        const activeName = ref('Health');

        return {
            activeName,
        };
    },
};
</script>

<style lang="less" scoped>
.maintain {
    height: 100%;

    :deep(.el-tabs__nav-wrap) {
        &::after {
            width: 0;
        }
    }
}
</style>
