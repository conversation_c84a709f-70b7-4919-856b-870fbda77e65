export default function useCancelRequest(request) {
    return (url, params, config) => {
        const controller = new AbortController();
        config = {
            ...(config || {}),
            signal: controller.signal,
        };
        const data = request(url, params, config);
        const cancel = () => {
            controller.abort();
        };
        return [
            data,
            cancel,
        ];
    };
};
