// 获取宏观地图数据
import {ref, unref} from 'vue';
import {
    getMacroPlan,
    getMesoPlan,
    getMicro2dPlan,
    getPlanDetail,
    getUnExecutList,
    getHistogram,
} from '@MaintainPlanLayer/api.js';
import {
    micro3dMapTypeDictMap,
    mapTypeDictMap,
    auditStatusDictMap,
} from '@MaintainPlanLayer/config';
import {isEmpty} from 'lodash';

export const showToolBtn = ref(true);
export const collapse = ref(false);
export const orgId = ref('');
export const sectionId = ref('');
export const periodType = ref('');
// 地图哪个层级角度
export const mapType = ref(mapTypeDictMap.MACRO);

export const macroData = ref([]);
export const mesoData = ref([]);
export const micro2dData = ref([]);
export const micro3dData = ref({});
export const pendingPlanList = ref([]);
export const maintainChartData = ref({
    totalNum: 0,
    totalMoney: 0,
    list: [],
});

// 微观三维地图类型 PLAN-显示计划列表  EMULATION-显示仿真
export const micro3dMapType = ref(micro3dMapTypeDictMap.PLAN);

export async function fetchMacroData() {
    const type = unref(periodType);
    const {data = []} = await getMacroPlan(type);
    macroData.value = data.map(i => ({
        pointId: i.orgId,
        position: [i.orgLng, i.orgLat, i.orgAlt],
        pointName: i.orgShortName,
        pointValue: i.num,
    }));
}

export async function fetchMesoData() {
    const planType = unref(periodType);
    const id = unref(orgId);
    const {data = []} = await getMesoPlan({
        planType,
        orgId: id,
    });
    mesoData.value = data.map(i => ({
        pointId: i.sectionId,
        position: [i.sectionLng, i.sectionLat, i.sectionAlt],
        pointName: i.sectionName,
        pointValue: i.num,
    }));
}

export async function fetchMicro2dData() {
    const planType = unref(periodType);
    const sectionIds = unref(sectionId);
    const {data = []} = await getMicro2dPlan({
        planType,
        sectionIds,
    });
    micro2dData.value = data.map(i => ({
        pointList: i.pointList,
        sectionName: i.sectionName,
        sectionId: i.sectionId,
        planList: i.planInfoList?.map(plan => ({
            position: [plan.startLng, plan.startLat, plan.startAlt],
            planId: plan.planId,
            type: plan.planClass,
            warning: plan.isPlan === 0,
        })),
    }));
}

/**
 * 按需转换成组件所需流程状态
 * 后端返回：1创建 2修改 3审核 4执行
 * 组件所需：1-创建，2-调整，3-审核不通过，4-审核通过 5-计划执行中
 * @param {number} flowType 后端返回流程状态
 * @param {number} auditStatus 审核状态
 * @returns 组件所需流程状态
 */
function transfoFlowStatus(flowType, auditStatus) {
    //  流程为审核 且审核状态为通过
    if (flowType === '3' && auditStatus === 1) {
        return 4;
    }
    //  流程为审核 且审核状态为不通过
    else if (flowType === '3' && auditStatus === 2) {
        return 3;
    }
    const flowTypeMap = {
        1: 1,
        2: 2,
        4: 5,
    };
    return flowTypeMap[flowType];

}

export async function fetchMicro3dData(id) {
    const {data = {}} = await getPlanDetail(id);
    micro3dData.value = {
        ...data,
        planId: data.planId,
        position: [data.startLng, data.startLat, data.startAlt],
        type: data.planClass,
        warning: data.isPlan === 0,
        sectionId: data.sectionId,
        flowList: data?.flowList?.map(flow => {
            const {
                flowType,
                startTime,
                endTime,
                planClass,
                sectionName,
                startMilepost,
                endMilepost,
                direction,
                lane,
                operator,
                auditTime,
                auditStatus,
                executeStatus,
                executeTime,
                operatorTime,
                isPlan,
            } = flow;
            return {
                operator,
                auditTime,
                auditStatus,
                executeTime,
                executeStatus,
                closeLane: lane,
                executePlan: isPlan,
                planEndTime: endTime,
                maintainType: planClass,
                planStartTime: startTime,
                adjustTime: operatorTime,
                createTime: operatorTime,
                status: transfoFlowStatus(flowType, auditStatus),
                maintainRoadName: `${sectionName} ${direction} ${startMilepost}至${endMilepost}`,
            };
        }),
    };
}

export async function fetchPendingPlanList() {
    const parmas = {
        planType: unref(periodType),
    };
    switch (unref(mapType)) {
        case mapTypeDictMap.MESO:
            parmas.orgId = unref(orgId);
            break;
        case mapTypeDictMap.MICRO_2D:
            parmas.sectionIds = unref(sectionId);
            break;
    }
    const {data = []} = await getUnExecutList(parmas);
    pendingPlanList.value = data.map(i => ({
        orgId: i.orgId,
        planId: i.planId,
        startTime: i.startTime,
        endTime: i.endTime,
        status: auditStatusDictMap[i.auditStatus],
        sectionId: i.sectionId,
        roadName: `${i.sectionName} ${i.direction} ${i.startMilepost}至${i.endMilepost}`,
    }));
}

export async function fetchMaintainChartData() {
    const parmas = {
        planType: unref(periodType),
    };
    switch (unref(mapType)) {
        case mapTypeDictMap.MESO:
            parmas.orgId = unref(orgId);
            break;
        case mapTypeDictMap.MICRO_2D:
            parmas.sectionIds = unref(sectionId);
            break;
    }
    const {data = {}} = await getHistogram(parmas);
    maintainChartData.value = {
        totalNum: data.num,
        totalMoney: data.totalMoney || 0,
        list: data?.list.map(i => {
            const [unExecuted, executedError, executed] = i.executeList;
            return {
                type: i.planClass,
                // 未执行
                unExecuted,
                // 未按计划执行
                executedError,
                // 已执行
                executed,
            };
        }),
    };
}

export async function fetchStatisticsData() {
    await Promise.all([fetchPendingPlanList(), fetchMaintainChartData()]);
}

export function initMacroMap() {
    macroData.value = [];
    mapType.value = mapTypeDictMap.MACRO;
    fetchMacroData();
    fetchStatisticsData();
};

export function initMesoMap() {
    mesoData.value = [];
    mapType.value = mapTypeDictMap.MESO;
    fetchMesoData();
    fetchStatisticsData();
};

/**
 * 初始化微观二维地图
 */
export async function initMicro2dMap() {
    micro2dData.value = [];
    mapType.value = mapTypeDictMap.MICRO_2D;
    fetchMicro2dData();
    fetchStatisticsData();
}

/**
 * 初始化微观二维地图
 */
export async function initMicro3dMap(id) {
    micro3dData.value = {};
    mapType.value = mapTypeDictMap.MICRO_3D;
    fetchMicro3dData(id);
}

/**
 * 返回
 */
export function handleBack() {
    // 当前所在图层
    const currentMap = unref(mapType);
    const optMap = {
        [mapTypeDictMap.MESO]: () => {
            orgId.value = '';
            sectionId.value = [];
            if (isEmpty(macroData.value)) {
                initMacroMap();
                return;
            }
            mapType.value = mapTypeDictMap.MACRO;
            fetchStatisticsData();
        },
        [mapTypeDictMap.MICRO_2D]: () => {
            sectionId.value = '';
            if (isEmpty(mesoData.value)) {
                initMesoMap();
                return;
            }
            mapType.value = mapTypeDictMap.MESO;
            fetchStatisticsData();
        },
        [mapTypeDictMap.MICRO_3D]: () => {
            /**
             * 当前在微观三维显示仿真，则返回显示养护计划列表，
             * 否侧返回微观二维
             */
            if (unref(micro3dMapType) === micro3dMapTypeDictMap.EMULATION) {
                micro3dMapType.value = micro3dMapTypeDictMap.PLAN;
                return;
            }
            if (isEmpty(micro2dData.value)) {
                initMicro2dMap();
                return;
            }
            mapType.value = mapTypeDictMap.MICRO_2D;
        },
    };
    const opt = optMap[currentMap];
    opt();
}

/**
 * 养护计划类型选择框变化回调
 */
export function afterChangePeriodType() {
    const optMap = {
        [mapTypeDictMap.MACRO]: () => initMacroMap(),
        [mapTypeDictMap.MESO]: () => initMesoMap(),
        [mapTypeDictMap.MICRO_2D]: () => initMicro2dMap(),
    };
    const opt = optMap[unref(mapType)];
    opt();
}