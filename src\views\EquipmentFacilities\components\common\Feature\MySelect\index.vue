<template>
    <div>
        <span>{{ title }}</span>
        <el-select
            v-model="value"
            :placeholder="placeholder"
            size="small"
            class="el-icon-select"
            @change="changeFn"
        >
            <template #prefix>
                <span class="icon el-icon-location-information"></span>
            </template>
            <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
        </el-select>
    </div>
</template>

<script>
import {Select, Option} from 'element-ui';
export default {
    props: {
        options: {
            type: Array,
            default: () => ([]),
        },
        value: {
            type: String,
            default: '',
        },
        title: {
            type: String,
            default: '',
        },
        placeholder: {
            type: String,
            default: '请选择',
        },
        size: {
            type: String,
            default: 'small',
        },
    },
    components: {
        [Select.name]: Select,
        [Option.name]: Option,
    },
    model: {
        prop: 'value',
        event: 'update:value',
    },
    setup(props, {emit}) {
        const changeFn = e => {
            emit('update:value', e);
            emit('change', e);
        };
        return {
            changeFn,
        };
    },
};
</script>

<style lang="less" scoped>

</style>