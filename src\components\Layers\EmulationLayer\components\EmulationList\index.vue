<template>
    <div class="box-b">
        <div class="emulation-list">
            <div class="header">
                <span>{{ titleConfig?.title }}</span>
                <span v-if="titleConfig?.btnName" @click="onAdd">{{ titleConfig?.btnName }}</span>
            </div>
            <div class="content">
                <div v-if="emulationList.length">
                    <div
                        v-for="item in emulationList"
                        :key="item.id"
                        class="item"
                    >
                        <div class="left">
                            <div class="l-b">
                                <span class="name" :title="item.name">{{ item.name }}</span>
                                <span class="time">{{ item.emulationStartTime }}</span>
                            </div>
                            <span
                                class="status"
                                :style="{'--color': ESC[item.status]}"
                            >{{ formatStatus(item.status) }}</span>
                            <div class="r-b" @click="onReport(item)">
                                <i class="el-icon-document"></i>
                                <span class="text">仿真报告</span>
                            </div>
                        </div>
                        <div class="right">
                            <el-button v-if="item.status === 1" @click="onRun(item)">运行</el-button>
                            <el-button
                                v-else
                                :disabled="item.status === 2"
                                @click="onCheck(item)"
                            >查看</el-button>
                            <el-button
                                :disabled="[2, 3].includes(item.status)"
                                @click="onEdit(item.id, item)"
                            >修改</el-button>
                            <el-button @click="onCopy(item.id, item)">复制</el-button>
                            <el-popconfirm
                                icon="el-icon-info"
                                icon-color="red"
                                title="确定删除该仿真方案?"
                                @onConfirm="onDel(item.id, item)"
                            >
                                <el-button slot="reference" style="display: inherit;">删除</el-button>
                            </el-popconfirm>
                            <!-- <span class="item" @click="onReset(item.id)">重置</span> -->
                        </div>
                    </div>
                </div>
                <div v-else class="no-data">
                    <no-data/>
                </div>
            </div>
            <div class="footer">
                <div class="total">共搜索到<span>{{ total }}</span>条数据</div>
                <div class="page-btn">
                    <i class="el-icon-arrow-left icon" @click="onPrev"></i>
                    <div class="page">
                        <span>{{ pageNo }}</span>/<span>{{ pageCount }}</span>
                    </div>
                    <i class="el-icon-arrow-right icon" @click="onNext"></i>
                </div>
            </div>
        </div>
        <emulation-report
            v-if="fileVisible"
            :scheme-id="schemeId"
            :file-visible="fileVisible"
            :emulation-time="emulationTime"
            @close="fileVisible = false"
        />
    </div>
</template>

<script>
import {computed, reactive, toRefs, watch} from 'vue';
import {emulation_status, EmulationStatusColor} from './config';
import {emulationList, delScheme, copyScheme, resetEmulation} from '../../api';
import {Message, Button, Popconfirm} from 'element-ui';
import {v4 as uuidv4} from 'uuid';
import {viewTo} from '@/utils';
import {strategyId} from '@EmulationLayer/store/index';
import {createConfig} from '@EmulationLayer/store/emulationCreate';

import NoData from '@EmulationLayer/components/NoData.vue';
import EmulationReport from '../EmulationReport/index.vue';
import dayjs from 'dayjs';

export default {
    name: 'EmulationList',
    components: {
        EmulationReport,
        [Button.name]: Button,
        [Popconfirm.name]: Popconfirm,
        NoData,
    },
    props: {
        titleConfig: {
            type: Object,
            default: () => {
                return {
                    title: '交通仿真方案列表',
                    btnName: '新增',
                };
            },
        },
        emulationType: {
            type: Number,
            default: 1,
        },
        info: {
            type: Array,
            default: () => null,
        },
        createConfig: {
            type: Object,
            default: () => createConfig,
        },
        platformSource: {
            type: [String, Number],
            default: 'emulation',
        },
    },
    setup(props, {emit}) {
        const state = reactive({
            total: 0,
            pageNo: 1,
            pageSize: 10,

            emulationList: [],

            schemeId: '',
            emulationTime: null,
        });

        const pageCount = computed(() => Math.ceil(state.total / state.pageSize));

        const info =  computed(() => props.info);

        const emulation_type =  computed(() => props.emulationType);

        const platform_source = computed(() => props.platformSource);

        const ui = reactive({
            fileVisible: false,
        });
        const formatStatus = val => {
            const cur = emulation_status.find(item => item.code === val);
            return cur ? cur.name : '';
        };

        const getData = async () => {
            // 如果数据是参数参数则不请求内部接口了 主要为了把组件提供给其他人去使用
            if (info.value) {
                const {total = 0, items = []} = info.value;
                state.total = total;
                state.emulationList = items;
                return;
            }

            try {
                const res = await emulationList({
                    emulationType: emulation_type.value,
                    pageNo: state.pageNo,
                    pageSize: state.pageSize,
                    mapLayer: platform_source.value,
                });
                const {total = 0, items = []} = res.data;
                state.total = total;
                state.emulationList = items;
            }
            catch (error) {
                console.log('error', error); // eslint-disable-line
            }
        };

        const onReset = async id => {
            const res = await resetEmulation(id);
            state.pageNo = 1;
            getData();
        };

        const onAdd = () => {
            emit('add');
        };

        const onDel = async (id, item) => {
            // info存在说明是外部调用组件方式 直接把事件暴露出去让客户自己去处理逻辑
            if (info.value) {
                emit('onDel', item);
                return;
            }
            const {code} = await delScheme(id);
            if (code === 200) {
                Message.success('删除成功');
                getData();
            }
        };

        const addTrafficEvent = row => {
            let eventPositionArr = row.eventPosition.split(',');
            let position = [Number(eventPositionArr[0]), Number(eventPositionArr[1])];
            viewTo({
                zoom: 20,
                center: position,
            }, 1000);
            if (row.eventType === -1) {
                return;
            }
        };

        const onEdit = (id, item) => {
            item.eventList?.forEach(el => {
                if (el.eventType !== 23 && el.eventType !== -1) {
                    addTrafficEvent(el);
                }
            });
            emit('edit', id, item);
        };

        const onCopy = async (id, item) => {
            // info存在说明是外部调用组件方式 直接把事件暴露出去让客户自己去处理逻辑
            if (info.value) {
                emit('onCopy', item);
                return;
            }
            const {code} = await copyScheme(id);
            if (code === 200) {
                Message.success('复制成功');
                getData();
            }
        };
        const getConstruction = (data, type, remove = true) => {
            if (remove) {
                this.highspeedrefs.removePolygon();
                this.highspeedrefs.removeModel();
            }
            const areaData = data?.constructionGeoList?.map(item => {
                return item.map(el => [el.split(',')[0], el.split(',')[1], 1]);
            });
            const edgeData = data?.constructionCornerGeoList;

            if (areaData && areaData.length > 0) {
                areaData.forEach((item, index) => {
                    this.highspeedrefs.addPolygon(`area_more_${uuidv4()}_${index}`, item, type);
                });
            }
            else {
                this.highspeedrefs.removePolygon();
            }
            if (edgeData && edgeData.length > 0) {
                if (data.eventType === 23) {
                    viewTo({
                        zoom: 20,
                        center: data.constructionGeoList[0][0].split(','),
                    }, 1000);
                }
                else {
                    edgeData.forEach(item => {
                        item.forEach(el => {
                            this.highspeedrefs.addModel(
                                'maplayer/assets/models/broach.glb',
                                el.split(','),
                                `more_${el.eventPosition}_${uuidv4()}`
                            );
                        });
                    });
                }
            }
            else {
                this.highspeedrefs.removeModel();
            }
        };

        const onRun = item => {
            // const weather = weather_type_list.find(i => i.code === item.weatherScene)?.key;
            // this.highspeedrefs.tabWeather(weather);
            // info存在说明是外部调用组件方式 直接把事件暴露出去让客户自己去处理逻辑
            if (info.value) {
                emit('onRun', item);
                return;
            }
            item.eventList?.filter(e => e.eventType !== 23).forEach(item => {
                addTrafficEvent(item);
            });

            // emulationInfo.value = item;
            strategyId.value = item.id;
        };
        const onCheck = item => {
            // info存在说明是外部调用组件方式 直接把事件暴露出去让客户自己去处理逻辑
            if (info.value) {
                emit('onCheck', item);
                return;
            }
            // emulationInfo.value = item;
            strategyId.value = item.id;

            item.eventList?.forEach(item => {
                if (item.eventType !== 23 && item.eventType !== -1) {
                    addTrafficEvent(item);
                }
            });

            // const res = await checkEmulation(item.id);

        };
        const onReport = item => {
            const {id, emulationStartTime, emulationEndTime, status} = item;
            // info存在说明是外部调用组件方式 直接把事件暴露出去让客户自己去处理逻辑
            if (info.value) {
                emit('onReport', item);
                return;
            }
            if (status === 1) return Message.warning('仿真方案未进行仿真，不存在仿真评价!');
            if (status === 2) return Message.warning('仿真运行中，请结束后查看仿真评价!');

            ui.fileVisible = true;
            state.schemeId = id;
            state.emulationTime = {
                startTime: dayjs(emulationStartTime).format('HH:mm'),
                endTime: dayjs(emulationEndTime).format('HH:mm'),
            };
        };

        const onPrev = () => {
            if (state.pageNo <= 1) return;
            // info存在说明是外部调用组件方式 直接把事件暴露出去让客户自己去处理逻辑
            if (info.value) {
                emit('onPrev');
                return;
            }
            state.pageNo -= 1;
            getData();
        };
        const onNext = () => {
            if (state.pageNo >= pageCount.value) return;
            // info存在说明是外部调用组件方式 直接把事件暴露出去让客户自己去处理逻辑
            if (info.value) {
                emit('onNext');
                return;
            }
            state.pageNo += 1;
            getData();
        };

        watch(() => [props.formatStatus, props.emulationType], () => {
            state.pageNo = 1;
            getData();
        }, {
            immediate: true,
        });

        watch(() => props.createConfig.show, val => {
            console.log('createConfig.show--', val);
            if (!val) {
                state.pageNo = 1;
                getData();
            }
        }, {
            immediate: true,
        });

        return {
            ...toRefs(state),
            ...toRefs(ui),
            pageCount,
            onPrev,
            onNext,
            onReset,
            onEdit,
            onCopy,
            onCheck,
            onRun,
            onDel,
            onReport,
            onAdd,
            formatStatus,
            ESC: EmulationStatusColor,
            emulation_status,
        };
    },
};
</script>

<style scoped lang="less">
@import url('@EmulationLayer/assets/css/common.less');

.box-b {
    height: 100%;
    overflow: hidden;
}

.emulation-list {
    width: 100%;
    height: 100%;
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    // max-height: 100%;
    // position: absolute;
    // left: 1000px;
    // top: 100px;
    // float: right;
    // height: calc(100vh - 100px - 40px);
    // z-index: 3;
    .background-card();

    .header {
        // height: 48px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 24px;
        background: rgba(8, 176, 137, .2);
        cursor: pointer;

        span:nth-child(1) {
            font-size: 16px;
            color: #fff;
        }

        span:nth-child(2) {
            font-size: 14px;
            color: #08d6a5;
        }
    }

    .content {
        flex: 1;
        overflow-y: auto;
    }

    .item {
        padding: 16px 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 16px;
        border-bottom: 1px solid rgba(255, 255, 255, .2);

        .left {
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex: 1;
            height: 52px;

            .l-b {
                width: 140px;
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                line-height: 22px;

                .name {
                    display: block;
                    font-size: 16px;
                    color: #e5f1ff;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                }

                .time {
                    font-size: 14px;
                    color: #6f88a7;
                }
            }

            .status {
                padding: 3px 9px;
                border-radius: 3px;
                height: max-content;
                color: var(--color);
                border: 1px solid var(--color);
            }

            .el-icon-document {
                display: inline-block;
                font-size: 14px;
                color: #49c3ff;
            }

            .text {
                font-size: 12px;
                color: #49c3ff;
            }
        }

        .right {
            cursor: pointer;
            display: grid;
            gap: 8px;
            grid-template-columns: repeat(2, 50px);

            /deep/.el-button {
                padding: 3px 10px;
                background: #07af87;
                border: 1px solid #07af87;
                border-radius: 1px;
                font-size: 14px;
                color: #fff;
                margin: 0;

                &:last-child {
                    background: rgba(255, 101, 101, .8);
                    border: 1px solid #ff6565;
                }

                &.disabled {
                    pointer-events: none;
                    cursor: not-allowed;
                }
            }
        }
    }

    .footer {
        display: flex;
        justify-content: space-between;
        padding: 24px;
        align-items: center;
        cursor: pointer;

        .total {
            font-size: 14px;
            color: #c4cfde;

            span {
                margin: 0 8px;
            }
        }

        .page-btn {
            display: flex;

            .icon {
                color: #b2d6ff;
            }

            .page {
                color: #6f88a7;

                span {
                    margin: 0 16px;
                }

                span:nth-child(1) {
                    color: #e5f1ff;
                }
            }
        }
    }
}

.no-data {
    margin-top: 300px;
}

</style>