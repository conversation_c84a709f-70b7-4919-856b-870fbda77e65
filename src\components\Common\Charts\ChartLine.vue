<template>
    <!-- 折线图 -->
    <div ref="rootCharts" class="rootCharts"></div>
</template>
<script>

import {echarts} from './common';
import {LineChart} from 'echarts/charts';
import {onBeforeUnmount, onMounted, ref, watch} from 'vue';

echarts.use([LineChart]);

export default {
    data() {
        return {
        };
    },
    props: {
        data: {
            type: Array,
        },
    },
    setup(props, {emit}) {

        let chart = null;
        const rootCharts = ref(null);

        const resize = () => {
            if (chart) {
                chart.resize();
            }
        };

        const update = value => {
            console.log('value--', value);
            const xAxis = [];
            const yAxis = [];

            value?.forEach(item => {
                xAxis.push(item.name);
                yAxis.push(item.value);
            });
            console.log('chart--', chart);
            console.log('xAxis--', xAxis, yAxis);


            chart.setOption({
                grid: {
                    top: 20,
                    bottom: 30,
                    right: 20,
                    left: 40,
                },
                tooltip: {
                    trigger: 'axis',
                    // backgroundColor: 'rgb(56 145 255 / 54%)',
                    // borderColor: 'transparent',
                    // textStyle: {
                    //     color: '#fff',
                    //     fontFamily: 'robo data',
                    // },
                    // extraCssText: 'borderRadius: 0',
                },
                xAxis: {
                    type: 'category',
                    splitLine: false,
                    boundaryGap: false,
                    data: xAxis,
                    axisLabel: {
                        fontSize: 12,
                        fontFamily: 'robo data',
                    },
                },
                yAxis: {
                    type: 'value',
                    axisLine: false,
                    axisLabel: {
                        fontSize: 12,
                        fontFamily: 'robo data',
                    },
                },
                series: [
                    {
                        name: 'Email',
                        type: 'line',
                        stack: 'Total',
                        data: yAxis,
                        //   areaStyle: {
                        //         normal: {
                        //             color: new echarts.graphic.LinearGradient(
                        //                 0,
                        //                 0,
                        //                 0,
                        //                 1,
                        //                 [
                        //                     {
                        //                         offset: 0,
                        //                         color: '#efa86f',
                        //                     },
                        //                     {
                        //                         offset: 1,
                        //                         color: 'rgba(0,0,0,0.1)',
                        //                     },
                        //                 ],
                        //                 false
                        //             ),
                        //         },
                        //     },
                    },
                ],
            });
        };

        const initChart = () => {
            if (!rootCharts.value) {
                return;
            }
            chart = echarts.init(rootCharts.value, 'emulation');
            window.addEventListener('resize', resize);
        };

        watch(() => props.data, update, {
        });

        onMounted(initChart);

        onBeforeUnmount(() => {
            window.removeEventListener('resize', resize);
            chart.dispose();
        });

        return {
            rootCharts,
        };
    },
};

</script>

<style>
.rootCharts {
    width: 100%;
    height: 100%;
}
</style>