import dayjs from 'dayjs';

// 基础配置默认信息
export const baseDefaultInfo = {
    id: undefined,
    // 仿真方案名称
    name: '',
    areaChoose: 1,
    weatherScene: 1,
    highSpeedName: 'S15',
    emulationStartStake: '',
    emulationEndStake: '',
    emulationStartTime: dayjs().subtract(2, 'hours').format('YYYY-MM-DD HH:mm:ss'),
    emulationEndTime: dayjs().subtract(1, 'hours').format('YYYY-MM-DD HH:mm:ss'),
    flowDistance: 1000,
    eventType: '',
    eventLocationType: '',
    eventStartTime: dayjs().subtract(2, 'hours').format('YYYY-MM-DD HH:mm:ss'),
    duration: 60,
    highSpeedName1: 'S15',
    eventStartStake: 'K69+400',
    eventPosition: '',
    direction: 1,
    closeLane: [1, 2],
    limitSpeed: 60,
    upstreamTransitionLength: 100,
    constructionLength: 100,
    downstreamTransitionLength: 100,
    // 施工相关
    constructionCornerGeoList: undefined,
    constructionGeoList: undefined,
};

// 流量配置默认信息
export const flowDefaultInfo = {
    flowInputType: 1,
    flowList: [],
    customFlowStartTime: dayjs().subtract(2, 'hours').format('YYYY-MM-DD HH:mm:ss'),
    customFlowEndTime: dayjs().subtract(1, 'hours').format('YYYY-MM-DD HH:mm:ss'),
};

// 策略配置默认信息
export const strategyDefaultInfo = {
    strategyInputType: 1,
    // 策略选择
    strategyType: [],
    // 开放应急车道配置
    emergencyConfig: {
        controlStake: '',
        position: '',
        controlLength: '',
        limitSpeed: '',
        controlDuration: '',
        type: 1,
    },
    // 行车道控制配置
    laneControlConfig: {
        controlStake: '',
        position: '',
        controlLength: '',
        configList: [],
    },
    // 出入口配置
    entryAndExitConfig: {
        rampName: '',
        configList: [],
    },
};

// 模型配置默认信息
export const modelDefaultInfo = {
    smallVehicleAccel: 2.6,
    bigVehicleAccel: 1.3,
    smallVehicleDecel: 4.5,
    bigVehicleDecel: 4,
    smallVehicleEmergencyDecel: 9,
    bigVehicleEmergencyDecel: 7,
    smallVehicleMaxSpeed: 120,
    bigVehicleMaxSpeed: 100,
    bigCarRate: 10,
};