<template>
    <div>
        <map-component
            ref="mapRef"
            :options="{
                center: [113.29290479974627, 23.57957115989944],
                showSatelliteMap: true,
                showTollStations: false,
            }"
            class="map-box"
            :style="{
                height: height + 'px',
            }"
            @mapLoaded="mapLoaded"
            @clickMap="clickMap"
        />
        <manual-inspection-layer v-if="mapInitStatus"/>
    </div>
</template>

<script>
import {onMounted, ref} from 'vue';
import {Map, ManualInspectionLayer} from '@/index';
import {initManualInspectionLayerConfig} from '@/components/Layers/ManualInspectionLayer/utils/index';

export default {
    components: {
        MapComponent: Map,
        ManualInspectionLayer,
    },
    setup() {
        const mapRef = ref(null);
        const engine = ref(null);
        const height = ref(1080);
        const mapInitStatus = ref(false);
        const apiHost = import.meta.env.MODE === 'test' ? 'http://************:8751' : '';

        const wsHost = ['test', 'development'].includes(import.meta.env.MODE) ? 'ws://************:8751' : `wss://${location.host}`;

        // 地图加载完成后执行的回调函数
        const mapLoaded = () => {
            initManualInspectionLayerConfig({
                engine: mapRef.value.map,
                apiHost,
                wsHost,
            });
            mapInitStatus.value = true;
        };

        // 地图点击后执行的回调函数
        const clickMap = e => {
            // console.log('clickMap===点击地图，参数：', e);
        };

        onMounted(() => {
            height.value = window.innerHeight;
            document.title = '人工巡检图层';
        });

        return {
            mapRef,
            engine,
            height,
            wsHost,
            apiHost,
            mapInitStatus,
            mapLoaded,
            clickMap,
        };
    },
};
</script>

<style scoped>
.map-box {
    position: relative;
    width: 100vw;
}
</style>
