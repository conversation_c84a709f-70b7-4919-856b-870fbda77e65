<template>
    <Card title="ETC门架数据查询" card-type="card-long-2">
        <template #content>
            <api-table
                :columns="columns" :api="getEtcPage"
                :request-params="params" :request-options="{
                    pageField: 'pageNumber',
                    sizeField: 'pageSize',
                    listField: 'result',
                    totalField: 'totalCount',
                }"
            />
        </template>
        <template #titleContent>
            <cardSelect
                @changeRoadFn="changeRoadFn" @changeStakeFn="changeStakeFn"
                @timeChangeFn="timeChangeFn"
            />
        </template>
    </Card>
</template>

<script setup lang="ts">
import {ApiTable, Card} from '@/components/Common';
import {useUnit} from '@/utils';
import cardSelect from '@/views/ServiceManagement/Card/cardSelect.vue';
import {ref, reactive} from 'vue';
const {ratio} = useUnit();
import dayjs from 'dayjs';
import {serviceId} from '@/views/ServiceManagement/utils/index';

import {getEtcPage} from '@/api/serviceManager/index.js';
const params = reactive({
    'startTime': null,
    'endTime': null,
    'startStake': null,
    'roadId': null,
    'pageNumber': 1,
    'pageSize': 10,
    serviceId: serviceId.value,
});

// const testData = {
//     'code': 200,
//     'msg': 'msg_cc1cd9c17807',
//     'ts': 0,
//     'data': {
//         'pageNumber': 1,
//         'pageSize': 20,
//         'totalCount': 20,
//         'result': [
//             {
//                 'id': 'G001544009006010040',
//                 'name': '昆东立交至赤草立交门架',
//                 'dirName': '广州方向',
//                 'dirNo': 1,
//                 'roadId': 'RD1301',
//                 'roadName': '连接新台-连接江罗',
//                 'stakeNumber': 'K3125+450',
//                 'flow': 999,
//                 'reportTime': '2024-09-19 00:00:00',
//             },
//         ],
//     },
// };
const columns = [
    {
        label: '采集设备',
        prop: 'name',
    },
    {
        label: '行车方向',
        prop: 'dirName',
        width: 120 * ratio.value,

    },
    {
        label: '所属路段',
        prop: 'roadName',

    },
    {
        label: '设备桩号',
        prop: 'stakeNumber',
        width: 160 * ratio.value,

    },
    {
        label: '断面流量pcu/h',
        prop: 'flow',
        width: 100 * ratio.value,

    },
    {
        label: '上报时间',
        prop: 'reportTime',
    },
];

const changeStakeFn = e => {
    params.startStake = e;
    // console.log('output->EtcchangeStakeFn', e);
};

const changeRoadFn = e => {
    params.roadId = e;
    console.log('output->changeRoadFn', e);

};
const timeChangeFn = e => {
    params.startTime = dayjs(e[0]).format('YYYY-MM-DD HH:mm:ss');
    params.endTime = dayjs(e[1]).format('YYYY-MM-DD HH:mm:ss');
};
</script>