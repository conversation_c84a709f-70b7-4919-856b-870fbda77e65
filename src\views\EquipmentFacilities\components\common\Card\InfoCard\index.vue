<template>
    <div class="info-card" :style="{'--w': widthPx, '--h': heightPx}">
        <div class="info-card__left">
            <div v-if="needIcon" class="info-card__left__icon">
                <icon :name="info.icon || light"/>
            </div>
            <div class="info-card__left__info">
                <span class="cnTitle" :style="{fontSize: styleObj.cnTitleFontSize * ratio + 'px'}">
                    {{ info.cnTitle }}
                </span>
                <span class="enTitle" :style="{fontSize: styleObj.enTitleFontSize * ratio + 'px'}">
                    {{ info.enTitle }}
                </span>
            </div>
        </div>
        <div class="info-card__right" :style="{fontSize: styleObj.unitFontSize * ratio + 'px'}">
            <span class="value" :style="{fontSize: styleObj.numFontSize * ratio + 'px'}">{{ info.value }}</span>
            <span>{{ info.unit }}</span>
        </div>
    </div>
</template>

<script>
import {Icon} from '@/components/Common';
import {useUnit} from '@/utils/hooks/useUnit';
import {computed} from 'vue';

export default {
    name: 'info-card',
    props: {
        info: {
            type: Object,
            default: () => ({}),
        },
        width: {
            type: [String, Number],
            default: '274',
        },
        height: {
            type: [String, Number],
            default: '72',
        },
        needIcon: {
            type: Boolean,
            default: true,
        },
        styleObj: {
            type: Object,
            default: () => ({
                cnTitleFontSize: 18,
                enTitleFontSize: 14,
                unitFontSize: 14,
                numFontSize: 32,
            }),
        },
    },
    components: {
        Icon,
    },
    setup(props) {
        const {ratio} = useUnit();
        const widthPx = computed(() => ratio.value * props.width + 'px');
        const heightPx = computed(() => ratio.value * props.height + 'px');

        return {
            ratio,
            widthPx,
            heightPx,
        };
    },
};
</script>

<style lang="less" scoped>
.info-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: var(--w);
    height: var(--h);
    padding: 0 0 0 24px;
    background-color: rgba(18, 74, 166, 0.24);
    border-top: 1px solid;
    border-image: linear-gradient(to right, rgb(36, 104, 242), rgba(1, 255, 229, .5)) 1;
    backdrop-filter: blur(10px);
    position: relative;

    &.active {
        background-image: linear-gradient(to right, rgba(1, 255, 229, .3), rgba(1, 255, 229, 0));
    }

    &__left {
        display: flex;
        align-items: flex-start;

        &__icon {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 31px;
            height: 24px;
            margin-right: 9px;
            background: url('@/assets/images/base/iconBg.png') no-repeat center center / 100% 100%;
        }

        &__info {
            display: flex;
            flex-direction: column;
            width: 164px;

            .cnTitle {
                font-family: 'PingFang';
                font-weight: 500;
                color: #fff;
                margin-bottom: 6px;
            }

            .enTitle {
                display: inline-block;
                white-space: nowrap;
                overflow: hidden;
                text-overflow:ellipsis;
                width: 100%;
                font-family: 'RoboData';
                font-weight: 400;
                color: rgba(#fff, .3);
                overflow: hidden;
            }
        }
    }

    &__right {
        position: absolute;
        right: 21px;
        font-weight: 400;
        font-family: 'RoboData';
        color: rgba(#fff, .5);

        .value {
            font-family: 'RoboData';
            color: rgba(#fff, .9);
            margin-right: 4px;
        }
    }
}
</style>