import {round} from 'lodash';
import {onMounted, onUnmounted, ref} from 'vue';

export function useUnit(base = 1080) {

    const ratio = ref(window.innerHeight / base);

    function updateRatio() {
        ratio.value = window.innerHeight / base;
    }

    onMounted(() => {
        window.addEventListener('resize', updateRatio);
    });

    onUnmounted(() => {
        window.removeEventListener('resize', updateRatio);
    });

    function px2rem(px) {
        return round(px / 108, 5);
    }

    return {
        px2rem,
        ratio,
    };
}