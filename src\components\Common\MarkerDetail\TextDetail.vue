<template>
    <div class="container_detail">
        <Card
            class="container_detail_content"
            v-bind="$attrs"
        >
            <template #content>
                <div class="content_detail mt-8">
                    <span
                        v-for="(v, k) in detailInfo?.info" :key="k"
                        class="row"
                    >
                        <span class="key">{{ k }}：</span>
                        <span>{{ v }}</span>
                    </span>
                    <slot v-if="$slots.bottomCustom" name="bottomCustom"></slot>
                </div>
            </template>
        </Card>
    </div>
</template>

<script setup>
import Card from '@/components/Common/Card/markerCard.vue';
import {ref} from 'vue';

const props = defineProps({
    detailInfo: {
        type: Object,
        default: () => ({}),
    },
});

</script>
<style scoped lang="less">
.container_detail {
    position: absolute;
    left: 25px;
    top: -40px;
    border-top: 1px solid rgba(255, 255, 255, .2);

    .container_detail_content {
        width: 400px;

        .content_detail {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;

            .row {
                display: flex;
                justify-content: space-between;
                font-size: 16px;
                width: 100%;
                padding: 6px 12px;
                margin-bottom: 12px;
                background: rgba(0, 98, 167, .3);

                .key {
                    margin-right: 10px;
                    opacity: .7;
                }
            }
        }
    }
}
</style>