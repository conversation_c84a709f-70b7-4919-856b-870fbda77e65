export const eventTypeList = [
    {
        label: '异常停车',
        value: 0,
    },
    {
        label: '行人闯入',
        value: 1,
    },
    {
        label: '交通拥堵',
        value: 2,
    },
    {
        label: '危化品车',
        value: 24,
    },
    // {
    //     label: '排队长度',
    //     value: 3,
    // },
    {
        label: '车辆逆行',
        value: 4,
    },
    {
        label: '抛洒物',
        value: 5,
    },
    // {
    //     label: '机动车驶离',
    //     value: 6,
    // },
    {
        label: '非机动车闯入',
        value: 7,
    },
    // {
    //     label: '异常摩托车',
    //     value: 8,
    // },
    {
        label: '交通事故',
        value: 9,
    },
    // {
    //     label: '火灾烟雾报警',
    //     value: 10,
    // },
    {
        label: '异常低速',
        value: 11,
    },
    {
        label: '占用应急车道',
        value: 12,
    },
    // {
    //     label: '道路积雪事件',
    //     value: 14,
    // },
    // {
    //     label: '压线行驶',
    //     value: 20,
    // },
    {
        label: '施工区域',
        value: 21,
    },
    // {
    //     label: '恶劣气象',
    //     value: 22,
    // },
    {
        label: '货车占用主车道',
        value: 25,
    },
    {
        label: '连续变道',
        value: 25,
    },

];

export const road_direction = [
    {
        code: 0,
        name: '未知',
    },
    {
        code: 1,
        name: '上行',
    },
    {
        code: 2,
        name: '下行',
    },
    {
        code: 3,
        name: '双向',
    },
];

// 实时事件类型
export const burst_event_type_list = [
    {
        code: 0,
        name: '异常停车',
    },
    {
        code: 2,
        name: '交通拥堵',
    },
    {
        code: 5,
        name: '抛洒物',
    },
    {
        code: 9,
        name: '交通事故',
    },
    {
        code: -1,
        name: '无事件',
    },
    // {
    //     code: 21,
    //     name: '施工区域',
    // },
];

// 施工类型
export const build_type_list = [
    {
        code: 21,
        name: '占道施工',
    },
    // {
    //     code: 23,
    //     name: '道路扩建',
    // },
];


export const strategy_list = [
    {
        key: 1,
        name: '开放应急车道',
    },
    {
        key: 2,
        name: '行车道管控',
    },
    {
        key: 3,
        name: '出入口管控',
    },
];


export const gate_list = [
    {
        key: '1',
        name: '入口关闭',
    },
    {
        key: '2',
        name: '入口限流',
        config: true,
    },
    {
        key: '3',
        name: '出口分流',
        config: true,
    },
    // {
    //     key: 4,
    //     name: '车队编组',
    //     config: true,
    // },

];

export const vehicle_type_list = [
    {
        code: 1,
        name: '小车',
    },
    // {
    //     code: 4,
    //     name: '面包车',
    // },
    // {
    //     code: 5,
    //     name: '货车',
    // },
    {
        code: 2,
        name: '大车',
    },
];

// 车道配置
export const lane_config = ['1', '2', '3', '99'];
