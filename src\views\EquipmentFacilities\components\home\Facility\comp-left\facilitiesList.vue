<template>
    <div class="container show-scroll">
        <div class="container-list">
            <div
                v-for="item in list" :key="item.facilityName"
                class="container-item"
            >
                <p class="item-bridge">{{ item.facilityName }}</p>
                <p class="item-stake">{{ item.stake || '暂无信息' }}</p>
                <p class="item-direction">{{ item.direction || '暂无信息' }}</p>
            </div>
        </div>
    </div>
</template>

<script setup>
import {ref, onMounted, watch} from 'vue';
import {getFacilityList} from '@/api/equipment/facilitydisplay';
import {directionDict} from '@/config/maintain';

const props = defineProps({
    type: {
        type: String,
        default: '1',
    },
});

const list = ref([]);

const init = () => {
    getFacilityList(props.type).then(res => {
        list.value = res.data.map(item => ({
            ...item,
            direction: item.roadName || directionDict.get(item.direction + ''),
        }));
    });
};

onMounted(() => init());

watch(() => props.type, () => {
    init();
});
</script>

<style lang="less" scoped>
.container {
    overflow: scroll;
    height: 172px;

    .container-list {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;
        .container-item {
            display: flex;
            align-items: center;
            width: 48%;
            height: 40px;
            line-height: 40px;
            margin-bottom: 4px;
            font-size: 20px;
            font-weight: 500;
            font-family: 'PingFang';
            padding: 0 24px;
            background-color: rgba(8, 35, 79, 0.8);
            color: #fff;
            .item-bridge {
                width: 35%;
                text-align: left;
            }

            .item-stake {
                width: 35%;
                text-align: left;
            }

            .item-direction {
                width: 30%;
                text-align: right;
            }
        }
    }
}
</style>