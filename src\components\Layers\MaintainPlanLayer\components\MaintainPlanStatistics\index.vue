<template>
    <card class="maintain-statistics" title="养护计划统计">
        <template #content>
            <div class="statistics-num">
                <div class="num-item">
                    <div class="num-icon num-icon-1"></div>
                    <div class="num-content">
                        <div class="num-title">养护总数量</div>
                        <div class="num-value num-value-1">
                            {{ formatNumber(info.totalNum) }}
                            <span class="num-unit">{{ info.totalNum >= 10000 ? '万' : '' }}个</span>
                        </div>
                    </div>
                </div>
                <div class="num-item">
                    <div class="num-icon num-icon-2"></div>
                    <div class="num-content">
                        <div class="num-title">养护总金额</div>
                        <div class="num-value">
                            {{ formatNumber(info.totalMoney) }}
                            <span class="num-unit">{{ info.totalMoney >= 10000 ? '万' : '' }}元</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="maintain-plan-chart">
                <div class="chart-title-box">
                    <div class="chart-title">养护计划数量</div>
                    <div class="tab-box">
                        <span
                            class="tab-item"
                            :class="{'is-active': chartType === 'num'}"
                            @click="changeChartType('num')"
                        >
                            数量
                        </span>
                        <span
                            class="tab-item"
                            :class="{'is-active': chartType === 'money'}"
                            @click="changeChartType('money')"
                        >
                            金额
                        </span>
                    </div>
                </div>
                <div class="chart-content">
                    <common-chart :option="getChartOption"/>
                </div>
            </div>
        </template>
    </card>
</template>

<script>
import Card from '@/components/Common/Card/index.vue';
import CommonChart from '@MaintainPlanLayer/components/CommonChart/index.vue';
import {computed, ref, unref} from 'vue';

export default {
    name: 'MaintainStatistics',
    props: {
        info: {
            type: Object,
            default: () => ({
                totalNum: 0,
                totalMoney: 0,
                list: [],
            }),
        },
    },
    components: {
        Card,
        CommonChart,
    },
    setup(props) {
        const chartType = ref('num');

        function formatUnit(key, val) {
            const unit = {
                num: '个',
                money: '元',
            };
            return `${val}${unit[key]}`;
        }

        const getChartOption = computed(() => {
            const key = unref(chartType);
            const executedData = props.info?.list.map(i => i.executed[key]);
            const executedErrorData = props.info?.list.map(i => i.executedError[key]);
            const unExecutedData = props.info?.list.map(i => i.unExecuted[key]);
            return {
                color: ['rgb(103, 255, 193)', 'rgb(156, 125, 230)', 'rgb(255, 132, 25)'],
                grid: {
                    left: '0',
                    right: '0',
                    bottom: '0',
                    top: '46px',
                    containLabel: true,
                },
                tooltip: {
                    show: true,
                    valueFormatter: val => formatUnit(key, val),
                },
                legend: {
                    top: 10,
                    align: 'left',
                    icon: 'circle',
                    itemWidth: 7,
                    itemHeight: 7,
                    textStyle: {
                        fontSize: 12,
                        color: 'rgba(255, 255, 255, 0.8)',
                    },
                },
                xAxis: {
                    type: 'category',
                    data: ['路面', '路基', '桥梁', '隧道', '交安设施'],
                    axisLabel: {
                        fontSize: 12,
                        color: 'rgba(255, 255, 255, 0.8)',
                        interval: 0,
                    },
                },
                yAxis: {
                    type: 'value',
                    minInterval: key === 'num' ? 1 : 0,
                    axisLabel: {
                        fontSize: 12,
                        color: 'rgba(255, 255, 255, 0.8)',
                        interval: 0,
                        formatter: val => formatUnit(key, val),
                    },
                },
                series: [
                    {
                        name: '已按照计划执行',
                        type: 'bar',
                        stack: 'total',
                        barWidth: '12px',
                        emphasis: {
                            focus: 'series',
                        },
                        data: executedData,
                    },
                    {
                        name: '未按计划执行',
                        type: 'bar',
                        stack: 'total',
                        emphasis: {
                            focus: 'series',
                        },
                        data: executedErrorData,
                    },
                    {
                        name: '待执行计划',
                        type: 'bar',
                        stack: 'total',
                        emphasis: {
                            focus: 'series',
                        },
                        data: unExecutedData,
                    },
                ],
            };
        });

        function formatNumber(num) {
            if (num < 10000) {
                return num;
            }
            return num / 10000;
        }

        const changeChartType = val => {
            chartType.value = val;
        };
        return {
            chartType,
            changeChartType,
            getChartOption,
            formatNumber,
        };
    },
};
</script>

<style lang="less" scoped>
/* 组件样式 */
.maintain-statistics {
    // 样式
    margin-bottom: 16px;

    .statistics-num {
        display: flex;
        margin-bottom: 30px;

        .num-item {
            display: flex;
            flex: 1;
            height: 100px;
            align-items: center;
            justify-content: center;
            background-image: url('@MaintainPlanLayer/assets/img/num-item-bg.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;

            &:first-child {
                margin-right: 10px;
            }

            .num-icon {
                width: 26px;
                height: 26px;
                background-image: url('@MaintainPlanLayer/assets/img/num-icon-1.png');
                background-repeat: no-repeat;
                background-size: 100%;
                margin: 0 12px 0 32px;

                &.num-icon-2 {
                    background-image: url('@MaintainPlanLayer/assets/img/num-icon-2.png');
                }
            }

            .num-content {
                flex: 1;

                .num-title {
                    font-size: 14px;
                    color: #fff;
                    margin-bottom: 12px;
                }

                .num-value {
                    font-size: 20px;
                    color: rgb(11, 255, 118);

                    &.num-value-1 {
                        color: rgb(255, 216, 0);
                    }
                }

                .num-unit {
                    font-size: 14px;
                    color: #fff;
                }
            }
        }
    }

    .maintain-plan-chart {
        display: flex;
        flex-direction: column;
        height: 250px;
    }

    .chart-title-box {
        display: flex;

        .chart-title {
            flex: 1;
        }
    }

    .chart-content {
        flex: 1;
    }

    .chart-title {
        position: relative;
        font-size: 14px;
        color: #fff;
        height: 27px;
        line-height: 27px;
        background: linear-gradient(270deg, rgba(102, 255, 205, 0), rgba(182, 229, 214, .3) 95.93%);
        padding-left: 12px;
        font-weight: 500;

        &::after {
            content: '';
            display: block;
            position: absolute;
            left: 0;
            top: 7px;
            height: 11px;
            border-left: 3px solid #fff;
        }
    }

    .tab-box {
        display: flex;

        .tab-item {
            width: 48px;
            height: 28px;
            line-height: 28px;
            font-size: 12px;
            color: rgba(255, 255, 255, .8);
            border: 1px solid;
            border: 1px solid rgb(151, 151, 151);
            text-align: center;
            cursor: pointer;

            &:first-child {
                border-radius: 1px 0 0 1px;
            }

            &:last-child {
                border-radius: 0 1px 1px 0;
            }

            &:hover {
                color: rgba(255, 255, 255, 1);
            }

            &.is-active {
                border: none;
                color: rgba(255, 255, 255, 1);
                background: linear-gradient(180deg, rgba(26, 240, 147, .88) .07%, rgba(20, 162, 178, .88) 100%);
            }
        }
    }
}
</style>