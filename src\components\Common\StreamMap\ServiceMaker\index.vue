
<script>
import {onMounted, onUnmounted, watch, shallowRef} from 'vue';
import {isOnline} from '@/utils/common';
import {getAlt} from '@GodLayer/api.js';

export default {
    name: 'ServiceMaker',
    props: {
        data: {
            type: Object,
            default: () => null,
        },
        showDistance: {
            type: Boolean,
            default: true,
        },
        // 扎点实例
        managerInstance: {
            type: Object,
            required: true,
        },
    },
    setup(props, {emit}) {
        const maker = shallowRef();

        const createContent = () => {
            const {name, distance} = props.data;

            const http = isOnline()
                ? `${location.origin}/layer`
                : 'http://**************:8080';

            const content = [
                {
                    width: 87,
                    height: 21,
                    backgroundImage:
                        `${http}/maplayer/assets/image/god/distance.png`,
                    position: [44, 0],
                    textList: [
                        {
                            text: distance.toFixed(2),
                            fontSize: 14,
                            color: '#FFD800',
                            position: [33, 4],
                            textAlign: 'center',
                        },
                        {
                            text: 'KM',
                            fontSize: 10,
                            color: '#eff',
                            position: [70, 5.5],
                            textAlign: 'center',
                        },
                    ],
                },
                {
                    backgroundImage:
                        `${http}/maplayer/assets/image/god/service.png`,
                    width: 152,
                    height: 64,
                    position: [0, 25],
                    textList: [
                        {
                            text: name,
                            fontSize: 14,
                            color: '#fff',
                            position: [44 + 16, 8],
                            textAlign: 'left',
                        },
                    ],
                },
            ];
            return content.slice(
                props.showDistance ? 0 : 1
            );
        };

        const drawContent = () => {
            const content = createContent();
            maker.value?.drawContent?.(content);
        };

        const getPosition = async () => {
            const {latitude, longitude} = props.data;
            const alt = await getAlt({
                x: longitude,
                y: latitude,
            }).catch(e => 200);

            return [longitude, latitude, alt ?? 200];
        };

        const createMaker = async () => {
            const {name} = props.data;
            const position = await getPosition();
            const content = createContent();

            maker.value = props.managerInstance.add(`service-${name}`, position, {
                width: 152,
                height: 89,
                clickCallback(e) {
                    emit('handlerClick', props.data, e);
                },
                content,
            });
        };

        const removeMaker = (name = props.data?.name) => {
            props.managerInstance.removeByName(`service-${name}`);
        };

        watch(() => props.data, (n, o) => {
            if (o.name === n.name) return drawContent();
            if (o.name) {
                removeMaker(o.name);
            }
            if (n.name) {
                createMaker();
            }
        }, {
            deep: true,
        });

        onMounted(() => createMaker());
        onUnmounted(() => removeMaker(props.data?.name));
    },
};
</script>
