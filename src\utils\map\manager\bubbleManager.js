import {addBubble, removeBubble} from '../index';
import {getPointHeight} from '@/utils';

// bubble管理器
class BubbleManager {
    constructor(engine) {
        this.bubbleMap = new Map();
        this.setHaveMap = new Map();
        this.engine = engine;
    }
    async addBubble(name, point, options) {
        if (this.bubbleMap.has(name)) {
            this.removeIconByName(name);
        }

        if (this.setHaveMap.has(name)) {
            this.setHaveMap.get(name)();
        }

        const next = await this.initHeight(name, point);
        if (!next) return;

        const {
            color = '#5DE47E',
            size = 60,
        } = options || {};

        const {bubble} = addBubble(point, {
            size,
            color,
            type: 'Wave',
            _engine: this.engine,
        });

        this.bubbleMap.set(name, bubble);
    }

    // 初始化高程
    async initHeight(name, point) {
        if (!point[2]) {
            // 解决请求height时await阻塞导致再次添加时无法清除扎点
            let have = true;
            this.setHaveMap.set(name, () => {
                have = false;
            });
            const height = await getPointHeight(point);
            point[2] = height?.data?.[0] || 0;
            if (have) {
                this.setHaveMap.delete(name);
            }
            return have;
        }
        return true;
    }

    removeIconByName(name) {
        const bubble = this.bubbleMap.get(name);
        bubble && removeBubble(this.bubble);
        this.bubbleMap.delete(name);
    }

    clear() {
        [...this.bubbleMap.keys()].forEach(name => {
            this.removeIconByName(name);
        });
    }
}

export {
    BubbleManager,
};