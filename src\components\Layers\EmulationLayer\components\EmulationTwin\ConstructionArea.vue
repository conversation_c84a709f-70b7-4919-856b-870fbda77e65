<template>
    <div class="construction-area">
        <map-line
            v-if="lineManager"
            :line-instace="lineManager"
            :list="list"
            :info="{
                name: `${uuid}__line`,
                color: '#FF0000',
                lineWidth: 4,
            }"
        />
        <map-polygon
            v-if="polygonManager"
            :polygon-instace="polygonManager"
            :list="list"
            :info="{
                name: `${uuid}__polygon`,
                opacity: .8,
                color: '#FFA500',
            }"
        />
    </div>
</template>

<script>
import MapLine from '@/components/Common/MapLine/index.vue';
import MapPolygon from '@/components/Common/MapPolygon/index.vue';
import {v4 as uuidv4} from 'uuid';

const uuid = uuidv4();

export default {
    components: {
        MapLine,
        MapPolygon,
    },
    props: {
        lineManager: Object,
        polygonManager: Object,
        // 施工区域边界值坐标点集合
        list: {
            type: Array,
            default: () => [],
        },
    },
    setup() {
        return {
            uuid,
        };
    },
};
</script>