import Layout from '@/layout/index.vue';
import PCLayout from '@/layout/PCLayout/index.vue';

export const constantRouterMap = [
    // 默认布局
    {
        path: '/',
        name: 'DEFAULT_LAYOUT',
        component: Layout,
        redirect: '/service-area',
        children: [
            {
                path: '/equipment-facility',
                name: 'EquipmentFacilities',
                meta: {
                    title: '设施设备',
                },
                component: () => import('@/views/EquipmentFacilities/index.vue'),
            },
            {
                path: '/structure',
                name: 'Structure',
                meta: {
                    title: '结构物监测',
                },
                component: () => import('@/views/Structure/index.vue'),
            },
            {
                path: '/service-area',
                name: 'ServiceArea',
                meta: {
                    title: '服务区管理',
                },
                component: () => import('@/views/ServiceManagement/ServiceArea/index.vue'),
            },
            {
                path: '/toll-station',
                name: 'TollStation',
                meta: {
                    title: '收费运营管理',
                },
                component: () => import('@/views/TollStation/index.vue'),
            },
            {
                path: '/construction',
                name: 'Construction',
                meta: {
                    title: '施工管理',
                },
                component: () => import('@/views/Construction/index.vue'),
            },
        ],
    },
    {
        path: '/pc',
        name: 'PC_LAYOUT',
        component: PCLayout,
        redirect: '/',
        children: [
            {
                path: '/road/emulation',
                name: 'Emulation',
                meta: {
                    title: '应急事件仿真展示',
                },
                component: () => import('@/views/Road/index.vue'),
            },
            {
                path: '/maintain',
                name: 'Maintain',
                meta: {
                    title: '设备故障清单',
                },
                component: () => import('@/views/EquipmentFacilities/components/pccomponent/index.vue'),
            },
            {
                path: '/service-area',
                name: '服务区首页',
                component: () => import('@/views/ServiceManagement/ServiceArea/index.vue'),
            },
            {
                path: '/service-area/carFlow',
                name: 'CarFlow',
                component: () => import('@/views/ServiceManagement/StatisticalTable/carList/index.vue'),
            },
            {
                path: '/service-area/eventList',
                name: 'EventList',
                component: () => import('@/views/ServiceManagement/StatisticalTable/carList/eventList.vue'),
            },
            {
                path: '/service-area/CarDetail',
                name: 'CarDetail',
                component: () => import('@/views/ServiceManagement/StatisticalTable/CarDetail/index.vue'),
            },
        ],
    },
];
