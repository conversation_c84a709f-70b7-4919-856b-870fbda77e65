<template>
    <SmallCard
        class="duty-info-card"
        title="值班信息"
    >
        <template #content>
            <div class="duty-info-card__content">
                <api-table
                    :columns="columns"
                    :api="getDuty"
                    :height="300"
                    :pagination="false"
                    :request-params="{
                        pageAble: false,
                    }"
                />
            </div>
        </template>
    </SmallCard>
</template>

<script setup>
import {ApiTable} from '@/components/Common';
import {useUnit} from '@/utils';
import {getDuty} from '@/api/tollStation';
import SmallCard from '@/components/Common/Card/smallCard.vue';

const {ratio} = useUnit();

const columns = [
    {
        label: '姓名',
        prop: 'dutyPersonName',
        width: `${60 * ratio.value}px`,
        align: 'center',
    },
    {
        label: '部门',
        prop: 'affiliatedDepartment',
        width: `${100 * ratio.value}px`,
        align: 'center',
    },
    {
        label: '单位',
        prop: 'affiliatedUnit',
        width: `${70 * ratio.value}px`,
        align: 'left',
    },
    {
        label: '联系方式',
        prop: 'contactPhone',
        width: `${130 * ratio.value}px`,
        align: 'right',
    },
];

</script>

<style lang="less" scoped>
.duty-info-card {
    width: 400px;

    &__content {
        padding: 16px;
        backdrop-filter: blur(12px);
    }

    /deep/ .content {
        background-color: rgba(3, 31, 68, .3);
        backdrop-filter: blur(12px);
    }

    /deep/ .el-table {
        &__header {
            tr {
                font-size: 16px;
            }
        }

        &__body {
            font-size: 16px;
        }
    }
}
</style>