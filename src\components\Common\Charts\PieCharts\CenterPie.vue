<template>
    <div class="container full pr">
        <div ref="chartRef" class="echart"></div>
        <div class="big-radius pen"></div>
        <div class="radius pen" :class="{center: needCenter}">
            <div class="s-radius"></div>
        </div>
    </div>
</template>
<script setup>
import {ref, watch} from 'vue';
import usePieShadow from './usePieShadow.js';
import usePieHighlight from './usePieHighlight.js';
import usePieHeight from './usePieHeight.js';
import useChartCommon from '../useChartCommon';

const emit = defineEmits(['mouseover', 'mouseout']);

const props = defineProps({
    data: {
        type: Array,
        default: () => [],
    },
    activeIndex: {
        type: Number,
        default: 0,
    },
    active: {
        type: Boolean,
        default: true,
    },
    needCenter: {
        type: Boolean,
        default: true,
    },
});

const chartRef = ref(null);

const getOptions = ratio => {
    const options = {
        tooltip: {
            trigger: 'axis',
            show: true,
            z: 90,
        },
        series: [
            {
                name: '',
                type: 'pie',
                radius: ['52%', '62%'],
                center: ['50%', '50%'],
                clockWise: false,
                avoidLabelOverlap: true,
                selectedOffset: 10 * ratio,
                emphasis: {
                    scaleSize: 3 * ratio,
                },
                label: {
                    textStyle: {
                        color: '#fff',
                    },
                    normal: {
                        show: false,
                        position: 'center',
                        textStyle: {
                            color: '#fff',
                        },
                    },
                },
                data: props.data.map((e, i) => {
                    return {
                        ...e,
                        itemStyle: {
                            color: e.color,
                        },
                    };
                }),
            },
        ],
    };
    return options;
};

const mouseover = e => emit('mouseover', e.dataIndex);
const mouseout = e => emit('mouseout', e.dataIndex);

const {chart} = useChartCommon(chartRef, {
    getOptions,
    // eslint-disable-next-line no-use-before-define
    setOptionCb: () => resetHighlight(),
});

const {activeIndex, resetHighlight} = usePieHighlight(chart, {
    mouseover,
    mouseout,
    use: props.active,
});

if (props.active) {
    watch(() => props.activeIndex, n => activeIndex.value = n);
}

// 饼状图周围的阴影
const {shadowStartDeg, polygonPath} = usePieShadow();

const {px: bigPx} = usePieHeight(chartRef, 0.62 + 0.19);
const {px: smallPx} = usePieHeight(chartRef, 0.42);
</script>
<style lang="less" scoped>
.container {
    height: 100%;
    .big-radius {
        width: v-bind(bigPx);
        height: v-bind(bigPx);
        border-radius: 50%;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        box-shadow: inset 0 0 1px 1px rgba(255, 255, 255, .2);

        &::before {
            content: '';
            width: 100%;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
            border-radius: 50%;
            z-index: 1;
            box-shadow: inset 0 0 0 30px rgba(255, 255, 255, .1);
            border: 1px solid rgba(255, 255, 255, .4);
            clip-path: polygon(v-bind(polygonPath));
            transform: rotate(v-bind(shadowStartDeg));
        }
    }

    .radius {
        width: v-bind(smallPx);
        height: v-bind(smallPx);
        border-radius: 50%;
        border: 1px solid rgba(255, 255, 255, .3);
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        align-items: center;
        justify-content: center;

        &.center {
            .s-radius {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                border: 1px solid rgba(255, 255, 255, .5);
                position: relative;
                z-index: 2;
            }

            &::after {
                content: '';
                width: 100%;
                height: 1px;
                background: rgba(255, 255, 255, .14);
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
            }

            &::before {
                content: '';
                width: 1px;
                height: 100%;
                background: rgba(255, 255, 255, .14);
                position: absolute;
                top: 0;
                left: 50%;
                transform: translateX(-50%);
            }
        }
    }

    .echart {
        height: 100%;
    }
}
</style>
