<template>
    <div class="video-box1">
        <vue-aliplayer
            v-if="src !== ''"
            class="video-aliplayer"
            :vid="vid"
            :source="source"
            :options="options"
            :player-fn="playerFn"
        />
        <no-data v-else :message="errorMessage"/>
    </div>
</template>

<script>
import NoData from '../NoData/index.vue';
import VueAliplayer from './videoComp.vue';
import * as api from './api';

import {loadJs, loadStyles} from './config';

const mapDef = {
    LD: 'slaver',
    SD: 'master',
};

export default {
    name: 'video-player',
    components: {
        VueAliplayer,
        NoData,
    },
    props: {
        deviceId: String,
        systemCode: {
            type: [String, Number],
            default: 'tyrh',
        },
        userName: {
            type: String,
            default: 'test123',
        },
        protocol: {
            type: String,
            default: 'http',
        },
    },
    data() {
        return {
            id: '',
            src: '',
            token: '',
            firstLoad: true,
            vid: 'pc_jPrismPlayer',
            source: '',
            options: {},
            staticLoaded: {
                js: false,
                css: false,
            },
            errorMessage: '',
        };
    },
    watch: {
        async deviceId(val, old) {
            const oldToken = this.token;
            if (val) {
                await this.closeVideoRequestFlow(old, oldToken);
                this.getlivePlayStaData();
            }
        },
        src(newVal) {
            this.source = JSON.stringify({LD: newVal, SD: newVal});
        },
        'staticLoaded.css'() {
            if (this.staticLoaded?.css && this.staticLoaded?.js) {
                this.init();
                this.getlivePlayStaData();
            }
        },
        'staticLoaded.js'() {
            if (this.staticLoaded?.css && this.staticLoaded?.js) {
                this.init();
                this.getlivePlayStaData();
            }
        },
    },
    mounted() {
        this.getStaticUrl();
    },
    beforeDestroy() {
        this.closeVideoRequestFlow();
    },
    methods: {
        getStaticUrl() {
            // 动态引入静态js资源
            loadJs().then(e => this.staticLoaded.js = true);

            // 动态引入css样式资源
            loadStyles('maplayer/assets/css/video-player.css')
                .then(e => this.staticLoaded.css = true);
            // loadStyles(`${publicPath()}/gs-static/css/player-min.css`);
        },
        init() {
            this.options = {
                width: '100%', // 播放器宽度
                height: '100%', // 播放器高度
                playsinline: true, // H5 是否内置播放
                useH5Prism: true, // 指定使用 H5 播放器。
                rePlay: false, // 播放器自动循环播放.
                preload: true, // 播放器自动加载，目前仅 h5 可用。
                controlBarVisibility: 'hover', // 控制面板的实现，默认为‘hover’。可选的值为：‘click’、‘hover’、‘always’。
                autoplay: true, // 播放器是否自动播放，在移动端 autoplay 属性会失效。Safari11 不会自动开启自动播放如何开启。
                enableSystemMenu: true, // 是否允许系统右键菜单显示，默认为 false。
                loadDataTimeout: 5, // 缓冲多长时间后，提示用户切换低清晰度，默认：20 秒。
                showBarTime: '10000', // 控制栏自动隐藏时间（ms）。
                // cover: this.posterUrl, //播放器默认封面图片，请填写正确的图片 url 地址。需要 autoplay 为’false’时，才生效。Flash 播放器封面也需要开启允许跨域访问。
                disableSeek: true, // 禁用进度条的 Seek，默认为 false，仅 Flash 支持。
                // 自定义 不要进度条
                isLive: true,
                qualitySort: 'desc', // asc, desc
                components: [
                    {
                        name: 'QualityComponent',
                        type: AliPlayerComponent.QualityComponent,
                        args: [
                            (definition, desc) => {
                                this.getlivePlayStaData(mapDef[definition]);
                            },
                        ],
                    },
                ],
            };
        },
        async getlivePlayStaData(rate = 'master') {
            const res = await api.getlivePlaySta({
                // assetsCode: 'G04W3440011CG0024', // 测试视频是否能播放用（本地不能做代理，或者在线上环境测试）
                assetsCode: this.deviceId,
                rate,
                domain: 'private',
                systemCode: this.systemCode,
                userName: this.userName,
                protocol: this.protocol,
            });
            if (res.data.data && res.data.data.videoRequestUrl !== null) {
                this.src = res.data.data.videoRequestUrl.wsUrl;
                this.token = res.data.data.token;
                this.errorMessage = '';
            }
            else {
                this.src = '';
                this.errorMessage = res.data.msg;
            }
        },
        closeVideoRequestFlow(id, token) {
            console.log('请求关闭视频', id ? id : this.deviceId, token ? token : this.token);
            api.livePlayStop({
                assetsCode: id ? id : this.deviceId,
                token: token ? token : this.token,
            });
        },
        playerFn(player) {
            player.on('sourceloaded', params => {
                if (this.firstLoad) {
                    this.firstLoad = false;
                    // this.setCookie('selectedStreamLevel', 'LD', 365)
                    window.localStorage.setItem('selectedStreamLevel', 'SD');
                }
                let paramData = params.paramData;
                let desc = paramData.desc;
                let definition = paramData.definition;

                player
                    .getComponent('QualityComponent')
                    .setCurrentQuality(desc, definition);
            });
        },
    },
};
</script>

<style lang='less' scoped>
.video-box {
    width: 100%;
    height: 100%;
    background: #fff;
    box-shadow: 0 10px 15px 0 rgba(118, 142, 199, .36);
    border-radius: 6px;
    z-index: 99;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: height .5s ease;
    padding: 10px;

    .video-top {
        margin-top: 15px;
        position: relative;
        width: 100%;

        .bule-line {
            position: absolute;
            left: 2px;
            top: -10px;
            width: 4px;
            height: 18px;
            background: #1989fd;
            border-radius: 2px;
        }

        .video-title {
            width: 100%;
            padding-left: 10px;
            margin-top: -15px;
            height: 25px;
            font-size: 18px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 600;
            color: #333;
            line-height: 25px;
            letter-spacing: 1px;
            display: flex;
            justify-content: space-between;

            .video-icon {
                margin-left: 555px;
                margin-top: -3px;
                width: 28px;
                height: 28px;
                background: url("maplayer/assets/image/video_player/close.png") no-repeat;
                background-size: 100% 100%;
                z-index: 99;
            }
        }

        .video-nav {
            position: absolute;
            top: 30px;
            left: 12px;
            width: 392px;
            height: 46px;
            background:
                linear-gradient(
                    270deg,
                    rgba(54, 190, 255, 0) 0%,
                    #1989fd 100%
                );
            z-index: 99;
            display: flex;
            flex-direction: column;
            align-items: center;

            .nav-text {
                display: flex;
                margin-left: 20px;
                width: 269px;
                height: 22px;
                font-size: 16px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #fff;
                line-height: 22px;
            }
        }

        .video-aliplayer {
            margin-top: 9px;
        }

        .video-bom {
            position: absolute;
            right: 16px;
            bottom: 47px;
            width: 75px;
            height: 32px;
            padding: 0 13px;
            background: url("maplayer/assets/image/video_player/bule.png") no-repeat;
            background-size: 100% 100%;
            z-index: 101;
            font-size: 14px;
            font-weight: 400;
            color: #fff;
            line-height: 20px;
            letter-spacing: 1px;
            // display: flex;
            align-items: center;
            justify-content: space-between;
            display: none;
        }

        .video-svg {
            width: 16px;
            height: 16px;
            background: url("maplayer/assets/image/video_player/line-icon.svg") no-repeat;
            background-size: 100% 100%;
        }
    }

    .more-bom {
        width: 100%;
        display: none;

        .video-more {
            width: 100%;
            height: 53px;
            background: linear-gradient(260deg, #eef7ff 0%, #d8ebff 100%);
            margin: 10px auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-left: 20px;
            padding-right: 20px;

            .video-more-left {
                width: 45%;
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-right: 20px;

                .more-left {
                    height: 25px;
                    font-size: 16px;
                    font-weight: 600;
                    color: #333;
                    line-height: 25px;
                    letter-spacing: 1px;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .circle {
                        width: 7px;
                        height: 7px;
                        background: #1989fd;
                        border-radius: 50%; /* 将边框半径设置为50%以创建圆形 */
                        margin-right: 5px;
                    }
                }

                .more-right {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    width: 260px;
                    height: 22px;
                    font-size: 14px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 600;
                    color: #1989fd;
                    line-height: 22px;
                    margin-right: 7px;

                    .right-http {
                        overflow: hidden;
                        white-space: nowrap; /* 防止文本换行 */
                        text-overflow: ellipsis; /* 当文本溢出时显示省略号 */
                        width: 200px; /* 可选，设置容器宽度 */
                        text-decoration: underline;
                    }

                    .right-type {
                        display: flex;

                        .wifi {
                            width: 17px;
                            height: 14px;
                            background: url("maplayer/assets/image/video_player/wifi.svg") no-repeat;
                            background-size: 100% 100%;
                            margin-top: 3px;
                            margin-right: 4px;
                        }
                    }
                }

                .more-place {
                    height: 22px;
                    font-size: 14px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 600;
                    color: #1989fd;
                    line-height: 22px;

                    .right-number {
                        margin-right: 9px;
                    }
                }
            }

            .video-more-mid {
                width: 45%;
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-left: 10px;
                margin-right: 10px;

                .more-left {
                    height: 25px;
                    font-size: 16px;
                    font-weight: 600;
                    color: #333;
                    line-height: 25px;
                    letter-spacing: 1px;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .circle {
                        width: 7px;
                        height: 7px;
                        background: #1989fd;
                        border-radius: 50%; /* 将边框半径设置为50%以创建圆形 */
                        margin-right: 5px;
                    }
                }

                .more-right {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    width: 260px;
                    height: 22px;
                    font-size: 14px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 600;
                    color: #1989fd;
                    line-height: 22px;
                    margin-right: 7px;

                    .right-http {
                        overflow: hidden;
                        white-space: nowrap; /* 防止文本换行 */
                        text-overflow: ellipsis; /* 当文本溢出时显示省略号 */
                        width: 200px; /* 可选，设置容器宽度 */
                        text-decoration: underline;
                    }

                    .right-type {
                        display: flex;

                        .wifi {
                            width: 17px;
                            height: 14px;
                            background: url("maplayer/assets/image/video_player/wifi.svg") no-repeat;
                            background-size: 100% 100%;
                            margin-top: 3px;
                            margin-right: 4px;
                        }
                    }
                }

                .more-place {
                    height: 22px;
                    font-size: 14px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 600;
                    color: #1989fd;
                    line-height: 22px;

                    .right-number {
                        margin-right: 9px;
                    }
                }
            }
        }

        .video-more-table {
            width: 100%;
            height: 200px;
            background: linear-gradient(260deg, #eef7ff 0%, #d8ebff 100%);
            margin: 10px auto;
            padding-top: 10px;
            padding-left: 20px;
            padding-right: 20px;

            .more-top {
                width: 100px;
                height: 25px;
                font-size: 16px;
                font-weight: 600;
                color: #333;
                line-height: 25px;
                letter-spacing: 1px;
                display: flex;
                align-items: center;
            }

            .table-count {
                width: 100%;
                padding: 15px;

                .count-title {
                    height: 22px;
                    margin-bottom: 10px;
                    display: flex;
                    justify-content: space-between;
                    font-size: 14px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 600;
                    color: #1989fd;
                    line-height: 22px;
                }

                .count-item {
                    width: 100%;
                    height: 42px;
                    overflow: auto;
                    padding-left: 30px;
                    padding-right: 30px;
                    display: flex;
                    justify-content: space-between;
                    margin-left: -35px;
                    background: #c6e2ff;
                    border: 1px solid #e9f4ff;
                    font-size: 12px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #333;
                    line-height: 41px;
                }

                .count-item div:nth-child(1) {
                    width: 150px;
                }

                .count-item div:nth-child(2) {
                    width: 85px;
                    margin-right: 70px;
                }

                .count-item div:nth-child(3) {
                    width: 40px;
                    margin-right: 110px;
                }

                .count-item div:nth-child(4) {
                    width: 50px;
                }

                .more-bom {
                    width: 100%;
                    display: none;

                    .video-more {
                        width: 100%;
                        height: 53px;
                        background: linear-gradient(260deg, #eef7ff 0%, #d8ebff 100%);
                        margin: 10px auto;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding-left: 20px;
                        padding-right: 20px;

                        .video-more-left {
                            width: 45%;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-right: 20px;

                            .more-left {
                                height: 25px;
                                font-size: 16px;
                                font-weight: 600;
                                color: #333;
                                line-height: 25px;
                                letter-spacing: 1px;
                                display: flex;
                                justify-content: center;
                                align-items: center;

                                .circle {
                                    width: 7px;
                                    height: 7px;
                                    background: #1989fd;
                                    border-radius: 50%; /* 将边框半径设置为50%以创建圆形 */
                                    margin-right: 5px;
                                }
                            }

                            .more-right {
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                                width: 260px;
                                height: 22px;
                                font-size: 14px;
                                font-family: PingFangSC-Medium, PingFang SC;
                                font-weight: 600;
                                color: #1989fd;
                                line-height: 22px;
                                margin-right: 7px;

                                .right-http {
                                    overflow: hidden;
                                    white-space: nowrap; /* 防止文本换行 */
                                    text-overflow: ellipsis; /* 当文本溢出时显示省略号 */
                                    width: 200px; /* 可选，设置容器宽度 */
                                    text-decoration: underline;
                                }

                                .right-type {
                                    display: flex;

                                    .wifi {
                                        width: 17px;
                                        height: 14px;
                                        background: url("maplayer/assets/image/video_player/wifi.svg") no-repeat;
                                        background-size: 100% 100%;
                                        margin-top: 3px;
                                        margin-right: 4px;
                                    }
                                }
                            }

                            .more-place {
                                height: 22px;
                                font-size: 14px;
                                font-family: PingFangSC-Medium, PingFang SC;
                                font-weight: 600;
                                color: #1989fd;
                                line-height: 22px;

                                .right-number {
                                    margin-right: 9px;
                                }
                            }
                        }

                        .video-more-mid {
                            width: 45%;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-left: 10px;
                            margin-right: 10px;

                            .more-left {
                                height: 25px;
                                font-size: 16px;
                                font-weight: 600;
                                color: #333;
                                line-height: 25px;
                                letter-spacing: 1px;
                                display: flex;
                                justify-content: center;
                                align-items: center;

                                .circle {
                                    width: 7px;
                                    height: 7px;
                                    background: #1989fd;
                                    border-radius: 50%; /* 将边框半径设置为50%以创建圆形 */
                                    margin-right: 5px;
                                }
                            }

                            .more-right {
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                                width: 260px;
                                height: 22px;
                                font-size: 14px;
                                font-family: PingFangSC-Medium, PingFang SC;
                                font-weight: 600;
                                color: #1989fd;
                                line-height: 22px;
                                margin-right: 7px;

                                .right-http {
                                    overflow: hidden;
                                    white-space: nowrap; /* 防止文本换行 */
                                    text-overflow: ellipsis; /* 当文本溢出时显示省略号 */
                                    width: 200px; /* 可选，设置容器宽度 */
                                    text-decoration: underline;
                                }

                                .right-type {
                                    display: flex;

                                    .wifi {
                                        width: 17px;
                                        height: 14px;
                                        background: url("maplayer/assets/image/video_player/wifi.svg") no-repeat;
                                        background-size: 100% 100%;
                                        margin-top: 3px;
                                        margin-right: 4px;
                                    }
                                }
                            }

                            .more-place {
                                height: 22px;
                                font-size: 14px;
                                font-family: PingFangSC-Medium, PingFang SC;
                                font-weight: 600;
                                color: #1989fd;
                                line-height: 22px;

                                .right-number {
                                    margin-right: 9px;
                                }
                            }
                        }
                    }

                    .video-more-table {
                        width: 100%;
                        height: 200px;
                        background: linear-gradient(260deg, #eef7ff 0%, #d8ebff 100%);
                        margin: 10px auto;
                        padding-top: 10px;
                        padding-left: 20px;
                        padding-right: 20px;

                        .more-top {
                            width: 100px;
                            height: 25px;
                            font-size: 16px;
                            font-weight: 600;
                            color: #333;
                            line-height: 25px;
                            letter-spacing: 1px;
                            display: flex;
                            align-items: center;
                        }

                        .table-count {
                            width: 100%;
                            padding: 15px;

                            .count-title {
                                height: 22px;
                                margin-bottom: 10px;
                                display: flex;
                                justify-content: space-between;
                                font-size: 14px;
                                font-family: PingFangSC-Medium, PingFang SC;
                                font-weight: 600;
                                color: #1989fd;
                                line-height: 22px;
                            }

                            .count-item {
                                width: 100%;
                                height: 42px;
                                overflow: auto;
                                padding-left: 30px;
                                padding-right: 30px;
                                display: flex;
                                justify-content: space-between;
                                margin-left: -35px;
                                background: #c6e2ff;
                                border: 1px solid #e9f4ff;
                                font-size: 12px;
                                font-family: PingFangSC-Regular, PingFang SC;
                                font-weight: 400;
                                color: #333;
                                line-height: 41px;
                            }

                            .count-item div:nth-child(1) {
                                width: 150px;
                            }

                            .count-item div:nth-child(2) {
                                width: 85px;
                                margin-right: 70px;
                            }

                            .count-item div:nth-child(3) {
                                width: 40px;
                                margin-right: 110px;
                            }

                            .count-item div:nth-child(4) {
                                width: 50px;
                            }
                        }
                    }

                    .fade-enter-active,
                    .fade-leave-active {
                        transition: opacity 300ms;
                    }

                    .fade-enter,
                    .fade-leave-to {
                        opacity: 0;
                    }
                }
            }
        }
    }
}

.video-box1 {
    width: 100%;
    height: 100%;
    z-index: 99;
}
</style>