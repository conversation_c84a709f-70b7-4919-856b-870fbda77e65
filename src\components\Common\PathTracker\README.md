# 视野漫游组件

## 参数说明 props
|Prop name|Type|Default|Description|
|---|---|---|---|
|`managerInstace`|`Object`|`必填`|扎点管理器实例|
|`pathTracker`|`Object`|`必填`|视野漫游管理器实例|
|`lineManager`|`Object`|`必填`|线段管理器实例|
|`list`|`array`|`[]`|地图线和视野漫游模型移动的数组，需要二维数组|
|`iconList`|`array`|`[]`|扎点数据数组|
|`lineInfo`|`object`|`{}`|线参数对象|
|`iconInfo`|`object`|`{}`|扎点参数对象|
|`modelType`|`string`|`'maplayer/assets/models/kache.glb'`|模型地址路径|
|`viewMode`|`string`|`unlock`|视野追随效果|

## 详细参数

### iconList
```js
[
    {
        roadId: 'xx' // 道路id（必需）
        gantryId: 'xx' // 扎点id（必需，键名可自定义，自定义需配合iconInfo设置使用）
        gantryName: 'xx' // 扎点label名称（必需，键名可自定义，自定义需配合iconInfo设置使用）
        gantryLng: 100 // 扎点经度（必需，键名可自定义，自定义需配合iconInfo设置使用）
        gantryLat: 24 // 扎点纬度（必需，键名可自定义，自定义需配合iconInfo设置使用）
        status: 'normal' // 扎点状态，默认normal
        type: '龙门架' // 扎点类型，默认龙门架
    }
]
```

### lineInfo
|Key name|Type|Default|Description|
|---|---|---|---|
|`color`|`string`|`#d0a63c`|线颜色|
|`lineWidth`|`number`|`15`|线宽度|
|`opacity`|`number`|`1`|透明度|
|`name`|`string`|必传|线唯一标识|

### iconInfo
|Key name|Type|Default|Description|
|---|---|---|---|
|`name`|`string`|`gantryName`|扎点名称字段，若iconList数据中扎点名称字段不是gantryName则必传|
|`lng`|`string`|`gantryLng`|扎点经度字段，若iconList数据中扎点经度字段不是gantryLng则必传|
|`lat`|`string`|`gantryLat`|扎点纬度字段，若iconList数据中扎点纬度字段不是gantryLat则必传|
|`id`|`string`|`gantryId`|扎点id字段，若iconList数据中扎点id字段不是gantryId则必传|