<template>
    <div
        ref="chartRef"
        class="structure-chart"
    ></div>
</template>

<script>
import * as echarts from 'echarts';
import {ref, onMounted, nextTick, watch} from 'vue';
import {useUnit} from '@/utils/hooks/useUnit';
export default {
    props: {
        info: {
            type: Object,
            default: () => ({}),
        },
        tooltipCallback: {
            type: Function,
            default: null,
        },
    },
    setup(props) {
        const chartRef = ref(null);
        const myChart = ref(null);

        const {ratio} = useUnit();

        const option = ref();

        const init = () => {
            if (!myChart.value) return;
            myChart.value.setOption(option.value);
        };

        const update = () => {
            const seriesDataName = props.info.seriesData.reduce((acc, curr) => {
                if (!acc.includes(curr.name)) {
                    acc.push(curr.name);
                }
                return acc;
            }, []);
            option.value = {
                grid: {
                    top: '12%',
                    left: '0%',
                    right: '5%',
                    bottom: '0%',
                    containLabel: true,
                },
                tooltip: {
                    // 配置 tooltip
                    trigger: 'axis', // 触发类型，柱状图使用 'axis'
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    borderColor: 'rgba(255, 255, 255, 0.2)',
                    axisPointer: { // 坐标轴指示器配置
                        type: 'shadow', // 阴影指示器
                    },
                    formatter: function (params) {
                        if (props.tooltipCallback) {
                            return props.tooltipCallback(params);
                        }
                        // 自定义 tooltip 的内容
                        return `<div class="tet">
                            <div class="title">${params[0].name}</div>
                            <div class="info">
                                <div class="num">
                                    <div class="num-title">运行数量</div>
                                    <div class="num-info">${params[0].value}</div>
                                </div>
                                <div class="online">
                                    <div class="online-title">在线率</div>
                                    <div class="online-info">123%</div>
                                </div>
                            </div>
                        </div>`;
                    },
                },
                xAxis: {
                    type: 'category',
                    data: props.info.xAxisData ?? seriesDataName,
                },
                legend: {
                    type: 'scroll',
                    width: '80%',
                    data: props.info.legendData ?? seriesDataName,
                    top: 'top', // 设置图例显示在顶部
                    left: 'right', // 设置图例水平居中
                    icon: 'circle', // 设置图例的图标为圆形
                    textStyle: {
                        color: '#fff',
                        fontSize: 12 * ratio.value,
                        fontWeight: 400,
                        fontFamily: 'OPlusSans',
                    },
                },
                yAxis: {
                    type: 'value',
                    minInterval: 1, // 设置y轴的最小间隔为1
                    name: props.info.yAxisName,
                },
                series: props.info.seriesData.map((item, index) => {
                    let data = Array(props.info.seriesData.length).fill(0);
                    data[index] = item.data;
                    return {
                        name: item.name,
                        type: 'bar',
                        stack: 'myBar',
                        barWidth: 16 * ratio.value,
                        data, // 数据
                        itemStyle: {
                            color: item.itemColor || 'rgb(68, 218, 30)',
                        },
                        // 事件处理函数
                        emphasis: {
                            // 鼠标悬停时柱体颜色改变为红色
                            itemStyle: {
                                color: item.emphasisColor || item.itemColor || 'rgb(68, 218, 30)',
                            },
                        },
                    };
                }),
            };
            init();
        };

        watch(() => props.info, () => {
            update();
        }, {
            deep: true,
            immediate: true,
        });

        onMounted(() => {
            nextTick(() => {
                myChart.value = echarts.init(chartRef.value);
                init();
            });
        });

        return {
            chartRef,
            option,
        };
    },
};
</script>

<style lang="less"
    scoped>
    .structure-chart {
        width: 100%;
        height: 100%;
    }

    :deep(.tet) {
        width: 80px;
        height: 100%;

        .title {
            font-weight: 700;
            font-size: 12px;
            font-family: 'OPlusSans';
            margin-bottom: 4px;
            color: #fff;
        }

        .info {
            width: 100%;

            >div {
                display: flex;
                justify-content: space-between;
                width: 100%;

                .num-title,
                .online-title {
                    font-weight: 400;
                    font-family: 'OPlusSans';
                    font-size: 10px;
                    color: rgba(255, 255, 255, 0.5);
                }

                .num-info,
                .online-info {
                    font-weight: 700;
                    font-family: 'OPlusSans';
                    font-size: 10px;
                    color: rgb(255, 255, 255);
                }
            }
        }
    }
</style>