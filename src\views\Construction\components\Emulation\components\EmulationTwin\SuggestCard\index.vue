<template>
    <div v-if="showCard">
        <Card title="建议策略" card-type="card-short-2">
            <template #content>
                <div class="card-content show-scroll">
                    <el-form
                        class="emulation-form"
                        size="small"
                        :label-width="labelWidth"
                    >
                        <el-checkbox-group v-model="strategyInfo.strategyType">
                            <!-- 应急车道开放 -->
                            <div class="emulation-form__block">
                                <el-checkbox
                                    class="suggest-card__checkbox"
                                    :label="1"
                                >
                                    应急车道开放
                                </el-checkbox>
                                <div class="emulation-form__sub-item">
                                    <el-form-item label="开放起始位置：">
                                        <stake-select
                                            v-model="strategyInfo.emergencyConfig.controlStake"
                                            default-selected-middle
                                            :high-speed-name="emulationInfo.highSpeedName || 'G0423'"
                                            @change="handleChangeControlStake($event, 'emergencyConfig')"
                                        />
                                    </el-form-item>
                                    <el-form-item label="管控距离：">
                                        <el-input
                                            v-model="strategyInfo.emergencyConfig.controlLength"
                                            type="number"
                                            class="full-content"
                                            placeholder="请输入管控距离"
                                        >
                                            <template #suffix>m</template>
                                        </el-input>
                                    </el-form-item>
                                    <el-form-item label="应急车道限速值：">
                                        <el-input
                                            v-model="strategyInfo.emergencyConfig.limitSpeed"
                                            type="number"
                                            class="full-content"
                                            placeholder="请输入应急车道限速值"
                                        >
                                            <template #suffix>km/h</template>
                                        </el-input>
                                    </el-form-item>
                                    <el-form-item label="持续时间：">
                                        <el-input
                                            v-model="strategyInfo.emergencyConfig.controlDuration"
                                            type="number"
                                            class="full-content"
                                            placeholder="请输入持续时间"
                                        >
                                            <template #suffix>分钟</template>
                                        </el-input>
                                    </el-form-item>
                                </div>
                            </div>
                            <!-- 行车道管控 -->
                            <div class="emulation-form__block">
                                <el-checkbox
                                    class="suggest-card__checkbox"
                                    :label="2"
                                >
                                    行车道管控
                                </el-checkbox><div class="emulation-form__sub-item">
                                    <el-form-item label="开放起始位置：">
                                        <stake-select
                                            v-model="strategyInfo.laneControlConfig.controlStake"
                                            default-selected-middle
                                            :high-speed-name="highSpeedName"
                                            @change="handleChangeControlStake($event, 'laneControlConfig')"
                                        />
                                    </el-form-item>
                                    <el-form-item label="管控距离：">
                                        <el-input
                                            v-model="strategyInfo.laneControlConfig.controlLength"
                                            type="number"
                                            class="full-content"
                                            placeholder="请输入管控距离"
                                        >
                                            <template #suffix>m</template>
                                        </el-input>
                                    </el-form-item>
                                    <el-form-item label="行驶车道管控：">
                                        <el-checkbox-group v-model="selectedLane">
                                            <el-checkbox
                                                v-for="lane in laneOptions"
                                                :key="lane.value"
                                                :label="lane.value"
                                                @change="handleSelectLane($event, lane.value)"
                                            >
                                                {{ lane.label }}
                                            </el-checkbox>
                                        </el-checkbox-group>
                                    </el-form-item>
                                    <!-- 车道配置 -->
                                    <el-form-item
                                        v-for="item in strategyInfo.laneControlConfig.configList"
                                        :key="`LANE_CONFIG__${item}`"
                                        :label="item.lane === 99 ? '应急车道' : `${item.lane}车道`"
                                    >
                                        <el-radio-group v-model="item.laneControlType">
                                            <el-radio :label="1">限速</el-radio>
                                            <el-radio :label="0">关闭</el-radio>
                                        </el-radio-group>
                                        <el-input
                                            v-show="item.laneControlType === 1"
                                            v-model="item.limitSpeed"
                                            type="number"
                                            placeholder="请输入车道限速值"
                                        >
                                            <template #suffix>km/h</template>
                                        </el-input>
                                    </el-form-item>
                                </div>
                            </div>
                            <!-- 出入口管控 -->
                            <div class="emulation-form__block">
                                <el-checkbox
                                    class="suggest-card__checkbox"
                                    :label="3"
                                >
                                    出入口管控
                                </el-checkbox>
                                <div class="emulation-form__sub-item">
                                    <el-form-item label="出入口选择：">
                                        <api-select
                                            v-model="strategyInfo.entryAndExitConfig.rampName"
                                            default-selected-first
                                            :droup-down-fetch="false"
                                            :api="getTollList"
                                            :params="{
                                                highSpeedName: emulationInfo.highSpeedName,
                                                emulationStartStake: emulationInfo.emulationStartStake,
                                                emulationEndStake: emulationInfo.emulationEndStake,
                                            }"
                                            label-field="tollName"
                                            value-field="tollName"
                                        />
                                    </el-form-item>
                                    <el-form-item
                                        :label="`${strategyInfo.strategyType.rampName || ''}收费站：`"
                                    >
                                        <el-checkbox-group v-model="selectedTollConfig">
                                            <div
                                                v-for="tollConfig in tollConfigOptions"
                                                :key="tollConfig.value"
                                                style="display: flex; align-items: center;"
                                            >
                                                <el-checkbox
                                                    :label="tollConfig.value"
                                                    @change="handleSelectTollConfig($event, tollConfig.value)"
                                                >
                                                    {{ tollConfig.label }}
                                                </el-checkbox>
                                                <div
                                                    v-if="tollConfig.config"
                                                    class="btn-config"
                                                    @click="handleClickTollConfig(tollConfig.value)"
                                                >
                                                    配置
                                                </div>
                                            </div>
                                        </el-checkbox-group>
                                    </el-form-item>
                                </div>
                            </div>
                        </el-checkbox-group>
                    </el-form>
                </div>
            </template>
            <template #titleContent>
                <div class="btn-comfirn" @click="handleConfirm">生产对比方案</div>
            </template>
        </Card>
        <!-- 入口限流、出口分流配置弹窗 -->
        <el-dialog
            :visible.sync="dialogVisible"
            class="config-dialog"
            :title="getDialogTitle"
            :modal-append-to-body="false"
        >
            <el-form
                class="emulation-form"
                label-width="0.9rem"
            >
                <el-form-item label="持续时间：">
                    <el-input
                        v-model="dialogInfo.controlDuration"
                        type="number"
                        class="full-content"
                        placeholder="请输入持续时间"
                    >
                        <template #suffix>分钟</template>
                    </el-input>
                </el-form-item>
                <!-- 入口限流配置 -->
                <template v-if="dialogType === 2">
                    <el-form-item label="入口限流量：">
                        <el-input
                            v-model="dialogInfo.flowLimit"
                            type="number"
                            class="full-content"
                            placeholder="请输入入口限流量"
                        >
                            <template #suffix>辆/小时</template>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="车辆限制：">
                        <api-select
                            v-model="dialogInfo.vehicleType"
                            :options="vehicleTypeOptions"
                            placeholder="请选择"
                            class="full-content"
                        />
                    </el-form-item>
                </template>
                <!-- 出口分流配置  -->
                <template v-else>
                    <el-form-item label="绕行比例：">
                        <el-input
                            v-model="dialogInfo.bypassRate"
                            type="number"
                            class="full-content"
                            placeholder="请输入绕行比例"
                        >
                            <template #suffix>%</template>
                        </el-input>
                    </el-form-item>
                </template>
            </el-form>
            <template #footer>
                <div class="config-dialog__footer">
                    <div @click="dialogVisible = false">取消</div>
                    <div @click="handleSaveConfig">保存</div>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import {Card} from '@/components/Common';
import {computed, ref, watch} from 'vue';
import {
    Form as ElForm,
    FormItem as ElFormItem,
    Radio as ElRadio,
    RadioGroup as ElRadioGroup,
    Input as ElInput,
    Checkbox as ElCheckbox,
    CheckboxGroup as ElCheckboxGroup,
    Dialog as ElDialog,
} from 'element-ui';
import ApiSelect from '@/components/Common/Form/ApiSelect.vue';
import {useUnit} from '@/utils';
import StakeSelect from '../../EmulationCreate/components/StakeSelect.vue';
import {cloneDeep, uniq} from 'lodash-es';
import {laneOptions, tollConfigOptions, vehicleTypeOptions} from '../../../config';
import {emulationInfo, emulationStrategyList} from '../index';
import {getTollList, runEmulation} from '@/api/emulation';
import {strategyDefaultInfo} from '../../EmulationCreate/config';

const showCard = ref(false);
const {ratio} = useUnit();
const strategyInfo = ref(cloneDeep(strategyDefaultInfo));
const selectedLane = ref([]);
const selectedTollConfig = ref([]);
// 弹窗显隐状态
const dialogVisible = ref(false);
// 当天弹窗配置类型 2:入口限流 3:出口分流
const dialogType = ref(2);
// 弹窗配置信息
const dialogInfo = ref({
    controlDuration: '',
    flowLimit: '',
    vehicleType: '',
    bypassRate: '',
});
const getDialogTitle = computed(() => (dialogType.value === 2 ? '入口限流配置' : '出口分流配置'));
const labelWidth = computed(() => `${125 * ratio.value}px`);

function handleChangeControlStake({position}, key) {
    strategyInfo.value[key].position = position?.join(',');
}

function handleSelectLane(flag, lane) {
    // 取消选中
    if (!flag) {
        const targetIndex = strategyInfo.value
            .laneControlConfig.configList.findIndex(item => item.lane === lane);
        strategyInfo.value
            .laneControlConfig.configList.splice(targetIndex, 1);
    }
    // 选中
    else {
        strategyInfo.value
            .laneControlConfig.configList.push({
                lane,
                laneControlType: 0,
                limitSpeed: '',
            });
    }
}


function handleSelectTollConfig(flag, configType) {
    // 取消选中
    if (!flag) {
        const targetIndex = strategyInfo.value
            .entryAndExitConfig.configList.findIndex(item => item.entranceExitType === configType);
        strategyInfo.value
            .entryAndExitConfig.configList.splice(targetIndex, 1);
    }
    // 选中
    else {
        strategyInfo.value
            .entryAndExitConfig.configList.push({
                controlDuration: '',
                flowLimit: '',
                vehicleType: '',
                bypassRate: '',
                entranceExitType: configType,
            });
    }
}

function handleClickTollConfig(type) {
    dialogType.value = type;
    dialogVisible.value = true;
    const targetConfig = strategyInfo.value
        .entryAndExitConfig.configList.find(item => item.entranceExitType === type) || {};
    dialogInfo.value = {
        controlDuration: '',
        flowLimit: '',
        vehicleType: '',
        bypassRate: '',
        entranceExitType: type,
        ...targetConfig,
    };
}

function handleSaveConfig() {
    const type = dialogType.value;
    const targetIndex = strategyInfo.value.entryAndExitConfig
        .configList.findIndex(item => item.entranceExitType === type);
    if (targetIndex >= 0) {
        strategyInfo.value.entryAndExitConfig
            .configList[targetIndex] = dialogInfo.value;
    }
    else {
        strategyInfo.value.entryAndExitConfig
            .configList.push(dialogInfo.value);
        selectedTollConfig.value.push(dialogInfo.value.entranceExitType);
    }
    dialogVisible.value = false;
}

function getStrategyInfo() {
    const strategy = strategyInfo.value;
    const strategyList = [];
    const strategyType = strategy.strategyType;
    // 开放应急车道
    if (strategyType.includes(1)) {
        strategyList.push({
            ...strategy.emergencyConfig,
            type: 1,
        });
    }
    // 行车道管控
    if (strategyType.includes(2)) {
        const laneControlConfig = strategy.laneControlConfig;
        strategyList.push({
            type: 2,
            position: laneControlConfig.position,
            controlStake: laneControlConfig.controlStake,
            controlLength: laneControlConfig.controlLength,
            laneControlType: laneControlConfig.configList.map(i => i.laneControlType).join(','),
            limitSpeed: laneControlConfig.configList.map(i => i.limitSpeed || 0).join(','),
            lane: laneControlConfig.configList.map(i => i.lane).join(','),
        });
    }
    // 出入口管控
    if (strategyType.includes(3)) {
        const entryAndExitConfig  = strategy.entryAndExitConfig;
        const entranceExitType = entryAndExitConfig.configList.map(i => i.entranceExitType).join(',');
        const entryConfig = entryAndExitConfig.configList.find(i => i.entranceExitType === 2);
        const exportConfig = entryAndExitConfig.configList.find(i => i.entranceExitType === 3);
        strategyList.push({
            type: 3,
            entranceExitType,
            exportConfig,
            entryConfig,
            rampName: entryAndExitConfig.rampName,
        });
    }
    return strategyList;
}

async function handleConfirm() {
    const strategyList = getStrategyInfo();
    const {data} = await runEmulation({
        strategyList: [
            strategyList,
        ],
        id: emulationInfo.value.schemeId,
        uuid: emulationInfo.value.uuid,
    });
    const {emulationId} = data;
    emulationInfo.value.emulationIds?.push(emulationId[0]);
}

watch(
    () => emulationStrategyList.value,
    val => {
        showCard.value = val.length;
        if (!val.length) return;
        const strategyType = uniq(val?.map(item => item.type));
        const newStrategyInfo = {
            strategyType,
        };
        val.forEach(item => {
            if (item.type === 1) {
                newStrategyInfo.emergencyConfig = {
                    controlStake: item.controlStake,
                    position: item.position,
                    controlLength: item.controlLength,
                    limitSpeed: item.limitSpeed,
                    controlDuration: item.controlDuration,
                    type: 1,
                };
            }
            else if (item.type === 2) {
                const lane = item.lane?.split(',').map(Number);
                const laneControlType = item.laneControlType?.split(',').map(Number);
                const limitSpeed = item.limitSpeed?.split(',').map(Number);
                selectedLane.value = lane;
                newStrategyInfo.laneControlConfig = {
                    controlStake: item.controlStake,
                    position: item.position,
                    controlLength: item.controlLength,
                    configList: lane.map((l, index) => ({
                        lane: l,
                        laneControlType: laneControlType[index],
                        limitSpeed: limitSpeed[index],
                    })),
                };
            }
            else if (item.type === 3) {
                const entranceExitType = item.entranceExitType?.split(',').map(Number);
                newStrategyInfo.entryAndExitConfig = {
                    rampName: item.rampName,
                    configList: entranceExitType.map(type => ({
                        ...item,
                        entranceExitType: type,
                    })),
                };
            }
        });
        strategyInfo.value = {
            ...strategyInfo.value,
            ...newStrategyInfo,
        };
    },
    {
        deep: true,
        immediate: true,
    }
);


</script>

<style lang="less" scoped>
.card-content {
    height: 750px;
    overflow-y: scroll;
    margin-right: -12px;
    padding: 12px 12px 12px 0;
}

.suggest-card__checkbox {
    margin-bottom: 16px;
    margin-top: 32px;

    &:first-of-type {
        margin-top: 0;
    }
}

.btn-comfirn {
    display: flex;
    align-items: center;
    height: 24px;
    font-size: 16px;
    padding: 0 16px;
    color: #01ffe5;
    border: 1px solid #01ffe5;
    cursor: pointer;
    margin-right: 24px;
}

.emulation {
    &-form {
        &__sub {
            margin-bottom: 32px;

            &-item {
                padding-left: 12px;
            }
        }

        /deep/ .el-form {
            &-item {
                margin-bottom: 24px;

                &__label {
                    text-align: left;
                }
            }
        }

        /deep/ .el-radio {
            &:not(:last-of-type) {
                margin-right: 18px;
            }

            &__label {
                padding-left: 4px;
            }
        }

        /deep/ .el-input__inner {
            &[type="number"] {
                &::-webkit-inner-spin-button,
                &::-webkit-outer-spin-button {
                    -webkit-appearance: none;
                    margin: 0;
                }
            }
        }

        .full-content {
            width: 100%;
        }

        .btn-config {
            color: #28c282;
            cursor: pointer;
            margin-left: 4px;
            font-size: 14px;
        }
    }
}

.config-dialog {
    /deep/ .el-dialog {
        width: 400px;

        &__header {
            display: flex;

            &btn:hover .el-dialog__close {
                color: #fff;
            }
        }

        &__title {
            color: #fff;
        }
    }

    &__footer {
        display: flex;
        align-items: center;
        justify-content: center;

        div {
            padding: 8px 20px;
            text-align: center;
            border: 1px solid rgb(1, 255, 229);
            color: rgb(1, 255, 229);
            cursor: pointer;
            font-weight: 500;
            font-size: 16px;

            &:active {
                opacity: .85;
            }

            &:last-child {
                border: none;
                color: #000;
                background-color: rgb(1, 255, 229);
                margin-left: 24px;
            }
        }
    }
}
</style>