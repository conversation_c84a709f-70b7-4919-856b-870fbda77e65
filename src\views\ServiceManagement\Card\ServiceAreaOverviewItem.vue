<template>
    <div class="item">
        <div class="type">
            <div class="top">
                <div class="exa">
                    <div class="top-icon mt-12">
                        <img :src="info.info.icon" alt="">
                    </div>
                    <div class="text mt-12">
                        <p>{{ info.info.title }}</p>
                        <p class="last">{{ info.info.translate }}</p>

                    </div>
                    <!-- <div class="right mt-12">
                        <p>{{ info.onlineRate || '-' }}<span>%</span></p>
                    </div> -->
                </div>
            </div>

            <!-- <div class="progress">
                <progress-c
                    :line-width="info.onlineRate" :line-color="getColor(info.onlineRate)"
                    :margin-left="1"
                    background-color="rgba(0,0,0,.3)"
                />
            </div> -->
            <div class="bgd"></div>
            <div class="bottom">
                <p>总数<span class="tetxspan">{{ info.total || '-' }}</span></p>
                <p v-if="info.remain !== null">剩余
                    <span class="tetxspan">{{ info.remain || '-' }}</span>
                </p>
            </div>
        </div>

    </div>
</template>

<script>
import ProgressC from '@/components/Common/Progress/index.vue';
import {getColor} from '@/views/ServiceManagement/utils/index';

export default {
    components: {
        ProgressC,
    },
    props: {
        url: {
            type: String,
            default: '',
        },
        info: {
            type: Object,
            default: () => { },
        },
    },
    setup() {
        return {
            getColor,
        };

    },

};
</script>

<style lang="less" scoped>
.item {
    display: flex;
    flex-direction: column;

    .type {
        backdrop-filter: blur(10.87px);
        width: 268px;
        border-bottom: 1px solid rgba(255, 255, 255, .1);
    }

    .top {
        display: flex;
        flex-direction: column;
        width: 270px;
        font-size: 20px;

        .exa {
            display: flex;
        }

        .top-icon {
            width: 32px;
            height: 32px;
            display: flex;
            justify-content: center;
            border: 1px solid rgba(255, 255, 255, .4);
            border-top: 4px solid rgba(255, 255, 255, .4);
            align-items: center;
            background-size: 100% 100%;
            padding: 6px;
            margin-right: 12px;
        }

        position: relative;

        .text {
            line-height: 32px;
            width: 229px;

            p {
                font-size: 20px;
                font-family: PingFang SC;
                font-weight: 500;
                line-height: 32px;
                letter-spacing: 0;
                text-align: justify;
            }

            .last {
                position: absolute;
                font-family: RoboData;
                font-size: 14px;
                font-weight: 400;
                line-height: 20px;
                letter-spacing: 0;
                text-align: justify;
                opacity: .3;
            }
        }

        .trans {
            color: rgb(255, 255, 255);
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
            letter-spacing: 0;
            text-align: justify;
            margin-left: 32px;
            opacity: .3;
        }

        .right {
            p {
                color: rgb(255, 255, 255);
                font-family: RoboData;
                font-size: 32px;
            }

            span {
                font-size: 14px;
                opacity: .8;
            }
        }

        img {
            width: 20px;
            height: 20px;
        }

        .a {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 42px;
            height: 42px;
            border: 1px solid rgba(255, 255, 255, .1);
            border-top: 1px solid #8e8e8e;
        }
    }

    .progress {
        padding: 0 12px 0 18px;
    }

    .bgd {
        height: 12px;
    }

    .bottom {
        width: 270px;
        height: 31px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 9px;
        margin-bottom: 10px;

        p {
            opacity: .6;
            color: rgb(255, 255, 255);
            font-family: PingFang SC;
            font-size: 16px;
            font-weight: 500;
            line-height: 26px;

            .tetxspan {
                color: rgb(255, 255, 255);
                font-family: RoboData;
                font-size: 18px;
                line-height: 26px;
                margin-left: 8px;
            }
        }
    }
}
</style>