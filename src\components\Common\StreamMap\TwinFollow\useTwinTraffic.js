

import {twinManager} from '@TrafficLayer/utils/map/index.js';
import {onUnmounted, ref, watch} from 'vue';

import {io} from 'socket.io-client';
import {throttle} from 'lodash';

import {useIntervalFn} from '@vueuse/core';
import useSearchCar from './useSearchCar.js';

import {v4 as uuidV4} from 'uuid';

const main = ({
    id = uuidV4(),
    url,
    center,
}) => {
    let twin = null;
    let ws = null;
    const viewTracks = ref([]);
    const followInfo = ref(null);
    const followCarInfo = ref(null);
    const showPlate = ref(false);

    const canFollowListSet = new Set();
    const targetPosition = ref(null);

    const {search, cancel, searchIng} = useSearchCar({
        id,
        url,
    }, info => {
        const {longitude, latitude} = info;
        targetPosition.value = {longitude, latitude};
    });
    let str = '', i, t;
    const onCurrentCarList = ({content}) => {
        // console.log('content', content);
        const {index, total, data} = content;
        if (i === total && index !== 1) return;
        i = index;
        if (index === 1) str = '';
        // console.log('index', index, total, data);
        str = str + data;
        if (index === total) {
            canFollowListSet?.clear();
            // const list = JSON.parse(str)?.list ?? [];
            // list.forEach(({plate}) => {
            //     if (plate) canFollowListSet.add(plate);
            // });
            // console.log('eeeee', content);
        }
        // console.log('oncurrentlist', content);
        // canFollowListSet?.clear();
        // content?.List.forEach(({plate}) => {
        //     if (plate) canFollowListSet.add(plate);
        // });
    };

    const followTraffic = (info = followInfo.value) => {
        // 地图视角追踪车辆
        twin.viewFollow(true, info);
        // 告诉服务端追踪哪辆车
        ws.sendFollow(info);
    };

    const unFollowTraffic = (info = followInfo.value) => {
        twin?.viewFollow(false, info);
    };

    const canFollowPlate = plate => canFollowListSet.has(plate);

    const handlerChangeTrafficCenter = throttle(
        camera => setViewCenter(camera.x, camera.y),
        1000,
        {
            leading: true,
            trailing: true,
        }
    );
    watch(center, handlerChangeTrafficCenter);

    const {resume, pause} = useIntervalFn(() => {
        followTraffic();
    }, 350, {
        immediateCallback: false,
        immediate: false,
    });

    const follow = () => {
        console.log('fffff-follow');
        targetPosition.value = null;
        pause();
        cancel();
    };

    const unfollow = () => {
        console.log('fffff-unfollow');
        if (followInfo.value) {
            search(followInfo.value);
            resume();
        }
    };

    const onmessage = tracks => {
        viewTracks.value = tracks.filter(e => {
            if (!e.plate) return false;
            if (followInfo.value && e.plate === followInfo.value.plate) {
                followCarInfo.value = e;
            }
            // return canFollowListSet.has(e.plate);
            return true;
        });
    };

    const onConnected = () => {
        setViewCenter();
    };

    // 创建地图内部孪生类
    const createTwin = () => {
        twin = twinManager.add('text', {
            url: url.value,
            id: id,
            tid: 'imBDTwinsVehicleSocketIO',
            showPlate: showPlate.value,
            uiRenderDistance: 0,
            interval: 400,
            onDisconnected: () => {},
            onConnected,
            onCurrentCarList: onCurrentCarList,
            follow,
            unfollow,
        });
        twin.viewFollow = (follow = true, info) => {
            twin.followTraffic({
                isFollow: follow,
                followRotations: {Roll: false, Pitch: false, Yaw: true},
                carId: info?.id,
                plateNumber: info?.plate,
                color: {r: .1, g: .4, b: 1},
            });
        };
    };
    // 创建孪生ws，为了拿到数据
    const createWs = () => {
        if (ws) ws.close();
        ws = io(url.value, {
            transports: ['websocket'],
            secure: true,
            query: {
                twinId: id + '2',
                userType: 1,
                tid: 'imBDTwinsVehicleSocketIO',
            },
        });

        ws.sendFollow = info => {
            ws.emit('message', info);
        };

        // 从服务器接收消息
        ws.on('message', data => {
            try {
                const d = JSON.parse(data || '{}');
                d.tracks && onmessage(d.tracks ?? []);
            }
            catch (error) {}
        });
    };

    const initTwin = async () => {
        createWs();
        createTwin();
    };

    function setViewCenter(lng = center.value.x, lat = center.value.y, scope = 1000) {
        if (!twin) return;
        let vehicleScope = [{
            longitude: lng,
            latitude: lat,
        },
        targetPosition.value,
        ];
        vehicleScope = vehicleScope.filter(e => !!e).map(e => Object.assign(e, {'roadName': 'G0423', scope}));
        twin.message = {vehicleScope};
        // ws?.emit('message', { vehicleScope });
        ws?.send(twin.message);
    };

    const stop = () => {
        if (!twin) return;
        twin.message = {'cmd': 'stop'};
        ws?.emit('message', twin.message);
    };

    watch(url, v => {
        if (v) initTwin();
    },
    {immediate: true}
    );

    watch(showPlate, v => {
        if (twin) {
            twin.labelVisible = v;
        }
    });

    watch(targetPosition, v => (v ? resume() : pause()));

    const destroyTwin = () => {
        twinManager.removeByName('text');
        stop();
        ws.close();
        followInfo.value = null;
        targetPosition.value = null;
        followCarInfo.value = null;
    };

    const track = info => {
        followInfo.value = {...info};
        // 开始追踪
        resume();
    };

    const unTrack = () => {
        pause();
        cancel();
        unFollowTraffic();
        followInfo.value = null;
        followCarInfo.value = null;
    };

    const changePlateState = v => {
        showPlate.value = v;
    };

    onUnmounted(() => {
        destroyTwin();
    });

    return {
        searchIng,
        viewTracks,
        followCarInfo,
        canFollowPlate,
        track,
        unTrack,
        changePlateState,
    };
};

export default main;