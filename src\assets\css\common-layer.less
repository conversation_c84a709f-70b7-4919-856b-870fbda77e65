@font-face {
    font-family: 'iconfont';

    /* Project id 2643852 */
    src: url('../fonts/iconfont.woff2') format('woff2');
}

@font-face {
    font-family: 'material-icons';
    src: url('../fonts/material-icons.woff2') format('woff2');
}

@font-face {
    font-family: 'DINAlternate-Bold';
    src: url('../fonts/DINAlternate-Bold.ttf');
}

@font-face {
    font-family: 'RoboData';
    src: url('../fonts/RoboDataRegular.otf') format('opentype');
}

.card {
    position: relative;
    width: 604px;
    border-radius: 3px;
    box-sizing: border-box;

    &.card-short-1 {
        width: 604px;

        .header {
            background-image: url('@/assets/images/base/card-short-1.png');
        }
    }

    &.card-short-2 {
        width: 604px;

        .header {
            background-image: url('@/assets/images/base/card-short-2.png');
        }
    }

    &.card-long-1 {
        width: 1232px;

        .header {
            background-image: url('@/assets/images/base/card-long-1.png');
        }
    }

    &.card-long-2 {
        width: 1232px;

        .header {
            background-image: url('@/assets/images/base/card-long-2.png');
        }
    }

    .header {
        height: 36px;
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-size: 100% 100%;
        padding-left: 24px;
        padding-right: 2px;

        .title {
            display: flex;
            align-items: center;
            line-height: 24px;
            font-size: 22px;
            color: #fff;
            position: relative;
            font-family: 'OPlusSans';
            font-weight: 500;
            letter-spacing: 1px;

            span {
                margin-left: 10px;

                &.noMargin {
                    margin-left: 0;
                }
            }
        }

        .titleContent {
            display: flex;
            align-items: center;
        }

        .icon-close {
            color: #fff;
            width: 24px;
            transform: rotate(45deg);
            cursor: pointer;
            transition: transform .3s, filter .3s;
            filter: contrast(1);

            &:hover {
                transform: rotate(315deg);
                filter: contrast(1.3);
            }
        }

        .icon-close-no-t {
            color: #fff;
            width: 24px;
            transform: rotate(45deg);
            cursor: pointer;
            transition: transform .3s, filter .3s;
            filter: contrast(1);
        }
    }

    .content {
        margin-top: 4px;
        color: rgba(255, 255, 255, .6);
        font-size: 16px;
        font-weight: 500;
        padding: 24px;
        background-color: rgba(#08234f, .8);
        border: 1px solid rgba(#29569d, .8);
        backdrop-filter: blur(8px);

        &.marginBottom {
            margin-bottom: 10px;
        }

        // 列表格式的样式设置
        .list {
            .item {
                display: flex;
                justify-content: space-between;
                width: 100%;
                color: #fff;
                font-size: 14px;
                font-weight: 500;
                line-height: 18px;

                &:not(:last-child) {
                    margin-bottom: 12px;
                }

                .label {
                    flex-shrink: 0;
                    color: rgba(255, 255, 255, .6);
                }

                .value {
                    flex: 1;
                    padding-left: 10px;
                    text-align: right;
                }
            }
        }

        // 表格头格式的样式设置
        :deep(.content-title) {
            display: flex;
            align-items: center;
            height: 28px;
            font-size: 12px;
            font-family: 'OPlusSans';
            font-weight: 500;
            background-color: rgba(255, 255, 255, .05);
        }

        &::-webkit-scrollbar {
            /* 设置垂直滚动条宽度 */
            display: block;
            height: 6px;
        }

        &::-webkit-scrollbar-thumb {
            /* 滚动条里面小方块 */
            background: rgb(72, 72, 72);
            cursor: pointer;
        }

        &::-webkit-scrollbar-track {
            /* 滚动条里面轨道 */
            background: transparent;
            margin: 0 10px;
        }
    }

    .btn-group {
        display: flex;

        .btn-default:not(:last-child) {
            margin-right: 2px;
        }
    }

    .btn-default {
        min-width: 58px;
        padding: 0 8px;
        height: 22px;
        line-height: 20px;
        text-align: center;
        background-color: #142758;
        font-size: 14px;
        border: 1px solid #142758;
        color: rgb(128, 144, 187);
        cursor: pointer;
        clip-path: polygon(0 0, 100% 0, 100% calc(100% - 5px), calc(100% - 5px) 100%, 0 100%);

        &:hover {
            color: #fff;
        }

        &__active {
            position: relative;
            color: #fff;
            background-color: #2460b7;
            border-color: #6897e5;

            &::after {
                position: absolute;
                right: -1.5px;
                bottom: 0;
                content: '';
                width: 8px;
                height: 1.5px;
                background-color: #6897e5;
                transform: rotate(-45deg);
            }
        }
    }
}
