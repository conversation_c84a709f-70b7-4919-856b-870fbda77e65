import {
    getHighSpeedInfo,
    getStakenumberlist,
} from '@/api/emulation';
// mock
import {Mitt} from '@/utils/mitt';

const mitt = new Mitt();

export const cache = new Map();

/**
 * 获取接口数据，带有缓存，多次请求只发送一次
 * @param {*} key
 * @param {*} api
 * @param {*} params
 * @returns
 */
function baseFetch(key, api, params) {
    return new Promise(async (resolve, reject) => {
        // 首次
        if (!cache.has(key)) {
            try {
                cache.set(key, 'loading');
                const {data} = await api(params);
                data && cache.set(key, data);
                mitt.emit(key, data);
                resolve(data);
            }
            catch (error) {
                cache.delete(key);
                reject();
            }
            return;
        }
        // 缓存请求中
        if (cache.get(key) === 'loading') {
            mitt.one(key, resolve);
            return;
        }
        // 已缓存
        resolve(cache.get(key));
    });
}

// 获取速度信息，带有缓存
export async function getHighSpeedInfoCache() {
    const key = 'HighSpeedInfo';
    const data = await baseFetch(key, getHighSpeedInfo);
    return data.map(item => {
        if (item.highSpeedName === 'S15') {
            item.highSpeedNameCn = '佛开高速';
        }
        return item;
    });
}

export async function getStakenumberlistCache(highSpeedName = 'G0421') {
    const key = `Stakenumberlist__${highSpeedName}`;
    return baseFetch(key, getStakenumberlist, highSpeedName);
}