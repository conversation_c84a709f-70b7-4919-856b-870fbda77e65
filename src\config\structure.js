export const trafficTypeDict = new Map([
    [1, '计划性事件'],
    [2, '突发性事件'],
    [3, '养护施工'],
    [4, '重大社会活动'],
    [5, '重大事件'],
    [6, '车辆故障'],
    [7, '交通事故'],
    [8, '路面状况'],
    [9, '交通气象'],
    [10, '服务区事件'],
    [11, '交通灾害'],
    [12, '中间绿化带养护维修'],
    [13, '隧道养护维修'],
    [14, '桥梁养护维修'],
    [15, '路面养护维修'],
    [16, '路面清扫'],
    [17, '路况检测'],
    [18, '机电设施养护维修'],
    [19, '管线养护维修'],
    [20, '高空作业'],
    [21, '防护设施养护维修'],
    [22, '其他'],
    [23, '军事演习'],
    [24, '领导视察'],
    [25, '体育活动'],
    [26, '文化活动'],
    [27, '其他'],
    [28, '车流量大'],
    [29, '交通管制'],
    [30, '危化品事故'],
    [31, '警卫任务'],
    [32, '群体事件'],
    [33, '电力事故'],
    [34, '公共暴力'],
    [35, '恶意事件'],
    [36, '爆炸'],
    [37, '燃气事故'],
    [38, '化学污染'],
    [39, '其他'],
    [40, '抛锚'],
    [41, '爆胎'],
    [42, '其他'],
    [43, '追尾'],
    [44, '刮擦'],
    [45, '翻车'],
    [46, '车辆起火'],
    [47, '撞固定物'],
    [48, '撞抛洒物'],
    [49, '涉遂事故'],
    [50, '涉桥事故'],
    [51, '其他车车事故'],
    [52, '船舶撞桥'],
    [53, '其他设施相关'],
    [54, '撞动物'],
    [55, '撞行人'],
    [56, '人车坠落'],
    [57, '其他人车事故'],
    [58, '其他'],
    [59, '抛洒物'],
    [60, '倒车'],
    [61, '停车'],
    [62, '逆行'],
    [63, '积水'],
    [64, '非机动车'],
    [65, '劝离行人'],
    [66, '道路结冰'],
    [67, '湿滑'],
    [68, '货物倾斜'],
    [69, '货物散落'],
    [70, '液体'],
    [71, '机油泄漏'],
    [72, '道路障碍'],
    [73, '动物'],
    [74, '其他'],
    [75, '大雾'],
    [76, '霾'],
    [77, '暴雨'],
    [78, '台风'],
    [79, '大风'],
    [80, '高温'],
    [81, '暴雪'],
    [82, '雪'],
    [83, '冰雹'],
    [84, '寒潮'],
    [85, '霜'],
    [86, '冻'],
    [87, '雷电'],
    [88, '干旱'],
    [89, '沙尘'],
    [90, '其他'],
    [91, '缺油'],
    [92, '无停车位'],
    [93, '服务区关闭'],
    [94, '其他'],
    [95, '路面火灾'],
    [96, '路边火灾'],
    [97, '道路损坏'],
    [98, '水灾'],
    [99, '山体滑坡'],
    [100, '隧道塌方'],
    [101, '隧道火灾'],
    [102, '道路设施火灾'],
    [103, '桥梁损坏'],
    [104, '其他地质灾害'],
    [105, '环境污染'],
    [106, '海啸'],
    [107, '地震'],
    [108, '其他'],
    [111, '其他'],
    [112, '入口冲卡车辆'],
    [113, '出口拦截车辆'],
    [114, '其他伤亡事件'],
]);

// 车辆类型字典
export const carTypeDict = [
    {
        key: '0',
        value: '所有车辆',
    },
    {
        key: '11',
        value: '一型货车',
    },
    {
        key: '12',
        value: '二型货车',
    },
    {
        key: '13',
        value: '三型货车',
    },
    {
        key: '14',
        value: '四型货车',
    },
    {
        key: '15',
        value: '五型货车',
    },
    {
        key: '16',
        value: '六型货车',
    },
];

// 车辆轴型字典
export const carAxleDict = [
    {
        key: '0',
        value: '所有货车',
    },
    {
        key: '1',
        value: '一轴货车',
    },
    {
        key: '2',
        value: '二轴货车',
    },
    {
        key: '3',
        value: '三轴货车',
    },
    {
        key: '4',
        value: '四轴货车',
    },
    {
        key: '5',
        value: '五轴货车',
    },
    {
        key: '6',
        value: '六轴货车',
    },
];

// 方向字典
export const sectionDirectionDict = [
    {
        key: 1,
        name: '上行',
    },
    {
        key: 2,
        name: '下行',
    },
    {
        key: 3,
        name: '全向',
    },
];

// 结构物数据卡片——列表类型枚举
export const strcutureDataList = [
    {
        key: 1,
        keyword: 'bridge',
        name: '桥梁',
    },
    {
        key: 2,
        keyword: 'tunnel',
        name: '隧道',
    },
    {
        key: 3,
        keyword: 'slope',
        name: '边坡',
    },
];