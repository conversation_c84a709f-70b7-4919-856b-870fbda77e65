<template>
    <map-component
        :options="{
            center: mapCenter,
            showSatelliteMap: showSatelliteMap,
            showTollStations: true,
            showAssetsScene: true,
        }"
        :show-map-background="showMapBackground"
        :show-control="showControl"
        @mapLoaded="mapLoaded"
        @recover="$emit('mapRecover')"
    >
        <slot v-if="mapLoadState"></slot>
    </map-component>
</template>

<script>
import {Map} from '@/components/Common';
import {ref} from 'vue';
import {engine, mapCenter} from '@/store/engine.js';
import {viewToMac} from '@/utils';

export default {
    components: {
        MapComponent: Map,
    },
    props: {
        showSatelliteMap: {
            type: Boolean,
            default: true,
        },
        showMapBackground: {
            type: Boolean,
            default: true,
        },
        showControl: {
            type: Boolean,
            default: true,
        },
    },
    setup() {
        const mapLoadState = ref(false);

        function mapLoaded(map) {
            engine.value = map;
            mapLoadState.value = true;
            viewToMac();
        }

        return {
            mapLoaded,
            mapCenter,
            mapLoadState,
        };
    },
};
</script>