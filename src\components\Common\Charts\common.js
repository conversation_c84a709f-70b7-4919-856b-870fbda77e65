import * as echarts from 'echarts/core';
import {
    TooltipComponent,
    TitleComponent,
    GridComponent,
    DatasetComponent,
    TransformComponent,
    GridSimpleComponent,
    MarkLineComponent,
    LegendComponent,
    MarkAreaComponent,
} from 'echarts/components';
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart} from 'echarts/charts';
import {LabelLayout, UniversalTransition} from 'echarts/features';
import {SVGRenderer} from 'echarts/renderers';
import chartStyle from '@/assets/json/chartStyle.json';

echarts.use([
    TooltipComponent,
    TitleComponent,
    GridComponent,
    DatasetComponent,
    TransformComponent,
    LabelLayout,
    UniversalTransition,
    GridSimpleComponent,
    SVGRenderer,
    MarkLineComponent,
    LegendComponent,
    Mark<PERSON>rea<PERSON>omponent,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>hart,
]);

echarts.registerTheme('huhangyong', chartStyle);

export {echarts};