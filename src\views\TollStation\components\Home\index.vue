<template>
    <div class="home-wrapper">
        <LeftPanel :collapse.sync="leftCollapse"/>
        <RightPanel :collapse.sync="rightCollapse"/>
        <TollStationMarker class="pa" @clickMarker="handleClickMarker"/>
    </div>
</template>

<script setup>
import {useCollapsePanel} from '@/hooks/useCollapsePanel';
import LeftPanel from './LeftPanel/index.vue';
import RightPanel from './RightPanel/index.vue';
import TollStationMarker from './TollStationMarker/index.vue';

const emit = defineEmits(['clickMarker']);

const {collapse: leftCollapse} = useCollapsePanel();
const {collapse: rightCollapse} = useCollapsePanel();

function handleClickMarker(e) {
    emit('clickMarker', e);
}
</script>