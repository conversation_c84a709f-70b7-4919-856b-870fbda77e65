<template>
    <div class="pagination">
        <span>共搜索到 {{ total }} 条数据</span>
        <div class="right">
            <span
                class="btn" :class="{disable: prevBtnDisabled}"
                @click="handlerChangePageNo(-1)"
            >&lt;</span>
            <span>{{ pageNo }}</span>
            /
            <span>{{ pageCount }}</span>
            <span
                class="btn" :class="{disable: nextBtnDisabled}"
                @click="handlerChangePageNo(1)"
            >></span>
        </div>
    </div>
</template>

<script>
import {computed} from 'vue';

export default {
    name: 'Pagination',
    props: {
        total: {
            type: Number,
            default: 0,
        },
        pageSize: {
            type: Number,
            default: 5,
        },
        pageNo: {
            type: Number,
            default: 1,
        },
    },
    setup(props, {emit}) {
        const pageCount = computed(() => {
            return Math.max(Math.ceil(props.total / props.pageSize), 1);
        });

        const prevBtnDisabled = computed(() => props.pageNo <= 1);
        const nextBtnDisabled = computed(() => props.pageNo >= pageCount.value);

        const handlerChangePageNo = count => {
            if (count < 0 && prevBtnDisabled.value) return;
            if (count > 0 && nextBtnDisabled.value) return;

            emit('change', props.pageNo + count);
        };

        return {
            pageCount,
            prevBtnDisabled,
            nextBtnDisabled,
            handlerChangePageNo,
        };
    },
};
</script>


<style lang="less" scoped>
.pagination {
    width: 100%;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #e7efff;
    padding-left: 16px;
    font-size: 12px;
    background: rgba(120, 212, 184, .1);

    .right {
        display: flex;
        align-items: center;
        gap: 6px;

        .btn {
            margin-inline: 4px;
        }

        span {
            padding: 0 3px;
            text-align: center;
            cursor: pointer;

            &.disable {
                cursor: not-allowed;
                filter: contrast(.4);
            }
        }
    }
}
</style>