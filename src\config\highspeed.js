
// 高速总览收费站详情枚举
export const tollDetailConfig = [
    {
        label: '桩号位置：',
        value: 'stakeLocation',
    },
    {
        label: '入口车道数：',
        value: 'enterLaneNum',
    },
    {
        label: '出口车道数：',
        value: 'exitLaneNum',
    },
    {
        label: '收费站状态：',
        value: 'tollStationStatus',
    },
];

// 高速总览显示屏详情枚举
export const intelboardDetailConfig = [
    {
        label: '桩号位置：',
        value: 'stakeLocation',
    },
    {
        label: '道路侧向：',
        value: 'roadDirection',
    },
    {
        label: '投入使用时间：',
        value: 'useTime',
    },
    {
        label: '预计报废时间：',
        value: 'scrapTime',
    },
    {
        label: '设备运行状态：',
        value: 'deviceStatus',
    },
];

// 高速总览互通立交详情枚举
export const intelchangeDetailConfig = [
    {
        label: '桩号位置：',
        value: 'stakeLocation',
    },
];

// 高速总览桥梁详情枚举
export const bridgeDetailConfig = [
    {
        label: '桩号位置：',
        value: 'stakeLocation',
    },
    {
        label: '技术等级：',
        value: 'technicalGrade',
    },
];

// 高速总览服务区详情枚举
export const serviceDetailConfig = [
    {
        label: '桩号位置：',
        value: 'stakeLocation',
    },
    {
        label: '服务区状态：',
        value: 'serviceAreaStatus',
    },
];

// 高速总览摄像枪详情枚举
export const cameraDetailConfig = [
    {
        label: '桩号位置：',
        value: 'stakeLocation',
    },
    {
        label: '道路侧向：',
        value: 'roadDirection',
    },
    {
        label: '投入使用时间：',
        value: 'useTime',
    },
    {
        label: '预计报废时间：',
        value: 'scrapTime',
    },
    {
        label: '设备运行状态：',
        value: 'deviceStatus',
    },
];

export const cameraRoadDirectionDict = new Map([
    ['1', '广州方向'],
    ['2', '开平方向'],
    ['3', '全向'],
]);

export const cameraDeviceStatusDict = new Map([
    ['0', '不在线'],
    ['1', '在线'],
    ['2', '故障'],
]);