import {ref, onBeforeUnmount} from 'vue';
import {useIntervalFn} from '@vueuse/core';

// 获取当前时间 返回 {time: 当前时间 格式HH:mm:ss, week: 当前周几, date: 当前日期 格式yyyy-MM-dd}
export const useTimeNow = () => {
    let _date = ref('');
    let week = ref('');
    let time = ref('');

    const uptime = (date = new Date()) => {
        _date.value = date.toISOString().split('T')[0];
        week.value = date.toLocaleString('zh-CN', {weekday: 'long'});
        time.value
            = date.toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit', second: '2-digit'});
    };

    const {pause, resume, isActive} = useIntervalFn(
        () => uptime(),
        1000,
        {
            immediateCallback: true,
            immediate: true,
        }
    );

    onBeforeUnmount(() => {
        pause?.();
    });

    return {
        time,
        week,
        date: _date,
        resume,
        pause,
        isActive,
    };
};