
import {watch, shallowRef} from 'vue';
import {position} from '@/store/engine';
import {AssetLayer} from '@baidu/cloudrenderengine';

const token = '5E962E0D519D193FAF23';
const projectId = '418690084255052706';

// 加载地域上的模型等资源 例如灯杆龙门架等等
export default ({
    map,
}) => {

    let assetLayer = shallowRef(null);

    // 初始化动态加载模型的参数
    const initAssetsRequest = () => {
        const body = {
            all: 1,
            common: 1,
            project_id: projectId,
        };
        map.value.setupModelInfo(
            'http://172.23.8.101/twin/assetApi/screen/searchModelInfos?system=mapvUnreal',
            body,
            {
                Authorization: token,
            },
            'http://172.23.8.101/twin/assetApi/getStaticResourceUrl?system=mapvUnreal'
        );
    };
    // 初始化静态资源图层
    const initAssetsManager = () => {
        initAssetsRequest();

        const assetsLayerBody = {
            project_id: projectId,
            radius: 500,
            type: 'online',
            status: 1,
            ...position,
        };
        assetLayer = new AssetLayer({
            url: 'http://172.23.8.101/twin/assetApi/screen/getDeviceListByRadius?system=mapvUnreal',
            header: {
                Authorization: token,
            },
            body: assetsLayerBody,
            deviceAutoUpdate: true,
            deviceAutoUpdateInterval: 3,
        });
        map.value.addToScene(assetLayer);
    };

    watch(map, v => {
        if (v) {
            initAssetsManager();
        }
    }, {
        immediate: true,
    });
};