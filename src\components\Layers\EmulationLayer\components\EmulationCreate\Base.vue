<template>
    <div>
        BaseComponent
    </div>
</template>

<script>
import {ref} from 'vue';
export default {
    name: 'BaseComponent',
    components: {
    },
    props: {

    },
    inject: [],
    data() {
        return {

        };
    },
    filters: {

    },
    watch: {

    },
    computed: {


    },
    methods: {

    },
    setup(props, {emit}) {

        return {
        };
    },

};
</script>
