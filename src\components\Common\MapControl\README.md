# 按钮组组件

### 参数说明  props
|Prop name|Type|Description|
|---|---|---|
|`list`|`Options[]`|按钮列表|

eg:
```ts

interface BooleanFunction {
    (void): Boolean
}

interface JobFunction {
    (void): void
}

interface Options {
    icon: string // 图标 iconfont 图标名称
    name: string // 按钮名称i,
    children: Options[] // 子按钮列表
    hide: BooleanFunction, // 判断是否隐藏
    active: BooleanFunction, // 判断是否激活
    job: JobFunction, // 点击事件触发的函数
}
```