<template>
    <div
        ref="videoContainer"
        v-loading="loading"
        class="video-container"
        :class="{
            noUse: !usePlay,
        }"
        customClass="video-load-icon"
        element-loading-background="rgba(8, 12, 25, 0.8)"
    >
    </div>
</template>
<script>
import Tp from './tiny-player';

export default {
    name: 'BackVideoPlayer',
    props: {
        url: {
            type: String,
            default() {
                return '';
            },
        },
        id: {
            type: String,
            default() {
                return '';
            },
        },
        autoplay: {
            type: Boolean,
            default() {
                return false;
            },
        },
        live: {
            type: Boolean,
            default() {
                return true;
            },
        },
        controls: {
            type: <PERSON><PERSON><PERSON>,
            default() {
                return true;
            },
        },
        loop: {
            type: <PERSON><PERSON><PERSON>,
            default() {
                return false;
            },
        },
        usePlay: {
            type: Boolean,
            default: true,
        },
        poster: {
            type: String,
            default: './files/loding.png',
        },
    },
    data() {
        return {
            loading: false,
            timer: '',
        };
    },
    watch: {
        url: {
            handler: 'handlerChangeVideoUrl',
            immediate: true,
            // deep: true
        },
    },
    mounted() {},
    beforeDestroy() {
        if (this.player) {
            this.player?.dispose?.();
            this.player?.destroy?.();
        }
    },
    methods: {
        handlerChangeVideoUrl(newUrl) {
            if (newUrl) {
                if (this.player) {
                    this.player?.destroy?.();
                }
                this.$nextTick(() => {
                    this.createPlayer();
                });
            }
        },
        createPlayer() {
            this.loading = true;
            const that = this;

            this.player = new Tp(
                {
                    container: this.$refs.videoContainer,
                    autoplay: this.autoplay,
                    controls: this.controls,
                    preload: 'none',
                    loop: this.loop,
                    // muted: 'muted',
                    type: this.live ? 'hls' : 'mp4',
                    src: this.url,
                }
            );
            this.player.on('loadeddata', () => {
                that.loading = false;
            });
            this.player.on('waiting', () => {
                that.handlerSetPoster(1);
            });
            this.player.on('seeking', () => {
                // 正在去拿视频流的路上
                if (that.seekTimeout) {
                    clearTimeout(that.seekTimeout);
                    that.seekTimeout = null;
                }
            });
            this.player.on('seeked', () => {
                clearTimeout(this.timer);
                // 已经拿到视频流,可以播放
                that.handlerSetPoster(0);
                if (that.seekTimeout) {
                    clearTimeout(that.seekTimeout);
                    that.seekTimeout = null;
                }
                that.loading = false;
            });
            this.player.on('playing', () => {
                // console.log('视频播放中'); // eslint-disable-line
            });
            this.player.on('pause', () => {
                        console.log('视频暂停播放'); // eslint-disable-line
            });
            this.player.on('ended', () => {
                        console.log('视频播放结束'); // eslint-disable-line
            });
            this.player.on('error', () => {
                const url = that.url;
                that.$emit('error-callback', url);
            });
        },
        handlerSetPoster(type) {
        },
        captureImage() {
            const canvas = document.createElement('canvas');
            canvas.width = this.$refs.videoPlayer.clientWidth;
            canvas.height = this.$refs.videoPlayer.clientHeight;
            canvas.getContext('2d').drawImage(this.$refs.videoPlayer, 0, 0, canvas.width, canvas.height);
            const img = canvas.toDataURL('image/png');
            return img;
        },
        handerFullScreen() {
            if (this.player.requestFullscreen) {
                return this.player.requestFullscreen();
            }
            else if (this.player.webkitRequestFullScreen) {
                return this.player.webkitRequestFullScreen();
            }
            else if (this.player.mozRequestFullScreen) {
                return this.player.mozRequestFullScreen();
            }
        },
        pause() {
            this.player.pause();
        },
        play() {
            this.player.play();
        },
    },
};
</script>
<style lang="less" scoped>
@-webkit-keyframes loading {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(-360deg);
    }
}

@keyframes loading {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(-360deg);
    }
}

.video-container {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
    z-index: 100;

    &.noUse {
        /deep/ .tp-play-icon {
            display: none !important;
        }
    }

    /deep/ .el-loading-mask {
        display: none;
    }

    /deep/ .vjs-waiting .vjs-poster,
    /deep/ .vjs-paused .vjs-poster,
    /deep/ .vjs-error .vjs-poster {
        // display: block !important;
        // background-size: 100% 100%;
    }
    // /deep/ .vjs-has-started .vjs-poster,
    // /ddep/ .vjs-playing .vjs-poster,
    // /deep/ .vjs-live .vjs-poster {
    //     display: none !important;
    // }

    /deep/ .el-loading-spinner {
        display: none;
        width: 60px;
        height: 60px;
        transform: translate(-50%, -50%);
        top: 50%;
        left: 50%;
        margin: 0 auto;

        svg {
            width: 60px;
            height: 60px;
            // background-image: url('../assets/images/<EMAIL>');
            background-size: 100%;
            animation: loading 1s linear 0s infinite;
            backface-visibility: hidden;
            transform-style: preserve-3d;
        }
    }

    .video {
        width: 100%;
        position: relative;
        // height: 150%;
        // top: -25%;
        height: 100%;
        z-index: 101;

        video {
            margin-left: auto;
            margin-right: auto;
            object-fit: fill;
        }

        &.show-control {
            height: 100%;
            top: unset;

            .vjs-control-bar {
                display: flex !important;
            }
        }
    }
}

.empty-box {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    text-align: center;
    z-index: 102;

    .empty-text {
        font-size: 10.16px;
        color: rgba(255, 255, 255, .5);
        position: relative;
        top: 50%;
        transform: translateY(-50%);
    }
}

</style>

<style lang="less">
.vjs-big-play-button {
    position: absolute;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
}
</style>