<!-- 设备设施 -->
<template>
    <map-comp class="equipment-facilities">
        <collapse-panel :collapse.sync="collapseLeft">
            <div style="display: flex;">
                <component :is="routeName + 'Left'"/>
                <div class="duty-card-info"><duty-card/></div>
            </div>
        </collapse-panel>

        <tab-change v-model="routeName"/>

        <collapse-panel
            :collapse.sync="collapseRight" direction="right"
        >
            <component :is="routeName + 'Right'"/>
        </collapse-panel>

        <map-line
            v-for="item in baseList" :key="item.sectionId + item.direction"
            :list="item.sparseLoc"
            :info="{
                name: item.sectionId + item.direction,
                color: '#00FFA4',
            }"
        />

        <emquipment-maker v-if="routeName === 'Equipment'" :map-filter-data="mapFilterData"/>
        <faility-maker v-else :map-filter-data="mapFilterData"/>

        <map-icon-filter
            :class="{'close_right': collapseRight}"
            :list="mapFilterList" :value="mapFilterData"
            multiple
        />

        <detail-list
            v-model="showDetail"
            :style="{'--top': top, '--left': left, '--right': right}"
            class="detail-card"
            :width="width"
            :list="detail.data"
            :title="detail.name"
            @close="handleClose"
        >
            <template #close>
                <div class="arrow_close">
                    <i class="el-icon-caret-top"></i>
                </div>
            </template>
        </detail-list>

        <!-- 设施管理等级图例 -->
        <div
            v-if="routeName !== 'Equipment'" class="level_legend"
            :class="{'collapseRight': collapseRight}"
        ></div>
    </map-comp>
</template>

<script>
import {ref, onMounted} from 'vue';
import {CollapsePanel, DetailList, MapIconFilter} from '@/components/Common';
import EquipmentLeft from './components/home/<USER>/comp-left/index.vue';
import EquipmentRight from './components/home/<USER>/comp-right/index.vue';
import FacilityLeft from './components/home/<USER>/comp-left/index.vue';
import FacilityRight from './components/home/<USER>/comp-right/index.vue';
import {
    detail, showDetail, width, top, left, right,
    mapFilterData, mapFilterList, activeIndex,
} from '@/views/EquipmentFacilities/utils';
import MapComp from './components/map/index.vue';
import MapLine from './components/map/line.vue';
import EmquipmentMaker from './components/map/equipmentMaker.vue';
import FailityMaker from './components/map/failityMaker.vue';
import {getSectionPath} from '@/api/equipment/highspeed';
import {strToArr} from './utils/methods';
import {TabChange} from './components/subcomponent/index';
import {initMaker} from './utils/map.js';
import {DutyCard} from './components/common';
import {useUnit} from '@/utils';
import {useCollapsePanel} from '@/hooks/useCollapsePanel';

export default {
    name: 'EquipmentFacilities',
    components: {
        EquipmentLeft,
        CollapsePanel,
        EquipmentRight,
        FacilityLeft,
        FacilityRight,
        DetailList,
        MapComp,
        MapLine,
        EmquipmentMaker, FailityMaker,
        TabChange,
        MapIconFilter,
        DutyCard,
    },
    setup(props) {
        const routeName = ref('Equipment');
        const {collapse: collapseLeft} = useCollapsePanel();
        const {collapse: collapseRight} = useCollapsePanel();
        const {ratio} = useUnit();

        // 佛开高速基础高速线
        const baseList = ref([]);
        const getLine = () => {
            getSectionPath().then(res => {
                baseList.value = res.data.map(item => ({
                    ...item,
                    sparseLoc: strToArr(item.sparseLoc),
                }));
            });
        };

        onMounted(() => {
            getLine();
            initMaker();
        });

        const handleClose = () => {
            activeIndex.value = null;
        };

        return {
            collapseLeft,
            collapseRight,
            routeName,
            detail, showDetail, mapFilterData, width, top, left, right,
            baseList, mapFilterList,
            ratio,
            handleClose,
        };
    },
};
</script>

<style lang="less" scoped>
.equipment-facilities {
    position: absolute;
    display: flex;
    justify-content: space-between;

    .duty-card-info {
        height: 402px;
    }

    .arrow_close {
        width: 12px;
        height: 12px;
        background-color: rgba(46, 210, 255, .1);
        border: 1px solid rgba(46, 210, 255, .3);
    }

    .map-icon-filter-wrapper {
        position: fixed;
        bottom: 85px;
        right: 1300px;
        z-index: 500;
        transition: all .35s;
        // background-color: red;
    }

    .detail-card {
        position: fixed;
        top: var(--top);
        left: var(--left);
        right: var(--right);
        z-index: 9999;
    }

    .close_right {
        right: 40px;
    }

    .level_legend {
        position: fixed;
        bottom: 50px;
        right: 1360px;
        width: 268px;
        height: 38px;
        background: url('./images/level_legend.png') no-repeat center / 100%;
        z-index: 300;
        transition: all .3s;

        &.collapseRight {
            right: 120px;
        }
    }
}
</style>