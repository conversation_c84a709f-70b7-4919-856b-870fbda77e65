import {request} from '@/utils/network-helper/index';
import {baseConfig} from '@/utils/common';
const {basePrefix} = baseConfig;

const baseConstruction = `${basePrefix}/build`;

const Api = {
    GetProjectList: `${baseConstruction}/getProjectList`,
    GetProjectInfoById: `${baseConstruction}/getProjectInfo`,
    GetDeviceListById: `${baseConstruction}/getDeviceList`,
    GetStatusNum: `${baseConstruction}/getStatusNum`,
    GetAllProject: `${baseConstruction}/getAllProject`,
    GetDeviceByLngLat: `${baseConstruction}/getDeviceByLngLat`,
    GetEventByDay: `${baseConstruction}/getEventByDay`,
    GetRoadEventNum: `${baseConstruction}/getRoadEventNum`,
    GetKeyCarList: `${baseConstruction}/getKeyCarList`,
    GetEventList: `${baseConstruction}/getEventList`,
    GetCongestedRoad: `${baseConstruction}/getCongestedRoad`,
    GetInfoboard: `${baseConstruction}/getInfoboard`,
};

/**
 * 获取施工项目列表
 */
export function getProjectList(projectType) {
    return request.post(Api.GetProjectList, {projectType});
}

/**
 * 根据施工项目id获取详情
 */
export function getProjectInfoById(id) {
    return request.post(Api.GetProjectInfoById, {id});
}


/**
 * 根据施工项目id获取设备列表
 */
export function getDeviceListById(id) {
    return request.post(`${Api.GetDeviceListById}?id=${id}`);
}

/**
 * 施工事件统计
 */
export function getStatusNum() {
    return request.get(Api.GetStatusNum);
}

/**
 * 获取工程列表
 */
export function getAllProject(params) {
    return request.post(Api.GetAllProject, params);
}

/**
 * 根据项目工程经纬度 获取设备
 */
export function getDeviceByLngLat(parmas) {
    return request.get(Api.GetDeviceByLngLat, parmas);
}

/**
 * 根据日期获取事件24小时数量
 */
export function getEventByDay(parmas) {
    return request.post(Api.GetEventByDay, parmas);
}

/**
 * 根据日期获取路段-事件24小时数量
 */
export function getRoadEventNum(parmas) {
    return request.post(Api.GetRoadEventNum, parmas);
}

/**
 * 根据工程类型获取重点车辆
 */
export function getKeyCarList(parmas) {
    return request.post(Api.GetKeyCarList, parmas);
}

/**
 * 根据工程类型获取最近1小时事件
 */
export function getEventList(parmas) {
    return request.post(Api.GetEventList, parmas);
}

/**
 * 根据工程类型获取路况信息
 */
export function getCongestedRoad(parmas) {
    return request.post(Api.GetCongestedRoad, parmas);
}

/**
 * 根据工程类型获取路况信息
 */
export function getInfoboard(parmas) {
    return request.get(Api.GetInfoboard, parmas);
}
