<template>
    <div class="health-state">
        <div
            v-for="(item, index) in list" :key="index"
            class="health-state__item"
        >
            <p class="health-state__item__title">{{ item.title }}</p>
            <progress-bar :percentage="item.percentage"/>
            <p class="health-state__item__point">{{ item.percentage }}%</p>
            <p class="health-state__item__total">{{ item.total }}</p>
        </div>
    </div>
</template>

<script>
import {ProgressBar} from '@/components/Common';
import {facilityCountHealth} from '@/api/equipment/facilitydisplay';
import {ref, onMounted} from 'vue';
export default {
    name: '桥梁结构物健康状态',
    components: {
        ProgressBar,
    },
    setup(props) {
        const list = ref([
            {
                title: '健康度一级',
                percentage: 0,
                total: 0,
                type: 'oneHealth',
            },
            {
                title: '健康度二级',
                percentage: 0,
                total: 0,
                type: 'twoHealth',
            },
            {
                title: '健康度三级',
                percentage: 0,
                total: 0,
                type: 'threeHealth',
            },
            {
                title: '健康度四级',
                percentage: 0,
                total: 0,
                type: 'fourHealth',
            },
        ]);

        const init = () => {
            facilityCountHealth().then(res => {
                list.value = list.value.map(item => ({
                    ...item,
                    percentage: res.data[item.type + 'Pro'],
                    total: res.data[item.type],
                }));
            });
        };

        onMounted(() => init());

        return {
            list,
        };
    },
};
</script>

<style lang="less" scoped>
.health-state {
    width: 100%;
    height: 146px;

    &__item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 16px;
        font-weight: 400;
        color: rgba(#fff, .6);
        margin-bottom: 20px;

        &:last-child {
            margin-bottom: 0;
        }

        &__title {
            width: 85px;
            font-family: 'PingFang';
        }

        .x-progress-wrapper {
            width: 280px;
            margin: 0 16px;
            transform: translateY(4px);
        }

        &__point {
            position: relative;
            min-width: 65px;
            font-family: 'RoboData';
            margin-right: 32px;

            &::after {
                content: '';
                position: absolute;
                right: -16px;
                top: 0;
                display: block;
                width: 2px;
                height: 16px;
                background-color: rgba(#fff, .6);
            }
        }

        &__total {
            flex: 1;
            font-family: 'RoboData';
            color: rgba(#fff, .8);
        }
    }
}
</style>