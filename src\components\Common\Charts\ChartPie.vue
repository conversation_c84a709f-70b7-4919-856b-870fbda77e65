<template>
    <!-- 柱状图 -->
    <div ref="rootCharts" class="rootCharts"></div>
</template>
<script>

import {echarts} from './common';
import {PieChart} from 'echarts/charts';
import {onBeforeUnmount, onMounted, ref, watch} from 'vue';

echarts.use([PieChart]);

export default {
    data() {
        return {};
    },
    props: {
        data: {
            type: Array,
            default: () => [],
        },
    },
    setup(props, {emit}) {
        let chart = null;
        const rootCharts = ref(null);

        const resize = () => {
            if (chart) {
                chart.resize();
            }
        };


        // 数据处理
        let max = 100;
        let ratio = 1;
        const formateStrByName = pName => {
            const {name, value, ratio} = props.data.find(e => e.name === pName);
            return `${name}   ${value}起   ${ratio}`;
        };

        const update = () => {
            // 裁剪
            chart.setOption({
                legend: {
                    show: true,
                    selectedMode: false,
                    left: '45%',
                    itemGap: 6 * ratio,
                    top: 'middle',
                    orient: 'vertical',
                    type: 'scroll',
                    itemWidth: 7 * ratio,
                    itemHeight: 7 * ratio,
                    icon: 'circle',
                    textStyle: {
                        color: '#edfffb',
                        fontSize: 12 * ratio,
                    },
                    pageIconSize: 10, // 翻页按钮尺寸
                    pageTextStyle: {
                        // 翻页文本信息
                        color: '#edfffb',
                        fontSize: 12 * ratio,
                    },
                    formatter: params => {
                        return formateStrByName(params);
                    },
                },
                series: [
                    {
                        name: '',
                        type: 'pie',
                        // roseType: 'radius',
                        radius: ['88%', '98%'],
                        center: ['19%', '50%'],
                        clockWise: false,
                        avoidLabelOverlap: true,
                        // selectedOffset: 10,
                        emphasis: {
                            scaleSize: 0,
                        },
                        label: {
                            textStyle: {
                                color: '#fff',
                            },
                            normal: {
                                show: false,
                                position: 'center',
                                textStyle: {
                                    color: '#fff',
                                },
                            },

                            emphasis: {
                                show: true,
                                formatter: params => {
                                    const {name, value} = params;
                                    // const percent = `${params.percent}%`;
                                    const str = `${value}起 \n ${name}`;
                                    return str;
                                },
                                textStyle: {
                                    color: '#fff',
                                    textAlign: 'center',
                                    fontSize: 14 * ratio,
                                    lineHeight: 16 * ratio,
                                    fontWeight: 'bold',
                                },
                            },
                        },
                        data: props.data,
                    },
                ],
            });
        };


        const initChart = () => {
            if (!rootCharts.value) {
                return;
            }
            chart = echarts.init(rootCharts.value, 'emulation');
            window.addEventListener('resize', resize);
        };

        watch(() => props.data, () => {
            update();
        }, {
            // imediate: true,
            // deep: true,
        });

        onMounted(() => {
            initChart();
            update();
        });

        onBeforeUnmount(() => {
            window.removeEventListener('resize', resize);
            chart.dispose();
        });

        return {
            rootCharts,
        };
    },
};

</script>

<style>
.rootCharts {
    width: 100%;
    height: 100%;
}
</style>