<template>
    <div v-if="searchIng" class="search-title">
        <div>
            <span class="bg-icon"></span>
            车辆<span class="green">{{ followPlate }}</span>当前未被感知设备检测，正在全域检索中。
            请<span class="green">继续等待</span>或者
            <span class="cancel" @click="cancelFollow">退出追踪</span>
        </div>
    </div>
</template>

<!-- 孪生组件 -->
<script>
import useCamera from '@/utils/hooks/useCamera';
import useTwinTraffic from './useTwinTraffic';
import {map} from '@/store/engine.js';

import {onBeforeUnmount, watch, computed} from 'vue';
export default {
    name: 'TwinFollow',
    props: {
        showPlate: {
            type: Boolean,
            default: false,
        },
        followPlate: {
            type: String,
            default: '',
        },
        url: {
            type: String,
            required: true,
        },
    },
    setup(props, {emit}) {
        const {center} = useCamera(map);
        const url = computed(() => props.url);

        const {
            searchIng,
            viewTracks,
            followCarInfo,
            track,
            unTrack,
            changePlateState,
        } = useTwinTraffic({
            url,
            center,
        });

        const cancelFollow = () => {
            emit('close');
        };

        watch(
            () => props.followPlate,
            v => {
                if (v) {
                    track({plate: v});
                }
                else {
                    unTrack();
                }
            }, {
                immediate: true,
            }
        );

        watch(() => props.showPlate, v => {
            changePlateState(v);
        }, {
            immediate: true,
        });

        watch(followCarInfo, v => {
            emit('onInfoChange', v);
        });

        watch(viewTracks, v => {
            emit('onListChange', v);
        });

        onBeforeUnmount(() => {
            unTrack();
        });
        return {
            searchIng,
            cancelFollow,
        };
    },
};
</script>

<style lang="less" scoped>

@keyframes rotation {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.search-title {
    position: fixed;
    left: 50%;
    top: 180px;
    transform: translateX(-50%);
    width: max-content;
    background: rgba(32, 72, 63, .6);
    border: 1px solid #08cf12;
    color: #ddd;
    padding: 8px 12px;
    display: flex;
    line-height: 20px;
    font-size: 14px;

    > div {
        display: flex;
        align-items: center;
    }

    .bg-icon {
        background: url("@TrafficLayer/assets/images/leida.png") no-repeat;
        background-size: 100% 100%;
        width: 25px;
        height: 25px;
        margin-right: 5px;
        animation: rotation 2s linear infinite;
    }

    .green {
        margin: 0 5px;
        color: #fff;
        text-shadow: 2px 2px 2px #000;
    }

    .cancel {
        background: rgb(122, 25, 18);
        color: #fff;
        padding: 3px 7px;
        margin: 0 3px;
        cursor: pointer;
        border-radius: 3px;
    }
}
</style>