<template>
    <map-component
        :options="{
            center: mapCenter,
            showSatelliteMap: true,
            showTollStations: true,
        }"
        @mapLoaded="mapLoaded"
    >
        <template v-if="mapInitStatus">
            <slot></slot>
        </template>
    </map-component>
</template>

<script>
import {Map} from '@/components/Common';
import {ref, watch} from 'vue';
import {engine, mapCenter} from '@/store/engine.js';
import {viewToFk} from '@/utils';

export default {
    components: {
        MapComponent: Map,
    },
    setup() {

        const mapInitStatus = ref(false);

        function mapLoaded(map) {
            engine.value = map;
            mapInitStatus.value = true;
        }

        watch(
            () => engine.value,
            val => {
                if (val) {
                    viewToFk();
                }
            },
            {
                immediate: true,
            }
        );

        return {
            mapInitStatus,
            mapLoaded,
            mapCenter,
        };
    },
};
</script>