<template>
    <div class="app-menu">
        <el-menu
            :default-active="getCurrentActive"
            background-color="#1c1c1c"
            mode="horizontal"
            @select="handleSelectMenu"
        >
            <menu-item
                v-for="item in list"
                :key="item"
                :info="item"
            />
        </el-menu>
    </div>
</template>

<script>
import {Menu} from 'element-ui';
import MenuItem from './MenuItem.vue';
import {useRoute} from '@/utils';
import {computed} from 'vue';

export default {
    components: {
        [Menu.name]: Menu,
        MenuItem,
    },
    props: {
        list: Array,
    },
    setup(props, {emit}) {
        const route = useRoute();

        const getCurrentActive = computed(() => route.meta?.currentActiveMenu || route.path);

        function handleSelectMenu(e) {
            emit('select', e);
        }

        return {
            handleSelectMenu,
            getCurrentActive,
        };
    },
};
</script>

<style lang="less" scoped>
.app-menu {
    & /deep/ .el-menu {
        border-bottom: none;

        &-item,
        .el-submenu,
        .el-submenu__title {
            width: 132px !important;
            padding: 0;
            text-align: center;
            font-size: 16px;
        }

        &-item {
            background-color: transparent !important;
        }

        .el-submenu__title:hover,
        .el-submenu.is-opened .el-submenu__title,
        .el-submenu.is-active .el-submenu__title,
        &-item:hover,
        &-item.is-active {
            border-bottom-color: #28c282 !important;
            font-weight: bold;
            color: #fff !important;
            background-image: linear-gradient(180deg, rgba(0, 255, 149, 0), rgba(0, 255, 149, .2) 100%);
        }
    }
}
</style>