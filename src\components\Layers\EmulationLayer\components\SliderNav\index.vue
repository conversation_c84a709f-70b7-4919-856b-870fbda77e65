<template>
    <div class="slider-nav-box">
        <div class="slider-nav-list">
            <div
                v-for="item in typeList"
                :key="item.value"
                :class="[
                    'slider-nav-item',
                    {'slider-nav-item-active': item.value === type},
                ]"
                @click="handleClick(item.value)"
            >
                <img class="slider-nav-img" :src="eventIcon">
                <span>{{ item.label }}</span>
            </div>
        </div>
    </div>
</template>

<script>
import eventIcon from '@EmulationLayer/assets/images/nav/event.svg';
import {ref, watch} from 'vue';
export default {
    name: 'SliderNav',
    components: {},
    props: {
        emulationType: {
            type: [String, Number],
            default: 1,
        },
    },
    setup(props, {emit}) {
        const type = ref(1);

        const typeList = [
            {
                label: '突发事件',
                value: 1,
                icon: eventIcon,
            },
            {
                label: '施工区域',
                value: 3,
                icon: eventIcon,
            },
        ];

        watch(
            () => props.emulationType,
            (newVal, oldVal) => {
                if (newVal === oldVal) return;
                type.value = newVal;
            }
        );

        const handleClick = e => {
            if (e === type.value) return;
            type.value = e;
            emit('change', e);
        };

        return {
            eventIcon,
            typeList,
            type,
            handleClick,
        };
    },
};
</script>

<style lang="less" scoped>
.slider-nav-box {
    background: rgba(32, 72, 63, .6);
    width: 78px;
    // height: calc(100vh - 140px);
    // padding: 120px 0 20px 20px;
    margin-left: 20px;
    pointer-events: auto;

    .slider-nav-list {
        display: flex;
        flex-direction: column;

        .slider-nav-item {
            width: 100%;
            height: 70px;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            color: #fff;
            flex-direction: column;
            cursor: pointer;

            .slider-nav-img {
                width: 38px;
                height: 30px;
            }
        }

        .slider-nav-item-active {
            background: rgb(7, 175, 135);
        }
    }
}

</style>