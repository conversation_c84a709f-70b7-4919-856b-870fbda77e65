import {request} from '@/utils/network-helper/index';
import {baseConfig} from '@/utils/common';
const {basePrefix} = baseConfig;

const baseUrl = () => {
    return import.meta.env.MODE !== 'release' ? 'http://10.39.235.24:8751' : origin;
};

const baseCommon = `${basePrefix}/common`;

// 获取广东省的区域数据
export const getProvinceData = async () => {
    const {data, code} = await request.get(baseUrl() + baseCommon
    + '/region/getProvinceData', null, {});
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 根据自划片区，获取对应对城市信息(简要字段)
export const getSimpleCityInfoBySelfZone = async payload => {
    const {data, code} = await request.get(
        baseUrl() + baseCommon + '/region/getSimpleCityInfoBySelfZone', payload, {}
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 根据城市code，获取对应高速路度信息
export const getSimpleRoadInfoByCityCode = async payload => {
    const {data, code} = await request.get(
        baseUrl() + baseCommon + '/region/getSimpleRoadInfoByCityCode',
        payload
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};

// 模糊搜索城市
export const getSimpleCityInfoByKeyWords = async payload => {
    const {data, code} = await request.get(
        baseUrl() + baseCommon + '/region/getSimpleCityInfoByKeyWords',
        payload
    );
    if (+code === 200) {
        return {data, code};
    }
    return {};
};