<template>
    <div class="emulation-button-group">
        <div class="emulation-button" @click="handleChangeState">
            <icon
                :size="36"
                :name="state === 'play' ? 'pause' : 'play'"
                color="rgba(255, 255, 255, .6)"
            />
            <span>{{ state === 'play' ? '暂停' : '继续' }}</span>
        </div>
        <div class="emulation-button" @click="handleClickStop">
            <icon
                :size="36"
                name="stop"
                color="rgba(255, 255, 255, .6)"
            />
            <span>结束</span>
        </div>
    </div>
</template>

<script>
import {Icon} from '@/components/Common';
import {ref} from 'vue';

export default {
    components: {
        Icon,
    },
    setup(props, {emit}) {
        const state = ref('play');

        function handleChangeState() {
            state.value = state.value === 'pause' ? 'play' : 'pause';
            emit(state.value, state.value);
        }

        function handleClickStop() {
            emit('stop');
        }

        return {
            state,
            handleClickStop,
            handleChangeState,
        };
    },
};
</script>

<style lang="less" scoped>
.emulation-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background-color: rgba(#fff, .1);
    backdrop-filter: blur(10px);
    cursor: pointer;
    user-select: none;

    &:not(:first-child) {
        margin-left: 16px;
    }

    &:active {
        opacity: .8;
    }

    span {
        margin-top: 8px;
        color: rgba(255, 255, 255, .6);
    }

    &-group {
        display: flex;
        position: absolute;
        top: 20px;
        right: 20px;
    }
}
</style>