
import {watch, onMounted, getCurrentInstance, shallowRef} from 'vue';
import {useResizeObserver} from '@vueuse/core';
import {debounce} from 'lodash';
import {echarts} from './common';
import useRatio from './useRatio';


const main = (chartRef, options) => {
    const {
        getOptions,
        setOptionCb,
    } = options;
    const props = getCurrentInstance()?.props;
    const chart = shallowRef();
    const {ratio, resetRatio} = useRatio(1080);

    const initChart = () => {
        const option = getOptions(ratio.value);
        chart.value?.setOption(option, true, false);
        setOptionCb?.();
    };

    const forceRender = () => {
        initChart();
    };

    onMounted(() => {
        chart.value = echarts.init(chartRef.value);
        initChart();
    });

    watch(() => props?.data, initChart);

    useResizeObserver(
        chartRef,
        debounce(() => {
            const ratioIsChange = resetRatio();
            if (ratioIsChange) {
                initChart();
            }
            chart.value?.resize();
        }, 300, {
            leading: true,
            trailing: true,
        })
    );

    return {
        chart,
        ratio,
        resetRatio,
        forceRender,
    };
};


export default main;