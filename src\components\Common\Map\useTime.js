
import {shallowRef, ref, watch} from 'vue';
import {StaticSky} from '@baidu/mapv-three';

const sky = shallowRef(null);

const main = () => {

    const initSky = () => {
        sky.value && window.ENGINE?.remove(sky.value);
        sky.value = window.ENGINE.add(new StaticSky());
        sky.value.time = 3600 * 14.5;
        return sky.value;
    };

    return {
        sky,
        initSky,
    };
};



export default main;