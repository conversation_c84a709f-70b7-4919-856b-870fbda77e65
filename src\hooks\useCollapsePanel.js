import {useWindowSize, watchDebounced} from '@vueuse/core';
import {ref} from 'vue';

export function useCollapsePanel() {
    const collapse = ref(window.innerWidth / window.innerHeight < 3.6);

    const {width, height} = useWindowSize();

    const toggleCollapse = () => {
        collapse.value = !collapse.value;
    };

    function init() {
        const aspectRatio = window.innerWidth / window.innerHeight;
        collapse.value = aspectRatio < 3.6;
    }

    watchDebounced(
        () => [width.value, height.value],
        () => {
            init();
        },
        {
            immediate: true,
            debounce: 200,
        }
    );


    return {
        collapse,
        toggleCollapse,
    };
}