<template>
    <Card
        class="lane-perf-card"
        title="车道性能"
        card-type="card-long-1"
    >
        <template #content>
            <div class="lane-perf-card-content">
                <div
                    v-for="card in getCardInfo"
                    :key="card.title"
                    class="lane-perf-card-item"
                >
                    <div class="lane-perf-card-item__top">
                        <div class="lane-perf-chart">
                            <LeftPie
                                v-if="show"
                                :colors="card.chartColor"
                                :data="card.chartData"
                                :active-index="card.activeIndex"
                            />
                        </div>
                        <div class="lane-perf-data">
                            <div class="lane-perf-data__value">
                                <span>{{ card.value }}</span>
                                <span>{{ card.unit }}</span>
                            </div>
                            <div class="lane-perf-data__percentage">
                                <span>成功率</span>
                                <span>{{ card.percentage }}%</span>
                            </div>
                        </div>
                    </div>
                    <div class="lane-perf-card-item__bottom">
                        {{ card.title }}
                    </div>
                </div>
            </div>
        </template>
    </Card>
</template>

<script setup>
import {Card, LeftPie} from '@/components/Common';
import {computed, onMounted, ref} from 'vue';
import {tollStation} from '@/views/TollStation/store';
import {getLanePerformance} from '@/api/tollStation';
import {formatCurrency} from '@/utils';

const cardData = ref({
    tranRation: 0,
    initiatedTransNum: 0,
    ration: 0,
    plateCaptureSucNum: 0,
});

const show = ref(true);

const cardConfig = [
    {
        title: '通行成功率',
        enTitle: 'Traffic success rate',
        percentageField: 'tranRation',
        percentageText: '成功率',
        valueField: 'initiatedTransNum',
        chartColor: ['rgb(0, 176, 255)', 'rgb(208, 208, 208)'],
        unit: '万辆',
        activeIndex: 0,
    },
    {
        title: '车牌识别准确率',
        enTitle: 'License plate recognition accuracy',
        percentageField: 'ration',
        percentageText: '准确率',
        valueField: 'plateCaptureSucNum',
        chartColor: ['rgb(85, 218, 218)', 'rgb(208, 208, 208)'],
        unit: '万辆',
        activeIndex: 0,
    },
];

const getCardInfo = computed(() => {
    return cardConfig.map(item => {
        const value = cardData.value[item.valueField] || 0;
        const percentage = cardData.value[item.percentageField] || 0;
        const total = (value / (percentage / 100) || 0);
        const other = total - value;
        return {
            ...item,
            value: formatCurrency(value),
            percentage: percentage,
            chartData: [
                {
                    name: '成功率',
                    value: value,
                },
                {
                    name: '失败率',
                    value: other,
                },
            ],
        };
    });
});

async function fetchData() {
    show.value = false;
    const {data} = await getLanePerformance({
        stationId: tollStation.value.stationId,
        stationName: tollStation.value.stationName,
    });
    cardData.value = {
        tranRation: data.tranRation,
        initiatedTransNum: data.initiatedTransNum,
        ration: data.ration,
        plateCaptureSucNum: data.plateCaptureSucNum,
    };
    show.value = true;
}

onMounted(() => {
    fetchData();
});

</script>

<style lang="less" scoped>
.lane-perf-card {
    &-content {
        display: flex;
        justify-content: space-between;
        height: 315px;
    }

    &-item {
        width: 584px;
        height: 100%;
        display: flex;
        flex-direction: column;
        background-image:
            linear-gradient(
                to right,
                rgba(36, 104, 242, 0),
                rgba(36, 104, 242, .3),
            );
        border-top: 1px solid;
        border-image:
            linear-gradient(
                to right,
                rgba(1, 255, 229, .5),
                rgb(36, 104, 242),
            ) 1;

        &__top {
            flex: 1;
            padding: 32px;
            display: flex;
            align-items: center;
        }

        &__bottom {
            flex-shrink: 0;
            height: 72px;
            line-height: 72px;
            text-align: center;
            font-size: 24px;
            color: #fff;
            background-color: rgba(18, 74, 166, .24);
            backdrop-filter: blur(10px);
        }
    }
}

.lane-perf-chart {
    width: 178px;
    height: 178px;
}

.lane-perf-data {
    margin-left: 32px;

    &__value {
        font-size: 14px;

        span:first-child {
            font-size: 42px;
            font-family: RoboData;
            color: #fff;
            margin-right: 4px;
        }
    }

    &__percentage {
        margin-top: 16px;
        width: 310px;
        height: 48px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-image:
            linear-gradient(
                to right,
                rgba(0, 0, 0, .3),
                rgba(0, 0, 0, 0)
            );

        span:nth-child(1) {
            font-size: 20px;
            display: inline-flex;
            align-items: center;

            &::before {
                content: '';
                display: inline-block;
                width: 6px;
                height: 6px;
                background-color: rgb(0, 176, 255);
                margin-right: 8px;
            }
        }

        span:nth-child(2) {
            font-size: 20px;
            color: #fff;
            font-family: RoboData;
        }
    }
}
</style>