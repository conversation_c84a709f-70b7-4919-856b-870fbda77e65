

<script>
import {lineManager} from '@TrafficLayer/utils/map/index';
import {onMounted, onUnmounted, watch} from 'vue';
import {number} from 'echarts';
export default {
    name: 'Lines',
    props: {
        info: {
            type: Object,
            default: () => null,
        },
        lineWidth: {
            type: number,
            default: 8,
        },
    },
    setup(props, {emit}) {
        const drewLine = () => {
            const {id, position, color, lineWidth = 8} = props.info;
            lineManager.add(
                id,
                position,
                {
                    color,
                    width: lineWidth,
                    clickCallback: e => {
                        emit('handlerClick', e, props.info);
                    },
                }
            );
        };

        const removeLine = id => {
            lineManager.removeByName(id);
        };

        watch(() => props.info, (n, o) => {
            if (o.id && o.id !== n.id) {
                removeLine(o.id);
            }
            drewLine();
        });

        onMounted(() => {
            drewLine();
        });

        onUnmounted(() => {
            removeLine(props.info.id);
        });
        // return {

        // };
    },
};
</script>