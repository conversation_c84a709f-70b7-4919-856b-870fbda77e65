<template>
    <div class="toll-station-marker">
        <bubble-marker
            v-for="item in tollList"
            :key="item"
            :manager-instace="domManager"
            bubble-type="rect"
            bubble-color="#009539"
            icon-name="toll-station"
            :info="{
                ...item,
                clickCallback: handleClick,
            }"
        >
            <div
                class="toll-station-popup"
            >
                <detail-list-card
                    value
                    :show-close="false"
                    :width="312"
                    :title="item.stationName"
                    :list="[
                        {
                            label: '桩号位置',
                            value: item.stake,
                        },
                        {
                            label: '入口车道数：',
                            value: item.enterLaneNum,
                        },
                        {
                            label: '出口车道数：',
                            value: item.exitLaneNum,
                        },
                        {
                            label: '收费站状态：',
                            ortherClass: {
                                'toll-station-status': true,
                            },
                            value: item.tollStationStatus || '正常运行中',
                        },
                    ]"
                />
            </div>
        </bubble-marker>
    </div>
</template>

<script setup>
import {BubbleMarker} from '@/components/Common';
import {domManager} from '@/views/TollStation/utils';
import DetailListCard from '@/components/Common/Card/detailList.vue';
import {getTollList} from '@/api/tollStation';
import {onMounted, ref} from 'vue';

const emit = defineEmits(['clickMarker']);

const tollList = ref([]);

async function fetchData() {
    const {data} = await getTollList();
    tollList.value = data.map(i => ({
        ...i,
        position: [i.lng, i.lat, i.alt],
    })); ;
}

function handleClick(e) {
    emit('clickMarker', e);
}

onMounted(() => {
    fetchData();
});
</script>

<style lang="less" scoped>
.bubble-marker {
    &:hover .toll-station-popup {
        display: block;
    }

    .toll-station-popup {
        display: none;
    }

    /deep/ .toll-station-status {
        background-color: rgba(#4de800, .6);
        color: #fff;
        line-height: 24px;
        padding: 0 6px;
        margin-top: 4px;
    }
}
</style>