import {addTwin, removeTwin} from '../index';
// 车辆孪生管理器
class TwinManager {
    constructor(engine) {
        this.twinMap = new Map();
        this.engine = engine;
    }
    addTwin(name, showLabel = false) {
        const twin = addTwin(showLabel, this.engine);
        this.twinMap.set(name, twin);
    }
    getTwinByName(name) {
        return this.twinMap.get(name);
    }
    removeTwinByName(name) {
        const twin = this.twinMap.get(name);
        twin && removeTwin(twin, this.engine);
        this.twinMap.delete(name);
    }

    getAll() {
        return [...this.twinMap.values()];
    };

    clear() {
        [...this.twinMap.keys()].forEach(icon => {
            this.removeTwinByName(icon);
        });
        this.twinMap.clear();
    }

}

export {
    TwinManager,
};