<template>
    <svg
        class="icon"
        aria-hidden="true"
        :style="{
            '--fontSize': fontSize,
        }"
    >
        <use :xlink:href="`#${type}-${name}`"/>
    </svg>
</template>

<script setup>
import Icons from '../iconFont/index.js';
const iconName = new Set();
const props = defineProps({
    type: {
        type: String,
        default: () => 'hw',
    },
    name: {
        type: String,
        required: true,
    },
    fontSize: {
        type: Number,
        default: 16,
    },
});

if (!iconName.has(props.type)) {
    Icons[props.type]?.();
    iconName.add(props.type);
}

</script>
<style lang="less" scoped>
.icon {
    display: inline-block;
    aspect-ratio: 1;
    fill: currentColor;
    width: calc(var(--fontSize) * 1px);
    height: max-content;
    // vertical-align: -.1em;
    // font-size: calc(var(--fontSize) * 1px);
}
</style>