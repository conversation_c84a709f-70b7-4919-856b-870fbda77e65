import * as echarts from 'echarts';
import chartStyle from '@/assets/json/chartStyle.json';
import {onMounted} from 'vue';
import {useResizeObserver} from '@vueuse/core';
import {debounce} from 'lodash-es';

echarts.registerTheme('default', chartStyle);

export function useEcharts(chartElRef, theme = 'default') {

    /** @type {echarts.ECharts} */
    let chartInstance = null;

    function initEcharts() {
        chartInstance = echarts.init(chartElRef.value, theme);
    }

    onMounted(() => {
        initEcharts();
    });

    /**
     *
     * @param {echarts.EChartsCoreOption} options
     */
    function setOptions(options) {
        chartInstance.setOption(options);
    }

    /**
     *
     * @param {echarts.ResizeOpts} options
     */
    function resize(options) {
        if (!options) {
            options = {
                animation: {
                    duration: 200,
                    easing: 'linear',
                },
            };
        }
        chartInstance.resize(options);
    }

    useResizeObserver(
        chartElRef,
        debounce(() => {
            resize();
        }, 100)
    );

    return {
        echarts,
        chartInstance,
        setOptions,
        resize,
    };
}