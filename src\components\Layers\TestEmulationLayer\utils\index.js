import {apiHost, wsHost} from '@WeatheWarningLayer/store/common';

import {engine} from '@/store/engine';
import {BaseVideoHost} from '@/store/common';

// 初始化基础配置
export const initTestEmulationLayerConfig  = options => {
    const {engine: _engine, apiHost: _apiHost, wsHost: _wsHost, videoHost: _videoHost} = options;
    if (!engine) {
        console.error('engine地图实例 is required');
        return;
    }
    engine.value = _engine;
    apiHost.value = _apiHost;
    wsHost.value = _wsHost;
    BaseVideoHost.value = _videoHost;
};