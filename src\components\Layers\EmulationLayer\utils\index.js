import {apiHost, wsHost} from '@EmulationLayer/store/common';

import {engine} from '@/store/engine';
import {BaseVideoHost} from '@/store/common';
import {burst_event_type_list, build_type_list} from '@EmulationLayer/config';

// 初始化基础配置
export const initEmulationLayerConfig  = options => {
    const {engine: _engine, apiHost: _apiHost, wsHost: _wsHost, videoHost: _videoHost} = options;
    if (!engine) {
        console.error('engine地图实例 is required');
        return;
    }
    engine.value = _engine;
    apiHost.value = _apiHost;
    wsHost.value = _wsHost;
    BaseVideoHost.value = _videoHost;
};

/**
 * 查找事件类型
 * @param {*} emulationType 仿真类型
 * @param {*} eventType 事件类型
 * @returns
 */
export const findEventType = (eventType, emulationType = 1) => {
    // 1-实时事件
    const list = {
        1: burst_event_type_list,
        3: build_type_list,
    };

    if ([0, -1].includes(eventType) || eventType) {
        const cur = list[emulationType]?.find(item => item.code === eventType);
        return cur;
    }
};