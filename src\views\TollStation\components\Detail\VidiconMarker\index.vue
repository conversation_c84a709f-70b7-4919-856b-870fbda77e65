<template>
    <div>
        <vidicon-marker
            v-for="vidicon in vidiconList"
            :key="vidicon.code"
            :info="vidicon"
            :device-code="vidicon.code"
            :manager-instace="domManager"
        />
    </div>
</template>

<script setup>
import {VidiconMarker} from '@/components/Common';
import {getTollCameraList} from '@/api/tollStation';
import {tollStation} from '@/views/TollStation/store';
import {onMounted, ref} from 'vue';
import {domManager} from '@/views/TollStation/utils';

const vidiconList = ref([]);

async function fetchData() {
    const {data} = await getTollCameraList({
        stationId: tollStation.value.stationId,
        stationName: tollStation.value.stationName,
    });
    vidiconList.value = data.map(item => ({
        position: [item.longitude, item.latitude, tollStation.value.alt],
        sectionName: item.sectionName,
        deviceStake: item.stakeNumber,
        code: item.cameraIndexCode,
        name: item.deviceName,
    }));
}

onMounted(() => {
    fetchData();
});

</script>