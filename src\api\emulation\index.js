import {request} from '@/utils/network-helper/index';
import {emulationConfig} from '@/utils/common';


const {
    baseApiHost = '',
    basePrefix,
} = emulationConfig;

const baseEmulation = `${baseApiHost}${basePrefix}`;

const Api = {
    GetSchemeList: `${baseEmulation}/emulation/schemeList`,
    CopyScheme: `${baseEmulation}/scheme/copy`,
    DeleteScheme: `${baseEmulation}/scheme/delete`,
    GetHighSpeedInfo: `${baseEmulation}/emulation/getHighSpeedInfo`,
    GetStakenumberlist: `${baseEmulation}/emulation/getStakeNumberList`,
    GetStakeNumber: `${baseEmulation}/stakeNumber/getStakeNumber`,
    GetEmulationRange: `${baseEmulation}/emulation/getEmulationRange`,
    GetConstructionGeo: `${baseEmulation}/emulation/getConstructionGeo`,
    GetTollList: `${baseEmulation}/emulation/getTollList`,
    FlowList: `${baseEmulation}/emulation/flowList`,
    CreateScheme: `${baseEmulation}/scheme/add`,
    UpdateScheme: `${baseEmulation}/scheme/modify`,
    GetUuid: `${baseEmulation}/emulation/getUuid`,
    GetEvaluateReport: `${baseEmulation}/emulation/statistics`,
    GetSchemeInfoById: `${baseEmulation}/emulation/schemeById`,
    SeeTravelResult: `${baseEmulation}/scheme/seeTravelResult`,
    RunEmulation: `${baseEmulation}/scheme/run`,
    EndEmulation: `${baseEmulation}/scheme/end`,
    GetCustomHighInfo: `${baseEmulation}/emulation/getCustomHighInfo`,
};

/**
 * 获取方案列表
 */
export function getSchemeList(params) {
    return request.post(Api.GetSchemeList, params);
}

/**
 * 复制方案
 * @param targetId 目标方案ID
 */
export function copyScheme(targetId) {
    return request.get(`${Api.CopyScheme}?id=${targetId}`);
}


/**
 * 复制方案
 * @param targetId 目标方案ID
 */
export function deleteScheme(targetId) {
    return request.get(`${Api.DeleteScheme}?id=${targetId}`);
}

/**
 * 高速的基础信息
*/
export function getHighSpeedInfo(params) {
    return request.get(Api.GetHighSpeedInfo, params);
}

/**
 * 获取高速度的桩号列表
 */
export function getStakenumberlist(highSpeedName) {
    const url = `${Api.GetStakenumberlist}?toCoordinateType=gcj02&highSpeedName=${highSpeedName}`;
    return request.get(url);
};

export const getPointsElevation = async payload => {
    const envMode = import.meta.env.MODE;
    const _baseUrl = envMode === 'release' || envMode === 'live'
        ? 'https://gjdt.private.gdcg.cn'
        : 'http://10.27.57.4:8211';
    const {data}
        = await fetch(`${_baseUrl}/height/?x=${payload[0]}&y=${payload[1]}`,
            {
                method: 'GET',
            }).then(response => response.json());
    if (data) {
        return {
            data,
        };
    }
    return {};
};

// 根据经纬度获取高速路信息
export function getStakeNumber(longitude, latitude) {
    const params = `longitude=${longitude}&latitude=${latitude}`;
    return request.get(`${Api.GetStakeNumber}?${params}`);
};

// 获取仿真桩号范围
export function getEmulationRange(parmas) {
    return request.post(Api.GetEmulationRange, parmas);
};

// 获取施工渲染路段信息
export function getConstructionGeo(parmas) {
    return request.post(Api.GetConstructionGeo, parmas);
};

// 高速路收费站列表
export function getTollList(parmas) {
    return request.post(Api.GetTollList, parmas);
};

export function flowList(parmas) {
    return request.post(Api.FlowList, parmas);
};

export function createScheme(parmas) {
    return request.post(Api.CreateScheme, parmas);
};

export function updateScheme(parmas) {
    return request.post(Api.UpdateScheme, parmas);
};

export function getUuid() {
    return request.get(Api.GetUuid);
};

export function getEvaluateReport(parmas) {
    return request.post(Api.GetEvaluateReport, parmas);
};

export function getSchemeInfoById(id) {
    return request.get(`${Api.GetSchemeInfoById}?schemeId=${id}`);
};

export function seeTravelResult({id, uuid}) {
    return request.get(`${Api.SeeTravelResult}?id=${id}&uuid=${uuid}`);
};

export function runEmulation(parmas) {
    return request.post(Api.RunEmulation, parmas);
};

export function endEmulation({emulationId, schemeId, uuid}) {
    return request.get(`${Api.EndEmulation}?emulationId=${emulationId}&schemeId=${schemeId}&uuid=${uuid}`);
};

// 通过自定义区域获取仿真高速信息
export function getCustomHighInfo(parmas) {
    return request.post(Api.GetCustomHighInfo, parmas);
};