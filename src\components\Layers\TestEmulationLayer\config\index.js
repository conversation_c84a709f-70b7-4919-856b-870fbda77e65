// tab状态枚举
export const tabSelectEnum = {
    LIVE: 'live',
    WARNING: 'warning',
    TYPHOON: 'typhoon',
    FREEZE: 'freeze',
};

// 天气类型枚举
export const weatherTypeMap = {
    1: 'qing',
    2: 'yu',
    3: 'wu',
    4: 'xue',
    5: 'lei', //
    6: 'shachen',
    7: 'bingbao', //
    8: 'yun',
    9: 'yin',
};

// 告警天气预警枚举映射 1-台风,2-暴雨,3-高温,4-寒冷,5-大雾,6-灰霾,7-雷雨,8-道路结冰,9-冰雹,10-森林火险
export const warningTypeMap = {
    1: 'taifeng',
    2: 'baoyu',
    3: 'gaowen',
    4: 'hanleng',
    5: 'dawu',
    6: 'huimai',
    7: 'leiyu',
    8: 'jiebing',
    9: 'bingbao',
    10: 'huoxian',

};

// 影响路段线颜色枚举 根据level等级
export const lineColorMap = {
    1: 'rgb(58, 106, 246)',
    2: 'rgb(216, 180, 19)',
    3: 'rgb(239, 124, 18)',
    4: 'rgb(201, 62, 52)',
};

export const levelMap = {
    1: '一级',
    2: '二级',
    3: '三级',
    4: '四级',
};


export const typhoonLevel = [
    {
        min: 1,
        max: 8,
        value: 3,
    },
    {
        min: 8,
        max: 12,
        value: 3,
    },
    {
        min: 12,
        max: 99,
        value: 1,
    },
];