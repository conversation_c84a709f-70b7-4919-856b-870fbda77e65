<template>
    <div class="faility-maker">
        <!-- 互通立交扎点 -->
        <template v-if="mapFilterData.includes('lijiao')">
            <intelchange-marker
                v-for="(item) in interChangeIconList"
                :key="item.sicId"
                :manager-instace="domManager" icon-name="camera-hutong<PERSON>jiao"
                bubble-color="rgb(7, 237, 222)"
                :info="{
                    position: [item.lng, item.lat, item.alt],
                    label: item.sicId,
                }"
            />
        </template>
        <template v-if="mapFilterData.includes('qiaoliang1')">
            <!-- 桥梁扎点 -->
            <bridge-marker
                v-for="(item, index) in bridgeIconList"
                :key="item.structureId"
                :manager-instace="domManager" icon-name="camera-bridge"
                bubble-color="rgb(7, 118, 237)"
                :info="{
                    position: [item.lng, item.lat, item.alt],
                    label: item.structureId,
                    clickCallback: () => clickCallback(item, 'structureId', index),
                }"
            />
        </template>

        <!-- 路面渲染 -->
        <template v-if="mapFilterData.includes('lumian')">
            <map-line
                v-for="item in roadListData" :key="item.roadId"
                :info="{
                    name: item.roadId,
                    color: item.color,
                    lineWidth: 8,
                    transparent: true,
                    onClick: () => lineClick(item),
                }"
                :list="item.positionList"
            />
        </template>

        <div class="detail-list-container">
            <detail-list-bridge
                v-model="showBridge"
                class="detail-card"
                :list="detailBridge.data"
                :title="detailBridge.name"
            />

            <detail-list-road
                v-model="showRoad"
                class="detail-card"
                :list="detailRoad.data"
                :title="detailRoad.name"
            />
        </div>
    </div>
</template>

<script>
import {BubbleMarker, DetailList} from '@/components/Common/index.js';
import {domManager} from './index.js';
import {
} from '@/api/equipment/highspeed.js';
import {ref, onMounted} from 'vue';
import {
    cameraIconList, intelboardIconList, interChangeIconList, bridgeIconList, serviceIconList, tollStationIconList,
} from '../../utils/map.js';
import {
    getRoadInfo,
    roadList,
    getBridgeInfo,
} from '@/api/equipment/facilitydisplay.js';
import {
    mqiGradeDict, mqiGradeRoadDict,
} from '@/config/maintain.js';
import MapLine from './line.vue';

export default {
    props: {
        mapFilterData: {
            type: Array,
            default: () => ([]),
        },
    },
    components: {
        IntelchangeMarker: BubbleMarker,
        BridgeMarker: BubbleMarker,
        MapLine,
        DetailListBridge: DetailList,
        DetailListRoad: DetailList,
    },
    setup(props) {
        const roadListData = ref([]);

        const init = () => {
            roadList().then(res => {
                roadListData.value = res.data.map(item => {
                    return {
                        ...item,
                        color: mqiGradeRoadDict.find(e => e.mqiGrade === item.mqiGrade)?.color || '#ccc',
                        positionList: item.positionList.map(e => {
                            return e.position;
                        }),
                    };
                });
            });
        };
        onMounted(() => {
            init();
        });

        const showBridge = ref(false);
        const showRoad = ref(false);
        const detailRoad = ref({});
        const detailBridge = ref({});
        const raodDict = [
            {label: '所属行政区', value: 'districtName'},
            {label: '桩号区间', format: e => {
                return `${e.startStake}-${e.endStake}`;
            }},
            {label: '起始经纬度', format: e => {
                return `${e.positionList[0].position.map(item => item.toFixed(4))}`;
            }},
            {label: '结束经纬度', format: e => {
                return `${e.positionList[e.positionList.length - 1].position.map(item => item.toFixed(4))}`;
            }},
            {label: '全长', value: 'roadLength'},
            {label: '评定等级', format: e => {
                return `${mqiGradeRoadDict.find(item => e.mqiGrade === item.mqiGrade)?.value || '暂无信息'}`;
            }},
        ];
        const bridgeDict = [
            {label: '所属行政区', value: 'districtName'},
            {label: '桩号区间', format: e => {
                return `${e.startStake || '暂无起始桩号信息'}-${e.endStake || '暂无结束桩号信息'}`;
            }},
            {label: '起始经纬度', format: e => {
                return `${(e.startLng && parseFloat(e.startLng).toFixed(4))
                    || parseFloat(e.centerLng).toFixed(4)
                    || '暂无起始经度信息'}， ${(e.startLat && parseFloat(e.startLat).toFixed(4))
                    || parseFloat(e.centerLat).toFixed(4)
                    || '暂无起始纬度信息'}`;
            }},
            {label: '结束经纬度', format: e => {
                return `${(e.endLng && parseFloat(e.endLng).toFixed(4))
                    || parseFloat(e.centerLng).toFixed(4)
                    || '暂无起始经度信息'}， ${(e.endLat && parseFloat(e.endLat).toFixed(4))
                    || parseFloat(e.centerLat).toFixed(4)
                    || '暂无起始纬度信息'}`;
            }},
            {label: '全长', value: 'bridgeLength'},
            {label: '评定等级', format: e => {
                return `${mqiGradeDict.find(item => e.mqiGrade === item.mqiGrade)?.value || '未评定'}`;
            }},
        ];
        // 道路点击
        const lineClick = e => {
            getRoadInfo({
                roadId: e.roadId,
                startStake: e.startStake,
                endStake: e.endStake,
            }).then(res => {
                detailRoad.value = {
                    name: res.data.roadNameStake,
                    data: raodDict.map(item => ({
                        label: item.label,
                        value: item.format ? item.format(res.data) : res.data[item.value],
                    })),
                };
                showRoad.value = true;
            });
        };

        const clickCallback = e => {
            getBridgeInfo(e.structureId).then(res => {
                detailBridge.value = {
                    name: res.data.brdgeName,
                    data: bridgeDict.map(item => ({
                        label: item.label,
                        value: item.format ? item.format(res.data) : res.data[item.value],
                    })),
                };
                showBridge.value = true;
            });
        };

        return {
            domManager,
            cameraIconList, serviceIconList,
            interChangeIconList, bridgeIconList,
            intelboardIconList, tollStationIconList,
            roadListData,
            showRoad, showBridge,
            detailRoad, detailBridge,
            lineClick, clickCallback,
        };
    },
};
</script>

<style lang="less" scoped>
.detail-list-container {
    display: flex;
    align-items: center;
    min-width: 500px;
    position: fixed;
    top: 50%;
    left: 50%;
    z-index: 999;
    transform: translate(-50%, -50%);

    .detail-card {
        &:last-child {
            margin-left: 20px;
        }
    }
}
</style>